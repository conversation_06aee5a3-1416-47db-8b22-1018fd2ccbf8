/* 初始化
----------------------------------------------- */
body {
 font-family: Microsoft Yahei, \5FAE\8F6F\96C5\9ED1, \5b8b\4f53, Arial, Lucida, Verdana, Helvetica, sans-serif;
 color: #555;
 background-color: #F1F1F1;
 min-width: 320px;
}
body, button, input, textarea {
 font-size: 12px;
 line-height: 1.531;
 outline: none;
 margin: 0;
 padding: 0;
 border: 0;
}
p, ul, ol, dl, dt, dd, form, blockquote {
 margin: 0;
 padding: 0;
}
a {
 text-decoration: none;
 color: #333333;
}
a:hover {
 text-decoration: underline;
}
ul, ol {
 list-style: none;
}
h1, h2, h3, h4, h5, h6 {
 font-size: 12px;
 margin: 0;
 padding: 0;
}
input, select {
 vertical-align: middle;
}
em, b, i {
 font-style: normal;
 font-weight: normal;
}
img {
 vertical-align: middle;
 border: 0;
}
label {
 cursor: pointer;
}
/* 主体框架
----------------------------------------------- */
#wrapper {
 width: 100%;
 min-width: 320px;
 margin: 0 auto;
 max-width: 640px;
 background-color: #F5F5F5;
 overflow: hidden;
}
/* -- header -- */
#header {
 zoom: 1;
 overflow: hidden;
 background-color: #0072C6;
 height: 45px;
 margin-bottom: 1px;
 color: #FFF;
 text-align: center;
 font-size: 16px;
}
#header a {
 color: #FFF;
}
#header em {
 line-height: 45px;
}
#header .logo {
 float: left;
 padding-left: 11px;
}
#header .logo img {
 vertical-align: top;
}
#header a {
 display: block;
 width: 45px;
 height: 45px;
}
#header a.back {
 float: left;
 background: url(images/icon_head.png) no-repeat -135px top;
}
#header a.home {
 float: right;
 background: url(images/icon_head.png) no-repeat left top;
}
#header a.siteMap {
 float: right;
 background: url(images/icon_head.png) no-repeat -45px top;
}
#header a.order {
 float: right;
 background: url(images/icon_head.png) no-repeat -90px top;
}
#header .topSearch {
 float: left;
 width: 50%;
 margin: 8px 0 0 8px;
}
#header .topSearch .searchBox {
 border: 1px solid #FFF;
 text-align: left;
}
/* -- mainNav -- */
#mainNav {
 font-size: 13px;
 background-color: #FFF;
}
#mainNav ul {
 zoom: 1;
 overflow: hidden;
}
#mainNav li {
 float: left;
 width: 25%;
}
#mainNav li a {
 display: block;
 text-align: center;
 border-right: solid #EEEEEE 1px;
 border-bottom: solid #EEEEEE 1px;
 line-height: 55px;
}
#mainNav li a.last {
 border-right: none;
}
/* 首页样式
----------------------------------------------- */
/* -- indexSearch -- */
#indexSearch {
 background-color: #EEEEEE;
 height: 30px;
 padding: 10px;
}
#indexSearch .searchBox {
 border: 1px solid #DDDDDD;
}
/* page
----------------------------------------------- */
#page {
 padding-bottom: 30px;
 background-color: #FFF;
}
#page h1 {
 border-bottom: 1px solid #EEEEEE;
 color: #333333;
 font-size: 14px;
 font-weight: bold;
 padding: 0 0 10px 10px;
}
#page .content {
 color: #878787;
 line-height: 200%;
 padding: 10px;
}
#page .content img {
 max-width: 100%;
}
/* productList
----------------------------------------------- */
.productList {
 font-size: 13px;
 zoom: 1;
 overflow: hidden;
 background-color: #FFF;
}
.productList dl {
 float: left;
 width: 50%;
}
.productList dd {
 text-align: center;
 border-right: solid #EEEEEE 1px;
 border-top: solid #EEEEEE 1px;
 padding: 10px;
}
.productList dd.clearBorder {
 border-right: none;
}
.productList dd .img {
 margin-bottom: 10px;
}
.productList dd .name {
 margin-bottom: 6px;
}
.productList dd .price {
 color: #999999;
}
/* product
----------------------------------------------- */
#product {
 padding-bottom: 30px;
 background-color: #FFF;
}
#product .img {
 border-bottom: 1px solid #EEEEEE;
 padding: 10px;
 text-align: center;
}
#product .info {
 padding: 10px;
 line-height: 180%;
}
#product .info h1 {
 font-size: 16px;
 color: #333333;
}
#product .info .defined {
 color: #999999;
 padding: 5px 0;
}
#product .info .price {
 color: #FF0000;
 font-size: 16px;
}
#product .info .btnBuy {
 margin-top: 20px;
}
#product .info .btnBuy .addToCart {
 background: #1979CC url(images/btn_addtocart.png) no-repeat 20px 50%;
 color: #FFFFFF;
 padding: 8px 25px 8px 50px;
 text-decoration: none;
 cursor: pointer;
 font-size: 16px;
 -webkit-appearance: none;
}
#product .info .tel {
 margin-top: 20px;
 zoom: 1;
 overflow: hidden;
 line-height: 35px;
 background-color: #1979CC;
 font-weight: bold;
 font-size: 14px;
}
#product .info .tel dt {
 float: left;
 width: 82px;
 height: 35px;
 text-align: right;
 padding-right: 3px;
 background-color: #E0E0E0;
 color: #333;
}
#product .info .tel dd {
 float: left;
 width: 150px;
 height: 35px;
 color: #FFF;
 padding-left: 10px;
}
#product .info .tel dd a {
 color: #FFF;
}
#product .content {
 padding: 15px 0;
}
#product .content img {
 max-width: 100%;
}
#product .content h3 {
 height: 35px;
 line-height: 35px;
 color: #777;
 font-weight: bold;
 font-size: 14px;
 border-bottom: 1px solid #EEE;
 padding-left: 10px;
}
#product .content ul {
 padding: 10px;
 color: #888888;
 line-height: 200%;
}
/* articleList
----------------------------------------------- */
.articleList {
 background-color: #FFF;
}
.articleList dl {
 border-top: solid #EEEEEE 1px;
 padding: 13px;
 height: 42px;
}
.articleList dt {
 margin-bottom: 5px;
 font-size: 13px;
}
.articleList dd {
 color: #999999;
}
.articleList dd em {
 margin-right: 8px;
}
/* article
----------------------------------------------- */
#article {
 padding-bottom: 30px;
 background-color: #FFF;
}
#article h1 {
 border-bottom: 1px solid #EEEEEE;
 color: #333333;
 font-size: 16px;
 font-weight: bold;
 padding: 0 0 10px 10px;
}
#article .info {
 padding: 10px;
 color: #999999;
 font-size: 13px;
}
#article .content {
 color: #666666;
 line-height: 180%;
 font-size: 16px;
 padding: 15px 0;
 overflow: hidden;
}
#article .content h1, 
#article .content h2, 
#article .content h3, 
#article .content h4, 
#article .content h5, 
#article .content h6 {
 margin: 25px 0 15px;
 color: #333;
 font-weight: bold;
 line-height: 1.4;
}
#article .content h1 { font-size: 28px; }
#article .content h2 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
#article .content h3 { font-size: 20px; }
#article .content h4 { font-size: 18px; }
#article .content h5 { font-size: 16px; }
#article .content h6 { font-size: 16px; color: #777; }
#article .content p {
 margin-bottom: 16px;
 line-height: 1.8;
 text-indent: 2em;
}
#article .content p:empty {
 margin: 0;
}
#article .content img {
 max-width: 100%;
 height: auto;
 display: block;
 margin: 20px auto;
 border-radius: 4px;
}
#article .content table {
 width: 100%;
 border-collapse: collapse;
 border-spacing: 0;
 margin: 20px 0;
 overflow: auto;
}
#article .content table th,
#article .content table td {
 padding: 10px 15px;
 border: 1px solid #ddd;
 text-align: left;
}
#article .content table th {
 background-color: #f7f7f7;
 font-weight: bold;
}
#article .content table tr:nth-child(even) {
 background-color: #fcfcfc;
}
#article .content table tr:hover {
 background-color: #f5f5f5;
}
#article .content strong,
#article .content b {
 font-weight: bold;
}
#article .content em,
#article .content i {
 font-style: italic;
}
#article .content u {
 text-decoration: underline;
}
#article .content del,
#article .content s {
 text-decoration: line-through;
}
#article .content ul,
#article .content ol {
 padding-left: 2em;
 margin: 16px 0;
 list-style: inherit;
}
#article .content ul {
 list-style: disc;
}
#article .content ol {
 list-style: decimal;
}
#article .content li {
 margin-bottom: 8px;
}
#article .content span[style*="text-wrap-mode: wrap"] {
 color: #999;
 font-size: 14px;
 font-style: italic;
}
/* 站点地图
----------------------------------------------- */
#catalog {
 zoom: 1;
 overflow: hidden;
}
#catalog li a {
 display: block;
 background: #FFF url(images/icon_arrow_right.png) no-repeat right center;
 border-bottom: 1px solid #EEE;
 line-height: 50px;
 height: 50px;
 padding-left: 10px;
}
/* 提示信息
----------------------------------------------- */
#douMsg {
 text-align: center;
 padding: 50px 0;
 background-color: #FFF;
}
#douMsg dt {
 color: #0072C6;
 font-size: 16px;
 margin-bottom: 30px;
 font-weight: bold;
}
#douMsg dd {
 color: #666;
}
#douMsg dd .back {
 display: block;
 width: 100px;
 line-height: 30px;
 background-color: #1979CC;
 color: #FFF;
 margin: 30px auto;
}
/* 全局样式
----------------------------------------------- */
/* -- incBox -- */
.incBox {
 margin-bottom: 5px;
 background-color: #FFF;
}
.incBox h3 {
 font-weight: bold;
 font-size: 16px;
 color: #0072C6;
 padding: 15px 10px 8px 10px;
}
.incBox h3 .more {
 float: right;
 font-size: 13px;
 color: #777777;
}
/* -- urHere -- */
.urHere {
 background-color: #EEE;
 color: #555;
 padding: 10px;
 height: 24px;
 font-weight: bold;
 font-size: 15px;
}
.urHere a {
 color: #0072C6;
}
.urHere b {
 margin: 0 8px;
}
/* -- tree -- */
.treeBox {
 zoom: 1;
 overflow: hidden;
 padding: 10px 5px;
 background-color: #FFF;
}
.treeBox a {
 display: block;
 float: left;
 margin: 5px;
 border: solid #EEEEEE 1px;
 padding: 3px 6px;
}
.treeBox a.cur {
 background-color: #1979CC;
 color: #FFF;
 border: solid #FFF 1px;
}
/* -- searchBox -- */
.searchBox {
 position: relative;
 background-color: #FFF;
 height: 28px;
 padding-left: 10px;
}
.searchBox .keyword {
 width: 90%;
 color: #999;
 height: 20px;
 line-height: 20px;
 margin: 4px 0;
}
.searchBox .btnSearch {
 position: absolute;
 right: 0;
 border: 0;
 background: #E0E0E0 url(images/btn_search.gif) no-repeat;
 width: 28px;
 height: 28px;
 text-indent: -999px;
 cursor: pointer;
}
/* -- common -- */
.clear {
 clear: both;
 height: 0;
 line-height: 0;
 font-size: 0;
}
.cue {
 color: red;
 font-size: 12px;
}
.none {
 display: none;
}
.pointer {
 cursor: pointer;
}
.fixex {
 position: fixed;
 left: 0;
 bottom: 0;
 z-index: 10000;
 width: 100%;
}
/* -- form -- */
.btn {
 box-sizing:border-box;
 -moz-box-sizing:border-box;
 -webkit-box-sizing:border-box;
 display: inline-block;
 background-color: #0072C6;
 color: #EEE;
 padding: 8px 5px;
 text-transform: capitalize;
 cursor: pointer;
 font-weight: bold;
 text-align: center;
 font-size: 14px;
 -webkit-appearance: none;
 width: 100%;
}
.btnGray {
 box-sizing:border-box;
 -moz-box-sizing:border-box;
 -webkit-box-sizing:border-box;
 display: inline-block;
 border: 1px solid #CCCCCC;
 color: #555555;
 padding: 6px 5px;
 cursor: pointer;
 font-weight: bold;
 text-align: center;
 font-size: 14px;
 -webkit-appearance: none;
 width: 100%;
}
.btnMini {
 display: inline-block;
 border: 1px solid #CCCCCC;
 color: #555555;
 padding: 4px 20px;
 cursor: pointer;
 font-weight: bold;
 text-align: center;
 -webkit-appearance: none;
}
.btnRed {
 border: 1px solid #e4393c;
 color: red;
}
.btnPayment {
 display: inline-block;
 background-color: red;
 color: #FFF;
 padding: 4px 20px;
 text-transform: capitalize;
 cursor: pointer;
 font-weight: bold;
 text-align: center;
 font-size: 14px;
 -webkit-appearance: none;
}
/* -- captcha -- */
.smsCaptchaBox {
 position: relative;
 height: 39px;
}
.smsCaptchaBox .textInput {
 position: absolute;
 left: 0;
 top: 0;
}
.smsCaptchaBox .btnCaptcha {
 position: absolute;
 right: 0;
 top: 0;
}
.btnCaptcha {
 display: inline-block;
 background-color: #1979CC;
 border: 0;
 color: #FFF;
 padding: 10px 22px;
 text-transform: capitalize;
 cursor: pointer;
 font-size: 13px;
}
.btnCaptcha[disabled], .btnCaptcha:disabled, .btnCaptcha.disabled {
 color: #FFF;
 background-color: #b2b2b2;
 opacity: 1;
}
.select {
 border: 1px solid #DDDDDD;
}
/* -- color -- */
.cRed {
 color: #F40;
}
.cOra {
 color: #f30;
}
.cGre {
 color: #0c6;
}
.cBlu {
 color: #69c;
}
.cGra {
 color: #999;
}
/* -- input -- */
.textInput {
 box-sizing:border-box;
 -moz-box-sizing:border-box;
 -webkit-box-sizing:border-box;
 padding: 8px 5px;
 border: 1px solid #DDDDDD;
 font-size: 14px;
 -webkit-appearance: none;
 width: 100%;
}
.textArea {
 box-sizing:border-box;
 -moz-box-sizing:border-box;
 -webkit-box-sizing:border-box;
 padding: 8px 5px;
 border: 1px solid #DDDDDD;
 font-size: 14px;
 -webkit-appearance: none;
 width: 100%;
}
.textAreaAuto {
 box-sizing:border-box;
 -moz-box-sizing:border-box;
 -webkit-box-sizing:border-box;
 border: 1px solid #DDDDDD;
 padding: 0;
 font-size: 12px;
 line-height: 20px;
 resize: none;
 min-height: 40px;
 -webkit-appearance: none;
 width: 100%;
}
/*- inputFile -*/
.inputFile {
 width: 80px;
}
.inputFile .inputFileShow {
 height: 80px;
 line-height: 80px;
 text-align: center;
 color: #555;
 background-color: #F5F5F5;
}
.inputFile .inputFileShow img {
 width: 100%;
 height: 80px;
}
.inputFile .inputFileBtn {
 width: 100%;
 background-color: #EEE;
 color: #999;
 line-height: 25px;
 cursor: pointer;
 text-align: center;
 -webkit-appearance: none;
}
/* tableBasic */
.tableBasic {
 font-size: 13px;
 padding: 5px 10px;
 background-color: #FFF;
}
.tableBasic dl {
 overflow: hidden;
 zoom: 1;
 padding: 3px 0;
}
.tableBasic dl dt {
 float: left;
 width: 70px;
}
.tableBasic dl dd {
 margin-left: 70px;
}
/* formBasic */
.formBasic {
 font-size: 13px;
 padding: 15px 10px;
 background-color: #FFF;
}
.formBasic dl {
 overflow: hidden;
 zoom: 1;
 margin-bottom: 15px;
}
.formBasic dl dt {
 margin-bottom: 3px;
}
.formBasic dl dt i {
 color: #F00;
 margin-left: 5px;
 font-weight: bold;
}
.formBasic dl dd label {
 margin-right: 25px;
 line-height: 38px;
}
.formBasic dl .captcha {
 text-transform: uppercase;
 width: 100px;
}
.formBasic dl .select {
 height: 38px;
}
/* -- pager -- */
.pager {
 text-align: center;
 padding: 10px;
 height: 36px;
 background-color: #FFF;
 margin-top: 1px;
}
.pager a, .pager span, .pager em {
 display: inline-block;
 color: #525252;
 padding: 0 15px;
 line-height: 36px;
}
.pager em {
 color: #AAAAAA;
}
/* -- pager class two -- */
.pager ul {
 display: inline-block;
 *display: inline;
 border-left: 1px solid #dddddd;
}
.pager ul li {
 display: inline;
}
.pager ul li a {
 float: left;
 padding: 0 14px;
 line-height: 38px;
 text-decoration: none;
 background-color: #ffffff;
 border: 1px solid #dddddd;
 border-left-width: 0;
}
.pager ul li a:hover {
 background-color: #f5f5f5;
}
.pager ul .active a {
 background-color: #f5f5f5;
 color: #999999;
 cursor: default;
}
/* -- screen -- */
.screen {
 padding: 5px 15px;
 background-color: #FFF;
 border-bottom: 1px solid #EEE;
}
.screen dl {
 padding: 7px 0;
}
.screen dl dt {
 display: inline-block;
 font-weight: bold;
}
.screen dl dd {
 display: inline-block;
}
.screen dl dd a {
 display: inline-block;
 background-color: #EEE;
 color: #777;
 padding: 1px 5px;
 margin-right: 5px;
}
.screen dl dd a.cur {
 background-color: #CCC;
 color: #555;
}
/* footer
----------------------------------------------- */
#footer {
 background-color: #FFF;
}
#footer .goTop {
 display: block;
 margin: 0 auto 20px auto;
 width: 60px;
 text-align: center;
 background: url(images/icon_gotop.png) no-repeat center 40px;
 padding-top: 48px;
}
#footer ul {
 background-color: #EEEEEE;
 padding: 15px 0;
 text-align: center;
 color: #999999;
}
#footer a {
 color: #555555;
}
#footer li {
 padding: 5px 0;
}
#footer .footNav a {
 margin: 0 5px;
 color: #0072C6;
}
#footer .powered {
 font-size: 11px;
}
