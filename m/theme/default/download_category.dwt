<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width,user-scalable=0,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"/>
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="black" />
<meta name="format-detection" content="telephone=no" />
<link rel="apple-touch-icon-precomposed" href="{$site.m_url}data/logo-icon.png" >
<title>{$page_title}</title>
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="style.css" rel="stylesheet" type="text/css" />
<link href="download.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="images/jquery.min.js"></script>
<script type="text/javascript" src="images/global.js"></script>
</head>
<body>
<div id="wrapper">
 {include file="inc/header.tpl"}
 {include file="inc/download_tree.tpl"}
 <div id="downloadCat">
  <div class="downloadList"> 
   <!-- {foreach from=$download_list name=download_list name=download item=download} --> 
   <dl<!-- {if $smarty.foreach.download.iteration % 4 eq 0} --> class="last"<!-- {/if} -->>
    <dt><a href="{$download.url}">{$download.title}</a></dt>
    <dd>{$lang.add_time}：{$download.add_time} {$lang.click}：{$download.click}</dd>
   </dl>
   <!-- {/foreach} --> 
  </div>
  {include file="inc/pager.tpl"}
 </div>
 {include file="inc/footer.tpl"} </div>
</body>
</html>