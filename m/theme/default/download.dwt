<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width,user-scalable=0,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"/>
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="black" />
<meta name="format-detection" content="telephone=no" />
<link rel="apple-touch-icon-precomposed" href="{$site.m_url}data/logo-icon.png" >
<title>{$page_title}</title>
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="style.css" rel="stylesheet" type="text/css" />
<link href="download.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="images/jquery.min.js"></script>
<script type="text/javascript" src="images/global.js"></script>
</head>
<body>
<div id="wrapper">
 {include file="inc/header.tpl"}
 {include file="inc/ur_here.tpl"}
 <div id="download">
  <!-- {if $download.image} -->
  <div class="img"><a href="{$download.image}" target="_blank"><img src="{$download.image}" width="300" /></a></div>
  <!-- {/if} -->
  <div class="info">
   <h1>{$download.title}</h1>
   <ul>
    <li><b>{$lang.add_time}：</b>{$download.add_time}</li>
    <li><b>{$lang.click}：</b>{$download.click}</li>
    <li><b>{$lang.download_size}：</b>{$download.size}</li>
    <!-- {foreach from=$defined name=defined item=defined} -->
    <li><b>{$defined.arr}：</b>{$defined.value}</li>
    <!-- {/foreach} -->
   </ul>
   <a href="{$download.download_link}" class="btn" target="_blank">{$lang.download_link}</a>
  </div>
  <div class="content">
   <h3>{$lang.download_content}</h3>
   <ul>{$download.content}</ul>
  </div>
 </div>
 {include file="inc/footer.tpl"} </div>
</body>
</html>