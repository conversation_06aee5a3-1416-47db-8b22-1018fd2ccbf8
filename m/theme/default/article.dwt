<!DOCTYPE html><!DOCTYPE html><!DOCTYPE html><!DOCTYPE HTML>










































































































































































</html></body><script type="text/javascript" src="js/dou.js"></script><script type="text/javascript" src="js/bootstrap.min.js"></script><script type="text/javascript" src="js/jquery.min.js"></script> {include file="inc/footer.tpl"} </div> {include file="inc/online_service.tpl"} </div>  </div>   </div>    <!-- {/if} -->    {include file="inc/comment.tpl"}    <!-- {if $open.comment} -->    </div>     </div>      <!-- {/if} -->      <span>{$lang.article_next}：<a href="{$lift.next.url}">{$lift.next.title}</a></span>      <!-- {if $lift.next} -->      <!-- {/if} -->      <span>{$lang.article_previous}：<a href="{$lift.previous.url}">{$lift.previous.title}</a></span>      <!-- {if $lift.previous} -->     <div class="lift">     <div class="content"> {$article.content} </div>     </div>      <!-- {/if} -->      <!-- {foreach from=$defined name=defined item=defined} --> {$defined.arr}：{$defined.value}<!-- {/foreach} -->       <!-- {if $defined} -->      <div class="info">{$lang.add_time}：{$article.add_time} {$lang.click}：{$article.click}      <h1>{$article.title}</h1>    <div id="article">   <div class="col-md-10"> {include file="inc/ur_here.tpl"}   <div class="col-md-2"> {include file="inc/article_tree.tpl"} </div>  <div class="row"> <div class="container mb"><div id="wrapper"> {include file="inc/header.tpl"}<body></head><link rel="canonical" href="{$article.url}" /><!-- Canonical Link -->{/literal}</style>} background-color: #f5f5f5;#article .content table tr:hover {} background-color: #fcfcfc;#article .content table tr:nth-child(even) {} font-weight: bold; background-color: #f7f7f7;#article .content table th {} text-align: left; border: 1px solid #ddd; padding: 10px 15px;#article .content table td {#article .content table th,} overflow: auto; margin: 20px 0; border-spacing: 0; border-collapse: collapse; width: 100%;#article .content table {/* 表格样式 */} border-radius: 4px; margin: 20px auto; display: block; height: auto; max-width: 100%;#article .content img {} margin: 0;#article .content p:empty {} /* 移除了text-indent: 2em; 不再强制段首缩进 */ line-height: 1.6;  /* 修改行距为更合理的1.6 */ margin-bottom: 16px;#article .content p {#article .content h6 { font-size: 16px; color: #777; }#article .content h5 { font-size: 16px; }#article .content h4 { font-size: 18px; }#article .content h3 { font-size: 20px; }#article .content h2 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }#article .content h1 { font-size: 28px; }} line-height: 1.4; font-weight: bold; color: #333; margin: 25px 0 15px;#article .content h6 {#article .content h5, #article .content h4, #article .content h3, #article .content h2, #article .content h1, }  color: inherit !important;#article .content [style*="color"] {/* 确保行内样式的颜色优先级更高 */}  color: inherit; /* 继承父元素的颜色，而不是强制设置 */#article .content * {/* 确保文章内容中的元素可以保留自定义颜色 */}  overflow: hidden;  padding: 15px 0;  font-size: 16px;  line-height: 1.6;  color: #333333 !important; /* 使用!important增加优先级 */#article .content {/* 文章内容样式强化 */<style type="text/css">{literal}<!-- 内联样式确保文章内容样式正确渲染 -->{/literal}</script>}  "datePublished": "{$article.add_time}"  "url": "{$article.url}",  "description": "{$description}",  "image": "{$article.image}",  "name": "{$article.title}",  "@type": "Article",  "@context": "https://schema.org",{<script type="application/ld+json">{literal}<!-- Structured Data --><meta name="twitter:image" content="{$article.image}" /><meta name="twitter:description" content="{$description}" /><meta name="twitter:title" content="{$page_title}" /><meta name="twitter:card" content="summary_large_image" /><!-- Twitter Card tags --><meta property="og:type" content="website" /><meta property="og:url" content="{$article.url}" /><meta property="og:image" content="{$article.image}" /><meta property="og:description" content="{$description}" /><meta property="og:title" content="{$page_title}" /><!-- Open Graph tags --><link href="style.css?v={$css_timestamp}" rel="stylesheet" type="text/css" /><link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" /><link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" /><link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" /><title>{$page_title}</title><meta name="generator" content="{$generator}" /><meta name="description" content="{$description}" /><meta name="keywords" content="{$keywords}" /><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"><meta charset="utf-8"><head><html lang="zh-CN">
































































































































































</html></body><script type="text/javascript" src="js/dou.js"></script><script type="text/javascript" src="js/bootstrap.min.js"></script><script type="text/javascript" src="js/jquery.min.js"></script> {include file="inc/footer.tpl"} </div> {include file="inc/online_service.tpl"} </div>  </div>   </div>    <!-- {/if} -->    {include file="inc/comment.tpl"}    <!-- {if $open.comment} -->    </div>     </div>      <!-- {/if} -->      <span>{$lang.article_next}：<a href="{$lift.next.url}">{$lift.next.title}</a></span>      <!-- {if $lift.next} -->      <!-- {/if} -->      <span>{$lang.article_previous}：<a href="{$lift.previous.url}">{$lift.previous.title}</a></span>      <!-- {if $lift.previous} -->     <div class="lift">     <div class="content"> {$article.content} </div>     </div>      <!-- {/if} -->      <!-- {foreach from=$defined name=defined item=defined} --> {$defined.arr}：{$defined.value}<!-- {/foreach} -->       <!-- {if $defined} -->      <div class="info">{$lang.add_time}：{$article.add_time} {$lang.click}：{$article.click}      <h1>{$article.title}</h1>    <div id="article">   <div class="col-md-10"> {include file="inc/ur_here.tpl"}   <div class="col-md-2"> {include file="inc/article_tree.tpl"} </div>  <div class="row"> <div class="container mb"><div id="wrapper"> {include file="inc/header.tpl"}<body></head><link rel="canonical" href="{$article.url}" /><!-- Canonical Link -->{/literal}</style>} background-color: #f5f5f5;#article .content table tr:hover {} background-color: #fcfcfc;#article .content table tr:nth-child(even) {} font-weight: bold; background-color: #f7f7f7;#article .content table th {} text-align: left; border: 1px solid #ddd; padding: 10px 15px;#article .content table td {#article .content table th,} overflow: auto; margin: 20px 0; border-spacing: 0; border-collapse: collapse; width: 100%;#article .content table {/* 表格样式 */} border-radius: 4px; margin: 20px auto; display: block; height: auto; max-width: 100%;#article .content img {} margin: 0;#article .content p:empty {} /* 移除了text-indent: 2em; 不再强制段首缩进 */ line-height: 1.6;  /* 修改行距为更合理的1.6 */ margin-bottom: 16px;#article .content p {#article .content h6 { font-size: 16px; color: #777; }#article .content h5 { font-size: 16px; }#article .content h4 { font-size: 18px; }#article .content h3 { font-size: 20px; }#article .content h2 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }#article .content h1 { font-size: 28px; }} line-height: 1.4; font-weight: bold; color: #333; margin: 25px 0 15px;#article .content h6 {#article .content h5, #article .content h4, #article .content h3, #article .content h2, #article .content h1, } overflow: hidden; padding: 15px 0; font-size: 16px; line-height: 1.6; /* color: #666666; */ /* 移除强制颜色设置，允许内容保留原有颜色 */#article .content {/* 文章内容样式强化 */<style type="text/css">{literal}<!-- 内联样式确保文章内容样式正确渲染 -->{/literal}</script>}  "datePublished": "{$article.add_time}"  "url": "{$article.url}",  "description": "{$description}",  "image": "{$article.image}",  "name": "{$article.title}",  "@type": "Article",  "@context": "https://schema.org",{<script type="application/ld+json">{literal}<!-- Structured Data --><meta name="twitter:image" content="{$article.image}" /><meta name="twitter:description" content="{$description}" /><meta name="twitter:title" content="{$page_title}" /><meta name="twitter:card" content="summary_large_image" /><!-- Twitter Card tags --><meta property="og:type" content="website" /><meta property="og:url" content="{$article.url}" /><meta property="og:image" content="{$article.image}" /><meta property="og:description" content="{$description}" /><meta property="og:title" content="{$page_title}" /><!-- Open Graph tags --><link href="style.css?v={$css_timestamp}" rel="stylesheet" type="text/css" /><link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" /><link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" /><link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" /><title>{$page_title}</title><meta name="generator" content="{$generator}" /><meta name="description" content="{$description}" /><meta name="keywords" content="{$keywords}" /><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"><meta charset="utf-8"><head><html lang="zh-CN">































































































































































</html></body><script type="text/javascript" src="js/dou.js"></script><script type="text/javascript" src="js/bootstrap.min.js"></script><script type="text/javascript" src="js/jquery.min.js"></script> {include file="inc/footer.tpl"} </div> {include file="inc/online_service.tpl"} </div>  </div>   </div>    <!-- {/if} -->    {include file="inc/comment.tpl"}    <!-- {if $open.comment} -->    </div>     </div>      <!-- {/if} -->      <span>{$lang.article_next}：<a href="{$lift.next.url}">{$lift.next.title}</a></span>      <!-- {if $lift.next} -->      <!-- {/if} -->      <span>{$lang.article_previous}：<a href="{$lift.previous.url}">{$lift.previous.title}</a></span>      <!-- {if $lift.previous} -->     <div class="lift">     <div class="content"> {$article.content} </div>     </div>      <!-- {/if} -->      <!-- {foreach from=$defined name=defined item=defined} --> {$defined.arr}：{$defined.value}<!-- {/foreach} -->       <!-- {if $defined} -->      <div class="info">{$lang.add_time}：{$article.add_time} {$lang.click}：{$article.click}      <h1>{$article.title}</h1>    <div id="article">   <div class="col-md-10"> {include file="inc/ur_here.tpl"}   <div class="col-md-2"> {include file="inc/article_tree.tpl"} </div>  <div class="row"> <div class="container mb"><div id="wrapper"> {include file="inc/header.tpl"}<body></head><link rel="canonical" href="{$article.url}" /><!-- Canonical Link -->{/literal}</style>} background-color: #f5f5f5;#article .content table tr:hover {} background-color: #fcfcfc;#article .content table tr:nth-child(even) {} font-weight: bold; background-color: #f7f7f7;#article .content table th {} text-align: left; border: 1px solid #ddd; padding: 10px 15px;#article .content table td {#article .content table th,} overflow: auto; margin: 20px 0; border-spacing: 0; border-collapse: collapse; width: 100%;#article .content table {/* 表格样式 */} border-radius: 4px; margin: 20px auto; display: block; height: auto; max-width: 100%;#article .content img {} margin: 0;#article .content p:empty {} /* 移除了text-indent: 2em; 不再强制段首缩进 */ line-height: 1.6;  /* 修改行距为更合理的1.6 */ margin-bottom: 16px;#article .content p {#article .content h6 { font-size: 16px; color: #777; }#article .content h5 { font-size: 16px; }#article .content h4 { font-size: 18px; }#article .content h3 { font-size: 20px; }#article .content h2 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }#article .content h1 { font-size: 28px; }} line-height: 1.4; font-weight: bold; color: #333; margin: 25px 0 15px;#article .content h6 {#article .content h5, #article .content h4, #article .content h3, #article .content h2, #article .content h1, } overflow: hidden; padding: 15px 0; font-size: 16px; line-height: 180%; color: #666666;#article .content {/* 文章内容样式强化 */<style type="text/css">{literal}<!-- 内联样式确保文章内容样式正确渲染 -->{/literal}</script>}  "datePublished": "{$article.add_time}"  "url": "{$article.url}",  "description": "{$description}",  "image": "{$article.image}",  "name": "{$article.title}",  "@type": "Article",  "@context": "https://schema.org",{<script type="application/ld+json">{literal}<!-- Structured Data --><meta name="twitter:image" content="{$article.image}" /><meta name="twitter:description" content="{$description}" /><meta name="twitter:title" content="{$page_title}" /><meta name="twitter:card" content="summary_large_image" /><!-- Twitter Card tags --><meta property="og:type" content="website" /><meta property="og:url" content="{$article.url}" /><meta property="og:image" content="{$article.image}" /><meta property="og:description" content="{$description}" /><meta property="og:title" content="{$page_title}" /><!-- Open Graph tags --><link href="style.css?v={$css_timestamp}" rel="stylesheet" type="text/css" /><link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" /><link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" /><link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" /><title>{$page_title}</title><meta name="generator" content="{$generator}" /><meta name="description" content="{$description}" /><meta name="keywords" content="{$keywords}" /><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"><meta charset="utf-8"><head><html lang="zh-CN"><html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width,user-scalable=0,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"/>
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="black" />
<meta name="format-detection" content="telephone=no" />
<title>{$page_title}</title>
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="style.css?v={$css_timestamp}" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="images/jquery.min.js"></script>
<script type="text/javascript" src="images/global.js"></script>
{literal}
<style type="text/css">
/* 文章内容样式强化 - 直接内联确保样式应用 */
#article .content h1, 
#article .content h2, 
#article .content h3, 
#article .content h4, 
#article .content h5, 
#article .content h6 {
 margin: 25px 0 15px;
 color: #333;
 font-weight: bold;
 line-height: 1.4;
}
#article .content h1 { font-size: 28px; }
#article .content h2 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
#article .content h3 { font-size: 20px; }
#article .content h4 { font-size: 18px; }
#article .content h5 { font-size: 16px; }
#article .content h6 { font-size: 16px; color: #777; }

#article .content p {
 margin-bottom: 16px;
 line-height: 1.8;
 text-indent: 2em;
}

#article .content p:empty {
 margin: 0;
}

#article .content img {
 max-width: 100%;
 height: auto;
 display: block;
 margin: 20px auto;
 border-radius: 4px;
}

/* 表格样式 */
#article .content table {
 width: 100%;
 border-collapse: collapse;
 border-spacing: 0;
 margin: 20px 0;
 overflow: auto;
}

#article .content table th,
#article .content table td {
 padding: 10px 15px;
 border: 1px solid #ddd;
 text-align: left;
}

#article .content table th {
 background-color: #f7f7f7;
 font-weight: bold;
}

#article .content table tr:nth-child(even) {
 background-color: #fcfcfc;
}

#article .content table tr:hover {
 background-color: #f5f5f5;
}
</style>
{/literal}
</head>
<body>
<div id="wrapper">
 {include file="inc/header.tpl"}
 {include file="inc/ur_here.tpl"}
 {include file="inc/article_tree.tpl"}
 <div id="article">
  <h1>{$article.title}</h1>
  <div class="info">{$lang.add_time}：{$article.add_time} {$lang.click}：{$article.click}
  <!-- {if $defined} -->
  <!-- {foreach from=$defined name=defined item=defined} --> {$defined.arr}：{$defined.value}<!-- {/foreach} -->
  <!-- {/if} -->
  </div>
  <div class="content">
   {$article.content}
  </div>
 </div>
 <!-- {if $open.comment} -->
 {include file="inc/comment.tpl"}
 <!-- {/if} -->
 {include file="inc/footer.tpl"} </div>
</body>
</html>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width,user-scalable=0,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"/>
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="black" />
<meta name="format-detection" content="telephone=no" />
<title>{$page_title}</title>
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="style.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="images/jquery.min.js"></script>
<script type="text/javascript" src="images/global.js"></script>
<style type="text/css">
/* 文章内容样式强化 - 直接内联确保样式应用 */
#article .content h1, 
#article .content h2, 
#article .content h3, 
#article .content h4, 
#article .content h5, 
#article .content h6 {
 margin: 25px 0 15px;
 color: #333;
 font-weight: bold;
 line-height: 1.4;
}
#article .content h1 { font-size: 28px; }
#article .content h2 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
#article .content h3 { font-size: 20px; }
#article .content h4 { font-size: 18px; }
#article .content h5 { font-size: 16px; }
#article .content h6 { font-size: 16px; color: #777; }

#article .content p {
 margin-bottom: 16px;
 line-height: 1.8;
 text-indent: 2em;
}

#article .content p:empty {
 margin: 0;
}

#article .content img {
 max-width: 100%;
 height: auto;
 display: block;
 margin: 20px auto;
 border-radius: 4px;
}

/* 表格样式 */
#article .content table {
 width: 100%;
 border-collapse: collapse;
 border-spacing: 0;
 margin: 20px 0;
 overflow: auto;
}

#article .content table th,
#article .content table td {
 padding: 10px 15px;
 border: 1px solid #ddd;
 text-align: left;
}

#article .content table th {
 background-color: #f7f7f7;
 font-weight: bold;
}

#article .content table tr:nth-child(even) {
 background-color: #fcfcfc;
}

#article .content table tr:hover {
 background-color: #f5f5f5;
}
</style>
</head>
<body>
<div id="wrapper">
 {include file="inc/header.tpl"}
 {include file="inc/ur_here.tpl"}
 {include file="inc/article_tree.tpl"}
 <div id="article">
  <h1>{$article.title}</h1>
  <div class="info">{$lang.add_time}：{$article.add_time} {$lang.click}：{$article.click}
  <!-- {if $defined} -->
  <!-- {foreach from=$defined name=defined item=defined} --> {$defined.arr}：{$defined.value}<!-- {/foreach} -->
  <!-- {/if} -->
  </div>
  <div class="content">
   {$article.content}
  </div>
 </div>
 <!-- {if $open.comment} -->
 {include file="inc/comment.tpl"}
 <!-- {/if} -->
 {include file="inc/footer.tpl"} </div>
</body>
</html>