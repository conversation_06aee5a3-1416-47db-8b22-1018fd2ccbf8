<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width,user-scalable=0,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"/>
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="black" />
<meta name="format-detection" content="telephone=no" />
<title>{$page_title}</title>
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="style.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="images/jquery.min.js"></script>
<script type="text/javascript" src="images/global.js"></script>
</head>
<body>
<div id="wrapper"> {include file="inc/header.tpl"}
 {include file="inc/ur_here.tpl"}
 <div id="product">
  <div class="img"><a href="{$product.image}" target="_blank"><img src="{$product.image}" width="300" /></a></div>
  <div class="info">
   <h1>{$product.name}</h1>
   <dl class="defined">
    <!-- {foreach from=$defined name=defined item=defined} -->
    <dd>{$defined.arr}：{$defined.value}</dd>
    <!-- {/foreach} -->
   </dl>
   <!-- {if $open.order} -->
   <dl class="btnBuy">
    <form action="{$site.m_url}order.php?rec=insert" method="post">
     <input type="hidden" name="module" value="product" />
     <input type="hidden" name="item_id" value="{$product.id}" />
     <input type="hidden" name="number" value="1" />
     <input type="submit" name="submit" class="addToCart" value="{$lang.order_addtocart}" />
    </form>
   </dl>
   <!-- {else} -->
   <dl class="tel">
    <dt>{$lang.contact_tel}：</dt>
    <dd><a href="tel:{$site.tel}">{$site.tel}</a></dd>
   </dl>
   <!-- {/if} --> 
  </div>
  <div class="content">
   <h3>{$lang.product_content}</h3>
   <ul>
    {$product.content}
   </ul>
  </div>
  <!-- {if $open.comment} --> 
  {include file="inc/comment.tpl"} 
  <!-- {/if} --> 
 </div>
 {include file="inc/footer.tpl"} </div>
</body>
</html>