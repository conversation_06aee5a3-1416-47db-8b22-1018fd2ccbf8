<?php

define('IN_DOUCO', true);

if (!isset($_REQUEST['rec']))
    define('EXIT_INIT', true);

require (dirname(__FILE__) . '/include/init.php');
require (ROOT_PATH . 'include/captcha.class.php');

// 开启SESSION
session_start();

if ($_REQUEST['rec'] == 'sms') {
    $response = $sms->captcha($_POST['sms_token'], $_POST['telphone']);
    echo $response['msg'];
} else {
    // 实例化验证码
    $captcha = new Captcha(70, 25);
    @ob_end_clean(); // 清除之前出现的多余输入
    $captcha->create_captcha();
}

?>