<?php

if (!defined('IN_DOUCO')) {
    die('Hacking attempt');
}
class Check {
    /**
     * +----------------------------------------------------------
     * 判断是否为数字
     * +----------------------------------------------------------
     */
    function is_number($number) {
        if (preg_match("/^[0-9]+$/", $number)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断是否为字母
     * +----------------------------------------------------------
     */
    function is_letter($letter) {
        if (preg_match("/^[a-z]+$/", $letter)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断是否为常规字符
     * +----------------------------------------------------------
     */
    function is_basic_string($string) {
        if (preg_match("/^[A-Za-z0-9._-]+$/", $string)) {
            return true;
        }
    }
 
    /**
     * +----------------------------------------------------------
     * 判断是否为字母
     * +----------------------------------------------------------
     */
    function is_letter_number($char) {
        if (preg_match("/^[a-zA-Z0-9]+$/", $char)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断验证码是否规范
     * +----------------------------------------------------------
     */
    function is_captcha($captcha) {
        if (preg_match("/^[A-Za-z0-9]{4}$/", $captcha)) {
            return true;
        }
    }

    /**
     * +----------------------------------------------------------
     * 判断是否为邮件地址
     * +----------------------------------------------------------
     */
    function is_email($email) {
        if (preg_match("/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/", $email)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断是否为手机号码
     * +----------------------------------------------------------
     */
    function is_telphone($mobile) {
        if (preg_match("/^(13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])\d{8}$/", $mobile)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断是否为身份证
     * +----------------------------------------------------------
     */
    function is_idcard($idcard) {
        if (preg_match("/^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/", $idcard)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断是否为QQ号码
     * +----------------------------------------------------------
     */
    function is_qq($qq) {
        if (preg_match("/^[1-9]*[1-9][0-9]*$/", $qq)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断邮编是否规范/国际通用
     * +----------------------------------------------------------
     */
    function is_postcode($postcode) {
        if (preg_match("/^[A-Za-z0-9_-\s]*$/", $postcode)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断价格是否规范
     * +----------------------------------------------------------
     */
    function is_price($price) {
        if (preg_match("/^[0-9.]+$/", $price)) {
            return true;
        }
    }
    
    /**
     * 判断网址是否规范
     */
    function is_url($url) {
        if (preg_match("/^(http(s)?:\/\/)?(www\.)?[A-Za-z0-9-]+\.[A-Za-z0-9]+\.?[A-Za-z0-9]+(\/)?$/", $url)) {
            return true;
        }
    }
    
    /**
     * 判断域名是否规范，包含localhost
     */
    function is_domain($domain) {
        if (!preg_match("/[\\\~@$%^&=+{};'\"<>]/", $domain)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断是否为rec操作项
     * +----------------------------------------------------------
     */
    function is_rec($rec = '') {
        if ($rec) {
            if (preg_match("/^[a-z_]+$/", $rec))
                return true;
        }
    }

    /**
     * +----------------------------------------------------------
     * 判断extend_id是否规范
     * +----------------------------------------------------------
     */
    function is_extend_id($extend_id) {
        if (preg_match("/^[A-Za-z0-9-_.]+$/", $extend_id)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断别名是否规范
     * +----------------------------------------------------------
     */
    function is_unique_id($unique) {
        if (preg_match("/^[a-z0-9-]+$/", $unique)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断搜素关键字是否合法：字母、中文、数字
     * +----------------------------------------------------------
     */
    function is_search_keyword($search_keyword) {
        if (preg_match("/^[\x{4e00}-\x{9fa5}0-9a-zA-Z_]*$/u", $search_keyword) && $search_keyword) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断用户名是否规范
     * +----------------------------------------------------------
     */
    function is_username($username) {
        if (preg_match("/^[a-zA-Z]{1}([0-9a-zA-Z]|[._]){3,19}$/", $username)) {
            return true;
        }
    }

    /**
     * +----------------------------------------------------------
     * 限制密码长度为6-32位
     * +----------------------------------------------------------
     */
    function is_password($password) {
        if (preg_match("/^.{6,}$/", $password)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 判断是否包含非法字符
     * +----------------------------------------------------------
     */
    function is_illegal_char($char) {
        if (preg_match("/[\\\~@$%^&=+{};'\"<>\/]/", $char)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 检查是否包含中文字符，防止垃圾信息
     * +----------------------------------------------------------
     */
    function if_include_chinese($value) {
        if (preg_match("/[\x{4e00}-\x{9fa5}]+/u", $value)) {
            return true;
        }
    }
    
    /**
     * +----------------------------------------------------------
     * 验证是否输入和输入长度
     * +----------------------------------------------------------
     */
    function ch_length($value, $length) {
        if (strlen($value) > 0 && strlen($value) <= $length) {
            return true;
        }
    }
 
    /**
     * +----------------------------------------------------------
     * 判断数据表名是否规范
     * +----------------------------------------------------------
     */
    function is_table_name($table_name) {
        if (preg_match("/^[A-Za-z0-9-_]+$/", $table_name)) {
            return true;
        }
    }
 
    /**
     * +----------------------------------------------------------
     * 验证表单项是否为空
     * +----------------------------------------------------------
     * $prefix 语言前缀
     * $field_list 需要验证的字段
     * $text_mode 文字模式
     * +----------------------------------------------------------
     */
    function fn_empty($prefix, $field_list, $text_mode = false) {
        $wrong = $GLOBALS['wrong']; // 合并前面的
        $prefix = $prefix . '_';
        $field_list = explode(',', $field_list);
     
        foreach ($field_list as $field) {
            $field = trim($field); // 过滤掉空格
            if (!$_POST[$field]) {
                if ($text_mode) {
                    $wrong .= $GLOBALS['_LANG'][$prefix . $field] . $GLOBALS['_LANG']['is_empty'] . '， ';
                } else {
                    $wrong[$field] = $GLOBALS['_LANG'][$prefix . $field] . $GLOBALS['_LANG']['is_empty'];
                }
            }
        }
     
        return $wrong;
    }
 
    /**
     * +----------------------------------------------------------
     * 验证表单项是否包含非法字符
     * +----------------------------------------------------------
     * $prefix 语言前缀
     * $field_list 需要验证的字段
     * $text_mode 文字模式
     * +----------------------------------------------------------
     */
    function fn_illegal_char($prefix, $field_list, $text_mode = false) {
        $wrong = $GLOBALS['wrong']; // 合并前面的
        $prefix = $prefix . '_';
        $field_list = explode(',', $field_list);
     
        foreach ($field_list as $field) {
            $field = trim($field); // 过滤掉空格
            if ($this->is_illegal_char($_POST[$field])) {
                if ($text_mode) {
                    $wrong .= $GLOBALS['_LANG'][$prefix . $field] . $GLOBALS['_LANG']['illegal_char'] . '， ';
                } else {
                    $wrong[$field] = $GLOBALS['_LANG'][$prefix . $field] . $GLOBALS['_LANG']['illegal_char'];
                }
            }
        }
     
        return $wrong;
    }
 
    /**
     * +----------------------------------------------------------
     * 验证是否登录.需安装会员模块
     * +----------------------------------------------------------
     */
    function login() {
        if (!is_array($GLOBALS['_USER']))
            $GLOBALS['dou']->dou_header($GLOBALS['_URL']['login']);
    }
 
    /**
     * +----------------------------------------------------------
     * 验证是否购买.需安装订单模块
     * +----------------------------------------------------------
     * $module 模块
     * $item_id 项目ID
     * +----------------------------------------------------------
     */
    function order($module, $item_id) {
        $sql = "SELECT * FROM " . $GLOBALS['dou']->table('order_item') . " AS oi LEFT JOIN " . $GLOBALS['dou']->table('order') . " AS o ON oi.order_id = o.order_id WHERE o.status = '1' AND oi.module = '$module' AND oi.item_id = '$item_id'";
        
        if (!$GLOBALS['dou']->num_rows($GLOBALS['dou' ]->query($sql)))
            return true;
    }
    
    /**
     * +----------------------------------------------------------
     * 验证语言包文件夹名称
     * +----------------------------------------------------------
     */
    function is_language_pack($language_pack) {
        if (preg_match("/^[a-z_]+$/", $language_pack)) {
            return true;
        }
    }
 
}

?>