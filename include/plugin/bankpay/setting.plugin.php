<?php
/**
 * DouPHP
 * --------------------------------------------------------------------------------------------------
 * 版权所有 2013-2018 漳州豆壳网络科技有限公司，并保留所有权利。
 * 网站地址: http://www.douco.com
 * --------------------------------------------------------------------------------------------------
 * 这不是一个自由软件！您只能在遵守授权协议前提下对程序代码进行修改和使用；不允许对程序代码以任何形式任何目的的再发布。
 * 授权协议：http://www.douco.com/license.html
 * --------------------------------------------------------------------------------------------------
 * Author: DouCo
 * Release Date: 2015-06-10
 */
if (!defined('IN_DOUCO')) {
    die('Hacking attempt');
}

/* 插件唯一ID
----------------------------------------------------------------------------- */
$plugin['unique_id'] = 'bankpay';

/* 插件名称
----------------------------------------------------------------------------- */
$plugin['name'] = $plugin_mysql['name'] ? $plugin_mysql['name'] : '支付宝网银支付';

/* 插件描述
----------------------------------------------------------------------------- */
$plugin['description'] = $plugin_mysql['description'] ? $plugin_mysql['description'] : '买家可直接通过网上银行进行支付，无需通过支付宝就能完成交易。申请前必须拥有企业支付宝账号。';

/* 插件版本
----------------------------------------------------------------------------- */
$plugin['ver'] = '1.0';

/* 所属组
----------------------------------------------------------------------------- */
$plugin['plugin_group'] = 'payment';

/* 插件参数提交表单
 * type默认为'text'及文本框，可选"text,select,textarea"
 * option默认为空，select默认选项，如array("选项一" => 0,"选项二" => 1)
----------------------------------------------------------------------------- */
// 收款支付宝账号
$plugin['config'][] = array (
        "field" => 'seller_email',
        "name" => '支付宝帐户',
        "value" => $plugin_mysql['config']['seller_email']
);

// 安全检验码，以数字和字母组成的32位字符
$plugin['config'][] = array (
        "field" => 'key',
        "name" => '安全校验码(Key)',
        "desc" => '安全检验码，以数字和字母组成的32位字符',
        "value" => $plugin_mysql['config']['key']
);

// 合作身份者id，以2088开头的16位纯数字
$plugin['config'][] = array (
        "field" => 'partner',
        "name" => '合作者身份(Pid)',
        "desc" => '合作身份者id，以2088开头的16位纯数字',
        "value" => $plugin_mysql['config']['partner']
);
?>