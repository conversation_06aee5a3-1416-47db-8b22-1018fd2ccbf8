<?php
/**
 * DouPHP
 * --------------------------------------------------------------------------------------------------
 * 版权所有 2013-2018 漳州豆壳网络科技有限公司，并保留所有权利。
 * 网站地址: http://www.douco.com
 * --------------------------------------------------------------------------------------------------
 * 这不是一个自由软件！您只能在遵守授权协议前提下对程序代码进行修改和使用；不允许对程序代码以任何形式任何目的的再发布。
 * 授权协议：http://www.douco.com/license.html
 * --------------------------------------------------------------------------------------------------
 * Author: DouCo
 * Release Date: 2015-06-10
 */
if (!defined('IN_DOUCO')) {
    die('Hacking attempt');
}

/* 插件唯一ID
----------------------------------------------------------------------------- */
$plugin['unique_id'] = 'alipay_wap';

/* 插件名称
----------------------------------------------------------------------------- */
$plugin['name'] = $plugin_mysql['name'] ? $plugin_mysql['name'] : '支付宝-手机网站支付';

/* 插件描述
----------------------------------------------------------------------------- */
$plugin['description'] = $plugin_mysql['description'] ? $plugin_mysql['description'] : '手机网站支付主要应用于手机、掌上电脑等无线设备的网页上，通过网页跳转或浏览器自带的支付宝快捷支付实现买家付款的功能，资金即时到账。申请前必须拥有企业支付宝账号。且需要手动上传“商户的私钥（rsa_private_key.pem）”和“支付宝公钥（alipay_public_key.pem）”到对应插件目录（include/plugin/alipay_wap/key）下，具体生成方法请咨询支付宝官方客服';

/* 插件版本
----------------------------------------------------------------------------- */
$plugin['ver'] = '1.0';

/* 所属组
----------------------------------------------------------------------------- */
$plugin['plugin_group'] = 'payment';

/* 插件参数提交表单
 * type默认为'text'及文本框，可选"text,select,textarea"
 * option默认为空，select默认选项，如array("选项一" => 0,"选项二" => 1)
----------------------------------------------------------------------------- */
// 合作身份者id，以2088开头的16位纯数字
$plugin['config'][] = array (
        "field" => 'partner',
        "name" => '合作者身份(Pid)',
        "desc" => '合作身份者id，以2088开头的16位纯数字',
        "value" => $plugin_mysql['config']['partner']
);
?>