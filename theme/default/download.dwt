<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<title>{$page_title}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="style.css" rel="stylesheet" type="text/css" />
<link href="download.css" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="{$page_title}" />
<meta property="og:description" content="{$description}" />
<meta property="og:image" content="{$download.image}" />
<meta property="og:url" content="{$download.url}" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{$page_title}" />
<meta name="twitter:description" content="{$description}" />
<meta name="twitter:image" content="{$download.image}" />

<!-- Structured Data -->
{literal}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "CreativeWork",
  "name": "{$download.title}",
  "image": "{$download.image}",
  "description": "{$description}",
  "url": "{$download.url}",
  "datePublished": "{$download.add_time}"
}
</script>
{/literal}

<!-- Canonical Link -->
<link rel="canonical" href="{$download.url}" />
</head>
<body>
<div id="wrapper"> {include file="inc/header.tpl"}
 <div class="container mb">
  <div class="row">
   <div class="col-md-2"> {include file="inc/download_tree.tpl"} </div>
   <div class="col-md-10"> {include file="inc/ur_here.tpl"}
    <div id="download">
     <!-- {if $download.image} -->
     <div class="img"><a href="{$download.image}" target="_blank"><img src="{$download.image}" alt="{$download.title}" title="{$download.title}" /></a></div>
     <!-- {/if} -->
     <div class="info">
      <h1>{$download.title}</h1>
      <ul>
       <li><b>{$lang.add_time}：</b>{$download.add_time}</li>
       <li><b>{$lang.click}：</b>{$download.click}</li>
       <li><b>{$lang.download_size}：</b>{$download.size}</li>
       <!-- {foreach from=$defined name=defined item=defined} -->
       <li><b>{$defined.arr}：</b>{$defined.value}</li>
       <!-- {/foreach} -->
      </ul>
      <a href="{$download.download_link}" class="btn" target="_blank">{$lang.download_link}</a>
     </div>
     <div class="clear"></div>
     <div class="content">
      <h3>{$lang.download_content}</h3>
      <ul>
       {$download.content}
      </ul>
     </div>
    </div>
   </div>
  </div>
 </div>
 {include file="inc/online_service.tpl"}
 {include file="inc/footer.tpl"} </div>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/dou.js"></script>
<script type="text/javascript" src="js/online_service.js"></script>
</body>
</html>