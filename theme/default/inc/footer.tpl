<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<footer id="footer">
 <div class="container">
  <div class="row">
   <!-- {foreach from=$nav_middle_list name=nav_middle_list item=nav} -->
   <!-- {if $smarty.foreach.nav_middle_list.iteration le 4} -->
   <div class="col-md-2">
    <div class="foot-nav">
     <div class="nav-parent">
      <a href="{$nav.url}">{$nav.nav_name}</a>
     </div>
     <div class="nav-child">
      <!-- {foreach from=$nav.child item=child} -->
      <a href="{$child.url}">{$child.nav_name}</a>
      <!-- {/foreach} -->
     </div>
    </div>
   </div>
   <!-- {/if} -->
   <!-- {/foreach} -->
   <div class="col-md-2">
    <!-- {if $fragment.weixin.image} -->
    <div class="weixin"><img src="{$fragment.weixin.image}" alt="深圳市华通通讯有限公司客服微信" title="深圳市华通通讯有限公司客服微信" /><p>{$fragment.weixin.text}</p></div>
    <!-- {/if} -->
   </div>
   <div class="col-md-2">
    <div class="contact">
     <div class="tel">{$site.tel}</div>
     <div class="email">{$site.email}</div>
<p><span style="color: #d7d7d7;"><img title="华通通讯微信客服" src="/ueditor/php/upload/image/20181109/1541773846590912.png" alt="华通通讯微信客服" width="100" height="100"/></span></p>
    </div>
   </div>
  </div>
  <div class="copy-right">{$lang.copyright} {$lang.powered_by}<br><span>本站信息仅供参考，所涉及其他品牌信息均归属原品牌所有。</span><!-- {if $site.icp} --><a href="https://beian.miit.gov.cn/" target="_blank">{$site.icp}</a><!-- {/if} --><!-- {if $site.net_safe_record} --><a href="https://beian.miit.gov.cn/portal/registerSystemInfo?recordcode={$site.net_safe_record_number}" class="net-safe-record" target="_blank"><img src="../images/icon_net_safe_record.png" alt="icon" />{$site.net_safe_record}</a><!-- {/if} --><a href="/os/index.html" style="display: inline-block; padding: 8px 10px; font-size: 14px; color: #fff; background-color: #1979CC; border: none; border-radius: 10px; text-align: center; text-decoration: none; cursor: pointer; margin-left: 10px; transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#155e9e'" onmouseout="this.style.backgroundColor='#1979CC'" target="_blank">在线客服</a></div>
  </div>
</footer>
<!-- {if $site.code} -->
<div style="display:none">{$site.code}</div>
<!-- {/if} -->
