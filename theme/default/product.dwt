<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<title>{$page_title}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="style.css" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="{$page_title}" />
<meta property="og:description" content="{$description}" />
<meta property="og:image" content="{$product.image}" />
<meta property="og:url" content="{$product.url}" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{$page_title}" />
<meta name="twitter:description" content="{$description}" />
<meta name="twitter:image" content="{$product.image}" />

<!-- Structured Data -->
{literal}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Product",
{/literal}
  "name": "{$product.name|escape:'javascript'}",
  "image": "{$product.image|escape:'javascript'}",
  "description": "{$description|escape:'javascript'}",
  "url": "{$product.url|escape:'javascript'}",
{literal}
  "offers": {
    "@type": "Offer",
    "availability": "https://schema.org/InStock",
    "price": "0",
    "priceCurrency": "CNY"
  }
}
</script>
{/literal}

<!-- Canonical Link -->
<link rel="canonical" href="{$product.url}" />
</head>
<body>
<div id="wrapper"> {include file="inc/header.tpl"}
 <div class="container mb">
  <div class="row">
   <div class="col-md-2"> {include file="inc/product_tree.tpl"} </div>
   <div class="col-md-10"> {include file="inc/ur_here.tpl"}
    <div id="product">
     <div class="product-img"><a href="{$product.image}" target="_blank"><img src="{$product.image}" width="300" alt="{$product.name}" title="{$product.name}" /></a></div>
     <div class="product-info">
      <h1>{$product.name}</h1>
      <ul>
       <!-- {foreach from=$defined name=defined item=defined} -->
       <li>{$defined.arr}：{$defined.value}</li>
       <!-- {/foreach} -->
      </ul>
      <!-- {if $open.order} -->
      <div class="btn-buy">
       <form action="{$site.root_url}order.php?rec=insert" method="post">
        <input type="hidden" name="module" value="product" />
        <input type="hidden" name="item_id" value="{$product.id}" />
        <input type="hidden" name="number" value="1" />
        <input type="submit" name="submit" class="add-to-cart" value="{$lang.order_addtocart}" />
       </form>
      </div>
      <!-- {else} -->
      <!-- {/if} -->
     </div>
     <div class="clear"></div>
     <div class="product-content">
      <h3>{$lang.product_content}</h3>
      <ul>
       {$product.content}
      </ul>
     </div>
    </div>
   </div>
  </div>
 </div>
 {include file="inc/online_service.tpl"}
 {include file="inc/footer.tpl"} </div>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/dou.js"></script>
</body>
</html>
