/* 初始化
----------------------------------------------- */
body {
 font-family: Microsoft Yahei, \5FAE\8F6F\96C5\9ED1, \5b8b\4f53, Arial, Lucida, Verdana, Helvetica, sans-serif;
 color: #555;
 background-color: #FFF;
}
body, button, input, textarea {
 font-size: 14px;
 line-height: 1.531;
 outline: none;
 margin: 0;
 padding: 0;
 border: 0;
}
a {
 color: #333333;
}
a:hover {
 color: #333333;
 text-decoration: none;
}
p, ul, ol, dl, dt, dd, form, blockquote {
 margin: 0;
 padding: 0;
}
ul, ol {
 list-style: none;
}
h1, h2, h3, h4, h5, h6 {
 font-size: 14px;
 margin: 0;
 padding: 0;
}
input, select {
 font-family: Microsoft Yahei;
 vertical-align: middle;
}
input:-webkit-autofill {
 -webkit-box-shadow: 0 0 0px 1000px #EEEEEE inset !important;
}
em, b, i {
 font-style: normal;
 font-weight: normal;
}
img {
 vertical-align: middle;
 border: 0;
}
label {
 cursor: pointer;
}
/* 主体框架
----------------------------------------------- */
#wrapper .mb {
 margin-bottom: 50px;
}
/* 头部导航
----------------------------------------------- */
/* -- top -- */
#header .top {
 background-color: #EEEEEE;
 height: 30px;
 border-bottom: 1px solid #DDDDDD;
 text-align: right;
}
/* top-nav */
#header .top .top-nav {
 float: left;
 position: relative;
 z-index: 1000;
 height: 30px;
 margin-left: -7px;
}
#header .top .top-nav li {
 border: 1px solid #EEEEEE;
 border-bottom: 0;
 float: left;
 zoom: 1;
 text-align: center;
}
#header .top .top-nav li.spacer {
 overflow: hidden;
 margin: 11px 5px 0;
 width: 1px;
 height: 10px;
 background-color: #BBB;
}
#header .top .top-nav li a {
 padding: 0 7px;
 height: 28px;
 line-height: 28px;
 color: #333;
}
#header .top .top-nav li.hover {
 position: relative;
 background: #FFFFFF;
 border: 1px solid #DDDDDD;
 border-bottom: 0;
}
#header .top .top-nav li.hover s {
 border-left: 1px solid #FFF;
}
#header .top .top-nav ul {
 background: #FFFFFF;
 border: 1px solid #DDDDDD;
 border-top: 0;
 width: 100%;
 display: none;
 position: absolute;
 top: 100%;
 left: -1px;
 padding-bottom: 4px;
}
#header .top .top-nav ul li {
 float: none;
}
#header .top .top-nav li.hover ul li {
 border: none;
}
#header .top .top-nav li.hover ul li a {
 height: 25px;
 line-height: 25px;
}
/* search */
#header .top .search {
 float: right;
 _width: 205px;
}
#header .top .search .search-box {
 border: 1px solid #545454;
 background-color: #545454;
 height: 28px;
 padding-left: 10px;
 box-sizing: content-box;
}
#header .top .search .search-box .keyword {
 background-color: #545454;
 color: #CCCCCC;
}
#header .top .search .search-box .btnSearch {
 background-color: #545454;
}
/* -- navbar -- */
#header .navbar {
	z-index: 1000;
 border-bottom: 1px solid #DDD;
 background-color: #FFF;
}
#header .navbar.fix {
	position: fixed;
 top: 0;
 right: 0;
 left: 0;
 z-index: 1030;
}
@media (min-width:768px) {
 #header .navbar {
  padding: 0;
 }
}
#header .navbar .logo {
 display: block;
 height: 55px;
}
#header .navbar .logo img {
 height: 100%;
}
#header .navbar .menu {
 background-color: transparent;
}
#header .navbar .fa {
 font-size: 30px;
 padding: 0 10px;
 color: #777;
}
/* -- main-nav -- */
#header .main-nav .dropdown-menu {
 padding: 0;
 margin: 0;
 min-width: 100%;
 font-size: 14px;
 color: #212529;
 text-align: left;
 list-style: none;
 background-color: #fff;
 background-clip: padding-box;
 border: 0;
 border-radius: 0;
}
@media (min-width:768px) {
 /* LEVEL ONE */
 #header .main-nav .nav-item .nav-link {
  display: block;
  padding: 0 20px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  text-align: center;
  color: #000;
  text-decoration: none;
 }
 #header .main-nav .nav-item .nav-link::after {
  display: none;
 }
 #header .main-nav .nav-item:hover .nav-link, #header .main-nav .active .nav-link {
  background-color: #1979CC;
  color: #FFF;
 }
 /* LEVEL TWO */
 #header .main-nav .dropdown:hover>.dropdown-menu {
  display: block;
 }
 #header .main-nav .dropdown-menu .dropdown-item {
  background: #E0E0E0;
  padding: 0 20px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
 }
 #header .main-nav .dropdown-menu .dropdown-item:hover {
  background: #1979CC;
  color: #FFF;
 }
 /* LEVEL THREE */
 #header .main-nav .dropdown-menu .dropdown-menu {
  left: 100%;
  top: 0;
  min-width: 98px;
  _width: 98px;
 }
 #header .main-nav .dropdown-submenu:hover>.dropdown-menu {
  display: block;
 }
 #header .main-nav .dropdown-menu .dropdown-toggle::after {
  position: absolute;
  right: 10px;
  top: 13px;
  vertical-align: 0;
  border-right: 0;
  border-top: .2em solid transparent;
  border-left: .3em solid;
  border-bottom: .2em solid transparent;
 }
}
@media (max-width:768px) {
 #header .main-nav .dropdown-toggle::after {
  position: absolute;
  right: 10px;
  top: 23px;
 }
 #header .main-nav .dropdown-menu {
  padding-left: 15px;
 }
 #header .main-nav .dropdown-menu .dropdown-item {
  position: relative;
 }
 #header .main-nav .dropdown-item,  #header .main-nav .nav-link {
  border-bottom: 1px solid #EEE;
  padding: 0.8rem 0;
  background-color: transparent;
 }
 #header .main-nav .dropdown-menu .dropdown-item:active {
  color: #555;
  background-color: #FFF;
 }
}
/* 首页样式
----------------------------------------------- */
/* -- slideShow -- */
.slide-show .swiper-slide a {
 display: block;
 background-color: #DDDDDD;
 height: 400px;
 background-repeat: no-repeat;
 background-position: center center;
 background-size: auto 100%;
}
.slide-show .swiper-pagination-bullet {
 width: 12px;
 height: 12px;
}
.slide-show .swiper-pagination-bullet-active {
 background-color: #1979CC;
}
.slide-show .swiper-button-prev {
 left: 50px;
}
.slide-show .swiper-button-next {
 right: 50px;
}
.slide-show .swiper-button-prev, .slide-show .swiper-button-next {
 display: none;
 color: #FFF;
}
.slide-show:hover .swiper-button-prev, .slide-show:hover .swiper-button-next {
 display: block;
}
@media (max-width:768px) {
 .slide-show .swiper-slide a {
  height: 200px;
 }
}
/* -- index-box -- */
#index .index-box {
  padding: 60px 0;  /* 增加内边距 */
  overflow: hidden;
}

@media (max-width: 768px) {
  #index .index-box {
    padding: 40px 0;  /* 移动端稍微增加内边距 */
  }
}

#index .index-box.bg {
  background-color: #F8F9FA;  /* 稍微调亮背景色 */
}

#index .index-box h3 {
  text-align: center;
  margin-bottom: 40px;  /* 增加标题下方间距 */
}

#index .index-box h3 b {
  color: #222;  /* 加深文字颜色 */
  font-size: 36px;  /* 增大字号 */
  font-weight: 700;  /* 加粗 */
}

#index .index-box h3 em {
  display: block;
  margin-top: 10px;
  text-transform: uppercase;
  color: #777;  /* 稍微加深颜色 */
  font-size: 14px;  /* 指定字号 */
}

#index .index-box .more {
  text-align: center;
  margin-top: 30px;  /* 增加上方间距 */
}

#index .index-box .more a {
  display: inline-block;
  padding: 10px 30px;  /* 增加按钮内边距 */
  border: 1px solid #1979CC;
  color: #1979CC;
  border-radius: 20px;  /* 增加圆角 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;  /* 过渡效果应用到所有属性 */
  font-weight: 600;  /* 加粗文字 */
}

#index .index-box .more a:hover {
  background-color: #1979CC;
  color: #FFF;
  transform: translateY(-2px);  /* 悬停时微微上浮 */
  box-shadow: 0 6px 12px rgba(25, 121, 204, 0.2);  /* 增强阴影效果 */
}
/* -- index-box about -- */
#index .about .img {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

#index .about .img:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

#index .about .img img {
    width: 100%;
    display: block;
}

#index .about p.about-slogan {
    color: #333;
    font-size: 32px;
    margin-bottom: 15px;
    position: relative;
    padding-bottom: 10px;
}

#index .about p.about-slogan:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: #1979CC;
}

#index .about .desc {
    color: #666666;
    margin: 20px 0;
    line-height: 1.8;
    max-width: 800px;
}

#index .about .more {
    text-align: left;
    margin-top: 25px;
}

#index .about .more a {
    display: inline-block;
    padding: 10px 20px;
    background-color: #1979CC;
    color: #fff;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

#index .about .more a:hover {
    background-color: #1565A9;
}

@media (max-width: 768px) {
    #index .about .img {
        margin-bottom: 25px;
    }
    
    #index .about p.about-slogan {
        font-size: 28px;
        text-align: center;
    }
    
    #index .about p.about-slogan:after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    #index .about .desc {
        text-align: center;
        margin-left: auto;
        margin-right: auto;
    }
    
    #index .about .more {
        text-align: center;
    }
}
/* -- recProduct -- */
#index .product-list {
 margin-bottom: 25px;
}
#index .product-list .col-md-3, #index .product-list .col-6 {
 padding-left: 9px;
 padding-right: 9px;
}
#index .product-list .item {
 margin-bottom: 15px;
 text-align: center;
}
#index .product-list .item .img {
 border: 1px solid #EEE;
 border-radius: 10px;
 overflow: hidden;
}
#index .product-list .item .img img {
 width: 100%;
 transition: transform 0.3s ease;
}
#index .product-list .item .img:hover img {
 transform: scale(1.05);
}
#index .product-list .item .name {
    margin-top: 12px;
    font-size: 16px;
}

#index .product-list .item .name a {
    display: inline-block;
    text-decoration: none;
    background: linear-gradient(90deg, #333333, #666666);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    transition: all 0.3s ease;
}

#index .product-list .item .name a:hover,
#index .product-list .item .name a:focus {
    background: linear-gradient(90deg, #1979CC, #44C4EE);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}
#index .product-list .item .price {
 margin-top: 5px;
}
.product-list .img:hover img {
  transform: scale(1.1);
  transition: all 0.3s ease;
}
/* -- article-list -- */
#index .article-list {
 margin-bottom: 20px;
}
#index .article-list .img {
 text-align: center;
 margin-bottom: 20px;
 overflow: hidden;
}
#index .article-list .img img {
 width: 100%;
}
@media (min-width: 1100px) {
 #index .article-list .img img {
  width: 278px;
  height: 278px;
 }
}
#index .article-list .img em {
 display: block;
 background-color: #EEE;
 line-height: 278px;
 color: #999;
}
#index .article-list .item {
 zoom:1;
 overflow:hidden;
 margin-bottom:20px;
}
#index .article-list .item dt {
 float:left;
 width:80px;
 font-family: Arial, Lucida, Verdana, Helvetica, sans-serif;
 text-align:center;
}
#index .article-list .item dt em {
 display:block;
 background-color:#EEE;
 font-size:36px;
 padding-bottom:3px;
 color:#464646;
}
#index .article-list .item dt b {
 display:block;
 background-color:#1979cc;
 font-size:15px;
 color:#fcfcfc;
}
#index .article-list .item dd {
	margin-left: 100px;
 font-size:14px;
}
#index .article-list .item dd a {
 color:#000;
 font-size: 16px;
}
#index .article-list .item dd p {
 color:#9A9A9A;
 line-height:180%;
}
/* -- link -- */
#index .link {
 padding: 10px 0;
}
#index .link strong {
 color: #7A7F81;
 font-weight: bold;
}
#index .link a {
 color: #555;
}
/* page
----------------------------------------------- */
#page {
 padding: 20px 0;
 min-height: 400px;
}
#page h1 {
 color: #333333;
 font-size: 18px;
 font-weight: bold;
}
#page .info {
 padding: 2px 0 20px 0;
 color: #999999;
 font-size: 13px;
}
#page .content {
 color: #666666;
 line-height: 200%;
 padding-top: 10px;
}
#page .content img {
 max-width: 100%;
}
/* product_category
----------------------------------------------- */
#product-category .product-list {
 margin-top: 20px;
 margin-bottom: 25px;
}
#product-category .product-list .item {
 margin-bottom: 15px;
 text-align: center;
}
#product-category .product-list .item .img {
 border: 1px solid #EEE;
 border-radius: 10px;
 overflow: hidden;
}
#product-category .product-list .item .img img {
 width: 100%;
 transition: transform 0.3s ease;
}
#product-category .product-list .item .img:hover img {
 transform: scale(1.05);
}
#product-category .product-list .item .name {
 margin-top: 12px;
 font-size: 16px;
}
#product-category .product-list .item .price {
 margin-top: 5px;
}
/* product
----------------------------------------------- */
#product {
 padding: 15px 0;
}
#product .product-img {
 float: left;
 width: 300px;
}
#product .product-info {
 margin-left: 320px;
 padding-top: 10px;
 line-height: 200%;
}
@media (max-width: 768px) {
 #product .product-img {
  float: none;
  width: auto;
 }
 #product .product-info {
  margin-left: 0;
 }
}
#product .product-info h1 {
 font-size: 18px;
}
#product .product-info ul {
 padding-top: 10px;
 margin-bottom: 50px;
}
#product .product-info .product-price {
 margin-bottom: 5px;
}
#product .product-info .product-price .price {
 font-family: Arial;
 font-size: 20px;
 font-weight: bold;
}
#product .product-info .btn-ask a {
 display: inline-block;
 padding: 5px 25px;
 background-color: #1979CC;
 color: #FFF;
}
#product .product-info .btn-ask a .fa {
 margin-right: 5px;
}
#product .product-info .btn-buy {
 margin-bottom: 40px;
}
#product .product-info .btn-buy .add-to-cart {
 background: #1979CC url(images/btn_addtocart.png) no-repeat 20px 50%;
 color: #FFFFFF;
 padding: 8px 25px 8px 50px;
 text-decoration: none;
 cursor: pointer;
 font-size: 16px;
}
#product .product-info .btn-buy .add-to-cart:hover {
 background-color: #44C4EE;
}
#product .product-content {
 padding: 15px 0;
}
#product .product-content h3 {
 padding: 30px 0 10px 0;
 color: #333;
 font-weight: bold;
 font-size: 16px;
 border-bottom: 1px solid #CBD1D3;
}
#product .product-content ul {
 padding: 15px 0;
 color: #666;
 line-height: 200%;
}
#product .product-content img {
 max-width: 100%;
}
/* article_category
----------------------------------------------- */
#article-list dl {
 border-bottom: 1px dotted #D1D1D1;
 padding: 20px 0;
 zoom: 1;
 overflow: hidden;
}
#article-list dl.last {
 border-bottom: 0;
}
#article-list dt {
 width: 620px;
 margin-bottom: 10px;
 font-size: 14px;
}
#article-list dd {
 color: #999999;
 line-height: 180%;
 zoom: 1;
 overflow: hidden;
}
#article-list dd p {
 float: left;
}
#article-list dd p.img {
 margin-right: 10px;
}
#article-list dd p.desc {
 width: 590px;
}
#article-list .num-date {
 float: right;
 text-align: right;
 padding-top: 12px;
}
#article-list .num-date em {
 background-color: #0072C6;
 color: #FFF;
 padding: 3px 4px;
 font-size: 14px;
 font-weight: bold;
}
#article-list .num-date p {
 font-size: 16px;
 color: #999999;
 margin-top: 5px;
}
/* article
----------------------------------------------- */
#article {
 padding: 15px 0;
 min-height: 400px;
}
#article h1 {
 color: #333333;
 font-size: 18px;
 font-weight: bold;
}
#article .info {
 padding: 2px 0 20px 0;
 color: #999999;
 font-size: 13px;
}
#article .content {
 /* 移除强制颜色设置，允许内容保留原有颜色 */
 color: #474747;
 line-height: 1.6;
 font-size: 16px;
 padding: 15px 0;
 overflow: hidden;
}

/* 文章内容样式强化 */
#article .content h1, 
#article .content h2, 
#article .content h3, 
#article .content h4, 
#article .content h5, 
#article .content h6 {
 margin: 25px 0 15px;
 color: #333;
 font-weight: bold;
 line-height: 1.4;
}

#article .content h1 { font-size: 28px; }
#article .content h2 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
#article .content h3 { font-size: 20px; }
#article .content h4 { font-size: 18px; }
#article .content h5 { font-size: 16px; }
#article .content h6 { font-size: 16px; color: #777; }

#article .content p {
 margin-bottom: 16px;
 line-height: 1.6;
}

#article .content p:empty {
 margin: 0;
}

#article .content img {
 max-width: 100%;
 height: auto;
 display: block;
 margin: 20px auto;
 border-radius: 4px;
}

/* 表格样式 */
#article .content table {
 width: 100%;
 border-collapse: collapse;
 border-spacing: 0;
 margin: 20px 0;
 overflow: auto;
}

#article .content table th,
#article .content table td {
 padding: 10px 15px;
 border: 1px solid #ddd;
 text-align: left;
}

#article .content table th {
 background-color: #f7f7f7;
 font-weight: bold;
}

#article .content table tr:nth-child(even) {
 background-color: #fcfcfc;
}

#article .content table tr:hover {
 background-color: #f5f5f5;
}

/* 文本样式 */
#article .content strong,
#article .content b {
 font-weight: bold;
}

#article .content em,
#article .content i {
 font-style: italic;
}

#article .content u {
 text-decoration: underline;
}

#article .content del,
#article .content s {
 text-decoration: line-through;
}

/* 列表样式 */
#article .content ul,
#article .content ol {
 padding-left: 2em;
 margin: 16px 0;
}

#article .content ul {
 list-style: disc;
}

#article .content ol {
 list-style: decimal;
}

#article .content li {
 margin-bottom: 8px;
}

/* 引用样式 */
#article .content blockquote {
 padding: 10px 15px;
 margin: 20px 0;
 border-left: 4px solid #ddd;
 background: #f9f9f9;
 color: #777;
}

/* 代码样式 */
#article .content pre,
#article .content code {
 font-family: Consolas, Monaco, 'Andale Mono', monospace;
 background-color: #f6f8fa;
 border-radius: 3px;
 padding: 2px 4px;
}

#article .content pre {
 padding: 16px;
 overflow: auto;
 line-height: 1.45;
 margin: 16px 0;
}

/* 水平线样式 */
#article .content hr {
 height: 1px;
 border: 0;
 background-color: #ddd;
 margin: 24px 0;
}

/* 链接样式 */
#article .content a {
 color: #1979CC;
 text-decoration: none;
}

#article .content a:hover {
 text-decoration: underline;
}

/* 特殊样式处理 */
#article .content span[style*="text-wrap-mode: wrap"] {
 color: #999;
 font-size: 14px;
 font-style: italic;
}

/* 图片下方的说明文字样式 */
#article .content img + em {
 display: block;
 text-align: center;
 font-size: 14px;
 color: #777;
 margin-top: -10px;
 margin-bottom: 20px;
}

/* article 样式重新定义，确保颜色样式不被覆盖 */
#article .content {
  /* 默认颜色设置为略深的颜色，但允许内联样式覆盖 */
  color: #333333 !important; /* 使用!important增加优先级 */
  line-height: 1.6;
  font-size: 16px;
  padding: 15px 0;
  overflow: hidden;
}

/* 确保文章内容中的元素可以保留自定义颜色 */
#article .content * {
  color: inherit; /* 继承父元素的颜色，而不是强制设置 */
}

/* 文章内容中特定元素的颜色控制 */
#article .content h1, 
#article .content h2, 
#article .content h3, 
#article .content h4, 
#article .content h5, 
#article .content h6 {
  color: #333; /* 标题颜色 */
}

#article .content a {
  color: #1979CC; /* 链接颜色 */
}

/* 确保行内样式的颜色优先级更高 */
#article .content span[style*="color"],
#article .content p[style*="color"],
#article .content div[style*="color"],
#article .content h1[style*="color"],
#article .content h2[style*="color"],
#article .content h3[style*="color"],
#article .content h4[style*="color"],
#article .content h5[style*="color"],
#article .content h6[style*="color"] {
  color: inherit !important;
}

/* 提示信息
----------------------------------------------- */
#dou-msg {
 padding: 10px 0 100px 0;
}
#dou-msg dl {
 padding: 160px 0 200px 0;
 text-align: center;
}
#dou-msg dt {
 color: #0072C6;
 font-size: 16px;
 margin-bottom: 30px;
 font-weight: bold;
}
#dou-msg dd {
 color: #666;
}
#dou-msg dd a {
 margin-left: 5px;
 color: #000;
}
/* 全局样式
----------------------------------------------- */
/* -- ur-here -- */
.ur-here {
 padding: 25px 0 10px 0;
 line-height: 20px;
 color: #999999;
 font-weight: bold;
 font-size: 14px;
 border-bottom: 1px solid #DDD;
}
@media (max-width:768px) {
 .ur-here {
  display: none;
 }
}
.ur-here a {
 color: #999999;
}
.ur-here b {
 margin: 0 8px;
}
/* -- tree -- */
.tree-box {
 margin-bottom: 15px;
}
.tree-box h3 {
 padding: 25px 0 10px 0;
 line-height: 20px;
 color: #999999;
 font-weight: bold;
 font-size: 14px;
 border-bottom: 1px solid #DDD;
 margin-bottom: 15px;
}
@media (max-width:768px) {
 .tree-box h3 {
  display: none;
 }
}
.tree-box li {
 padding: 8px 8px;
}
.tree-box li a {
 color: #000;
 font-size: 14px;
}
.tree-box li.cur {
 background-color: #1979CC;
}
.tree-box li.cur a {
 color: #FFF;
}
@media (max-width:768px) {
 .tree-box {
  margin-top: 10px;
 }
 .tree-box ul {
  display: inline-block;
 }
 .tree-box li {
  display: inline-block;
  padding: 0;
  margin-right: 5px;
  margin-top: 5px;
 }
 .tree-box li i {
  display: none;
 }
 .tree-box li a {
  display: block;
  border: 1px solid #EEE;
  padding: 5px 8px;
 }
 .tree-box ul ul li a {
  color: #888;
 }
}
.tree-box .search {
 display: block;
 margin: 20px 0;
}
@media (max-width: 768px) {
 .tree-box .search {
  margin: 12px 0;
 }
}
.tree-box .search .keyword {
 width: 85px;
}
/* -- screen -- */
.screen {
 padding-top: 20px;
 border-bottom: 1px dotted #DDD;
}
.screen dl {
 margin-bottom: 10px;
}
.screen dl dt {
 display: inline-block;
 font-weight: bold;
}
.screen dl dd {
 display: inline-block;
}
.screen dl dd a {
 display: inline-block;
 background-color: #DDD;
 color: #555;
 padding: 2px 10px;
 margin-right: 8px;
}
@media (max-width: 768px) {
 .screen dl dt {
  display: block;
 }
 .screen dl dd {
  display: block;
 }
 .screen dl dd a {
  margin-top: 8px;
 }
}
.screen dl dd a.cur {
 background-color: #1979CC;
 color: #FFF;
}
/* -- search-box -- */
.search-box {
 border: 1px solid #DDDDDD;
 background-color: #FFF;
 height: 28px;
 padding-left: 10px;
 position: relative;
}
.search-box .keyword {
 color: #CCCCCC;
}
.search-box .btnSearch {
 position: absolute;
 right: 0;
 top: 0;
 background: url(images/icon_search.png) no-repeat 50% 50%;
 width: 28px;
 height: 28px;
 text-indent: -999px;
 overflow: hidden;
 cursor: pointer;
}
/* -- scale -- */
.scale {  
 overflow: hidden;
}
.scale img {  
 width: 100%;
 cursor: pointer;  
 transition: all 0.6s;  
}
.scale img:hover {  
 transform: scale(1.1);  
}
/* -- common -- */
.cue {
 color: red;
 font-size: 14px;
}
.clear {
 clear: both;
 height: 0;
 line-height: 0;
 font-size: 0;
}
.captcha {
 text-transform: uppercase;
}

.price {
 color: #CC0000;
}
.none {
 display: none;
}
.pointer {
 cursor: pointer;
}
/* -- form -- */
.btn {
 display: inline-block;
 background-color: #0072C6;
 color: #EEE;
 padding: 6px 25px;
 text-transform: capitalize;
 cursor: pointer;
 font-weight: bold;
 text-align: center;
 -webkit-appearance: none;
 font-size: 14px;
 line-height: 1.5;
 border-radius: 0;
 border: 0;
}
.btn:hover {
 text-decoration: none;
 color: #FFFFFF;
 background-color: #007AD5;
}
.btn-gray {
 display: inline-block;
 background-color: #CCC;
 border: 0;
 color: #333;
 padding: 6px 15px;
 text-transform: capitalize;
 cursor: pointer;
 font-weight: bold;
 -webkit-appearance: none;
}
.btn-payment {
 display: inline-block;
 background-color: #ff4246;
 color: #FFF;
 padding: 7px 28px;
 text-transform: capitalize;
 cursor: pointer;
 font-weight: bold;
 font-size: 14px;
 text-align: center;
 -webkit-appearance: none;
}
.btn-captcha {
 display: inline-block;
 background-color: #1979CC;
 -moz-border-radius: 2px;
 -webkit-border-radius: 2px;
 border: 0;
 color: #FFF;
 padding: 7px 22px;
 text-transform: capitalize;
 cursor: pointer;
 font-size: 13px;
}
.btn-captcha[disabled], .btn-captcha:disabled, .btn-captcha.disabled {
 color: #FFF;
 background-color: #b2b2b2;
 opacity: 1;
}
/* -- color -- */
.cRed {
 color: #F40;
}
.cOra {
 color: #f30;
}
.cGre {
 color: #0c6;
}
.cBlu {
 color: #69c;
}
.cGra {
 color: #999;
}
/* -- input -- */
.text-input {
 height: 34px;
 padding: 6px;
 font-size: 14px;
 border: 1px solid #E9E9E9;
 background-color: #FFF;
 color: #666;
 -webkit-appearance: none;
}
.text-input[disabled], .text-input:disabled, .text-input.disabled {
	color: #999;
}
.text-area {
 padding: 4px;
 border: 1px solid #E9E9E9;
 background-color: #FFF;
 line-height: 20px;
 -webkit-appearance: none;
}
.text-area-auto {
 border: 1px solid #E9E9E9;
 background-color: #FFF;
 padding: 0;
 font-size: 12px;
 line-height: 20px;
 resize: none;
 min-height: 40px;
 -webkit-appearance: none;
}
@media (max-width: 768px) {
 .btn, .btn-gray, .btn-payment, .btn-captcha, .text-input, .text-area, .text-area-auto, .inp-main {
  max-width: 100%;
 }
}
/*- input-file -*/
.input-file {
 width: 80px;
}
.input-file .input-file-show {
 height: 80px;
 line-height: 80px;
 text-align: center;
 color: #555;
 background-color: #F5F5F5;
}
.input-file .input-file-show img {
 width: 100%;
 height: 80px;
}
.input-file .input-file-btn {
 width: 100%;
 background-color: #EEE;
 color: #999;
 line-height: 25px;
 cursor: pointer;
 text-align: center;
 -webkit-appearance: none;
}
/*- table-basic -*/
.table-basic {
 color: #666666;
 border-left: 1px solid #EEE;
 border-top: 1px solid #EEE;
 border-collapse: collapse;
}
.table-basic select {
 color: #8F8F8F;
}
.table-basic td, .table-basic th {
 border-right: 1px solid #EEE;
 border-bottom: 1px solid #EEE;
}
.table-basic th {
 background-color: #EEE;
}
.table-basic .child {
 background-color: #FFFFFF;
}
.table-basic td label {
 margin-right: 15px;
}
.table-basic td i {
 color: #F00;
 margin-left: 5px;
 font-weight: bold;
}
/*- form-basic -*/
.form-basic dl {
 zoom:1;
 overflow:hidden;
 margin-bottom:20px;
}
.form-basic dl dt {
 font-weight: bold;
 margin-bottom: 5px;
 font-size: 13px;
}
.form-basic dl dt i {
 color:#F00;
 margin-left:5px;
 font-weight:bold;
}
.form-basic dl dd label {
 margin-right: 15px;
 line-height:34px;
}
/*- table-div -*/
.table-div {
 zoom:1;
 overflow:hidden;
}
.table-div dl {
 zoom:1;
 overflow:hidden;
 margin-bottom:20px;
}
.table-div dl dt {
 font-weight: bold;
 margin-bottom: 5px;
 font-size: 13px;
}
.table-div dl dt i {
 color:#F00;
 margin-left:5px;
 font-weight:bold;
}
.table-div dl dd label {
 margin-right: 15px;
 line-height:34px;
}
@media (max-width:768px) {
 .table-div dl dt {
  float: none;
 }
}
/* -- lift -- */
.lift {
 margin-top: 60px;
 color: #999;
 font-size: 14px;
}
.lift a {
 color: #999;
}
.lift span {
 margin-right: 15px;
}
/* -- pager -- */
.pager {
 text-align: right;
 padding-top: 20px;
 color: #666;
 font-size: 14px;
}
.pager a {
 color: #666;
 text-decoration: underline;
}
/* -- pager class two -- */
.pager ul {
 display: inline-block;
 *display: inline;
 border-left: 1px solid #dddddd;
}
.pager ul li {
 display: inline;
}
.pager ul li a {
 float: left;
 padding: 0 14px;
 line-height: 38px;
 text-decoration: none;
 background-color: #ffffff;
 border: 1px solid #dddddd;
 border-left-width: 0;
}
.pager ul li a:hover {
 background-color: #f5f5f5;
}
.pager ul .active a {
 background-color: #f5f5f5;
 color: #999999;
 cursor: default;
}
/* -- online-service -- */
#online-service {
 position: fixed;
 _position: absolute;
 _top:expression(eval(document.documentElement.scrollTop+document.documentElement.clientHeight)-380+"px");
 top: 208px;
 right: 0;
 z-index: 999999;
 display: block;
}
#online-service .onlineIcon {
 position: relative;
 cursor: pointer;
 background: url(images/online_service.png) no-repeat;
 width: 34px;
 height: 110px;
 text-indent: -9999px;
 overflow: hidden;
}
#online-service #pop {
 border: 3px solid #1979CC;
 background-color: #F2F2F2;
 padding: 8px;
 width: 95px;
 min-height: 84px;
 position: absolute;
 right: 34px;
 top: 0;
 display: none;
 z-index: 999999;
}
#online-service .online-qq a {
 display: block;
 background: url(images/online_service.png) no-repeat left -160px;
 color: #1979CC;
 width: 82px;
 height: 24px;
 line-height: 23px;
 padding-left: 27px;
 margin-bottom: 8px;
}
#online-service .online-qq a:hover {
 text-decoration: none;
 color: #1979CC;
}
#online-service .service {
 border-top: 1px solid #DDDDDD;
 padding-top: 8px;
 color: #666;
}
#online-service .service a {
 color: #666;
}
#online-service .service li {
 background: url(images/online_service.png) no-repeat left -202px;
 padding-left: 8px;
}
#online-service .goTop {
 display: none;
}
#online-service .goTop .goBtn {
 background: #F2F2F2 url(images/online_service.png) no-repeat left -120px;
 width: 34px;
 height: 34px;
 display: block;
}
/* footer
----------------------------------------------- */
#footer {
 background-color: #F5F5F5;
 padding: 40px 0;
}
@media (max-width:768px) {
 #footer {
  padding: 20px 0;
 }
 #footer .foot-nav {
  margin-bottom: 15px;
  border-bottom: 1px solid #DDD;
 }
}
#footer .foot-nav .nav-parent {
 margin-bottom: 10px;
}
#footer .foot-nav .nav-parent a {
 font-size: 20px;
 display: block;
}
#footer .foot-nav .nav-child a {
 display: block;
 color: #999;
 margin-bottom: 5px;
}
@media (max-width:768px) {
 #footer .foot-nav .nav-parent a {
  font-size: 16px;
 }
 #footer .foot-nav .nav-child a {
  display: inline-block;
  margin-bottom: 10px;
  margin-right: 15px;
 }
}
/* -- contact -- */
#footer .weixin {
 text-align: center;
 margin-bottom: 15px;
}
#footer .weixin img {
 width: 120px;
 height: 120px;
}
#footer .weixin p {
 margin-top: 4px;
 color: #999;
}
/* -- contact -- */
#footer .contact {
 text-align: center;
}
#footer .contact .tel {
 color: #1979CC;
 font-size: 20px;
 margin-bottom: 10px;
}
#footer .contact .online-service {
 margin-bottom: 10px;
}
#footer .contact .online-service a {
 display: inline-block;
 color: #1979CC;
 padding: 5px 20px;
 border: 1px solid #1979CC;
}
#footer .contact .online-service a i {
 margin-right: 5px;
}
#footer .contact .email {
 color: #999;
}
#footer .copy-right {
 border-top: 1px solid #EEE;
 margin-top: 20px;
 padding-top: 20px;
}
#footer .net-safe-record {
 margin-left: 10px;
}
#footer .net-safe-record img {
 vertical-align: bottom;
}
@media (max-width:768px) {
 #footer .copy-right {
  text-align: center;
 }
}

/* 强制浏览器重新加载样式 - 添加版本号 */
html {
  /* v1.0.1 */
}
