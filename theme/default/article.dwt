<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<title>{$page_title}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="style.css?v={$css_timestamp}" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="{$page_title}" />
<meta property="og:description" content="{$description}" />
<meta property="og:image" content="{$article.image}" />
<meta property="og:url" content="{$article.url}" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{$page_title}" />
<meta name="twitter:description" content="{$description}" />
<meta name="twitter:image" content="{$article.image}" />

<!-- Structured Data -->
{literal}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "name": "{$article.title}",
  "image": "{$article.image}",
  "description": "{$description}",
  "url": "{$article.url}",
  "datePublished": "{$article.add_time}"
}
</script>
{/literal}

<!-- 内联样式确保文章内容样式正确渲染 -->
{literal}
<style type="text/css">
/* 文章内容样式强化 */
#article .content {
  color: #333333 !important; /* 使用!important增加优先级 */
  line-height: 1.6;
  font-size: 16px;
  padding: 15px 0;
  overflow: hidden;
}

/* 确保文章内容中的元素可以保留自定义颜色 */
#article .content * {
  color: inherit; /* 继承父元素的颜色，而不是强制设置 */
}

/* 确保行内样式的颜色优先级更高 */
#article .content [style*="color"] {
  color: inherit !important;
}

#article .content h1, 
#article .content h2, 
#article .content h3, 
#article .content h4, 
#article .content h5, 
#article .content h6 {
 margin: 25px 0 15px;
 color: #333;
 font-weight: bold;
 line-height: 1.4;
}

#article .content h1 { font-size: 28px; }
#article .content h2 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
#article .content h3 { font-size: 20px; }
#article .content h4 { font-size: 18px; }
#article .content h5 { font-size: 16px; }
#article .content h6 { font-size: 16px; color: #777; }

#article .content p {
 margin-bottom: 16px;
 line-height: 1.6;  /* 修改行距为更合理的1.6 */
 /* 移除了text-indent: 2em; 不再强制段首缩进 */
}

#article .content p:empty {
 margin: 0;
}

#article .content img {
 max-width: 100%;
 height: auto;
 display: block;
 margin: 20px auto;
 border-radius: 4px;
}

/* 表格样式 */
#article .content table {
 width: 100%;
 border-collapse: collapse;
 border-spacing: 0;
 margin: 20px 0;
 overflow: auto;
}

#article .content table th,
#article .content table td {
 padding: 10px 15px;
 border: 1px solid #ddd;
 text-align: left;
}

#article .content table th {
 background-color: #f7f7f7;
 font-weight: bold;
}

#article .content table tr:nth-child(even) {
 background-color: #fcfcfc;
}

#article .content table tr:hover {
 background-color: #f5f5f5;
}
</style>
{/literal}

<!-- Canonical Link -->
<link rel="canonical" href="{$article.url}" />
</head>
<body>
<div id="wrapper"> {include file="inc/header.tpl"}
 <div class="container mb">
  <div class="row">
   <div class="col-md-2"> {include file="inc/article_tree.tpl"} </div>
   <div class="col-md-10"> {include file="inc/ur_here.tpl"}
    <div id="article">
     <h1>{$article.title}</h1>
     <div class="info">{$lang.add_time}：{$article.add_time} {$lang.click}：{$article.click} 
      <!-- {if $defined} --> 
      <!-- {foreach from=$defined name=defined item=defined} --> {$defined.arr}：{$defined.value}<!-- {/foreach} --> 
      <!-- {/if} -->
     </div>
     <div class="content"> {$article.content} </div>
     <div class="lift">
      <!-- {if $lift.previous} -->
      <span>{$lang.article_previous}：<a href="{$lift.previous.url}">{$lift.previous.title}</a></span>
      <!-- {/if} -->
      <!-- {if $lift.next} -->
      <span>{$lang.article_next}：<a href="{$lift.next.url}">{$lift.next.title}</a></span>
      <!-- {/if} -->
     </div>
    </div>
    <!-- {if $open.comment} -->
    {include file="inc/comment.tpl"}
    <!-- {/if} -->
   </div>
  </div>
 </div>
 {include file="inc/online_service.tpl"}
 {include file="inc/footer.tpl"} </div>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/dou.js"></script>
</body>
</html>
