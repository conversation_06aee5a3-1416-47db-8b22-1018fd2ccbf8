<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<title>{$page_title}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="style.css" rel="stylesheet" type="text/css" />
<link href="download.css" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="{$page_title}" />
<meta property="og:description" content="{$description}" />
<meta property="og:image" content="{$download.thumb}" />
<meta property="og:url" content="{$download.url}" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{$page_title}" />
<meta name="twitter:description" content="{$description}" />
<meta name="twitter:image" content="{$download.thumb}" />

<!-- Structured Data -->
{literal}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "CreativeWork",
  "name": "{$download.title}",
  "image": "{$download.thumb}",
  "description": "{$description}",
  "url": "{$download.url}",
  "datePublished": "{$download.add_time}"
}
</script>
{/literal}

<!-- Canonical Link -->
<link rel="canonical" href="{$download.url}" />
</head>
<body>
<div id="wrapper"> {include file="inc/header.tpl"}
 <div id="download-category" class="container mb">
  <div class="row">
   <div class="col-md-2"> {include file="inc/download_tree.tpl"} </div>
   <div class="col-md-10"> {include file="inc/ur_here.tpl"}
    <div class="download-list"> 
     <!-- {foreach from=$download_list name=download_list name=download item=download} --> 
     <dl>
      <dt><i class="fa fa-caret-right"></i><a href="{$download.url}">{$download.title}</a></dt>
      <dd>{$lang.add_time}：{$download.add_time} {$lang.click}：{$download.click}</dd>
     </dl>
     <!-- {/foreach} --> 
    </div>
    {include file="inc/pager.tpl"} </div>
  </div>
 </div>
 {include file="inc/online_service.tpl"}
 {include file="inc/footer.tpl"} </div>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/dou.js"></script>
<script type="text/javascript" src="js/online_service.js"></script>
</body>
</html>