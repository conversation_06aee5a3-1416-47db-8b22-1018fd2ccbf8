!function(e,t,n){var o,a=function(){var e=function(e){return e&&"none"!=e?e.match(/(-?[0-9\.]+)/g):[1,0,0,1,0,0]},t=function(e){return e.css("-webkit-transform")||e.css("transform")||e.css("-moz-transform")||e.css("-o-transform")||e.css("-ms-transform")},n=function(n){var o=t(n);return e(o)},o=function(e,t){e.css("-webkit-transform",t),e.css("-moz-transform",t),e.css("-o-transform",t),e.css("-ms-transform",t),e.css("transform",t)},a=function(e){return"matrix("+e[0]+", "+e[1]+", "+e[2]+", "+e[3]+", "+e[4]+", "+e[5]+")"},i=function(e){var t=n(e);return{x:parseInt(t[4]),y:parseInt(t[5])}},l=function(e,t){var i=n(e);i[0]=i[3]=t;var l=a(i);o(e,l)},c=function(e,t,i){var l=n(e);l[4]=t,l[5]=i;var c=a(l);o(e,c)},r=function(e,t){var i=n(e),l=t*(Math.PI/180),c=-1*l;i[1]=l,i[2]=c;var r=a(i);o(e,r)};return{scale:l,translate:c,rotate:r,getTranslate:i}}(),i="MacIntel"==n.navigator.platform,l=0,c=0,r={command:!1,shift:!1,isSelecting:!1},s={66:"bold",73:"italic",85:"underline",112:"h1",113:"h2",122:"undo"},d={keyboard:{isCommand:function(e,t,n){i&&e.metaKey||!i&&e.ctrlKey?t():n()},isShift:function(e,t,n){e.shiftKey?t():n()},isModifier:function(e,t){var n=e.which,o=s[n];o&&t.call(this,o)},isEnter:function(e,t){13===e.which&&t()},isArrow:function(e,t){(e.which>=37||e.which<=40)&&t()}},html:{addTag:function(n,o,a,i){var l=e(t.createElement(o));return l.attr("contenteditable",Boolean(i)),l.append(" "),n.append(l),a&&(r.focusedElement=n.children().last(),d.cursor.set(n,0,r.focusedElement)),l}},cursor:{set:function(e,o,a){var i;if(t.createRange){i=t.createRange();var l=n.getSelection(),c=e.children().last(),r=c.html().length-1,s=a?a[0]:c[0],d="undefined"!=typeof o?o:r;i.setStart(s,d),i.collapse(!0),l.removeAllRanges(),l.addRange(i)}else i=t.body.createTextRange(),i.moveToElementText(a),i.collapse(!1),i.select()}},selection:{save:function(){if(n.getSelection){var e=n.getSelection();if(e.rangeCount>0)return e.getRangeAt(0)}else if(t.selection&&t.selection.createRange)return t.selection.createRange();return null},restore:function(e){if(e)if(n.getSelection){var o=n.getSelection();o.removeAllRanges(),o.addRange(e)}else t.selection&&e.select&&e.select()},getText:function(){var e="";return n.getSelection?e=n.getSelection().toString():t.getSelection?e=t.getSelection().toString():t.selection&&(e=t.selection.createRange().text),e},clear:function(){window.getSelection?window.getSelection().empty?window.getSelection().empty():window.getSelection().removeAllRanges&&window.getSelection().removeAllRanges():document.selection&&document.selection.empty()},getContainer:function(e){return n.getSelection&&e&&e.commonAncestorContainer?e.commonAncestorContainer:t.selection&&e&&e.parentElement?e.parentElement():null},getSelection:function(){return n.getSelection?n.getSelection():t.selection&&t.selection.createRange?t.selection:null}},validation:{isUrl:function(e){return/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(e)}}},u={updatePos:function(t,o){var i=n.getSelection(),l=i.getRangeAt(0),c=l.getBoundingClientRect(),r=o.width(),s=o.height(),d=(t.offset().left,{x:c.left+c.width/2-r/2,y:c.top-s-8+e(document).scrollTop()});a.translate(o,d.x,d.y)},updateState:function(e,t){t.find("button").removeClass("active");var o=n.getSelection(),a=[];u.checkForFormatting(o.focusNode,a);for(var i={b:"bold",i:"italic",u:"underline",h1:"h1",h2:"h2",a:"anchor",ul:"ul",ol:"ol"},l=0;l<a.length;l++){var c=a[l];t.find("button."+i[c]).addClass("active")}},checkForFormatting:function(e,t){var n=["b","i","u","h1","h2","ol","ul","li","a"];("#text"===e.nodeName||-1!=n.indexOf(e.nodeName.toLowerCase()))&&("#text"!=e.nodeName&&t.push(e.nodeName.toLowerCase()),u.checkForFormatting(e.parentNode,t))},buildMenu:function(t,n){var a=d.html.addTag(n,"ul",!1,!1);for(var i in o.modifiers){var l=d.html.addTag(a,"li",!1,!1),c=d.html.addTag(l,"button",!1,!1);c.attr("editor-command",o.modifiers[i]),c.addClass(o.modifiers[i])}n.find("button").click(function(n){n.preventDefault();var o=e(this).attr("editor-command");m.commands[o].call(t,n)});var r=d.html.addTag(n,"div",!1,!1);r.addClass("link-area");var s=d.html.addTag(r,"input",!1,!1);s.attr({type:"text"});var u=d.html.addTag(r,"button",!1,!1);u.click(function(t){t.preventDefault();e(this).closest(".editor");e(this).closest(".link-area").hide(),e(this).closest(".bubble").find("ul").show()})},show:function(){var t=e(this).parent().find(".bubble");t.length||(t=d.html.addTag(e(this).parent(),"div",!1,!1),t.addClass("jquery-notebook bubble")),t.empty(),u.buildMenu(this,t),t.show(),u.updateState(this,t),t.hasClass("active")?t.removeClass("jump"):t.addClass("jump"),u.updatePos(e(this),t),t.addClass("active")},update:function(){var t=e(this).parent().find(".bubble");u.updateState(this,t)},clear:function(){var t=e(this).parent().find(".bubble");t.hasClass("active")&&(t.removeClass("active"),u.hideLinkInput.call(this),u.showButtons.call(this),setTimeout(function(){t.hasClass("active")||t.hide()},500))},hideButtons:function(){e(this).parent().find(".bubble").find("ul").hide()},showButtons:function(){e(this).parent().find(".bubble").find("ul").show()},showLinkInput:function(t){u.hideButtons.call(this);var n=this,o=e(this).parent().find(".bubble").find("input[type=text]"),a=o.closest(".jquery-notebook").find("button.anchor").hasClass("active");o.unbind("keydown"),o.keydown(function(o){var i=e(this);d.keyboard.isEnter(o,function(){o.preventDefault();var e=i.val();d.validation.isUrl(e)?(o.url=e,m.commands.createLink(o,t),u.clear.call(n)):""===e&&a&&(m.commands.removeLink(o,t),u.clear.call(n))})}),o.bind("paste",function(){var t=e(this);setTimeout(function(){var e=t.val();/http:\/\/https?:\/\//.test(e)&&(e=e.substring(7),t.val(e))},1)});var i="http://";if(a){var l=e(d.selection.getContainer(t)).closest("a");i=l.prop("href")||i}e(this).parent().find(".link-area").show(),o.val(i).focus()},hideLinkInput:function(){e(this).parent().find(".bubble").find(".link-area").hide()}},h={bindEvents:function(t){t.keydown(f.keydown),t.keyup(f.keyup),t.focus(f.focus),t.bind("paste",m.paste),t.mousedown(f.mouseClick),t.mouseup(f.mouseUp),t.mousemove(f.mouseMove),t.blur(f.blur),e("body").mouseup(function(e){e.target==e.currentTarget&&r.isSelecting&&f.mouseUp.call(t,e)})},setPlaceholder:function(t){if(/^\s*$/.test(e(this).text())){e(this).empty();var n=d.html.addTag(e(this),"p").addClass("placeholder");n.append(e(this).attr("editor-placeholder")),d.html.addTag(e(this),"p","undefined"!=typeof t.focus?t.focus:!1,!0)}else e(this).find(".placeholder").remove()},removePlaceholder:function(){e(this).find(".placeholder").remove()},preserveElementFocus:function(){var e=n.getSelection()?n.getSelection().anchorNode:t.activeElement;if(e){var o=e.parentNode,a=o!==r.focusedElement,i=this.children,l=0;o===this&&(o=e);for(var c=0;c<i.length;c++)if(o===i[c]){l=c;break}a&&(r.focusedElement=o,r.focusedElementIndex=l)}},setContentArea:function(t){var n=e("body").find(".jquery-editor").length+1;t.attr("data-jquery-notebook-id",n);var o=e("body");contentArea=e("<textarea></textarea>"),contentArea.css({position:"absolute",left:-1e3}),contentArea.attr("id","jquery-notebook-content-"+n),o.append(contentArea)},prepare:function(e,t){if(o=t,h.setContentArea(e),e.attr("editor-mode",o.mode),e.attr("editor-placeholder",o.placeholder),e.attr("contenteditable",!0),e.css("position","relative"),e.addClass("jquery-notebook editor"),h.setPlaceholder.call(e,{}),h.preserveElementFocus.call(e),o.autoFocus===!0){var n=e.find("p:not(.placeholder)");d.cursor.set(e,0,n)}}},f={keydown:function(e){var t=this;r.command&&65===e.which&&setTimeout(function(){u.show.call(t)},50),d.keyboard.isCommand(e,function(){r.command=!0},function(){r.command=!1}),d.keyboard.isShift(e,function(){r.shift=!0},function(){r.shift=!1}),d.keyboard.isModifier.call(this,e,function(t){r.command&&m.commands[t].call(this,e)}),r.shift?d.keyboard.isArrow.call(this,e,function(){setTimeout(function(){var e=d.selection.getText();""!==e?u.show.call(t):u.clear.call(t)},100)}):d.keyboard.isArrow.call(this,e,function(){u.clear.call(t)}),13===e.which&&m.enterKey.call(this,e),27===e.which&&u.clear.call(this),86===e.which&&r.command&&m.paste.call(this,e),90===e.which&&r.command&&m.commands.undo.call(this,e)},keyup:function(t){d.keyboard.isCommand(t,function(){r.command=!1},function(){r.command=!0}),h.preserveElementFocus.call(this),h.removePlaceholder.call(this),/^\s*$/.test(e(this).text())&&(e(this).empty(),d.html.addTag(e(this),"p",!0,!0)),m.change.call(this)},focus:function(){r.command=!1,r.shift=!1},mouseClick:function(){if(r.isSelecting=!0,e(this).parent().find(".bubble:visible").length){var t=e(this).parent().find(".bubble:visible"),n=t.offset().left,o=t.offset().top,a=t.width(),i=t.height();if(l>n&&n+a>l&&c>o&&o+i>c)return}},mouseUp:function(e){var t=this;r.isSelecting=!1,setTimeout(function(){var n=d.selection.save();n&&(n.collapsed?u.clear.call(t):(u.show.call(t),e.preventDefault()))},50)},mouseMove:function(e){l=e.pageX,c=e.pageY},blur:function(){h.setPlaceholder.call(this,{focus:!1})}},m={commands:{bold:function(e){e.preventDefault(),t.execCommand("bold",!1),u.update.call(this),m.change.call(this)},italic:function(e){e.preventDefault(),t.execCommand("italic",!1),u.update.call(this),m.change.call(this)},underline:function(e){e.preventDefault(),t.execCommand("underline",!1),u.update.call(this),m.change.call(this)},anchor:function(e){e.preventDefault();var t=d.selection.save();u.showLinkInput.call(this,t),m.change.call(this)},createLink:function(e,n){d.selection.restore(n),t.execCommand("createLink",!1,e.url),u.update.call(this),m.change.call(this)},removeLink:function(t,n){var o=e(d.selection.getContainer(n)).closest("a");o.contents().first().unwrap(),m.change.call(this)},h1:function(n){n.preventDefault(),e(window.getSelection().anchorNode.parentNode).is("h1")?t.execCommand("formatBlock",!1,"<p>"):t.execCommand("formatBlock",!1,"<h1>"),u.update.call(this),m.change.call(this)},h2:function(n){n.preventDefault(),e(window.getSelection().anchorNode.parentNode).is("h2")?t.execCommand("formatBlock",!1,"<p>"):t.execCommand("formatBlock",!1,"<h2>"),u.update.call(this),m.change.call(this)},ul:function(e){e.preventDefault(),t.execCommand("insertUnorderedList",!1),u.update.call(this),m.change.call(this)},ol:function(e){e.preventDefault(),t.execCommand("insertOrderedList",!1),u.update.call(this),m.change.call(this)},undo:function(o){o.preventDefault(),t.execCommand("undo",!1);var a=n.getSelection(),i=a.getRangeAt(0),l=i.getBoundingClientRect();e(document).scrollTop(e(document).scrollTop()+l.top),m.change.call(this)}},enterKey:function(t){if("inline"===e(this).attr("editor-mode"))return t.preventDefault(),void t.stopPropagation();var n=d.selection.getSelection(),o=e(n.focusNode.parentElement),a=o.next();if(!a.length&&"LI"!=o.prop("tagName")){var i=o.prop("tagName");if("OL"===i||"UL"===i){var l=o.children().last();l.length&&""===l.text()&&l.remove()}d.html.addTag(e(this),"p",!0,!0),t.preventDefault(),t.stopPropagation()}m.change.call(this)},paste:function(){var n=(e(this),"jqeditor-temparea"),o=d.selection.save(),a=e("#"+n);if(a.length<1){var i=e("body");a=e("<textarea></textarea>"),a.css({position:"absolute",left:-1e3}),a.attr("id",n),i.append(a)}a.focus(),setTimeout(function(){for(var e="",n=a.val().split("\n"),i=0;i<n.length;i++)e+=["<p>",n[i],"</p>"].join("");a.val(""),d.selection.restore(o),t.execCommand("delete"),t.execCommand("insertHTML",!1,e),m.change.call(this)},500)},change:function(){var t=e("#jquery-notebook-content-"+e(this).attr("data-jquery-notebook-id"));t.val(e(this).html());var n=t.val(),o=new CustomEvent("contentChange",{detail:{content:n}});this.dispatchEvent(o)}};e.fn.notebook=function(t){return t=e.extend({},e.fn.notebook.defaults,t),h.prepare(this,t),h.bindEvents(this),this},e.fn.notebook.defaults={autoFocus:!1,placeholder:"Your text here...",mode:"multiline",modifiers:["bold","italic","underline","h1","h2","ol","ul","anchor"]}}(jQuery,document,window);