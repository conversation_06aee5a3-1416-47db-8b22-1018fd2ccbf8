function sendCurrentUrlToMiniProgram() {
  if (window.__wxjs_environment === 'miniprogram') {
    // 微信小程序环境
    if (window.WeixinJSBridge && window.WeixinJSBridge.invoke) {
      window.WeixinJSBridge.invoke('postMessage', { url: location.href });
    } else if (window.parent) {
      window.parent.postMessage({ url: location.href }, '*');
    }
  }
}

// 页面加载时发送一次
sendCurrentUrlToMiniProgram();

// 如果页面有前端路由（SPA），还需要监听页面变化
window.addEventListener('hashchange', sendCurrentUrlToMiniProgram);
window.addEventListener('popstate', sendCurrentUrlToMiniProgram);