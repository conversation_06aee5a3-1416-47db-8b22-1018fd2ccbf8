<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<title>{$page_title}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="style.css" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="{$page_title}" />
<meta property="og:description" content="{$description}" />
<meta property="og:image" content="{$site.root_url}images/logo.png" />
<meta property="og:url" content="{$page.url}" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{$page_title}" />
<meta name="twitter:description" content="{$description}" />
<meta name="twitter:image" content="{$site.root_url}images/logo.png" />

<!-- Structured Data -->
{literal}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "{$page.page_name}",
  "description": "{$description}",
  "url": "{$page.url}"
}
</script>
{/literal}

<!-- Canonical Link -->
<link rel="canonical" href="{$page.url}" />
</head>
<body>
<div id="wrapper"> {include file="inc/header.tpl"}
 <div class="container mb">
  <div class="row">
   <div class="col-md-2"> {include file="inc/page_tree.tpl"} </div>
   <div class="col-md-10"> {include file="inc/ur_here.tpl"}
    <div id="page">
     <h1>{$page.page_name}</h1>
     <div class="content"> {$page.content} </div>
    </div>
   </div>
  </div>
 </div>
 {include file="inc/online_service.tpl"}
 {include file="inc/footer.tpl"} </div>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/dou.js"></script>
</body>
</html>
