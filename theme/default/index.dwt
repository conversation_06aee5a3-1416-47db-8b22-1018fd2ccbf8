<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="description" content="{$description}" />
<title>{$page_title}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="css/swiper.min.css" rel="stylesheet" type="text/css" />
<link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="style.css" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="{$page_title}" />
<meta property="og:description" content="{$description}" />
<meta property="og:image" content="{$site.root_url}images/logo.png" />
<meta property="og:url" content="{$site.root_url}" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{$page_title}" />
<meta name="twitter:description" content="{$description}" />
<meta name="twitter:image" content="{$site.root_url}images/logo.png" />

<!-- Structured Data -->
{literal}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "{$page_title}",
  "url": "{$site.root_url}",
  "description": "{$description}",
  "publisher": {
    "@type": "Organization",
    "name": "{$site.name}",
    "logo": {
      "@type": "ImageObject",
      "url": "{$site.root_url}images/logo.png"
    }
  }
}
</script>
{/literal}

<!-- Canonical Link -->
<link rel="canonical" href="{$site.root_url}" />
</head>
<body>
<div id="wrapper">
 {include file="inc/header.tpl"}
 {include file="inc/slide_show.tpl"}
 <div id="index">
  <div class="index-box">
   <div class="container">
    {include file="inc/about.tpl"}
   </div>
  </div>
  <!-- {if $recommend_product} -->
  <div class="index-box bg">
   <h3><b>{$lang.product_news}</b><em>Recommend Product</em></h3>
   {include file="inc/recommend_product.tpl"}
   <div class="more"><a href="{$url.product}">{$lang.product_more}</a></div>
  </div>
  <!-- {/if} -->
  <!-- {if $recommend_article} -->
  <div class="index-box">
   <h3><b>{$lang.article_news}</b><em>Recommend News</em></h3>
   {include file="inc/recommend_article.tpl"}
   <div class="more"><a href="{$url.article}">{$lang.article_more}</a></div>
  </div>
  <!-- {/if} -->
  <!-- {if $open.link} -->
  {include file="inc/link.tpl"}
  <!-- {/if} -->
 </div>
 {include file="inc/footer.tpl"} </div>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/swiper.min.js"></script>
<script type="text/javascript" src="js/slide_show.js"></script>
<script type="text/javascript" src="js/dou.js"></script>
</body>
</html>
