<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<title>{$page_title}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="style.css" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="{$page_title}" />
<meta property="og:description" content="{$description}" />
<meta property="og:url" content="{$url}" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{$page_title}" />
<meta name="twitter:description" content="{$description}" />

<!-- Structured Data -->
{literal}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "{$page_title}",
  "description": "{$description}",
  "url": "{$url}"
}
</script>
{/literal}

<!-- Canonical Link -->
<link rel="canonical" href="{$url}" />

<!-- {if $url} -->
<meta http-equiv="refresh" content="{$time}; URL={$url}" />
<!-- {else} -->
<script type="text/javascript">
{literal}
function go() {
    window.history.go( - 1);
}
setTimeout("go()", 3000);
{/literal}
</script>
<!-- {/if} -->
</head>
<body>
<div id="wrapper"> {include file="inc/header.tpl"}
 <div id="dou-msg" class="wrap">
  <dl>
   <dt>{$text}</dt>
   <dd>{$cue}<a href="{if $url}{$url}{else}javascript:history.go(-1);{/if}">{$lang.dou_msg_back}</a></dd>
  </dl>
 </div>
 {include file="inc/online_service.tpl"}
 {include file="inc/footer.tpl"} </div>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/dou.js"></script>
</body>
</html>