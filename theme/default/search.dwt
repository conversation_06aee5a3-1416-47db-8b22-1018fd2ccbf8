<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="{$keywords}" />
<meta name="description" content="{$description}" />
<meta name="generator" content="{$generator}" />
<title>{$page_title}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="style.css" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="{$page_title}" />
<meta property="og:description" content="{$description}" />
<meta property="og:image" content="{$site.root_url}images/logo.png" />
<meta property="og:url" content="{$site.root_url}" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{$page_title}" />
<meta name="twitter:description" content="{$description}" />
<meta name="twitter:image" content="{$site.root_url}images/logo.png" />

<!-- Structured Data -->
{literal}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "{$page_title}",
  "description": "{$description}",
  "url": "{$site.root_url}"
}
</script>
{/literal}

<!-- Canonical Link -->
<link rel="canonical" href="{$site.root_url}" />
</head>
<body>
<div id="wrapper"> {include file="inc/header.tpl"}
 <!-- {if $search_module eq 'product'} --> 
 <div id="product-category" class="container mb">
  <div class="row">
   <div class="col-md-2">
    {include file="inc/product_tree.tpl"}
   </div>
   <div class="col-md-10">
    {include file="inc/ur_here.tpl"}
    <div class="row product-list"> 
     <!-- {foreach from=$search_list name=search_list item=product} -->
     <div class="col-md-3 col-6">
      <div class="item">
       <div class="img scale"><a href="{$product.url}"><img src="{$product.thumb}" alt="{$product.name}" title="{$product.name}" /></a></div>
       <div class="name"><a href="{$product.url}">{$product.name}</a></div>
      </div>
     </div>
     <!-- {/foreach} --> 
    </div>
    {include file="inc/pager.tpl"}
   </div>
  </div>
 </div>
 <!-- {else} --> 
 <div class="container mb"> 
  <div class="row">
   <div class="col-md-2">
    {include file="inc/article_tree.tpl"}
   </div>
   <div class="col-md-10">
    {include file="inc/ur_here.tpl"}
    <div id="article-list"> 
     <!-- {foreach from=$search_list name=search_list item=article} --> 
     <dl<!-- {if $smarty.foreach.search_list.last} --> class="last"<!-- {/if} -->>
     <div class="num-date"> <em>{$article.click}</em>
      <p>{$article.add_time_short}</p>
     </div>
     <dt><a href="{$article.url}">{$article.title}</a></dt>
     <dd><!-- {if $article.image} -->
      <p class="img"><img src="{$article.image}" alt="{$article.title}" title="{$article.title}" height="42"></p>
      <!-- {/if} -->
      <p class="desc">{$article.description|truncate:96:"..."}</p>
     </dd>
     </dl>
     <!-- {/foreach} --> 
    </div>
    {include file="inc/pager.tpl"}
   </div>
  </div>
 </div>
 <!-- {/if} --> 
 {include file="inc/online_service.tpl"}
 {include file="inc/footer.tpl"} </div>
<script type="text/javascript" src="js/jquery.min.js"></script>
<script type="text/javascript" src="js/bootstrap.min.js"></script>
<script type="text/javascript" src="js/dou.js"></script>
</body>
</html>
