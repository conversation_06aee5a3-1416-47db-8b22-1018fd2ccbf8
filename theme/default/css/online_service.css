/* online-service
----------------------------------------------- */
.online-service {
 position: fixed;
 _position: absolute;
 _top: expression(eval(document.documentElement.scrollTop+document.documentElement.clientHeight)-380+"px");
 top: 208px;
 right: 10px;
 z-index: 999999;
 display: block;
}
.online-service .item {
 position: relative;
 margin-bottom: 10px;
}
.online-service .item .item-icon {
 display: block;
 cursor: pointer;
 background: #FFF url(../images/online_service.png) no-repeat;
 width: 44px;
 height: 44px;
 border-radius: 4px;
 box-sizing: border-box;
 box-shadow: 0 0 9px 0 rgba(0,0,0,0.1);
}
.online-service .item .item-icon.chat {
 background-position: left 0;
}
.online-service .item .item-icon.qq {
 background-position: left -44px;
}
.online-service .item .item-icon.weixin {
 background-position: left -88px;
}
.online-service .item .item-icon.tel {
 background-position: left -132px;
}
.online-service .item.active .item-icon {
 background-color: #0072C6;
}
.online-service .item.active .item-icon.chat {
 background-position: right 0;
}
.online-service .item.active .item-icon.qq {
 background-position: right -44px;
}
.online-service .item.active .item-icon.weixin {
 background-position: right -88px;
}
.online-service .item.active .item-icon.tel {
 background-position: right -132px;
}
.online-service .item .pop-box {
 display: none;
 position: absolute;
 right: 100%;
 top: 0;
}
.online-service .item .item-box {
 background-color: #FFF;
 margin-right: 10px;
 padding: 10px;
 border-radius: 2px;
 box-sizing: border-box;
 box-shadow: 0 0 9px 0 rgba(0,0,0,0.1);
}
.online-service .item .qq-box {
 padding: 10px 20px 0 10px;
}
.online-service .item .qq-box .qq {
 display: block;
 position: relative;
 line-height: 34px;
 padding-left: 44px;
 white-space: nowrap;
 margin-bottom: 10px;
 color: #555;
}
.online-service .item .qq-box .qq .qq-icon {
 display: inline-block;
 width: 34px;
 height: 34px;
 background: #F1F1F3 url(../images/online_service.png) no-repeat 4px -208px;
 transition: all .2s;
 border-radius: 50%;
 position: absolute;
 left: 0;
 top: 0;
}
.online-service .item .qq-box .qq:hover .qq-icon {
 background-position: 4px -218px;
}
.online-service .item .weixin-box img {
 width: 120px;
}
.online-service .item .weixin-box p {
 text-align: center;
}
.online-service .item .tel-box {
 white-space: nowrap;
 font-size: 16px;
 padding: 0 10px;
}
.online-service .go-top {
 display: none;
}
.online-service .go-top .go-btn {
 display: block;
 background: #FFF url(../images/online_service.png) no-repeat left -176px;
 border-radius: 4px;
 box-sizing: border-box;
 box-shadow: 0 0 9px 0 rgba(0,0,0,0.1);
 width: 44px;
 height: 44px;
}
.online-service .go-top .go-btn:hover {
 background-position: right -176px;
 background-color: #0072C6;
}