/* 覆盖一些可能与 Pico CSS 冲突的样式 */
body {
  font-family: Microsoft Yahei, \5FAE\8F6F\96C5\9ED1, \5b8b\4f53, Arial, Lucida, Verdana, Helvetica, sans-serif;
  color: #555;
  line-height: initial;
}

/* 调整按钮样式以匹配网站现有风格 */
[role="button"],
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  --background-color: #1979CC;
  --border-color: #1979CC;
  --color: #fff;
  --box-shadow: none;
}

/* 调整表单元素样式 */
input:not([type="submit"], [type="button"], [type="reset"]),
select,
textarea {
  --border-color: #E9E9E9;
  --background-color: #FFF;
}

/* 自定义类保持原有样式 */
.ur-here {
  padding: 25px 0 10px 0;
  line-height: 20px;
  color: #999999;
  font-weight: bold;
  font-size: 14px;
  border-bottom: 1px solid #DDD;
}

/* 保持特定组件的样式 */
#online-service .onlineIcon {
  background: url(images/online_service.png) no-repeat;
  width: 34px;
  height: 110px;
}

/* 添加一些辅助类来维持现有布局 */
.mb {
  margin-bottom: 50px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ur-here {
    display: none;
  }
}
