<?php

// 伪静态模式下首页将作为其它内页入口
if (isset($_REQUEST['route'])) {
    include ('include/rewrite.class.php');
    $dou_rewrite = new Rewrite();
    
    $_REWRITE = $dou_rewrite->format($_REQUEST['route']);
    if ($_REWRITE['module'] && file_exists($original_file = $_REWRITE['site_path'] . $_REWRITE['module'] . '.php')) {
        foreach ($_REWRITE as $key => $value) {
            if ($value)
                $_REQUEST[$key] = $value;
        }
        
        require ($original_file);
        exit;
    } else {
        if (!$_REWRITE['is_lang_home']) {
            header("Location: " . dirname('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['PHP_SELF']));
            exit;
        }
    }
}

define('IN_DOUCO', true);

// 强制在移动端中显示PC版
if (isset($_REQUEST['mobile'])) {
    setcookie('client', 'pc');
    $_COOKIE['client'] = 'pc';
}

$_CUR = 'index';
require (dirname(__FILE__) . '/include/init.php');
$smarty->assign('cur', $_CUR);

// 如果存在搜索词则转入搜索页面
if (isset($_REQUEST['s'])) {
    if ($check->is_search_keyword($keyword = trim($_REQUEST['s']))) {
        require (ROOT_PATH . 'include/search.inc.php');
        exit;
    } else {
        $dou->dou_msg($_LANG['search_keyword_wrong']);
    }
}

// 获取关于我们信息
$sql = "SELECT * FROM " . $dou->table('page') . " WHERE id = '1'";
$query = $dou->query($sql);
$about = $dou->fetch_array($query);

// 多语言
$about = $dou->lang_box($about, 'page_name', 'title, description, content');

// 写入到index数组
$index['about_name'] = $about['page_name'];
$index['about_content'] = $about['description'] ? $about['description'] : $dou->dou_substr($about['content'], 300, false); // 这里的300数值不能设置得过大，否则会造成程序卡死
$index['about_link'] = $dou->rewrite_url('page', '1');
$index['cur'] = true;

// 代码扩展，此文件不会随着系统升级被覆盖
if (file_exists($code_include_file = ROOT_PATH . '..code.php'))
    require ($code_include_file);

// 赋值给模板-meta和title信息
$smarty->assign('page_title', $dou->page_title());
$smarty->assign('keywords', $_CFG['site_keywords']);
$smarty->assign('description', $_CFG['site_description']);

// 赋值给模板-导航栏
$smarty->assign('nav_top_list', $dou->get_nav('top'));
$smarty->assign('nav_middle_list', $dou->get_nav('middle'));
$smarty->assign('nav_bottom_list', $dou->get_nav('bottom'));

// 赋值给模板-数据
$smarty->assign('show_list', $dou->get_show_list());
$smarty->assign('link', $dou->get_link_list());
$smarty->assign('index', $index);
$smarty->assign('recommend_product', $dou->get_list('product', 'ALL', $_DISPLAY['home_product'], 'sort ASC'));
$smarty->assign('recommend_article', $dou->get_list('article', 'ALL', $_DISPLAY['home_article'], 'sort ASC'));

$smarty->display('index.dwt');

?>
