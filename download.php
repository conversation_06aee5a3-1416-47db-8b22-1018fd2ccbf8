<?php

define('IN_DOUCO', true);

$_CUR = 'download';
require (dirname(__FILE__) . '/include/init.php');
$smarty->assign('cur', $_CUR);

// 验证并获取合法的ID，如果不合法将其设定为-1
$id = $firewall->get_legal_id('download', $_REQUEST['id'], $_REQUEST['unique_id']);
$cat_id = $dou->get_one("SELECT cat_id FROM " . $dou->table('download') . " WHERE id = '$id'");
$parent_id = $dou->get_one("SELECT parent_id FROM " . $dou->table('download_category') . " WHERE cat_id = '" . $cat_id . "'");
if ($id == -1)
    $dou->dou_msg($GLOBALS['_LANG']['page_wrong'], ROOT_URL);
    
/* 获取详细信息 */
$download = $dou->get_row('download', '*', "id = '$id'");

// 格式化数据
$download['add_time'] = date("Y-m-d", $download['add_time']);
$download['image'] = $dou->dou_file($download['image']);

// 多语言
$download = $dou->lang_box($download, 'download', 'title, content, keywords, description');

// 对应的分类信息
$cate_info = $dou->get_row('download_category', 'cat_id, cat_name', "cat_id = '$download[cat_id]'");
$cate_info['url'] = $dou->rewrite_url('download_category', $download['cat_id']);
$download['cate_info'] = $cate_info;

// 访问统计
$click = $download['click'] + 1;
$dou->query("update " . $dou->table('download') . " SET click = '$click' WHERE id = '$id'");

// 赋值给模板-meta和title信息
$smarty->assign('page_title', $dou->page_title('download_category', $cat_id, $download['title']));
$smarty->assign('keywords', $download['keywords']);
$smarty->assign('description', $download['description']);

// 赋值给模板-导航栏
$smarty->assign('nav_top_list', $dou->get_nav('top'));
$smarty->assign('nav_middle_list', $dou->get_nav('middle', '0', 'download_category', $cat_id, $parent_id));
$smarty->assign('nav_bottom_list', $dou->get_nav('bottom'));

// 赋值给模板-数据
$smarty->assign('ur_here', $dou->ur_here('download_category', $cat_id, $download['title']));
$smarty->assign('download_category', $dou->get_category('download_category', 0, $cat_id));
$smarty->assign('lift', $dou->lift('download', $id, $cat_id));
$smarty->assign('download', $download);
$smarty->assign('defined', $dou->format_defined($download['defined']));

$smarty->display('download.dwt');
?>