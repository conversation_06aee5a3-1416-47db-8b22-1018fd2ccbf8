<?php


// 微信小程序
$_LANG['miniprogram'] = '微信小程序';
$_LANG['miniprogram_list'] = '我的小程序';
$_LANG['miniprogram_del'] = '刪除小程序';
$_LANG['miniprogram_system'] = '小程序參數設置';
$_LANG['miniprogram_nav'] = '小程序导航';
$_LANG['miniprogram_show'] = '幻燈圖片';
$_LANG['miniprogram_release'] = '發布小程序';
$_LANG['miniprogram_release_btn'] = '發布說明';
$_LANG['miniprogram_install'] = '更多小程序';

// 小程序簡介
$_LANG['miniprogram_description'] = '簡介';
$_LANG['miniprogram_enabled'] = '當前啟用的小程序';
$_LANG['miniprogram_preview'] = '預覽小程序';
$_LANG['miniprogram_installed'] = '可用小程序';

// 參數設置項
$_LANG['miniprogram_miniprogram_appid_lang'] = 'AppID/小程序ID';
$_LANG['miniprogram_miniprogram_appid_cue'] = '微信公眾平臺：mp.weixin.qq.com，“開發->開發設置”裏獲取';
$_LANG['miniprogram_miniprogram_appsecret_lang'] = 'AppSecret/小程序密鑰';
$_LANG['miniprogram_miniprogram_appsecret_cue'] = '微信公眾平臺：mp.weixin.qq.com，“開發->開發設置”裏獲取';
$_LANG['miniprogram_miniprogram_pay_mch_id_lang'] = '微信支付商戶號（如果沒有使用到微信支付無需設置）';
$_LANG['miniprogram_miniprogram_pay_mch_id_cue'] = '微信商戶平臺：pay.weixin.qq.com，“賬戶中心->商戶信息”裏獲取，另外需要在“產品中心-》AppID賬號管理”將上面填寫的AppID對應的小程序進行綁定';
$_LANG['miniprogram_miniprogram_pay_key_lang'] = '微信支付商戶API密鑰';
$_LANG['miniprogram_miniprogram_pay_key_cue'] = '微信商戶平臺：pay.weixin.qq.com，“賬戶中心->API安全->設置APIv2密鑰”裏獲取';


?>