<?php


// 微信小程序
$_LANG['miniprogram'] = '微信小程序';
$_LANG['miniprogram_list'] = '我的小程序';
$_LANG['miniprogram_del'] = '删除小程序';
$_LANG['miniprogram_system'] = '小程序参数设置';
$_LANG['miniprogram_nav'] = '小程序导航';
$_LANG['miniprogram_show'] = '幻灯图片';
$_LANG['miniprogram_release'] = '发布小程序';
$_LANG['miniprogram_release_btn'] = '发布说明';
$_LANG['miniprogram_install'] = '更多小程序';

// 小程序简介
$_LANG['miniprogram_description'] = '简介';
$_LANG['miniprogram_enabled'] = '当前启用的小程序';
$_LANG['miniprogram_preview'] = '预览小程序';
$_LANG['miniprogram_installed'] = '可用小程序';

// 参数设置项
$_LANG['miniprogram_miniprogram_appid_lang'] = 'AppID/小程序ID';
$_LANG['miniprogram_miniprogram_appid_cue'] = '微信公众平台：mp.weixin.qq.com，“开发->开发设置”里获取';
$_LANG['miniprogram_miniprogram_appsecret_lang'] = 'AppSecret/小程序密钥';
$_LANG['miniprogram_miniprogram_appsecret_cue'] = '微信公众平台：mp.weixin.qq.com，“开发->开发设置”里获取';
$_LANG['miniprogram_miniprogram_pay_mch_id_lang'] = '微信支付商户号（如果没有使用到微信支付无需设置）';
$_LANG['miniprogram_miniprogram_pay_mch_id_cue'] = '微信商户平台：pay.weixin.qq.com，“账户中心->商户信息”里获取，另外需要在“产品中心-》AppID账号管理”将上面填写的AppID对应的小程序进行绑定';
$_LANG['miniprogram_miniprogram_pay_key_lang'] = '微信支付商户API密钥';
$_LANG['miniprogram_miniprogram_pay_key_cue'] = '微信商户平台：pay.weixin.qq.com，“账户中心->API安全->设置APIv2密钥”里获取';


?>