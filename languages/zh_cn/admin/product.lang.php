<?php

// 系统设置
$_LANG['top_add_product'] = '商品';
$_LANG['display_product'] = '商品列表数量';
$_LANG['display_home_product'] = '首页展示商品数量';
$_LANG['defined_product'] = '商品自定义属性';
$_LANG['defined_product_cue'] = '如"颜色,尺寸,型号"中间以英文逗号隔开';
$_LANG['nav_product'] = '产品中心';
$_LANG['mobile_display_product'] = '手机版商品列表数量';
$_LANG['mobile_display_home_product'] = '手机版首页展示商品数量';

// 产品分类
$_LANG['product_category'] = '商品分类';
$_LANG['product_category_add'] = '添加分类';
$_LANG['product_category_edit'] = '编辑商品分类';
$_LANG['product_category_del'] = '删除商品分类';
$_LANG['product_category_del_is_parent'] = '"d%"不是末级分类或者分类下还存在商品，您不能删除';
$_LANG['product_category_cat_name'] = '分类名称';
$_LANG['product_category_add_succes'] = '添加商品分类成功';
$_LANG['product_category_edit_succes'] = '编辑商品分类成功';

// 产品中心
$_LANG['product'] = '商品列表';
$_LANG['product_add'] = '添加商品';
$_LANG['product_edit'] = '编辑商品';
$_LANG['product_thumb'] = '更新商品缩略图';
$_LANG['product_thumb_start'] = '开始更新';
$_LANG['product_thumb_count'] = '总计 d% 个商品图片需要重新生成';
$_LANG['product_thumb_succes'] = '操作完成！';
$_LANG['product_gallery'] = '主图';
$_LANG['product_gallery_btn'] = '点击上传';
$_LANG['product_model'] = '关联型号';
$_LANG['product_del'] = '删除商品';
$_LANG['product_name'] = '商品名称';
$_LANG['product_price'] = '商品价格';
$_LANG['product_level_price'] = '等级价格';
$_LANG['product_stock'] = '库存';
$_LANG['product_point'] = '消耗积分';
$_LANG['product_defined'] = '自定义属性';
$_LANG['product_defined_cue'] = '以英文逗号 , 隔开';
$_LANG['product_content'] = '商品描述';
$_LANG['product_add_succes'] = '添加商品成功';
$_LANG['product_edit_succes'] = '编辑商品成功';
$_LANG['product_select_empty'] = '没有选择任何商品';

// 提示信息
$_LANG['product_model_cue'] = '输入产品编号';
$_LANG['product_model_title'] = '在产品列表中查看编号，点击加号后关联';
$_LANG['product_model_wrong'] = '请先添加好商品，再次编辑时才可以关联型号';

?>