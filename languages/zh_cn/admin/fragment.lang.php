<?php


// 内容碎片
$_LANG['fragment'] = '内容碎片';
$_LANG['fragment_list'] = '内容碎片列表';
$_LANG['fragment_add'] = '添加碎片';
$_LANG['fragment_edit'] = '编辑碎片';
$_LANG['fragment_link_box'] = '关联内容盒子';
$_LANG['fragment_link_text'] = '关联后对应的内容盒子将会显示在“首页内容”';
$_LANG['fragment_link_box_cancel'] = '取消关联内容盒子';
$_LANG['fragment_link_box_a'] = '关联“分组别名”为';
$_LANG['fragment_link_box_b'] = '的内容盒子';
$_LANG['fragment_link_box_btn'] = '立即关联';
$_LANG['fragment_link_box_btn_cancel'] = '取消关联';
$_LANG['fragment_parent'] = '管理分组';
$_LANG['fragment_name'] = '碎片名称';
$_LANG['fragment_mark'] = '唯一标记';
$_LANG['fragment_image'] = '图片内容';
$_LANG['fragment_text'] = '文字内容';
$_LANG['fragment_link'] = '链接';
$_LANG['fragment_home'] = '显示在"首页内容"';

// 备注
$_LANG['fragment_name_cue'] = '前台调用标签：{$fragment.唯一标记.name}';
$_LANG['fragment_mark_cue'] = '唯一标记不能为空且只能使用小写字母、数字、下划线，前台调用标签为：{$fragment.唯一标记.mark}';
$_LANG['fragment_image_cue'] = '前台调用标签：{$fragment.唯一标记.image}';
$_LANG['fragment_text_cue'] = '前台调用标签：{$fragment.唯一标记.text}';
$_LANG['fragment_link_cue'] = '前台调用标签：{$fragment.唯一标记.link}';
$_LANG['fragment_parent_cue'] = '设置分组方便后台将碎片分组排列';

// 提示信息
$_LANG['fragment_add_succes'] = '添加碎片成功';
$_LANG['fragment_edit_succes'] = '编辑碎片成功';
$_LANG['fragment_del'] = '删除碎片';
$_LANG['fragment_del_is_parent'] = '"d%"存在子碎片，您不能删除';

?>