<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
            color: #333;
            text-align: center;
        }
        h3 {
            color: #4CAF50;
        }
        p {
            font-size: 1.2em;
        }
        .loader {
            border: 8px solid #f3f3f3;
            border-radius: 50%;
            border-top: 8px solid #4CAF50;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin-top: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .language-switcher {
            position: absolute;
            top: 20px;
            right: 20px;
        }
        .language-switcher button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 10px;
            font-size: 1em;
        }
    </style>
</head>
<body>
    <div class="language-switcher">
        <button onclick="switchLanguage('en')">English</button> | 
        <button onclick="switchLanguage('zh')">中文</button>
    </div>
    <h3>欢迎光临深圳市华通通讯有限公司网站</h3>
    <p>正在连线我们的客服，请稍等喔~~ 😊</p>
    <div class="loader" id="loader"></div>

    <script type="text/javascript">
        window.$crisp = [];
        window.CRISP_WEBSITE_ID = "96f816c2-7c89-480c-8f56-d21b5e7759f5";
        (function() {
            d = document;
            s = d.createElement("script");
            s.src = "https://crisp.woto.cn/l.js";
            s.async = 1;
            d.getElementsByTagName("head")[0].appendChild(s);
        })();

        // Open the chat window after Crisp script is loaded and stop the loader
        window.CRISP_READY_TRIGGER = function() {
            $crisp.push(["do", "chat:open"]);
            document.getElementById("loader").style.display = "none"; // 隐藏加载指示器
            document.querySelector('p').textContent = "客服已上线，请开始聊天~~ 😊"; // 更新文字
        };

        function switchLanguage(lang) {
            if (lang === 'en') {
                document.querySelector('h3').textContent = "Welcome to Shenzhen Woto Communication Co., Ltd.";
                document.querySelector('p').textContent = "Connecting to our customer service, please wait~~ 😊";
            } else if (lang === 'zh') {
                document.querySelector('h3').textContent = "欢迎光临深圳市华通通讯有限公司网站";
                document.querySelector('p').textContent = "正在连线我们的客服，请稍等喔~~ 😊";
            }
        }
    </script>
</body>
</html>
