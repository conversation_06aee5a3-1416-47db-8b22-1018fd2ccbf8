<?php

define('IN_DOUCO', true);

require (dirname(__FILE__) . '/include/init.php');

// rec操作项的初始化
$rec = $check->is_rec($_REQUEST['rec']) ? $_REQUEST['rec'] : 'default';

// 赋值给模板
$smarty->assign('rec', $rec);
$smarty->assign('cur', 'miniprogram');

// 小程序代码路径
define('MINIPROGRAM_CODE_PATH', ROOT_PATH . MINIPROGRAM_PATH . '/code/');

/**
 * +----------------------------------------------------------
 * 我的小程序
 * +----------------------------------------------------------
 */
if ($rec == 'default') {
    $smarty->assign('ur_here', $_LANG['miniprogram_list']);
    
    // 判断小程序目录是否存在
    if (!file_exists(MINIPROGRAM_CODE_PATH))
        mkdir(MINIPROGRAM_CODE_PATH, 0777);

    $miniprogram_enable = $dou->get_miniprogram_info($_CFG['miniprogram_code']);
    $miniprogram_array = $dou->get_subdirs(MINIPROGRAM_CODE_PATH);
    if (!$miniprogram_array)
        $dou->dou_header('miniprogram.php?rec=install');
    
    foreach ($miniprogram_array as $unique_id) {
        if ($unique_id == $_CFG['miniprogram_code'])
            continue;
        
        $miniprogram_info = $dou->get_miniprogram_info($unique_id);
        if ($miniprogram_info)
            $miniprogram_list[] = $miniprogram_info;
    }
    
    $smarty->assign('miniprogram_enable', $miniprogram_enable);
    $smarty->assign('miniprogram_list', $miniprogram_list);

    $smarty->display('miniprogram.htm');
}

/**
 * +----------------------------------------------------------
 * 发布小程序
 * +----------------------------------------------------------
 */
elseif ($rec == 'release') {
    $smarty->assign('ur_here', $_LANG['miniprogram_release']);
 
    $unique_id = $check->is_extend_id($_REQUEST['unique_id']) ? $_REQUEST['unique_id'] : '您购买的小程序目录';
    $miniprogram_path = MINIPROGRAM_PATH . '/' . $unique_id;
    
    $smarty->assign('miniprogram_path', $miniprogram_path);
    
    $smarty->display('miniprogram.htm');
}

/**
 * +----------------------------------------------------------
 * 自定义导航
 * +----------------------------------------------------------
 */
elseif ($rec == 'nav') {
    // act操作项的初始化
    $act = $check->is_rec($_REQUEST['act']) ? $_REQUEST['act'] : 'default';
    
    // 赋值给模板
    $smarty->assign('act', $act);
    $smarty->assign('nav_list', $dou->get_nav_admin('miniprogram'));
    
    // 幻灯列表
    if ($act == 'default') {
        $smarty->assign('ur_here', $_LANG['miniprogram_nav']);
        $smarty->assign('action_link', array (
                'text' => $_LANG['nav_add'],
                'href' => 'miniprogram.php?rec=nav&act=add' 
        ));
        
        // 赋值给模板
        $smarty->assign('nav_list', $dou->get_nav_admin('miniprogram'));
        
        $smarty->display('miniprogram.htm');
    }    

    // 导航添加
    elseif ($act == 'add') {
        $smarty->assign('ur_here', $_LANG['miniprogram_nav']);
        $smarty->assign('action_link', array (
                'text' => $_LANG['nav_list'],
                'href' => 'miniprogram.php?rec=nav' 
        ));
        
        // CSRF防御令牌生成
        $smarty->assign('token', $token = $firewall->get_token());
        
        // 赋值给模板
        $smarty->assign('catalog', $dou->get_catalog());
        $smarty->assign('nav_list', $dou->get_nav_admin('miniprogram'));
        
        $smarty->display('miniprogram.htm');
    }    

    // 导航添加处理
    elseif ($act == 'insert') {
        $nav_menu = explode(",", $_POST['nav_menu']);
        $module = $nav_menu[0];
        $guide = $module == 'nav' ? trim($_POST['guide']) : $nav_menu[1];
        
        if (empty($_POST['nav_name']))
            $dou->dou_msg($_LANG['nav_name'] . $_LANG['is_empty']);
            
        // CSRF防御令牌验证
        $firewall->check_token($_POST['token']);
        
        $sql = "INSERT INTO " . $dou->table('nav') . " (id, module, nav_name, guide, parent_id, type, sort)" . " VALUES (NULL, '$module', '$_POST[nav_name]', '$guide', 0, 'miniprogram', '$_POST[sort]')";
        $dou->query($sql);
        
        $dou->create_admin_log($_LANG['miniprogram'] . ' - ' . $_LANG['nav_add'] . ': ' . $_POST['nav_name']);
        $dou->dou_msg($_LANG['nav_add_succes'], 'miniprogram.php?rec=nav');
    }    

    // 导航编辑
    elseif ($act == 'edit') {
        $smarty->assign('ur_here', $_LANG['miniprogram_nav']);
        $smarty->assign('action_link', array (
                'text' => $_LANG['nav_list'],
                'href' => 'miniprogram.php?rec=nav' 
        ));
        
        // 验证并获取合法的ID
        $id = $check->is_number($_REQUEST['id']) ? $_REQUEST['id'] : '';
        
        $nav_info = $dou->get_row('nav', '*', "id = '$id'");
        
        // CSRF防御令牌生成
        $smarty->assign('token', $token = $firewall->get_token());
        
        // 格式化数据
        $nav_info['url'] = $nav_info['module'] == 'nav' ? $nav_info['guide'] : $dou->rewrite_url($nav_info['module'], $nav_info['guide']);
        
        // 赋值给模板
        $smarty->assign('catalog', $dou->get_catalog($nav_info['module'], $nav_info['guide']));
        $smarty->assign('nav_list', $dou->get_nav_admin('miniprogram'));
        $smarty->assign('nav_info', $nav_info);
        
        $smarty->display('miniprogram.htm');
    }    

    // 导航编辑处理
    elseif ($act == 'update') {
        // 验证并获取合法的ID
        $id = $check->is_number($_POST['id']) ? $_POST['id'] : '';
     
        if (empty($_POST['nav_name']))
            $dou->dou_msg($_LANG['nav_name'] . $_LANG['is_empty']);
            
        // CSRF防御令牌验证
        $firewall->check_token($_POST['token']);
        
        /* 判断是站内还是站外导航 */
        if ($_POST['nav_menu']) {
            $nav_menu = explode(',', $_POST['nav_menu']);
            $update = ", module='$nav_menu[0]', guide='$nav_menu[1]'";
        } else {
            $update = ", guide='$_POST[guide]'";
        }
        
        $sql = "update " . $dou->table('nav') . " SET nav_name = '$_POST[nav_name]'" . $update . ", sort = '$_POST[sort]' WHERE id = '$id'";
        $dou->query($sql);
        
        $dou->create_admin_log($_LANG['miniprogram'] . ' - ' . $_LANG['nav_edit'] . ': ' . $_POST['nav_name']);
        
        $dou->dou_msg($_LANG['nav_edit_succes'], 'miniprogram.php?rec=nav');
    }    

    // 导航删除
    elseif ($act == 'del') {
        // 验证并获取合法的ID
        $id = $check->is_number($_REQUEST['id']) ? $_REQUEST['id'] : $dou->dou_msg($_LANG['illegal'], 'miniprogram.php?rec=nav');
        
        $nav_name = $dou->get_one("SELECT nav_name FROM " . $dou->table('nav') . " WHERE id = '$id'");
        
        if (isset($_POST['confirm'])) {
            $dou->create_admin_log($_LANG['miniprogram'] . ' - ' . $_LANG['nav_del'] . ': ' . $nav_name);
            $dou->delete('nav', "id = '$id'", 'miniprogram.php?rec=nav');
        } else {
            $_LANG['del_check'] = preg_replace('/d%/Ums', $nav_name, $_LANG['del_check']);
            $dou->dou_msg($_LANG['del_check'], 'miniprogram.php?rec=nav', '', '30', "miniprogram.php?rec=nav&act=del&id=$id");
        }
    }
} 

/**
 * +----------------------------------------------------------
 * 小程序幻灯
 * +----------------------------------------------------------
 */
elseif ($rec == 'show') {
    $smarty->assign('ur_here', $_LANG['miniprogram_show']);
 
    // act操作项的初始化
    $act = $check->is_rec($_REQUEST['act']) ? $_REQUEST['act'] : 'default';
    
    // 图片上传
    include_once (ROOT_PATH . 'include/file.class.php');
    $file = new File('data/slide/' . MINIPROGRAM_PATH . '/'); // 实例化类文件(文件上传路径，结尾加斜杠)
                                                            
    // 赋值给模板
    $smarty->assign('act', $act);
    $smarty->assign('show_list', $dou->get_show_list('miniprogram'));
    
    // 幻灯列表
    if ($act == 'default') {
        // CSRF防御令牌生成
        $smarty->assign('token', $token = $firewall->get_token());
        
        $smarty->display('miniprogram.htm');
    }    

    // 幻灯添加处理
    elseif ($act == 'insert') {
        // 验证幻灯名称
        if (empty($_POST['show_name'])) $dou->dou_msg($_LANG['show_name'] . $_LANG['is_empty']);
            
        // 文件上传盒子
        if ($_FILES['show_img']['name'] != "") {
            $custom_filename = $dou->create_rand_string('letter', 6, date('Ymd'));
            $show_img = $file->box('show', $dou->auto_id('show'), 'show_img', 'main', $custom_filename);
        }
        
        // CSRF防御令牌验证
        $firewall->check_token($_POST['token']);
        
        $sql = "INSERT INTO " . $dou->table('show') . " (id, show_name, show_link, show_img, type, sort)" . " VALUES (NULL, '$_POST[show_name]', '$_POST[show_link]', '$show_img', 'miniprogram', '$_POST[sort]')";
        $dou->query($sql);
        
        $dou->create_admin_log($_LANG['miniprogram'] . ' - ' . $_LANG['show_add'] . ': ' . $_POST[show_name]);
        $dou->dou_msg($_LANG['show_add_succes'], 'miniprogram.php?rec=show');
    }    

    // 幻灯编辑
    elseif ($act == 'edit') {
        // 验证并获取合法的ID
        $id = $check->is_number($_REQUEST['id']) ? $_REQUEST['id'] : '';
        
        $show = $dou->get_row('show', '*', "id = '$id'");
        
        // 格式化数据
        $show['show_img'] = $dou->dou_file($show['show_img']);
        
        // CSRF防御令牌生成
        $smarty->assign('token', $token = $firewall->get_token());
        
        // 赋值给模板
        $smarty->assign('id', $id);
        $smarty->assign('show', $show);
        
        $smarty->display('miniprogram.htm');
    } 

    elseif ($act == 'update') {
        // 验证并获取合法的ID
        $id = $check->is_number($_POST['id']) ? $_POST['id'] : '';
     
        // 验证幻灯名称
        if (empty($_POST['show_name'])) $dou->dou_msg($_LANG['show_name'] . $_LANG['is_empty']);
            
        // 图片上传
        if ($_FILES['show_img']['name'] != "") {
            $file_name = $dou->get_file_name($dou->get_one("SELECT show_img FROM " . $dou->table('show') . " WHERE id = '$id'"));
            $custom_filename = $file_name ? $file_name : $dou->create_rand_string('letter', 6, date('Ymd'));
            $show_img = $file->box('show', $id, 'show_img', 'main', $custom_filename);
            $show_img = ", show_img = '" . $show_img . "'";
        }
        
        // CSRF防御令牌验证
        $firewall->check_token($_POST['token']);
        
        $sql = "update " . $dou->table('show') . " SET show_name='$_POST[show_name]'" . $show_img . " ,show_link = '$_POST[show_link]', sort = '$_POST[sort]' WHERE id = '$id'";
        $dou->query($sql);
        
        $dou->create_admin_log($_LANG['miniprogram'] . ' - ' . $_LANG['show_edit'] . ': ' . $_POST[show_name]);
        $dou->dou_msg($_LANG['show_edit_succes'], 'miniprogram.php?rec=show');
    }    

    // 幻灯删除
    elseif ($act == 'del') {
        // 验证并获取合法的ID
        $id = $check->is_number($_REQUEST['id']) ? $_REQUEST['id'] : $dou->dou_msg($_LANG['illegal'], 'miniprogram.php?rec=show');
        
        $show_name = $dou->get_one("SELECT show_name FROM " . $dou->table('show') . " WHERE id = '$id'");
        
        if (isset($_POST['confirm'])) {
            // 删除相应商品图片
            $show_img = $dou->get_one("SELECT show_img FROM " . $dou->table('show') . " WHERE id = '$id'");
            $dou->del_file($show_img);
            
            $dou->create_admin_log($_LANG['miniprogram'] . ' - ' . $_LANG['show_del'] . ': ' . $show_name);
            $dou->delete('show', "id = '$id'", 'miniprogram.php?rec=show');
        } else {
            $_LANG['del_check'] = preg_replace('/d%/Ums', $show_name, $_LANG['del_check']);
            $dou->dou_msg($_LANG['del_check'], 'miniprogram.php?rec=show', '', '30', "miniprogram.php?rec=show&act=del&id=$id");
        }
    }
}

/**
 * +----------------------------------------------------------
 * 小程序参数设置
 * +----------------------------------------------------------
 */
elseif ($rec == 'system') {
    $smarty->assign('ur_here', $_LANG['miniprogram_system']);
 
    // act操作项的初始化
    $act = $check->is_rec($_REQUEST['act']) ? $_REQUEST['act'] : 'default';
    
    // 系统设置
    if ($act == 'default') {
        // 配置项如果不存在，则先写入
        $set_list = array('miniprogram_appid', 'miniprogram_appsecret', 'miniprogram_pay_mch_id', 'miniprogram_pay_key');
        foreach ($set_list as $name) {
            if (!$dou->value_exist('parameter', 'name', $name)) {
                $dou->query("INSERT INTO " . $dou->table('parameter') . " (id, name, lang, value, cue, `group`)" . " VALUES (NULL, '$name', '" . $_LANG['miniprogram_' . $name . '_lang'] . "', '" . $_LANG['miniprogram_' . $name . '_value'] . "', '" . $_LANG['miniprogram_' . $name . '_cue'] . "', 'miniprogram')");
            }
        }
        
        // 读取配置项
        $sql = "SELECT * FROM " . $dou->table('parameter') . $where . " WHERE `group` = 'miniprogram' ORDER BY `group` DESC, sort ASC, id ASC";
        $query = $dou->query($sql);
        while ($row = $dou->fetch_array($query)) {
            $parameter_list[] = array (
                    "id" => $row['id'],
                    "name" => $row['name'],
                    "lang" => $row['lang'],
                    "value" => $row['value'],
                    "cue" => $row['cue']
            );
        }
        
        // CSRF防御令牌生成
        $smarty->assign('token', $token = $firewall->get_token());
        
        $smarty->assign('parameter_list', $parameter_list);
        $smarty->display('miniprogram.htm');
    }    

    // 系统设置数据更新
    elseif ($act == 'update') {
        // CSRF防御令牌验证
        $firewall->check_token($_POST['token']);
     
        foreach ($_POST as $name => $value) {
            $dou->query("UPDATE " . $dou->table('parameter') . " SET value = '$value' WHERE name = '$name'");
        }
        
        $dou->create_admin_log($_LANG['miniprogram'] . ' - ' . $_LANG['miniprogram_system'] . ': ' . $_LANG['edit_succes']);
        $dou->dou_msg($_LANG['edit_succes'], 'miniprogram.php?rec=system');
    }
} 

/**
 * +----------------------------------------------------------
 * 更多小程序
 * +----------------------------------------------------------
 */
elseif ($rec == 'install') {
    $smarty->assign('ur_here', $_LANG['miniprogram_install']);
 
    $smarty->assign('get', urlencode(serialize($_GET)));
    $smarty->assign('localsite', $dou->dou_localsite('miniprogram'));
    
    $smarty->display('miniprogram.htm');
}  

/**
 * +----------------------------------------------------------
 * 小程序启用
 * +----------------------------------------------------------
 */
if ($rec == 'enable') {
    if ($check->is_extend_id($unique_id = $_REQUEST['unique_id'])) {
        $miniprogram_array = $dou->get_subdirs(MINIPROGRAM_CODE_PATH);
        if (in_array($unique_id, $miniprogram_array)) { // 判断小程序是否存在
            // 如果不是默认小程序，要将默认小程序中的模块文件拷贝过来
            if ($unique_id != 'default')
                $dou->copy_dir(MINIPROGRAM_CODE_PATH . 'default', MINIPROGRAM_CODE_PATH . $unique_id, false, true);
            
            $dou->query("UPDATE " . $dou->table('config') . " SET value = '$unique_id' WHERE name = 'miniprogram_code'");
        }
    }
    
    $dou->dou_header('miniprogram.php');
} 

/**
 * +----------------------------------------------------------
 * 删除小程序
 * +----------------------------------------------------------
 */
elseif ($rec == 'del') {
    // 载入扩展功能
    include_once (ROOT_PATH . ADMIN_PATH . '/include/cloud.class.php');
    $dou_cloud = new Cloud('cache');

    if ($check->is_extend_id($unique_id = $_REQUEST['unique_id'])) {
        $miniprogram_array = $dou->get_subdirs(MINIPROGRAM_CODE_PATH);
        if (in_array($unique_id, $miniprogram_array)) { // 判断删除操作的小程序是否真实存在
            $dou->del_dir(MINIPROGRAM_CODE_PATH . $unique_id);
            $dou_cloud->change_updatedate('miniprogram', $unique_id, true); // 删除更新时间记录
            $dou->create_admin_log($_LANG['miniprogram_del'] . ': ' . $unique_id);
        }
    }
    
    $dou->dou_header('miniprogram.php');
}

?>