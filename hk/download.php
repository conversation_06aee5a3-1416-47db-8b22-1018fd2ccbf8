<?php

define('IN_DOUCO', true);

require (dirname(__FILE__) . '/include/init.php');

// rec操作项的初始化
$rec = $check->is_rec($_REQUEST['rec']) ? $_REQUEST['rec'] : 'default';

// 图片上传
include_once (ROOT_PATH . 'include/file.class.php');
$file = new File('images/download/'); // 实例化类文件(文件上传路径，结尾加斜杠)

// 赋值给模板
$smarty->assign('rec', $rec);
$smarty->assign('cur', 'download');

/**
 * +----------------------------------------------------------
 * 下载列表
 * +----------------------------------------------------------
 */
if ($rec == 'default') {
    $smarty->assign('ur_here', $_LANG['download']);
    $smarty->assign('action_link', array (
            'text' => $_LANG['download_add'],
            'href' => 'download.php?rec=add' 
    ));
    
    // 获取参数
    $cat_id = $check->is_number($_REQUEST['cat_id']) ? $_REQUEST['cat_id'] : 0;
    $keyword = isset($_REQUEST['keyword']) ? trim($_REQUEST['keyword']) : '';
    
    // 筛选条件
    $where = ' WHERE cat_id IN (' . $cat_id . $dou->dou_child_id('download_category', $cat_id) . ')';
    if ($keyword) {
        $where = $where . " AND title LIKE '%$keyword%'";
        $get = '&keyword=' . $keyword;
    }
 
    // 排序
    $sort = $_OPEN['sort'] ? "sort ASC, " : '';
    
    // 未加入分页条件的SQL语句
    $sql = "SELECT id, title, cat_id, image, size, sort, add_time FROM " . $dou->table('download') . $where . " ORDER BY " . $sort . "id DESC";
    
    // 分页
    $page = $check->is_number($_REQUEST['page']) ? $_REQUEST['page'] : 1;
    $page_url = 'download.php' . ($cat_id ? '?cat_id=' . $cat_id : '');
    $limit = $dou->pager($sql, 15, $page, $page_url, $get);
    
    $sql = $sql . $limit; // 加入分页条件的SQL语句
    $query = $dou->query($sql);
    while ($row = $dou->fetch_array($query)) {
        $cat_name = $dou->get_one("SELECT cat_name FROM " . $dou->table('download_category') . " WHERE cat_id = '$row[cat_id]'");
        $add_time = date("Y-m-d", $row['add_time']);
        
        $download_list[] = array (
                "id" => $row['id'],
                "cat_id" => $row['cat_id'],
                "cat_name" => $cat_name,
                "title" => $row['title'],
                "image" => $dou->dou_file($row['image']),
                "size" => $row['size'],
                "sort" => $row['sort'],
                "add_time" => $add_time 
        );
    }
    
    // 赋值给模板
    $smarty->assign('cat_id', $cat_id);
    $smarty->assign('keyword', $keyword);
    $smarty->assign('download_category', $dou->get_category_nolevel('download_category'));
    $smarty->assign('download_list', $download_list);
    
    $smarty->display('download.htm');
} 

/**
 * +----------------------------------------------------------
 * 下载添加
 * +----------------------------------------------------------
 */
elseif ($rec == 'add') {
    $smarty->assign('ur_here', $_LANG['download_add']);
    $smarty->assign('action_link', array (
            'text' => $_LANG['download'],
            'href' => 'download.php' 
    ));
    
    // 格式化自定义参数，并存到数组$download，下载编辑页面中调用下载详情也是用数组$download，
    if ($_DEFINED['download']) {
        $defined = explode(',', $_DEFINED['download']);
        foreach ($defined as $row) {
            $defined_download .= $row . "：\n";
        }
        $download['defined'] = trim($defined_download);
        $download['defined_count'] = count(explode("\n", $download['defined'])) * 2;
    }
    
    // CSRF防御令牌生成
    $smarty->assign('token', $token = $firewall->get_token());
    
    // 赋值给模板
    $smarty->assign('form_action', 'insert');
    $smarty->assign('download_category', $dou->get_category_nolevel('download_category'));
    $smarty->assign('download', $download);
    $smarty->assign('btn_lang', $dou->btn_lang('download', '', 'title, content, keywords, description'));
    
    $smarty->display('download.htm');
} 

elseif ($rec == 'insert') {
    // 验证标题
    if (empty($_POST['title'])) $dou->dou_msg($_LANG['download_title'] . $_LANG['is_empty']);
    
    // 文件上传盒子
    $image = $file->box('download', $dou->auto_id('download'), 'image', 'main');
    
    // 数据格式化
    $add_time = time();
    $_POST['defined'] = str_replace("\r\n", ',', $_POST['defined']);
        
    // CSRF防御令牌验证
    $firewall->check_token($_POST['token']);
    
    $sql = "INSERT INTO " . $dou->table('download') . " (id, cat_id, title, defined, content, image, download_link, size ,keywords, description, sort, add_time)" . " VALUES (NULL, '$_POST[cat_id]', '$_POST[title]', '$_POST[defined]', '$_POST[content]', '$image', '$_POST[download_link]', '$_POST[size]', '$_POST[keywords]', '$_POST[description]', '$_POST[sort]', '$add_time')";
    $dou->query($sql);
    
    $dou->create_admin_log($_LANG['download_add'] . ': ' . $_POST['title']);
    $dou->dou_msg($_LANG['download_add_succes'], 'download.php');
} 

/**
 * +----------------------------------------------------------
 * 下载编辑
 * +----------------------------------------------------------
 */
elseif ($rec == 'edit') {
    $smarty->assign('ur_here', $_LANG['download_edit']);
    $smarty->assign('action_link', array (
            'text' => $_LANG['download'],
            'href' => 'download.php' 
    ));
    
    // 验证并获取合法的ID
    $id = $check->is_number($_REQUEST['id']) ? $_REQUEST['id'] : '';
    
    $download = $dou->get_row('download', '*', "id = '$id'");
    
    // 格式化数据
    $download['image'] = $dou->dou_file($download['image']);
 
    // 格式化自定义参数
    if ($_DEFINED['download'] || $download['defined']) {
        $defined = explode(',', $_DEFINED['download']);
        foreach ($defined as $row) {
            $defined_download .= $row . "：\n";
        }
        // 如果下载中已经写入自定义参数则调用已有的
        $download['defined'] = $download['defined'] ? str_replace(",", "\n", $download['defined']) : trim($defined_download);
        $download['defined_count'] = count(explode("\n", $download['defined'])) * 2;
    }
    
    // CSRF防御令牌生成
    $smarty->assign('token', $token = $firewall->get_token());
    
    // 赋值给模板
    $smarty->assign('form_action', 'update');
    $smarty->assign('download_category', $dou->get_category_nolevel('download_category'));
    $smarty->assign('item_id', $id);
    $smarty->assign('item_content', $download['content']);
    $smarty->assign('download', $download);
    $smarty->assign('btn_lang', $dou->btn_lang('download', $id, 'title, content, keywords, description'));
    
    $smarty->display('download.htm');
} 

elseif ($rec == 'update') {
    // 验证并获取合法的ID
    $id = $check->is_number($_POST['id']) ? $_POST['id'] : '';
    
    // 验证标题
    if (empty($_POST['title'])) $dou->dou_msg($_LANG['download_title'] . $_LANG['is_empty']);
        
    // 文件上传盒子
    $image = $file->box('download', $id, 'image', 'main');
    $image = $image ? ", image = '" . $image . "'" : '';
    
    // 格式化自定义参数
    $_POST['defined'] = str_replace("\r\n", ',', $_POST['defined']);
    
    // CSRF防御令牌验证
    $firewall->check_token($_POST['token']);
    
    $sql = "UPDATE " . $dou->table('download') . " SET cat_id = '$_POST[cat_id]', title = '$_POST[title]', defined = '$_POST[defined]' ,content = '$_POST[content]'" . $image . ", download_link = '$_POST[download_link]', size = '$_POST[size]', keywords = '$_POST[keywords]', description = '$_POST[description]', sort = '$_POST[sort]' WHERE id = '$id'";
    $dou->query($sql);
    
    $dou->create_admin_log($_LANG['download_edit'] . ': ' . $_POST['title']);
    $dou->dou_msg($_LANG['download_edit_succes'], 'download.php');
} 

/**
 * +----------------------------------------------------------
 * 下载删除
 * +----------------------------------------------------------
 */
elseif ($rec == 'del') {
    // 验证并获取合法的ID
    $id = $check->is_number($_REQUEST['id']) ? $_REQUEST['id'] : $dou->dou_msg($_LANG['illegal'], 'download.php');
    $title = $dou->get_one("SELECT title FROM " . $dou->table('download') . " WHERE id = '$id'");
    
    if (isset($_POST['confirm'])) {
        // 删除相应商品图片
        $image = $dou->get_one("SELECT image FROM " . $dou->table('download') . " WHERE id = '$id'");
        $dou->del_file($image);
     
        // 删除对应语言项
        if ($_OPEN['language'])
            $dou->del_lang_value('download', $id);
        
        $dou->create_admin_log($_LANG['download_del'] . ': ' . $title);
        $dou->delete('download', "id = $id", 'download.php');
    } else {
        $_LANG['del_check'] = preg_replace('/d%/Ums', $title, $_LANG['del_check']);
        $dou->dou_msg($_LANG['del_check'], 'download.php', '', '30', "download.php?rec=del&id=$id");
    }
} 

/**
 * +----------------------------------------------------------
 * 批量操作选择
 * +----------------------------------------------------------
 */
elseif ($rec == 'action') {
    if (is_array($_POST['checkbox'])) {
        if ($_POST['action'] == 'del_all') {
            // 批量下载删除
            $dou->del_all('download', $_POST['checkbox'], 'id', 'image');
        } elseif ($_POST['action'] == 'category_move') {
            // 批量移动分类
            $dou->category_move('download', $_POST['checkbox'], $_POST['new_cat_id']);
        } else {
            $dou->dou_msg($_LANG['select_empty']);
        }
    } else {
        $dou->dou_msg($_LANG['download_select_empty']);
    }
}

?>