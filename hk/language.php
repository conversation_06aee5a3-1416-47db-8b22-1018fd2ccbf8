<?php

define('IN_DOUCO', true);

require (dirname(__FILE__) . '/include/init.php');

// rec操作项的初始化
$rec = $check->is_rec($_REQUEST['rec']) ? $_REQUEST['rec'] : 'default';

// 图片上传
include_once (ROOT_PATH . 'include/file.class.php');
$logo = new File('theme/' . $_CFG['site_theme'] . '/images/'); // 实例化类文件(文件上传路径，结尾加斜杠)

// 赋值给模板
$smarty->assign('rec', $rec);
$smarty->assign('cur', 'language');

/**
 * +----------------------------------------------------------
 * 语言列表
 * +----------------------------------------------------------
 */
if ($rec == 'default') {
    $smarty->assign('ur_here', $_LANG['language']);
    $smarty->assign('action_link', array (
            'text' => $_LANG['language_add'],
            'href' => 'language.php?rec=add' 
    ));
    
    $sql = "SELECT * FROM " . $dou->table('language') . " ORDER BY sort ASC, language_id DESC";
    $query = $dou->query($sql);
    while ($row = $dou->fetch_array($query)) {
        $language_list[] = array (
                "language_id" => $row['language_id'],
                "name" => $row['name'],
                "language_pack" => $row['language_pack'],
                "site_logo" => $row['site_logo'] ? "theme/" . $_CFG['site_theme'] . "/images/" . $dou->logo($row['language_pack']) : '',
                "sort" => $row['sort']
        );
    }
 
    // 赋值给模板
    $smarty->assign('language_list', $language_list);
    
    $smarty->display('language.htm');
} 

/**
 * +----------------------------------------------------------
 * 语言添加
 * +----------------------------------------------------------
 */
elseif ($rec == 'add') {
    $smarty->assign('ur_here', $_LANG['language_add']);
    $smarty->assign('action_link', array (
            'text' => $_LANG['language'],
            'href' => 'language.php' 
    ));
    
    // CSRF防御令牌生成
    $smarty->assign('token', $token = $firewall->get_token());
    
    // 赋值给模板
    $smarty->assign('form_action', 'insert');
    $smarty->assign('language_pack', get_language_pack());
    $smarty->assign('item_id', $dou->auto_id('language'));
    $smarty->assign('language', $language);
    
    $smarty->display('language.htm');
} 

elseif ($rec == 'insert') {
    // 验证语言包
    if (!$check->is_language_pack($_POST['language_pack']))
        $dou->dou_msg($_LANG['illegal']);
    
    // 判断是否为空
    $wrong = $check->fn_empty('box', 'name, site_name, site_title');
 
    // 显示错误提示
    if ($wrong) {
        foreach ($wrong as $key => $value) {
            $dou->dou_msg($wrong[$key]);;
        }
    }
    
    // 文件上传盒子
    if ($_FILES['site_logo']['name'] != "") {
        $site_logo = $logo->upload('site_logo', 'logo_' . $_POST['language_pack']); // 上传的文件域
    }
    
    // CSRF防御令牌验证
    $firewall->check_token($_POST['token']);
    
    $sql = "INSERT INTO " . $dou->table('language') . " (language_id, name, language_pack, site_name, site_title, site_keywords, site_description, site_logo, address, tel, fax, email, sort)" . " VALUES (NULL, '$_POST[name]', '$_POST[language_pack]', '$_POST[site_name]', '$_POST[site_title]', '$_POST[site_keywords]', '$_POST[site_description]', '$site_logo', '$_POST[address]', '$_POST[tel]', '$_POST[fax]', '$_POST[email]', '$_POST[sort]')";
    $dou->query($sql);
    
    $dou->create_admin_log($_LANG['language_add'] . ': ' . $_POST['name']);
    $dou->dou_msg($_LANG['language_add'] . $_LANG['success'], 'language.php');
} 

/**
 * +----------------------------------------------------------
 * 语言编辑
 * +----------------------------------------------------------
 */
elseif ($rec == 'edit' || $rec == 'system') {
    $smarty->assign('ur_here', $_LANG['language_edit']);
    $smarty->assign('action_link', array (
            'text' => $_LANG['language'],
            'href' => 'language.php' 
    ));
    
    // 验证并获取合法的ID
    if ($rec == 'system') {
        $language_pack = $check->is_language_pack($_REQUEST['language_pack']) ? $_REQUEST['language_pack'] : '';
        $language_id = $dou->get_one("SELECT language_id FROM " . $dou->table('language') . " WHERE language_pack = '$language_pack'");
        
        // 显示系统设置菜单
        $tab_list = array('main', 'display', 'defined', 'mail');

        // 载入当前细分系统的配置信息
        if ($dou->get_one("SELECT value FROM " . $dou->table('config') . " WHERE tab = 'system_param' AND name = 'core'")) {
            $tab_list_new[] = 'system_param';
            array_splice($tab_list, 1, 0, $tab_list_new);
        }

        // 生成设置项
        foreach ($tab_list as $tab) {
            $cfg[] = array (
                    "name" => $tab,
                    "lang" => $_LANG['system_' . $tab],
                    "list" => $dou->get_cfg_list($tab)
            );
        }

        // 参数设置
        $parameter_list = $dou->fn_query("SELECT * FROM " . $dou->table('parameter') . " WHERE `group` = 'system' ORDER BY `group` DESC, sort ASC, id ASC");

        // 短信模块
        if (file_exists($sms_class_file = ROOT_PATH . 'include/sms.class.php'))
            $smarty->assign('sms_tab', true);

        // 赋值给模板
        $smarty->assign('cfg', $cfg);
        $smarty->assign('parameter_list', $parameter_list);
        $smarty->assign('lang_list', $dou->get_lang_list($language_pack));
    } else {
        $language_id = $check->is_number($_REQUEST['language_id']) ? $_REQUEST['language_id'] : '';
    }
    
    $language = $dou->get_row('language', '*', "language_id = '$language_id'");
    
    // 格式化数据
    $language['site_logo'] = "theme/" . $_CFG['site_theme'] . "/images/" . $dou->logo($language['language_pack']);
    
    // CSRF防御令牌生成
    $smarty->assign('token', $token = $firewall->get_token());
     
    // 赋值给模板
    $smarty->assign('form_action', 'update');
    $smarty->assign('language_pack', get_language_pack($language['language_pack']));
    $smarty->assign('item_id', $language_id);
    $smarty->assign('language', $language);
    
    $smarty->display('language.htm');
} 

elseif ($rec == 'update') {
    // 验证并获取合法的ID
    $language_id = $check->is_number($_POST['language_id']) ? $_POST['language_id'] : '';
    
    // 判断是否为空
    $wrong = $check->fn_empty('box', 'name, site_name, site_title');
 
    // 显示错误提示
    if ($wrong) {
        foreach ($wrong as $key => $value) {
            $dou->dou_msg($wrong[$key]);;
        }
    }
    
    // 文件上传盒子
    if ($_FILES['site_logo']['name'] != "") {
        $site_logo = $logo->upload('site_logo', 'logo_' . $_POST['language_pack']); // 上传的文件域
        $site_logo = ", site_logo = '" . $site_logo . "'";
    }
    
    // CSRF防御令牌验证
    $firewall->check_token($_POST['token']);
    
    $sql = "UPDATE " . $dou->table('language') . " SET name = '$_POST[name]', site_name = '$_POST[site_name]', site_title = '$_POST[site_title]', site_keywords = '$_POST[site_keywords]', site_description = '$_POST[site_description]'" . $site_logo . ", address = '$_POST[address]', tel = '$_POST[tel]', fax = '$_POST[fax]', email = '$_POST[email]', sort = '$_POST[sort]' WHERE language_id = '$language_id'";
    $dou->query($sql);
    
    $dou->create_admin_log($_LANG['language_edit'] . ': ' . $_POST['name']);
 
    // 返回地址
    if ($_POST['mode'] == 'system') {
        $back_url = 'language.php?rec=system&language_pack=' . $_POST['language_pack'];
    } else {
        $back_url = 'language.php';
    }
    
    $dou->dou_msg($_LANG['language_edit'] . $_LANG['succes'], $back_url);
}

/**
 * +----------------------------------------------------------
 * 语言删除
 * +----------------------------------------------------------
 */
elseif ($rec == 'del') {
    // 验证并获取合法的ID
    $language_id = $check->is_number($_REQUEST['language_id']) ? $_REQUEST['language_id'] : $dou->dou_msg($_LANG['illegal'], 'language.php');
    $name = $dou->get_one("SELECT name FROM " . $dou->table('language') . " WHERE language_id = '$language_id'");
    
    if (isset($_POST['confirm'])) {
        // 删除相应商品图片
        $site_logo = $dou->get_one("SELECT site_logo FROM " . $dou->table('language') . " WHERE language_id = '$language_id'");
        $dou->del_file($site_logo);
        
        $dou->create_admin_log($_LANG['language_del'] . ': ' . $name);
        $dou->delete('language', "language_id = '$language_id'", 'language.php');
    } else {
        $_LANG['del_check'] = preg_replace('/d%/Ums', $name, $_LANG['del_check']);
        $dou->dou_msg($_LANG['del_check'], 'language.php', '', '30', "language.php?rec=del&language_id=$language_id");
    }
}

/**
 * +----------------------------------------------------------
 * 获取语言包
 * +----------------------------------------------------------
 */
function get_language_pack($current = '') {
    // 已经被选中的语言
    $pack_selected = $GLOBALS['dou']->get_no_repeat_value('language', 'language_pack');
    $pack_selected = is_array($pack_selected) ? $pack_selected : array();
    
    $pack_list = $GLOBALS['dou']->get_subdirs(ROOT_PATH . 'languages');
    foreach ($pack_list as $pack) {
        $language_pack[] = array (
                "value" => $pack,
                "cur" => $pack == $current ? true : false,
                "disabled" => fn_is_array($pack, $pack_selected) ? true : false
        );
    }
 
    return $language_pack;
}

/**
 * +----------------------------------------------------------
 * 判断字符串是否在数组中，包括二维数组--这里只能判断二维数组
 * +----------------------------------------------------------
 */
function fn_is_array($str, $arr){
    foreach ($arr as $row){
        if (in_array($str, $row)){
            return true;
        }
    }
    
    return false;
}

?>