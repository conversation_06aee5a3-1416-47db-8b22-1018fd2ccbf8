<?php

define('IN_DOUCO', true);

require (dirname(__FILE__) . '/include/init.php');

// rec操作项的初始化
$rec = $check->is_rec($_REQUEST['rec']) ? $_REQUEST['rec'] : 'default';

// 赋值给模板
$smarty->assign('rec', $rec);
$smarty->assign('cur', 'download_category');

/**
 * +----------------------------------------------------------
 * 分类列表
 * +----------------------------------------------------------
 */
if ($rec == 'default') {
    $smarty->assign('ur_here', $_LANG['download_category']);
    $smarty->assign('action_link', array (
            'text' => $_LANG['download_category_add'],
            'href' => 'download_category.php?rec=add' 
    ));
    
    // 赋值给模板
    $smarty->assign('download_category', $dou->get_category_nolevel('download_category'));
    
    $smarty->display('download_category.htm');
} 

/**
 * +----------------------------------------------------------
 * 分类添加
 * +----------------------------------------------------------
 */
elseif ($rec == 'add') {
    $smarty->assign('ur_here', $_LANG['download_category_add']);
    $smarty->assign('action_link', array (
            'text' => $_LANG['download_category'],
            'href' => 'download_category.php' 
    ));
    
    // CSRF防御令牌生成
    $smarty->assign('token', $token = $firewall->get_token());
    
    // 赋值给模板
    $smarty->assign('form_action', 'insert');
    $smarty->assign('download_category', $dou->get_category_nolevel('download_category'));
    $smarty->assign('btn_lang', $dou->btn_lang('download_category', '', 'cat_name, keywords, description'));
    
    $smarty->display('download_category.htm');
} 

elseif ($rec == 'insert') {
    if (empty($_POST['cat_name']))
        $dou->dou_msg($_LANG['download_category_cat_name'] . $_LANG['is_empty']);
    if (!$check->is_unique_id($_POST['unique_id']))
        $dou->dou_msg($_LANG['unique_id_wrong']);
        
    // CSRF防御令牌验证
    $firewall->check_token($_POST['token']);
    
    $sql = "INSERT INTO " . $dou->table('download_category') . " (cat_id, unique_id, parent_id, cat_name, keywords, description, sort)" . " VALUES (NULL, '$_POST[unique_id]', '$_POST[parent_id]', '$_POST[cat_name]', '$_POST[keywords]', '$_POST[description]', '$_POST[sort]')";
    $dou->query($sql);
    
    $dou->create_admin_log($_LANG['download_category_add'] . ': ' . $_POST['cat_name']);
    $dou->dou_msg($_LANG['download_category_add_succes'], 'download_category.php');
} 

/**
 * +----------------------------------------------------------
 * 分类编辑
 * +----------------------------------------------------------
 */
elseif ($rec == 'edit') {
    $smarty->assign('ur_here', $_LANG['download_category_edit']);
    $smarty->assign('action_link', array (
            'text' => $_LANG['download_category'],
            'href' => 'download_category.php' 
    ));
    
    // 获取分类信息
    $cat_id = $check->is_number($_REQUEST['cat_id']) ? $_REQUEST['cat_id'] : '';
    $cat_info = $dou->get_row('download_category', '*', "cat_id = '$cat_id'");
    
    // CSRF防御令牌生成
    $smarty->assign('token', $token = $firewall->get_token());
    
    // 赋值给模板
    $smarty->assign('form_action', 'update');
    $smarty->assign('download_category', $dou->get_category_nolevel('download_category', '0', '0', $cat_id));
    $smarty->assign('cat_info', $cat_info);
    $smarty->assign('btn_lang', $dou->btn_lang('download_category', $cat_id, 'cat_name, keywords, description'));
    
    $smarty->display('download_category.htm');
} 

elseif ($rec == 'update') {
    // 验证并获取合法的ID
    $cat_id = $check->is_number($_POST['cat_id']) ? $_POST['cat_id'] : '';
    
    if (empty($_POST['cat_name']))
        $dou->dou_msg($_LANG['download_category_cat_name'] . $_LANG['is_empty']);
    if (!$check->is_unique_id($_POST['unique_id']))
        $dou->dou_msg($_LANG['unique_id_wrong']);
        
    // CSRF防御令牌验证
    $firewall->check_token($_POST['token']);
    
    $sql = "update " . $dou->table('download_category') . " SET cat_name = '$_POST[cat_name]', unique_id = '$_POST[unique_id]', parent_id = '$_POST[parent_id]', keywords = '$_POST[keywords]' ,description = '$_POST[description]', sort = '$_POST[sort]' WHERE cat_id = '$cat_id'";
    $dou->query($sql);
    
    $dou->create_admin_log($_LANG['download_category_edit'] . ': ' . $_POST['cat_name']);
    $dou->dou_msg($_LANG['download_category_edit_succes'], 'download_category.php');
} 

/**
 * +----------------------------------------------------------
 * 分类删除
 * +----------------------------------------------------------
 */
elseif ($rec == 'del') {
    $cat_id = $check->is_number($_REQUEST['cat_id']) ? $_REQUEST['cat_id'] : $dou->dou_msg($_LANG['illegal'], 'download_category.php');
    $cat_name = $dou->get_one("SELECT cat_name FROM " . $dou->table('download_category') . " WHERE cat_id = '$cat_id'");
    $is_parent = $dou->get_one("SELECT id FROM " . $dou->table('download') . " WHERE cat_id = '$cat_id'") . $dou->get_one("SELECT cat_id FROM " . $dou->table('download_category') . " WHERE parent_id = '$cat_id'");
    
    if ($is_parent) {
        $_LANG['download_category_del_is_parent'] = preg_replace('/d%/Ums', $cat_name, $_LANG['download_category_del_is_parent']);
        $dou->dou_msg($_LANG['download_category_del_is_parent'], 'download_category.php', '', '3');
    } else {
        if (isset($_POST['confirm'])) {
         
            // 删除对应语言项
            if ($_OPEN['language'])
                $dou->del_lang_value('download_category', $cat_id);
            
            $dou->create_admin_log($_LANG['download_category_del'] . ': ' . $cat_name);
            $dou->delete('download_category', "cat_id = $cat_id", 'download_category.php');
        } else {
            $_LANG['del_check'] = preg_replace('/d%/Ums', $cat_name, $_LANG['del_check']);
            $dou->dou_msg($_LANG['del_check'], 'download_category.php', '', '30', "download_category.php?rec=del&cat_id=$cat_id");
        }
    }
}
?>