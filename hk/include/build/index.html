<!DOCTYPE html>
<html>

<head>
    <title>Bui 框架 富文本编辑器 SSM框架开发</title>
    <meta charset="UTF-8">
    <meta name="keywords" content="bui 富文本编辑器、富文本上传视频、富文本插入表格、富文本插入视频、简约版富文本、富文本上传图片、富文本插入代码" />
    <meta name="description"
        content="Bui editor是一个现代版富文本编辑器，简易，功能全面，可上传视频，上传图片，标准化html输出，支持chrome、firefox、360、搜狗、ie9（及以上）主流浏览器" />
    <link rel="shortcut icon" href="favicon.ico" />

    <link type="text/css" rel="stylesheet" lang="stylesheet" class="k_bui_css" href="theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" class="k_bui_css" href="theme/blue/main.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="css/site.css" />
    <script src="lib/jquery.min.js"></script>

    <script type="text/javascript" src="javascript/config.js"></script>
    <script type="text/javascript" src="javascript/bui-0.0.1.js"></script>
</head>

<body>
    <div id="body_main" class="k_box_size">
        <div id="wrap_header" class="k_box_size">
            <div style="position:relative;">
                <h6 style="position: absolute;right: 12px;top:2px;font-size: 10px;color:rgb(212, 211, 211)">
                    许可证：桂ICP备18011596号</h6>
                <div id="logo">

                </div>
                <div id="nav_wrap" class="k_box_size">
                    <a class='first_menu' key='b'><i class="fa fa-bold"></i>ui</a>
                    <a class='first_menu' key='demo'><i class="fa fa-doc-text"></i>Demo</a>
                    <a class='first_menu' key='doc'><i class="fa fa-file-word"></i>文档API</a>
                    <a class='first_menu' key='src'><i class="fa  fa-code"></i>源码下载</a>
                    <a href="/editor/index.html"><i class="fa fa-doc-text"></i>富文本</a>
                    <a href="/flow/index.html" target="_blank"><i class="fa fa-sitemap"></i>流程设计器</a>
                    <a class='first_menu' key='ssm'><i class="fa fa-laptop"></i>Bui SSM快速开发框架</a>
                    <a class='first_menu' key='contact'><i class="fa fa-user-o"></i>学习交流</a>
                    <a href="theme/icon.html" target="_blank"
                        style='font-size: 14px; position: absolute; right: 110px; bottom: 0px; line-height: 20px; color: rgb(37, 104, 202); border: none;'>
                        <i style="font-size: 12px;padding-right: 3px;color:#2568CA;" class="fa fa-font-1"></i>ICON
                    </a>
                    <a href="https://www.cnblogs.com/kevinJhuang/" target="_blank"
                        style='font-size: 14px; position: absolute; right: 55px; bottom: 0px; line-height: 20px; border: none;'>
                        <i style="font-size: 12px;padding-right: 3px;color:#67CE03;" class="fa fa-male-1"></i>博客
                    </a>
                    <a onClick="showLicense()"
                        style='font-size: 14px; position: absolute; right: 0px; bottom: 0px; line-height: 20px; border: none;'>
                        <i style="font-size: 12px;padding-right: 3px;color:#00BCCB;" class="fa fa-edit-1"></i>MIT
                    </a>
                    <a
                        style="border: none;font-size:12px;right:2px;position:absolute;color: #dad4d4;height:20px;top:30px;line-height: 20px;">by
                        kevin.huang</a>

                </div>
            </div>
        </div>
        <div id="wrap_content" class="k_box_size">
            <div class="k_box_size">
                <div style="width:250px;height:100%;z-index:999;"  class="k_box_size">
                    <div id="left_menus">
                        <ul style="list-style:none;" id="menus_ul">

                        </ul>
                    </div>
                </div>
                <div id="page_wrap" class="k_box_size" style="overflow:auto;">
                    <div id="page_content">

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        function showLicense() {
            $B.window({
                width: 800,
                height: 600,
                title: '本软件采用MIT license', //标题
                url: 'license.txt'
            });
        }
        var $page, $srcollWrap, EDITORS = [];
        $(function () {
            var curUrl;
            var $wrap_content = $("#wrap_content");
            var $code = $("<div style='display:none;position:absolute;right:12px;top:15px;width:120px;height:20px;border:none;padding:0px;z-index:9999;cursor:pointer;text-decoration: underline;'>查看源码</div>").appendTo($wrap_content);
            $code.click(function () {
                var $c = $("<textarea style='width:100%;height:100%;' class='k_box_size'></textarea>");
                $B.window({
                    width: '90%',
                    height: '96%',
                    title: '源码',
                    content: $c
                });
                $c.val("正在加载......");
                $.get(curUrl, function (data) {
                    $c.val(data);
                    console.log(data);
                });
            });
            var mJson = {
                "b": [{
                    icon: 'fa-bold',
                    text: 'Bui简介',
                    url: 'doc/info.html'
                }, {
                    icon: 'fa-cog',
                    text: 'Bui安装使用说明',
                    url: 'doc/setup.html'
                }],
                "demo": [{
                    icon: 'fa-angle-right',
                    text: '双向绑定(mini-mvvm)',
                    url: 'demo/mvvm.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '时间日期（calendar）',
                    url: 'demo/calendar.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '缩放（Resize）',
                    url: 'demo/resize.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '滚动条（scrollbar）',
                    url: 'demo/scrollbar.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '拖动（Draggable）',
                    url: 'demo/draggabel.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '树（Tree）',
                    url: 'demo/tree.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '按钮/工具栏（Toolbar）',
                    url: 'demo/toolbar.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '表格（DataGrid）',
                    url: 'demo/datagrid.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '树形表格（DataGrid）',
                    url: 'demo/treegrid.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '手风琴（Accordon）',
                    url: 'demo/accordion.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '拾色器（ColorPicker）',
                    url: 'demo/colorpicker.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '右键菜单（Ctxmenu）',
                    url: 'demo/ctxmenu.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '导航菜单（nav）',
                    url: 'demo/nav.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '下拉复选(ComboBox)',
                    url: 'demo/combox.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '布局（Layout）',
                    url: 'demo/layout.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '分页工具栏（Pagination）',
                    url: 'demo/pagination.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '文本标签（LabelTab）',
                    url: 'demo/labeltab.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '标签页（Tab）',
                    url: 'demo/tab.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '面板（Panel）',
                    url: 'demo/panel.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '窗口（Window）',
                    url: 'demo/window.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '文件上传（Upload）',
                    url: 'demo/upload.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '双向绑定联动表单',
                    url: 'demo/mvcform.html'
                }, {
                    icon: 'fa-angle-right',
                    text: '表单验证',
                    url: 'demo/formvalid.html'
                }, {
                    icon: 'fa-angle-right',
                    text: 'CURD封装',
                    url: 'demo/curd.html'
                }
                ],
                "doc": [
                    {
                        icon: 'fa-file-word',
                        text: '静态API与工具',
                        url: 'doc/api.html'
                    },
                    {
                        icon: 'fa-file-code',
                        text: '时间日期（calendar）',
                        url: 'doc/calendar.html'
                    }
                    , {
                        icon: 'fa-file-word',
                        text: '双向绑定(mini-mvvm)',
                        url: 'doc/mvvm.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '缩放（Resize）',
                        url: 'doc/resize.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '滚动条（scrollbar）',
                        url: 'doc/scrollbar.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '拖动（Draggable）',
                        url: 'doc/draggabel.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '树（Tree）',
                        url: 'doc/tree.html'
                    },
                    {
                        icon: 'fa-file-word',
                        text: '按钮/工具栏（Toolbar）',
                        url: 'doc/toolbar.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '[树形]表格（DataGrid）',
                        url: 'doc/datagrid.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '手风琴（Accordon）',
                        url: 'doc/accordion.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '拾色器（ColorPicker）',
                        url: 'doc/colorpicker.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '右键菜单（Ctxmenu）',
                        url: 'doc/ctxmenu.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '导航菜单（nav）',
                        url: 'doc/nav.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '下拉复选(ComboBox)',
                        url: 'doc/combox.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '布局（Layout）',
                        url: 'doc/layout.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '分页工具栏（Pagination）',
                        url: 'doc/pagination.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '文本标签（LabelTab）',
                        url: 'doc/labeltab.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '标签页（Tab）',
                        url: 'doc/tab.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '面板（Panel）',
                        url: 'doc/panel.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '窗口（Window）',
                        url: 'doc/window.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '文件上传（Upload）',
                        url: 'doc/upload.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '双向绑定联动表单',
                        url: 'doc/mvcform.html'
                    }, {
                        icon: 'fa-file-word',
                        text: '表单验证',
                        url: 'doc/formvalid.html'
                    }, {
                        icon: 'fa-file-word',
                        text: 'CURD封装',
                        url: 'doc/curd.html'
                    }
                ],
                "src": [{
                    icon: 'fa-code',
                    text: '开源项目地址',
                    url: 'doc/git.html'
                }],
                "contact": [{
                    icon: 'fa-qq',
                    text: '交流学习',
                    url: 'doc/contact.html'
                }],
                "ssm": [
                    {
                        icon: 'fa-file-word',
                        text: '介绍说明',
                        url: 'ssm/info.html'
                    },
                    {
                        icon: 'fa-file-code',
                        text: 'Demo与源码',
                        url: 'ssm/demo.html'
                    },
                    {
                        icon: 'fa-codeopen',
                        text: '开发环境说明',
                        url: 'ssm/env.html'
                    }, {
                        icon: 'fa-cubes',
                        text: '页面CURD开发说明',
                        url: 'ssm/curd.html'
                    }, {
                        icon: 'fa-database',
                        text: '服务端CURD开发说明',
                        url: 'ssm/scurd.html'
                    }, {
                        icon: 'fa-sitemap',
                        text: '工程目录及配置说明',
                        url: 'ssm/project.html'
                    }, {
                        icon: 'fa-question-circle-o',
                        text: '数据验证开发说明',
                        url: 'ssm/valid.html'
                    }, {
                        icon: 'fa-cog-alt',
                        text: '动态数据源开发说明',
                        url: 'ssm/dydb.html'
                    }, {
                        icon: 'fa-doc',
                        text: 'xls导入导出开发说明',
                        url: 'ssm/xls.html'
                    }, {
                        icon: 'fa-user-circle',
                        text: '权限设计及开发说明',
                        url: 'ssm/role.html'
                    }, {
                        icon: 'fa-registered',
                        text: '国际化语言开发说明',
                        url: 'ssm/lang.html'
                    }, {
                        icon: 'fa-user-o',
                        text: '用户登录开发说明',
                        url: 'ssm/login.html'
                    }, {
                        icon: 'fa-male-1',
                        text: '开发接口调试',
                        url: 'ssm/apitest.html'
                    }
                ]
            };
            var ul = $("#menus_ul");
            var curentActived;
            $page = $("#page_content");
            $srcollWrap = $page.parent();
            var scrollIns;
            var navs = $("#nav_wrap").children(".first_menu").click(function () {
                if(scrollIns){
                    scrollIns.resetSliderPosByTimer();//reset
                }                
                var $t = $(this).css("border-bottom", "2px solid #3387F5");
                $t.addClass("first_menu_clicked").children("i").addClass("first_menu_clicked");
                $t.siblings().removeClass("first_menu_clicked").css("border", "none").children("i").removeClass("first_menu_clicked");

                var key = $t.attr("key");
                ul.children().remove();
                var ms = mJson[key];
                for (var i = 0, len = ms.length; i < len; ++i) {
                    var m = ms[i];
                    var $a = $('<li><a class="k_box_size"><i class="fa ' + m.icon + '"></i>' + m.text + '</a></li>').appendTo(ul).children("a").click(function () {
                        if (curentActived) {
                            curentActived.removeClass("first_menu_clicked").children("i").removeClass("first_menu_clicked");
                        }
                        var a = $(this);
                        var url = a.data("url");
                        a.addClass("first_menu_clicked").children("i").addClass("first_menu_clicked");
                        curentActived = a;
                        if (url !== "") {
                            $page.children().remove();
                            if (key === "demo") {
                                var $ifr = $('<iframe id="dome_content_ifr" frameborder="0" height="100%" width="100%" class="k_box_size" style="overflow:visible;position:absolute;z-index:1;padding-left:22px;top:0;border-top:25px solid #fff;"></iframe>');
                                $page.css("padding-top", 0);
                                $page.append($ifr);
                                $ifr[0].src = url;
                                $code.show();
                                curUrl = url;
                            } else {
                                $page.css("padding-top", "25px");
                                $B.htmlLoad({
                                    target: $page,
                                    url: url
                                });
                                $code.hide();
                            }
                        } else {
                            $page.html("<p>无效url！</p>");
                            $code.hide();
                        }
                    }).data("url", m.url);
                    if (i === 0) {
                        $a.trigger("click");
                    }
                }
            });
            var uPrs = $B.getUrlParams();

            console.log(uPrs);
            if(uPrs.m){
                navs.eq(4).trigger("click");
            }else{
                navs.first().trigger("click");
            }           

            var style = {
                size: '8px',
                display:'auto',
                hightColor:"#1590FA",
                slider: {
                    "background-color": '#6CB7FA',
                    "border-radius": "8px",
                },bar:{
                    "background-color": "#E8E8E8",
                    "border-radius": "8px",
                    "opacity": 0.6
                }   
            };
             scrollIns = $("#left_menus").myscrollbar(style).getMyScrollIns();
            
            var cc = '<img  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAB5UlEQVQ4T63UP2gTcRwF8PcuqW2aOwu6iFJtGwQHFwdBFxUUJW2T2MFBp6KIk7gI6uTgUDq2uIgoLipONX9qURQ6iZNO4iBJaItFHBSaS9La5PekJ6k1vZwpePP7fu7d788R/+lhkCOcCM9F7MNOtf5xJ2aWgrKBUDGSOKIwX0C63e9m7xJQK6wltIhEd9W27pO4AGDOUi3e505/2jKU704OweIUiY61YUkTA+7SdWK25of5NlrAmR2rdtdTEKf/DOkHaxrsr2bftQ3lneRFgg8EvIXwGsReAqMSnkRcc3k3spVmbFOjL5FU70pIaZCHZDAeK6dvFu3EcdGalbAKo5FYJTMdCAlgwUndIDDmrUsT5A0Lrzrc5fO9ePl9I/ZXo3l76GCN4RyAfS0hz9KlWCnz0Bf6jHinZW+bIHGlEfBt9HsLP3TWmdpTTS80suuN8tHhk7CsKYLOPyGvFW4NlNLjjUPqQXmc6oEdfUTibNtXT1q0jEn0VXLv12Y8qGAnzwl83Dh865gw2UVzZ7luHUUImeaXSLhn3J/X9mNmhcXo4C4x/AzksU1Bv13bEBJUgjEjsXLuDQt24ipoTfp+kvQV5Dyk7SAP+GUkPIdbHmXBSWYBDre9Nk1BCd9Cph4P/I1sBf8FA4/Rq8SQbDUAAAAASUVORK5CYII=" style="width:18px;height:18px;margin-left:1px ;margin-right:1px;">	<span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background: none; font-weight: bold;" >特别声明！</span><br/><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(189, 87, 4); background: none; font-weight: bold;" >本站所有 文档类页面 均 采用 Bui-Editor编制</span>';

            $B.message({
                title: '提示！',
                position: 'top', //
                message: cc,
                width: 500, //宽度
                height: 150, //高度         
                timeout: 2,
                mask: false
            });
        });
    </script>
</body>

</html>