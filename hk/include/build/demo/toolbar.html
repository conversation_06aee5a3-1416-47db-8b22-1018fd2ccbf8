<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>


    <style>
        body {
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
        }

        .section-body h2 {
            margin-top: 12px;
        }
    </style>
</head>
<body>
    <div class="section-content">
        <h1 class="section-title">按钮规格</h1>
        <div class="section-body">
            <h2>正常规格</h2>
            <div id="toolbar"></div>
            <h2>小规格</h2>
            <div id="toolbarsmall"></div>
            <h2>大规格</h2>
            <div id="toolbarmax"></div>
            <h2>简约图标文件风格</h2>
            <div id="toolbarss"></div>
            <h2>简约图标规格</h2>
            <div id="toolbaricon"></div>
        </div>
    </div>
    <div class="section-content">
        <h1 class="section-title">下拉工具栏</h1>
        <div style="padding:12px;" class="clearfix" class="section-body">
            <div style="float: left;" id="dropTools"></div>

            <div style="float: left;margin-left: 50px" id="dropTools1"></div>
        </div>
    </div>
    <div class="section-content">
        <h1 class="section-title">分组工具栏</h1>
        <div class="section-body">
            <div id="grouptools"></div>
        </div>
    </div>
    <div class="section-content">
        <h1 class="section-title">工具栏控制</h1>
        <div class="section-body">
            <div style="margin-top:12px;margin-bottom: 12px;" id="ctrtools0"></div>
            <div style="margin-top:12px;margin-bottom: 12px;" id="ctrtools"></div>
        </div>
    </div>
    <script type="text/javascript">
        function myCallback(res){
            alert(JSON.stringify(res));
        }


        /**事件集合**/
        var toolMethods = {
            test: function (prs) {
                alert(JSON.stringify(prs));
            },
            test1: function (prs) {
                alert(JSON.stringify(prs));
            },
            btnClick: function (prs) {
                alert(JSON.stringify(prs));
            }
        };
        var toolMethods2 = {
            btnClick: function (prs) {
                alert("toolMethods2 " + JSON.stringify(prs));
            }
        };

        function pageLoaded() {

            var ctrTools = new $B.Toolbar($("#ctrtools"), {
                align: 'center', //对齐方式，默认是left 、center、right
                style: 'normal', // plain / min  / normal /  big                
                showText: true, // min 类型可以设置是否显示文字
                methodsObject: 'toolMethods',
                buttons: [{
                    id: 'btn1',
                    text: '按钮1',
                    click: 'btnClick'
                }, {
                    id: 'btn2',
                    text: '按钮2',
                    methodsObject: 'toolMethods2',
                    click: 'btnClick'
                }, {
                    id: 'btn3',
                    text: '按钮3',
                    click: function(prs){
                        alert("按钮3");
                    }
                }]
            });


            var groupBTs1 = [
                [{
                    "text": "新增按钮",
                    "color": '#D8006C',
                    "iconCls": "fa-ok-squared",
                    "click": function (prs) {
                        ctrTools.addButtons([{
                            id: 'btn11',
                            text: 'new'
                        }, {
                            id: 'new1',
                            text: 'new2'
                        }]);
                    }
                }],
                [{
                    "text": "删除按钮",
                    "color": '#D8006C',
                    "iconCls": "fa-cancel-2",
                    "click": function (prs) {
                        ctrTools.delButtons(["btn11", "new1"]);
                    }
                }],
                [{
                    "text": "禁用按钮",
                    "color": '#D8006C',
                    "iconCls": "fa-file-word",
                    "click": function (prs) {
                        ctrTools.disableButtons(["btn1", "btn2"]);
                    }
                }],
                [{
                    "text": "启用按钮",
                    "color": '#D8006C',
                    "iconCls": "fa-plus-circled",
                    "click": function (prs) {
                        ctrTools.enableButtons(["btn1", "btn2"]);
                    }
                }]
            ];

            new $B.Toolbar($("#ctrtools0"), {
                align: 'center', //对齐方式，默认是left 、center、right
                style: 'normal', // plain / min  / normal /  big                
                showText: true, // min 类型可以设置是否显示文字
                buttons: groupBTs1
            });

            var childrenBtns = [{
                    "id": "line_btn_0",
                    "text": "删除",
                    "iconCls": "fa-trash",
                    "params": {
                        "cmd": "delete",
                        "privilage": "1"
                    },
                    "visualable": true,
                    "disabled": false,
                    "click": function (prs) {
                        alert(JSON.stringify(prs));
                    }
                },
                {
                    "id": "line_btn_1",
                    "text": "更新",
                    "color": "",
                    "methodsObject": "toolMethods",
                    "params": {
                        "cmd": "update",
                        "privilage": "1"
                    },
                    "visualable": true,
                    "disabled": false,
                    "iconCls": "fa-doc-text"
                },
                {
                    "id": "line_btn_2",
                    "text": "管理子菜单",
                    "methodsObject": "toolMethods",
                    "params": {
                        "cmd": "update",
                        "privilage": "1"
                    },
                    "visualable": true,
                    "disabled": false,
                    "iconCls": "fa-share-2"
                }
            ];

            groupBTs = [
                [{
                    "text": "操作1",
                    "color": '#55B21C',
                    "iconCls": "fa-ok-squared",
                    "click": function (prs) {
                        alert("click1");
                    }
                }, {
                    "text": "操作2",
                    "color": '#55B21C',
                    "iconCls": "fa-file-word",
                    "click": function (prs) {
                        alert("click2");
                    }
                }],
                [{
                    "text": "新增",
                    "color": '#55B21C',
                    "iconCls": "fa-plus-circled",
                    "click": function (prs) {
                        alert("click1");
                    }
                }, {
                    "text": "删除",
                    "color": '#55B21C',
                    "iconCls": "fa-cancel-2",
                    "click": function (prs) {
                        alert("click2");
                    }
                }],
                [{
                    "text": "配置1",
                    "color": '#55B21C',
                    "iconCls": "fa-file-word",
                    "click": function (prs) {
                        alert("配置1");
                    }
                }, {
                    "text": "配置2",
                    "color": '#55B21C',
                    "iconCls": "fa-cancel-2",
                    "click": function (prs) {
                        alert("配置2");
                    }
                }],
                [{
                    "text": "下拉按钮",
                    "color": '#55B21C',
                    "iconCls": "fa-down",
                    "childrens": childrenBtns
                }, {
                    "text": "下拉按钮",
                    "color": '#55B21C',
                    "iconCls": "fa-up-open-2",
                    "childrens": childrenBtns
                }]

            ];

            new $B.Toolbar($("#grouptools"), {
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'normal', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                buttons: groupBTs,
                onOperated: function (prs) {
                    alert(JSON.stringify(prs));
                }
            });

            var dropList = [{
                "id": "line_btn_3",
                "text": "操作",
                "methodsObject": "toolMethods",
                "params": {
                    "cmd": "lineMrg"
                },
                "disabled": false,
                "color": '#B26A1C',
                "iconCls": "fa-down-dir",

                "visualable": true,
                "childrens": childrenBtns
            }];


            new $B.Toolbar($("#dropTools"), {
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'normal', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                params: {
                    "abc": 999
                },
                buttons: dropList
            });
            new $B.Toolbar($("#dropTools1"), {
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'plain', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                buttons: dropList
            });


            new $B.Toolbar($("#toolbaricon"), {
                params: {
                    "abc": 99
                }, //用于集成到tree datagrid时 行按钮的数据参数
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'normal', // plain / min  / normal /  big
                showText: false, // min 类型可以设置是否显示文字
                buttons: [{
                        id: '00',
                        iconCls: 'fa-floppy',
                        color: '#ffffff',
                        iconColor: '#0F0358',
                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '新增', //文本

                    },
                    {
                        id: '1', //按钮id
                        color: '#ffffff',
                        iconCls: 'fa-scissors',
                        iconColor: '#0F0358',
                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '删除', //文本
                        click: function (pr) { //点击事件，如果存在click
                            alert(JSON.stringify(pr));
                        }
                    }, {
                        id: '2', //按钮id
                        color: '#ffffff',
                        iconCls: 'fa-edit-1',
                        iconColor: '#0F0358',
                        params: {
                            pr1: 8888
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '修改', //文本
                        click: 'test', //window.methodsObject对象中 test
                        methodsObject: 'toolMethods'
                    }
                ]
            });


            new $B.Toolbar($("#toolbarss"), {
                params: null, //用于集成到tree datagrid时 行按钮的数据参数
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'plain', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                buttons: [{
                        id: '00',
                        iconCls: 'fa-floppy',
                        color: '#2C8B67',
                        iconColor: '#39FF00',

                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '新增', //文本
                    },
                    {
                        id: '1', //按钮id
                        iconCls: 'fa-scissors',
                        color: '#FF52C4',
                        iconColor: '#902400',

                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '删除', //文本
                        click: function (pr) { //点击事件，如果存在click
                            alert(JSON.stringify(pr));
                        }
                    }, {
                        id: '2', //按钮id
                        iconCls: 'fa-edit-1',
                        color: "#938E1C",

                        params: {
                            pr1: 8888
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '修改', //文本
                        click: 'test', //window.methodsObject对象中 test
                        methodsObject: 'toolMethods'
                    }
                ]
            });

            new $B.Toolbar($("#toolbarmax"), {
                params: null, //用于集成到tree datagrid时 行按钮的数据参数
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'big', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                buttons: [{
                        id: '00',
                        iconCls: 'fa-floppy',
                        color: '#2C8B67',
                        iconColor: '#39FF00',
                        fontColor: '#ffffff',
                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '新增', //文本
                    },
                    {
                        id: '1', //按钮id
                        iconCls: 'fa-scissors',
                        color: '#FF52C4',
                        iconColor: '#902400',
                        fontColor: '#ffffff',
                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '删除', //文本
                        click: function (pr) { //点击事件，如果存在click
                            alert(JSON.stringify(pr));
                        }
                    }, {
                        id: '2', //按钮id
                        iconCls: 'fa-edit-1',
                        color: "#938E1C",

                        params: {
                            pr1: 8888
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '修改', //文本
                        click: 'test', //window.methodsObject对象中 test
                        methodsObject: 'toolMethods'
                    }
                ]
            });

            new $B.Toolbar($("#toolbarsmall"), {
                params: null, //用于集成到tree datagrid时 行按钮的数据参数
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'min', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                buttons: [{
                        id: '00',
                        iconCls: 'fa-floppy',
                        color: '#2C8B67',
                        iconColor: '#39FF00',
                        fontColor: '#ffffff',

                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '新增', //文本
                    },
                    {
                        id: '1', //按钮id
                        iconCls: 'fa-scissors',
                        color: '#FF52C4',
                        iconColor: '#902400',
                        fontColor: '#ffffff',

                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '删除', //文本
                        click: function (pr) { //点击事件，如果存在click
                            alert(JSON.stringify(pr));
                        }
                    }, {
                        id: '2', //按钮id
                        iconCls: 'fa-edit-1',
                        color: "#938E1C",

                        params: {
                            pr1: 8888
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '修改', //文本
                        click: 'test', //window.methodsObject对象中 test
                        methodsObject: 'toolMethods'
                    }
                ]
            });

            $toolbar = new $B.Toolbar($("#toolbar"), {
                params: null, //用于集成到tree datagrid时 行按钮的数据参数
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'normal', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                buttons: [{
                        id: '00',
                        iconCls: 'fa-doc-text',
                        color: '#0F17E8',
                        iconColor: '#39FF00',
                        fontColor: '#ffffff',
                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '新增JSON', //文本
                        click:function(){
                            $.ajax("http://127.0.0.1:8080/indigo-speerit-designer-web/speerit/form/addJsonp?extUserId=111&callback=myCallback", {  
                                data:{
                                        categoryId:-1,
                                        stylesheetId:'ffff121121233343434341111', //没有意义 可以固定一个值，或者将数据库改为可控
                                        deleteFlag:"0",
                                        description:"mytempl1100001111118888",
                                        name:"mytempl1100001111118888",
                                        projectId:"2cc7b2f05e268388015e2cb63f4b0000",
                                        status:"0",
                                        worksId:"2cc7b2ea5f951823015f951ca7f30000"
                               },                             
                                dataType: "jsonp"//指定服务器返回的数据类型
                            });
                        }
                    },
                    {
                        id: '1', //按钮id
                        iconCls: 'fa-cancel-2',
                        color: '#0F17E8',
                        iconColor: '#FF0005',
                        fontColor: '#ffffff',

                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '删除', //文本
                        click: function (pr) { //点击事件，如果存在click
                            alert(JSON.stringify(pr));
                        }
                    }, {
                        id: '2', //按钮id
                        iconCls: 'fa-eraser',

                        params: {
                            pr1: 8888
                        }, //额外的参数
                        disabled: true, //是否禁用
                        text: '修改', //文本
                        click: 'test', //window.methodsObject对象中 test
                        methodsObject: 'toolMethods'
                    }
                ]
            });

            new $B.Toolbar($("#toolbar1"), {
                params: null, //用于集成到tree datagrid时 行按钮的数据参数
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'normal', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                buttons: [{
                    id: '1', //按钮id
                    iconCls: 'icon-ok-sign',
                    params: {
                        pr1: 123
                    }, //额外的参数
                    disabled: false, //是否禁用
                    text: '测试', //文本
                    click: function (pr) { //点击事件，如果存在click
                        alert(JSON.stringify(pr));
                    }
                }, {
                    id: '2', //按钮id
                    iconCls: 'icon-trash',
                    color: '#0F17E8',
                    params: {
                        pr1: 8888
                    }, //额外的参数
                    disabled: true, //是否禁用
                    text: '按钮2', //文本
                    click: 'test', //window.methodsObject对象中 test
                    methodsObject: 'toolMethods'
                }]
            });


            new $B.Toolbar($("#toolbar2"), {
                params: null, //用于集成到tree datagrid时 行按钮的数据参数
                align: 'center', //对齐方式，默认是left 、center、right
                style: 'big', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                buttons: [{
                    id: '1', //按钮id
                    iconCls: 'icon-signal',
                    color: '#E92218',

                    params: {
                        pr1: 123
                    }, //额外的参数
                    disabled: false, //是否禁用
                    text: '测试', //文本
                    click: function (pr) { //点击事件，如果存在click
                        alert(JSON.stringify(pr));
                    }
                }, {
                    id: '2', //按钮id
                    iconCls: 'icon-print',

                    params: {
                        pr1: 8888
                    }, //额外的参数
                    disabled: false, //是否禁用
                    text: '按钮2', //文本
                    color: '#5BB55B',
                    click: 'test', //window.methodsObject对象中 test
                    methodsObject: 'toolMethods'
                }]
            });
            new $B.Toolbar($("#toolbar3"), {
                params: null, //用于集成到tree datagrid时 行按钮的数据参数
                align: 'left', //对齐方式，默认是left 、center、right
                style: 'plain', // plain / min  / normal /  big
                showText: true, // min 类型可以设置是否显示文字
                buttons: [{
                    id: '1', //按钮id
                    iconCls: 'icon-bar-chart',

                    params: {
                        pr1: 123
                    }, //额外的参数
                    disabled: false, //是否禁用
                    text: '测试', //文本
                    click: function (pr) { //点击事件，如果存在click
                        alert(JSON.stringify(pr));
                    }
                }, {
                    id: '2', //按钮id
                    iconCls: 'icon-trash',

                    params: {
                        pr1: 8888
                    }, //额外的参数
                    disabled: false, //是否禁用
                    text: '按钮2', //文本
                    click: 'test', //window.methodsObject对象中 test
                    methodsObject: 'toolMethods'
                }]
            });
        }

        function add() {
            $toolbar.addButtons([{
                    id: '9999', //按钮id
                    iconCls: 'icon-bar-chart',

                    params: {
                        pr1: 12345678
                    }, //额外的参数
                    disabled: true, //是否禁用
                    text: '按钮3', //文本
                    click: function (pr) { //点击事件，如果存在click
                        alert(JSON.stringify(pr));
                    }
                },
                {
                    id: '1111', //按钮id
                    iconCls: 'icon-reorder',

                    params: {
                        pr1: 123,
                        p1: 456
                    }, //额外的参数
                    disabled: false, //是否禁用
                    text: '按钮4', //文本
                    click: 'test4', //window.methodsObject对象中 test
                    methodsObject: 'toolMethods'
                }
            ]);
        }

        function del() {
            $toolbar.delButtons(['1111']);
        }

        function enable() {
            $toolbar.enableButtons(['9999']);
        }

        function disable() {
            $toolbar.disableButtons(['9999']);
        }
        var methodsObject = {
            test: function (pr) {
                alert(JSON.stringify(pr));
            }
        };

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'plugin': '../javascript/plugin',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'accordion': '../javascript/accordion',
                'colorpicker': '../javascript/colorpicker',
                'combox': '../javascript/combox',
                'ctxmenu': '../javascript/ctxmenu',
                'datagrid': '../javascript/datagrid',
                'kedit': '../javascript/kedit',
                'labeltab': '../javascript/labeltab',
                'layout': '../javascript/layout',
                'pagination': '../javascript/pagination',
                'panel': '../javascript/panel',
                'resize': '../javascript/resize',
                'tab': '../javascript/tab',
                'toolbar': '../javascript/toolbar',
                'tree': '../javascript/tree',
                'upload': '../javascript/upload',
                'validate': '../javascript/validate'
            }
        });

        var loadModel = ['toolbar'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</div>

</html> 