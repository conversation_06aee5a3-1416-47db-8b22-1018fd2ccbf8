<!DOCTYPE html>
<html>
    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、日期时间控件、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body>
    <!-- <div style="margin-bottom:5px;">选择模式： <select id="models"><option>单选</option> <option>多选</option></select></div> -->
    <div class="section-content" style="height:300px;">        
        <table>
            <tr>
                <td style="width:120px;text-align: right;">月模式<span style="font-size: 12px;">(固定显示)</span>：</td><td><input  type="text" id="month" value="2019-03"/></td>
                <td style="width:90px;text-align: right ;">月-日模式：</td><td><input type="text" id="month_day"  value="2019-03-12"/></td>   
                <td style="width:130px;text-align: right;">月-日-时-分模式：</td><td> <input type="text" id="month_day_time" value="2019-04-05 16:12"/></td>   
                <td style="width:140px;text-align: right;">月-日-时-分秒模式：</td><td> <input type="text" id="_time_sc" value=""/></td>                     
            </tr>
        </table>
    </div>
    <div>
        <table>
            <tr>
                <td colspan="4" style="padding: 10px 2px;">时间范围限制(单列模式，一个Calendar实例控制N个输入框，节省页面资源)</td>
            </tr>
            <tr>
                <td style="width:150px;text-align: right;">开始时间：</td><td> <input  type="text" id="month_day_time_s" value="2019-02-02 09:10:22"/></td>
                <td style="width:150px;text-align: right;">结束时间：</td><td> <input  type="text" id="end_time" value="2019-05-01 10:09:50"/></td>
            </tr>
        </table>
    </div>

    <div style="margin-top: 50px;">
        <table>
            <tr>
                <td colspan="4" style="padding: 10px 2px;">仅时间格式模式</td>
            </tr>
            <tr>
                <td style="width:150px;text-align: right;">HH:mm:ss模式：</td><td> <input style="width: 100px;" type="text" id="time_1" value="09:11:22"/></td>
                <td style="width:150px;text-align: right;">HH:mm模式：</td><td> <input  style="width: 100px;" type="text" id="time_2" value="10:09"/></td>
            </tr>
        </table>
    </div>

    <script type="text/javascript">
        var accordion;

        function pageLoaded() {


            setTimeout(function(){
                new $B.Calendar($("#time_1"),{
                    fmt:'hh:mm:ss',
                    readonly:false,
                    fitWidth:false
                });
                new $B.Calendar($("#time_2"),{
                    fmt:'hh:mm',
                    readonly:false,
                    fitWidth:false
                });
            },100);

            console.log("Calendar >>>>>>>>>>>>>>>>>>");

            var c1 = new $B.Calendar($("#month"),{
                fmt:'yyyy-MM',
                readonly:false,
                fitWidth:true,
                shadow:false,
                fixed:true 
            });

            var c2 = new $B.Calendar($("#month_day"),{
                fmt:'yyyy-MM-dd',
                show:true,
                readonly:false,
                onChange:function(date){
                    var day = date.getDate();
                    if(day < 10){
                        return "自定义限制:<span style='color:red'>只能选择10号及以上的日期！</span>";
                    }
                }
            });     

            var c3 = new $B.Calendar($("#month_day_time"),{
                fmt:'yyyy-MM-dd hh:mm',
                readonly:false,
                show:true
            });


            setTimeout(function(){
                c3.setValue( new Date(1999,8,9,9,9,9) );
            },5000);

            var now = new Date();
            var c3 = new $B.Calendar($("#_time_sc"),{
                fmt:'yyyy-MM-dd hh:mm:ss',
                readonly:false,
                show:true,
                initValue: now
            });
            

            /***时间对比,单列模式 节约页面资源***/
            var c4 =  new $B.Calendar($("#month_day_time_s"),{
                isSingel: true,
                fmt:'yyyy-MM-dd hh:mm:ss',
                fitWidth:true,
                clickHide:true,
                initValue: now.format("yyyy-MM-dd hh:mm:ss"),                
                range:{ max:'#end_time'  }, //{ max:'now'  }
                onChange:function(date,strDate){
                    console.log("开始时间 "+ strDate);
                }
            }); 

            now.setDate(now.getDate() + 40);//加一天
            now.setHours(now.getHours() + 2);//加一天
            var maxDate = new Date();
            maxDate.setDate(maxDate.getDate() + 120);//加30天

            var c5 =  new $B.Calendar($("#end_time"),{
                isSingel: true,
                fitWidth: true,
                clickHide:true,
                initValue: now.format("yyyy-MM-dd hh:mm:ss"),
                fmt:'yyyy-MM-dd hh:mm:ss',
                range:{ min:'#month_day_time_s',max: maxDate.format("yyyy-MM-dd hh:mm:ss")  },
                onChange:function(date,strDate){
                    console.log("结束时间 "+ strDate);
                }
            });
            $("#models").change(function(){
                var v = $(this).val();
                console.log(v);
            });
            
        }
           
        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'calendar':'../javascript/calendar',
            }
        });

        var loadModel = ['utils','calendar'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</body>

</html>