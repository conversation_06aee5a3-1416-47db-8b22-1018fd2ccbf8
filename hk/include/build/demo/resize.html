﻿<!DOCTYPE html>
<html>
    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />  
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body style="position:relative">
    <div id="box" style="position:absolute;top:83px;left:570px; width:200px;height:200px;background-color:#A58BF7;">
        <h1>等比例大小</h1>
    </div>

    <div id="box2" style="position:absolute;top:350px;left:420px; width:400px;height:200px;background-color:#D1D1DE;">
        <h1>自定义ui</h1>
    </div>

    <div id="box3" style="position:absolute;top:50px;left:120px; width:300px;height:160px;background-color:#B9CEFF;">
        <h1>自定义ui2</h1>
    </div>
    <div id="message" style="position: absolute; top: 0;left: 0;width:850px;line-height: 24px;">

    </div>

    <script>
        function pageLoaded() {
            var $msg = $("#message");
            var box = $("#box").draggable({
                nameSpace: 'draggable', //命名空间，一个对象可以绑定多种拖动方式
                which: undefined, //鼠标键码，是否左键1,右键3 才能触发拖动，默认左右键均可
                isProxy: false, //是否空代理
                holdTime: undefined, //按下鼠标500毫秒后才可以拖动
                handler: undefined, //触发拖动的对象
                cursor: 'move',
                disabled: false, //是否禁用拖动
                axis: undefined, // v or h  水平还是垂直方向拖动 ，默认全向
                onDragReady: function () { //鼠标按下时候的事件，返回true则往下执行，返回false则停止
                    return true;
                },
                onMouseUp: function (args) {
                    resize.setStyle({
                        "background": "#C40FE5",
                        "width": 8,
                        "height": 8
                    }, {
                        "border": "1px solid #C40FE5"
                    });
                    resize.bind(args.state.target);
                    resize.zoomScale(true);
                },
                onStartDrag: function (args) { //开始拖动
                    var state = args.state;
                    var data = state._data;
                    //console.log(">>>>>>onStartDrag nameSpace = "+args.nameSpace + " data="+JSON.stringify(data));
                    var bodyWidth = $("body").width();
                    var bodyHeight = $("body").height();
                    maxTop = bodyHeight - data.height;
                    maxLeft = bodyWidth - data.width;
                    //console.log("maxTop="+maxTop+"  maxLeft="+maxLeft);
                    resize.hide();
                },
                onStopDrag: function (args) { //拖动结束
                    //console.log("----->onStopDrag nameSpace = "+args.nameSpace + " data="+JSON.stringify(args.data));
                    resize.setStyle({
                        "background": "#C40FE5",
                        "width": 8,
                        "height": 8
                    }, {
                        "border": "1px solid #C40FE5"
                    });
                    resize.bind(args.state.target);
                    resize.zoomScale(true);
                }
            });
            var box2 = $("#box2").draggable({
                nameSpace: 'draggable', //命名空间，一个对象可以绑定多种拖动方式
                which: undefined, //鼠标键码，是否左键1,右键3 才能触发拖动，默认左右键均可
                isProxy: false, //是否空代理
                holdTime: undefined, //按下鼠标500毫秒后才可以拖动
                handler: undefined, //触发拖动的对象
                cursor: 'move',
                disabled: false, //是否禁用拖动
                axis: undefined, // v or h  水平还是垂直方向拖动 ，默认全向
                onDragReady: function () { //鼠标按下时候的事件，返回true则往下执行，返回false则停止
                    return true;
                },
                onMouseUp: function (args) {
                    resize.setStyle({
                        "background": "#0C9CE5",
                        "width": 6,
                        "height": 6
                    }, {
                        "border": "1px dashed #0C9CE5"
                    });
                    resize.bind(args.state.target);
                    resize.zoomScale(false);
                },
                onStartDrag: function (args) { //开始拖动
                    var data = args.state._data;
                    //console.log(">>>>>>onStartDrag nameSpace = "+args.nameSpace + " data="+JSON.stringify(data));
                    var bodyWidth = $("body").width();
                    var bodyHeight = $("body").height();
                    maxTop = bodyHeight - data.height;
                    maxLeft = bodyWidth - data.width;
                    //console.log("maxTop="+maxTop+"  maxLeft="+maxLeft);
                    resize.hide();
                },
                onDrag: function (args) { //拖动中
                    var data = args.state._data;
                    //拖动限制
                    if (data.top < 0) {
                        data.top = 0;
                    }
                    if (data.left < 0) {
                        data.left = 0;
                    }
                    if (data.top > maxTop) {
                        data.top = maxTop;
                    }
                    if (data.left > maxLeft) {
                        data.left = maxLeft;
                    }
                    //console.log("......onDrag nameSpace = "+args.nameSpace + " data="+JSON.stringify(data));
                },
                onStopDrag: function (args) { //拖动结束
                    //console.log("----->onStopDrag nameSpace = "+args.nameSpace + " data="+JSON.stringify(args.data));
                    resize.setStyle({
                        "background": "#0C9CE5",
                        "width": 6,
                        "height": 6
                    }, {
                        "border": "1px dashed #0C9CE5"
                    });
                    resize.bind(args.state.target);
                    resize.zoomScale(false);
                }
            });

        //    $("#transformRotate").on("change",function(){
        //         var r = $(this).val();
        //         box2.css("transform","rotate("+r+"deg)");                
        //     });

            $("#box3").draggable({
                nameSpace: 'draggable', //命名空间，一个对象可以绑定多种拖动方式
                which: undefined, //鼠标键码，是否左键1,右键3 才能触发拖动，默认左右键均可
                isProxy: false, //是否空代理
                holdTime: undefined, //按下鼠标500毫秒后才可以拖动
                handler: undefined, //触发拖动的对象
                cursor: 'move',
                disabled: false, //是否禁用拖动
                axis: undefined, // v or h  水平还是垂直方向拖动 ，默认全向
                onDragReady: function () { //鼠标按下时候的事件，返回true则往下执行，返回false则停止
                    return true;
                },
                onMouseUp: function (args) {
                    resize.setStyle({
                        "background": "#FFC900",
                        "width": 7,
                        "height": 7
                    }, {
                        "border": "2px dashed #FFC900"
                    });
                    resize.bind(args.state.target);
                    resize.zoomScale(false);
                },
                onStartDrag: function (args) { //开始拖动
                    var data = args.state._data;
                    //console.log(">>>>>>onStartDrag nameSpace = "+args.nameSpace + " data="+JSON.stringify(data));
                    var bodyWidth = $("body").width();
                    var bodyHeight = $("body").height();
                    maxTop = bodyHeight - data.height;
                    maxLeft = bodyWidth - data.width;
                    //console.log("maxTop="+maxTop+"  maxLeft="+maxLeft);
                    resize.hide();
                },
                onStopDrag: function (args) { //拖动结束
                    //console.log("----->onStopDrag nameSpace = "+args.nameSpace + " data="+JSON.stringify(args.data));
                    resize.setStyle({
                        "background": "#FFC900",
                        "width": 7,
                        "height": 7
                    }, {
                        "border": "2px dashed #FFC900"
                    });
                    resize.bind(args.state.target);
                    resize.zoomScale(false);
                }
            });



            var resize = new $B.Resize({
                target: box,
                zoomScale: true, //等比例缩放
                dragStart: function (args) {
                    $msg.html("dragStart " + JSON.stringify(args.state._data));
                    console.log("dragStart = " + JSON.stringify(this.data("resizeData")));
                },
                onDrag: function (args) {
                    $msg.html("onDrag " + JSON.stringify(args.state._data));
                    console.log("onDrag = " + JSON.stringify(this.data("resizeData")));
                },
                dragEnd: function (args) {
                    $msg.html("dragEnd " + JSON.stringify(args.state._data));
                    console.log("dragEnd = " + JSON.stringify(this.data("resizeData")));
                }
            });
            $("body").click(function (e) {
                console.log("body click-------------");
                if (e.target.localName === "body") {
                    resize.hide();
                    setTimeout(function () {
                        resize.show();
                    }, 3000);
                }
            });
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'plugin': '../javascript/plugin',
                '$B': '../javascript/basic',
                'resize': '../javascript/resize'
            }
        });
        var loadModel = ['resize'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</body>
</html> 