<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>


    <style>
        body{
             box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
         }
    </style>
</head> 

<body>
    <div class="section-content">
        <h1 class="section-title">固定数量文件上传</h1>
        <div style="height:250px" class="section-body clearfix">
            <table style="width:100%;">
                <tr style="height:35px">
                    <td style="width:200px">自动上传方式：</td>
                    <td width="250px">
                        <div id="fileUpload1"></div>
                    </td>
                    <td style="padding-left: 30px">
                        提示：选择文件后自动立即触发文件上传
                    </td>
                </tr>
                <tr style="height:45px">
                    <td style="width:150px;border-top: 1px dashed #cccccc;padding-top:20px">提交上传方式：</td>
                    <td style="border-top: 1px dashed #cccccc;padding-top:20px">
                        <div id="fileUpload2"></div>
                    </td>
                    <td style="border-top: 1px dashed #cccccc;padding-top:20px;padding-left: 30px">
                        <button id="uploadFiles" style="background:#B90524"><i class="fa fa-upload"></i>点击上传</button>
                    
                        <!-- <button id="checkUpload" style="background:rgb(5, 77, 185)">检查wps安装</button> -->
                    
                    </td>
                </tr>
                <tr style="height:45px">
                    <td style="width:150px;border-top: 1px dashed #cccccc;padding-top:20px">
                        额外参数：
                    </td>
                    <td colspan="2" style="border-top: 1px dashed #cccccc;padding-top:20px">
                        <input id="extparams" type="text" value="123" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <div class="section-content">
        <h1 class="section-title">动态数量文件上传</h1>
        <div style="height:300px" class="section-body clearfix">
            <table style="width:100%;">
                <tr style="height:35px">
                    <td style="padding-left: 30px">
                        <button id="addFiles">添加文件</button>
                        <select id="uploadType">
                            <option selected="selected">自动上传</option>
                            <option>点击上传</option>
                        </select>
                        <button id="userUpload" style="display:none;background:#B90524">点击上传</button>
                       
                        
                    </td>
                </tr>
                <tr style="height:35px">
                    <td style="padding-left: 30px;border-top: 1px dashed #cccccc;">
                        <div id="fileUpload3"></div>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <script type="text/javascript">
        function pageLoaded() {

            $("#checkUpload").click(function(){

                $B.request({                   
                    url:'http://127.0.0.1:6789/check'
                });
            });


            var upload3 = new $B.MutilUpload({
                target: $("#fileUpload3"),
                timeout: 60, //60秒钟超时             
                immediate: true, //选择文件后是否立即自动上传，即不用用户点击提交按钮就上传
                url: '/bui/api/json?flag=upload',
                success: function (res) { //成功时候的回调
                    var _t = this;
                    console.log("fileUpload3：" + JSON.stringify(res));
                },
                error: function (res) { //错误回调
                    console.log("fileUpload3 error：" + res);
                },
                setParams: function (fileName) { //设置参数
                    console.log("MutilUpload " + JSON.stringify(fileName));
                    return {
                        prs: 888
                    };
                },
                ondeleted: function (res) { //删除回调
                    console.log("ondeleted：" + res);
                }
            });
            var files = [{
                name: 'xmlfile',
                type: '.xml',
                label: '请选择xml文件请选择xml文件请选择xml文件',
                must: '必须上传文件!'
            }, {
                name: 'xlsfile',
                type: '.xls,.xlsx',
                label: '请选择xls(x)文件'
            }, {
                name: 'words',
                type: '.doc,.docx',
                label: '请选择doc(x)文件'
            }];
            var curIdx = 0;
            $("#addFiles").click(function () {
                if (curIdx > 2) {
                    curIdx = 0;
                }
                upload3.addFile(files[curIdx]);
                curIdx++;
            });
            var userBtn = $("#userUpload").click(function () {
                upload3.submit();
            });
            $("#uploadType").on("change", function () {
                var v = $(this).val();
                if (v === "自动上传") {
                    upload3.immediate = true;
                    userBtn.hide();
                } else {
                    upload3.immediate = false;
                    userBtn.show();
                }
            });

            new $B.MutilUpload({
                target: $("#fileUpload1"),
                timeout: 180, //超时时间 秒
                init: true, //是否初始化文件选择按钮
                immediate: true, //选择文件后是否立即自动上传，即不用用户点击提交按钮就上传
                url: '/bui/api/json?flag=upload',
                // 待上传的文件列表，用于设置input的 name 以在服务器区分文件  	
                files: [{
                    name: 'xmlfile',
                    type: '.xml',
                    label: '请选择xml文件请选择xml文件请选择xml文件',
                    must: '必须上传文件!'
                }, {
                    name: 'xlsfile',
                    type: '.xls,.xlsx',
                    label: '请选择xls(x)文件'
                }],
                onselected: function (value, id, accept) { //选择文件后的事件请返回true 以便通过验证
                    console.log(id + " onselected = " + value);
                    return true;
                },
                ondeleted: function (res) { //删除回调
                    console.log("ondeleted：" + res);
                },
                setParams: function (fileName) { //设置参数
                    console.log("setParams " + JSON.stringify(fileName));
                    return {
                        p1: 111
                    };
                },
                success: function (res) { //成功时候的回调
                    var _t = this;
                    console.log("上传成功：" + JSON.stringify(res));
                },
                error: function (res) { //错误回调
                    console.log("error" + res);
                }
            });


            var uploadObj = new $B.MutilUpload({
                target: $("#fileUpload2"),
                timeout: 180, //超时时间 秒
                init: true, //是否初始化文件选择按钮
                immediate: false, //选择文件后是否立即自动上传，即不用用户点击提交按钮就上传
                //url: 'http://127.0.0.1:6789/upload',
                url: '/bui/api/json?flag=upload',
                // 待上传的文件列表，用于设置input的 name 以在服务器区分文件  	
                files: [{
                    name: 'uploadfile',
                    type: '.*',
                    label: '请选择数据文件',
                    must: '必须上传文件!'
                }, {
                    name: 'xlsfile12',
                    type: '.xls,.xlsx',
                    label: '请选择xls(x)文件'
                }],
                onselected: function (value, id, accept) { //选择文件后的事件请返回true 以便通过验证
                    console.log(id + " onselected = " + value);
                    return true;
                },
                success: function (res) { //成功时候的回调
                    console.log("手动上传成功：" + JSON.stringify(res));
                },
                ondeleted: function (res) { //删除回调
                    console.log("手动ondeleted：" + res);
                },
                setParams: function (fileName,fileList) { //设置参数
                    console.log("手动 setParams " + JSON.stringify(fileName));
                    var extparams = $("#extparams").val();
                    return {  
                        uuid:'111111', 
                        username:"kevin",
                        userpwd:"12222",                    
                        filename: encodeURIComponent("会议系统优化方案.doc"),
                        serverurl: encodeURIComponent("http://127.0.0.1:8080/web/dict/upload")
                    };
                    setInterval(function(){console.log(me);},1000);
                },
                error: function (res) { //错误回调
                    console.log("手动 error" + res);
                    // $B.request({
                    //     url:'http://127.0.0.1:6789/getres',
                    //     ok:function(){
                            
                    //     }
                    // });
                }
            });
            $("#uploadFiles").click(function () {
                uploadObj.submit();
            });
        }
        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'upload': '../javascript/upload'
            }
        });
        var loadModel = ['upload', 'utils'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</body>
 
</html> 