<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body>
        <style>
                body {
                    box-sizing: border-box;
                    -moz-box-sizing: border-box;
                    -webkit-box-sizing: border-box;
                }
        
                #wrap {
                    width: 90%;
                    height: 100%;
                    margin: 0 auto;
                    position: relative;
                }
        
                #centerwrap {
                    position: absolute;
                    bottom: 150px;
                    left: 300px;
                }
            </style>
    <div class="section-content">
        <h1 class="section-title">静态数据带工具列</h1>
        <div class="section-body clearfix" style="height:500px">
            <table id="datagrid"></table>
        </div>
    </div>
    <div class="section-content">
        <h1 class="section-title">动态Ajax异步加载</h1>
        <div class="section-body clearfix">
            <table id="datagrid2"></table>
        </div>
    </div>

    <script type="text/javascript">
        var dg, dg2;
        var treeCol = [{
                "title": "菜单名称",
                "field": "menuText",
                "width": 500,
                "align": "center",
                "sortable": false,
                "formatter": ""
            },
            {
                "title": "id",
                "field": "id",
                "align": "left",
                "sortable": true,
                "formatter": ""
            },
            {
                "title": "controller",
                "field": "controller",
                "align": "center",
                "sortable": true,
                "formatter": "formatfn1"
            },
            {
                "title": "depth",
                "field": "depth",
                "width": "auto",
                "align": "center",
                "sortable": true,
                "formatter": ""
            }
        ];
        var childrenBtns = [{
                "id": "line_btn_0",
                "text": "删除",
                "iconCls": "fa-trash",
                "params": {
                    "cmd": "delete",
                    "privilage": "1"
                },
                "visualable": true,
                "disabled": false,
                "click": function (prs) {
                    alert(JSON.stringify(prs));
                }
            },
            {
                "id": "line_btn_1",
                "text": "更新",
                "color": "",
                "methodsObject": "toolMethods",
                "params": {
                    "cmd": "update",
                    "privilage": "1"
                },
                "visualable": true,
                "disabled": false,
                "iconCls": "fa-doc-text"
            },
            {
                "id": "line_btn_2",
                "text": "管理子菜单",
                "methodsObject": "toolMethods",
                "params": {
                    "cmd": "update",
                    "privilage": "1"
                },
                "visualable": true,
                "disabled": false,
                "iconCls": "fa-share-2"
            }
        ];

        var groupBTs = [
            [{
                "text": "重新加载",
                "iconCls": "fa-search",
                "click": function (prs) {
                    dg2.reload({
                        q: 123
                    });
                }
            }, {
                "text": "获取数据",
                "iconCls": "fa-file-word",
                "click": function (prs) {
                    alert(JSON.stringify(dg2.getData()));
                }
            }],
            [{
                "text": "删除",
                "iconCls": "fa-cancel-2",
                "click": function (prs) {
                    alert("click2");
                }
            }],
            [{
                "text": "获取勾选的数据",
                "iconCls": "fa-ok-circled",
                "click": function (prs) {
                    var data = dg2.getCheckedData();
                    alert(JSON.stringify(data));
                }
            }, {
                "text": "获取勾选的id集合",
                "iconCls": "fa-cancel-2",
                "click": function (prs) {
                    var data = dg2.getCheckedId();
                    alert(JSON.stringify(data));
                }
            }],
            [{
                "text": "其他",
                "iconCls": "fa-down",
                "childrens": childrenBtns
            }]
        ];

        function pageLoaded() {
            var args = {
                data: treeData1, // data,  treeData
                cols: treeCol, // treeCol cols
                isTree: true,
                title: "树形表格",
                methodsObject: "toolMethods",
                loadImd: true, //是否立即加载
                treeIconColor: '#0D8AE6',
                oprCol: true, //是否需要操作列
                oprColWidth: 200, //定义操作列宽
                fillParent: true,
                //url: 'http://localhost/myui/demo/Handler.ashx?flag=datagridTree', // flag=datagrid datagridTree
                //toolbar: groupBTs, //toolbars
                checkBox: false, //是否需要复选框               
                idField: 'id', //id字段名称  
                toolbarOpts: {
                    style: 'min', //工具栏按钮样式
                    color: '#EDEDED',
                    iconColor: '#BCB9C9',
                    fontColor: '#666666'
                },
                pgposition: "bottom", //both bottom top
                iconCls: 'fa-table',
                btnStyle: 'plain', //plain
                //splitColLine: 'k_datagrid_td_none_line',//是否需要td列分割线 k_datagrid_td_v_line k_datagrid_td_h_line k_datagrid_td_none_line
                showBtnText: true,
                sortField: {
                    'order_index': 'desc',
                    "fieldName": 'asc'
                }, //默认排序字段
                setParams: function () { //设置查询时候附加的参数
                    return {
                        "p": "p1"
                    };
                },
                onDbClickRow: function () { //双击一行时触发 fn(rowData)
                    alert("onDbClickRow " + JSON.stringify(this.data("data")));
                },
                onClickCell: function (field, value) { //单击一个单元格时触发fn(field, value)
                    alert("onClickCell " + field + "  " + value);
                },
                onCheck: function (isChked, data) { //复选事件 
                    alert("onCheck " + isChked + " data=" + JSON.stringify(data));
                },
                onLoaded: function (data) { //加载完成回调
                    //console.log("onLoaded" + JSON.stringify(data));
                },
                onRowRender: function (data, rowIdx) { //行渲染事件
                    if (rowIdx % 2 === 0) {
                        //this.children().css("background","#E0CEFF");
                    }
                    //console.log("rowRender"+JSON.stringify(data));
                }
            };
            var grid = new $B.Datagrid($("#datagrid"), args);


            treeCol = [
                // [   {
                //     "title": "项目1",
                //     "field": "menuText",
                //     "colspan":2
                //     },
                //     {
                //     "title": "项目2",
                //     "field": "menuText",
                //     "colspan":2
                //     },
                //     {
                //     "title": "项目3",
                //     "field": "depth",
                //     "rowspan":2
                //     }                    
                // ],
                [{
                        "title": "菜单名称",
                        "field": "menuText",
                        "width": 500,
                        "align": "center",
                        "sortable": false,
                        "formatter": ""
                    },
                    {
                        "title": "id",
                        "field": "id",
                        "align": "left",
                        "sortable": true,
                        "formatter": ""
                    },
                    {
                        "title": "controller",
                        "field": "controller",
                        "align": "center",
                        "sortable": true,
                        "formatter": "formatfn1"
                    },
                    {
                        "title": "depth",
                        "field": "depth",
                        "width": "auto",
                        "align": "center",
                        "sortable": true,
                        "formatter": ""
                    }
                ]
            ];
            args.treeIconColor = '#C1054B';
            args.cols = treeCol;
            args.data = undefined;
            args.oprCol = false; //是否需要操作列
            args.fillParent = false;
            args.url = '/bui/api/json?flag=datagridTree'; // flag=datagrid datagridTree
            args.toolbar = groupBTs; //toolbars
            args.checkBox = true; //是否需要复选框 
            dg2 = new $B.Datagrid($("#datagrid2"), args);
        };

        function formatfn(cellData, rowData, field) {
            if (field !== "menuText") {
                return "<a style='color:red'>格式化:" + cellData + "</a>"
            }
            // if (cellData === 1 || cellData === "1") {
            //     return "ok";
            // }
            // return "no"
        }
        window.toolMethods = {
            lineTestFn: function (prs) {
                alert("lineDetailFn " + JSON.stringify(prs));
            },
            lineDleteFn: function (prs) {
                alert("lineDleteFn " + JSON.stringify(prs));
            },
            lineDetailFn: function (prs) {
                dg.openInner(this, {
                    content: 'test.html',
                    type: 'iframe', //如果是url请求的时候，type=html/iframe,
                    onLoaded: function () { //如果是url加载，会触发加载完成事件
                        console.log("loading is ok");
                    }
                });
            }
        };
        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'pagination': '../javascript/pagination',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree',
                'datagrid': '../javascript/datagrid'
            }
        });
        var loadModel = ['datagrid'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</body>

 </html>