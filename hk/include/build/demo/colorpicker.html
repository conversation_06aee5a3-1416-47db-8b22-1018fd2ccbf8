 <!DOCTYPE html>
<html>
    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body style="padding-top:20px">
    <div id="edit" style="margin:0px auto;padding-left:100px;">
        <div style="float:left">
            <input id="picker1" type="text" style="width:200px" value="" />
        </div>
        <div style="float: left;margin-left: 20px;">
            <div id="picker2" style="height:25px;width:200px;border: 1px solid #cccccc"></div>
        </div>
    </div>
    <script type="text/javascript">
        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'plugin': '../javascript/plugin',
                'panel': '../javascript/panel',
                'toolbar': '../javascript/toolbar',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'colorpicker': '../javascript/colorpicker'
            }
        });
        var loadModel = ['colorpicker'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
        var color, color2;

        function pageLoaded() {
            color = new $B.ColorPicker($("#picker1"), {
                clickHide: true,
                change: function (val) {
                    console.log("color " + val);
                }
            });
            color.show();
            color.setValue("rgb(199, 0, 0)");
            color2 = new $B.ColorPicker($("#picker2"), {
                clickHide: false,
                update2Target: true,
                positionFix: {
                    top: 2,
                    left: 2
                },
                change: function (val) {
                    console.log("color2 " + val);
                }
            });
            color2.show();
        }
        $(function(){
            pageLoaded();
        });
    </script>
</body>

</html>