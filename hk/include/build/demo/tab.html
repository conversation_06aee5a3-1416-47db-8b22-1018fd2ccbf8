 <!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body>
        <style>
                #main{
                    width:98%;
                    height: 100%;
                    position: relative;
                    margin: 0 auto;
                }
                #header{
                    height:50px;
                    width: 100%;
                    position: absolute;
                    z-index: 2;
                }
                #tabwrap{
                    height:100%;
                    width: 100%;
                    position: absolute;
                    top:0;
                    left: 0;
                    z-index: 1;
                    border-top: 50px solid #ffffff;
                }
            </style>
    <div id="main" class="k_box_size">
        <div id="header">
            <button onclick="add()">添加</button>
            <button onclick="delet()">删除</button>
            <button onclick="active()">active</button>
            <button onclick="roload()">roload</button>
        </div>
        <div id="tabwrap" class="k_box_size">
            <div id="tab"></div>
        </div>
    </div>
    <script type="text/javascript">
        var $tab, i = 0;

        function delet() {
            $tab.close('ADD标题标题_' + i);
            i--;
            if (i < 0) i = 0;
        }

        function active() {
            $tab.active("tab标题标题1");
        }

        function add() {
            i++;
            $tab.add({
                title: 'ADD标题标题_' + i,
                closeable: true,
                content: "<p>我是静态内容，这里可以是html标签内容 " + i + "</p>"
            });
        }

        function roload() {
            //$tab.reload("IFRAM内容");
            $tab.reload("HTML片段");
        }

        function pageLoaded() {

            $tab = new $B.Tab($("#tab"), {
                cxtmenu: true, //右键菜单
                onLoaded: function (title) { //加载后
                    console.log(title + " 加载完成.......");
                    if (arguments.length == 2) {
                        console.log("JSON 加载完成:" + JSON.stringify(arguments[1]));
                        this.html("JSON 加载完成:" + JSON.stringify(arguments[1]));
                    }
                },
                onClosed: function (title) {
                    console.log("onclosed =" + title);
                },
                onClick: function (title) {
                    alert("你点击了" + title);
                },
                tabs: [{
                    title: 'tab标题标题1',
                    iconCls: 'fa-database',
                    closeable: false,
                    actived: false,
                    content: "<div style='height: 500px;width: 800px'><p>我是静态内容，这里可以是html标签内容！！！！！！！！！！！！</p></div>"
                }, {
                    title: 'IFRAM内容',
                    closeable: true,
                    iconCls: 'fa-chart-bar',
                    actived: true,
                    url: 'test.html',
                    dataType: 'iframe'
                }, {
                    title: 'HTML片段',
                    closeable: false,
                    iconCls: 'fa-docs',
                    actived: false,
                    url: 'fragment.html',
                    dataType: 'html'
                }, {
                    title: 'JSON标签请求',
                    iconCls: 'fa-print',
                    closeable: false,
                    actived: false,
                    url: '/bui/api/json?flag=datagridTree',
                    dataType: 'json'
                }, {
                    title: 'IFRAM内容2',
                    closeable: true,
                    actived: true,
                    url: 'test.html',
                    dataType: 'iframe'
                }, {
                    title: 'HTML片段2',
                    iconCls: 'fa-edit-1',
                    closeable: false,
                    actived: false,
                    url: 'fragment.html',
                    dataType: 'html'
                }, {
                    title: 'IFRAM内容28',
                    iconCls: 'fa-cubes',
                    closeable: true,
                    actived: true,
                    url: 'test.html',
                    dataType: 'iframe'
                }, {
                    title: 'HTML片段28',
                    closeable: false,
                    actived: false,
                    url: 'fragment.html',
                    dataType: 'html'
                }]
            });
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'utils': '../javascript/utils',
                'plugin': '../javascript/plugin',               
                'mutilUpload': '../javascript/upload',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree',
                "colorpicker": '../javascript/colorpicker',
                'panel': '../javascript/panel',
                'resize': '../javascript/resize',
                'toolbar': '../javascript/toolbar',
                'tab': '../javascript/tab',
                'ctxmenu': '../javascript/ctxmenu'
            }
        });

        var loadModel = ['tab', 'panel', 'tree'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</body>
 </html>