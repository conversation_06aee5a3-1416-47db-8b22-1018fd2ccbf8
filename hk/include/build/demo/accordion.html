<!DOCTYPE html>
<html>
    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body>
    <div class="section-content">        
        <h1 class="section-title">远程ajax/html/iframe/自定义样式--demo</h1>
        <div style="height:600px" class="section-body clearfix">
            <div style="height:100%;width: 46%;float: left; ">
                <div id="accordion"></div>
            </div>
            <div style="height:100%;width: 46%;float: right; ">
                <div id="accordion1">
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        var accordion;

        function pageLoaded() {
            var opts = {
                // width:400,
                // height:700,
                iconCls: 'fa-angle-double-right', //收起图标
                iconClsAct: 'fa-angle-double-down', //展开图标
                iconPositon: 'right', //图标位置
                iconColor: '#666666', //图标颜色
                // fontStyle:{"font-size":"14px","font-weight":"bold","color":"#666666"},//标题字体颜色、大小配置
                // activedStyle:{"background":"#1C2428","color":"#FFFFFF","iconColor":'#FFFFFF'},//激活状态样式            
                // accordionStyle:{"background":"#263238","border":"1px solid #364248"},//手风琴边框、颜色定义
                onCreate: function (name) {
                    console.log(name + "  onCreate " + this[0].outerHTML);
                },
                onOpened: function (name) {
                    console.log(name + "  onOpened " + this[0].outerHTML);
                },
                onLoaded: function (type, data) { //加载完成
                    console.log("onLoaded  " + this.attr("_title"));
                    if (type === "json") {
                        console.log(JSON.stringify(data));
                        var ul = $("<ul style='padding-top:12px;padding-left:3px;'/>").appendTo(this);
                        //创建树
                        new $B.Tree(ul, {
                            data: data, //'数据' 
                            url: "/bui/api/json?flag=tree",                          
                            nodeParentIcon: 'fa-folder-empty', //父节点图标关闭状态
                            nodeParentOpenIcon: 'fa-folder-open-empty', //打开状态图标
                            leafNodeIcon: 'fa-doc', //子节点图标 
                            clickItemCls: 'k_tree_actived_cls',
                            plainStyle: true,
                            checkbox: false,
                            showLine: false,
                            onClick: function () {
                            }
                        });
                    }
                },
                items: [{
                        name: '项目远程加载html片段',
                        icon: 'fa-menu',
                        type: 'html',
                        url: "div.html"
                    },
                    {
                        actived: true,
                        name: '远程加载页面【firame】',
                        type: 'iframe',
                        icon: 'fa-database',
                        url: "test.html"
                    },
                    {
                        name: '远程加载json',
                        type: 'json',
                        icon: 'fa-cubes',
                        url: "/bui/api/json?flag=datagridTree"
                    },
                    {
                        name: '项目A',
                        content: '<div>项目静态内容</div>'
                    },
                    {
                        name: '项目B',
                        content: '<div>项目静态内容</div>'
                    }
                ]
            };
            accordion = new $B.Accordion($("#accordion"), opts);


            opts.iconCls = 'fa-right-open-1'; //收起图标
            opts.iconClsAct = 'fa-up-open-2'; //展开图标
            opts.iconPositon = 'right'; //图标位置
            opts.iconColor = '#666666'; //图标颜色
            opts.fontStyle = {
                "font-size": "14px",
                "font-weight": "bold",
                "color": "#666666"
            }; //标题字体颜色、大小配置
            opts.activedStyle = {
                "background": "#1C2428",
                "color": "#FFFFFF",
                "iconColor": '#FFFFFF'
            }; //激活状态样式            
            // opts.accordionStyle = {
            //     "background": "#ffffff",
            //     "border": "1px solid #364248"
            // }; //手风琴边框、颜色定义
            accordion = new $B.Accordion($("#accordion1"), opts);
        }
        var methodsObject = {
            test: function (pr) {
                alert(JSON.stringify(pr));
            }
        };      
        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',                
                'plugin': '../javascript/plugin',
                '$B': '../javascript/basic',
                'accordion': '../javascript/accordion',
                'panel': '../javascript/panel',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree'
            }
        });

        var loadModel = ['accordion', 'tree'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</body>

</html>