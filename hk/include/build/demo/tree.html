<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body>
        <style>
                div.k_tree_clicked_cls {
                    background-color: #2F4050;
                    color: #ffffff;
                }
                div.k_tree_clicked_cls div{
                    color: #ffffff;
                }
            </style>
    <div class="section-content">
        <h1 class="section-title">全功能Tree Demo(节点线、复选框、节点图标、工具栏、远程加载,自定义项渲染)</h1>
        <div class="section-body clearfix">
            <div style="float:left;width: 60%">
                <div>
                    <button onclick="getClickItem()">获取当前点击项</button>
                    <button onclick="getCheckedData()">获取复选数据</button>
                    <button onclick="setCheckOrSelect()">设置复选数据</button>
                    
                    <div id="msg">

                    </div>
                </div>
                <ul id="fullTree"></ul>
            </div>
            <div style="float:right;width: 40%">
                <ul id="simpleTree"></ul>
                
            </div>
        </div>
    </div>
    <div class="section-content">
        <h1 class="section-title">定制Tree UI Demo</h1>
        <div class="section-body clearfix">
            <div style="float:left;width: 33%">
                <h2>fontawesome图标及颜色定义</h2>
                <ul id="tree1"></ul>
            </div>
            <div style="float:left;width: 33%">
                <h2>css自定义图标混合fontawesome图标</h2>
                <ul id="tree2"></ul>
            </div>
            <div style="float:left;width: 33%">
                <h2>自定义间约风格</h2>
                <ul id="tree3"></ul>
            </div>
        </div>
    </div>


    <script type="text/javascript">

        function setCheckOrSelect(){
            tree.setCheckDatas(["1_0" ,"1_1deep2_1","deep2_2deep3_3","deep2_3deep3_0"]);
        }

        function getClickItem() {
            console.log("getClickItem >>>>>>>>>>")
            var $m = $("#msg").html(JSON.stringify(tree.getClickItem()));
            setTimeout(function(){$m},3500);
        }

        function getCheckedData() {
            var $m = $("#msg").html(JSON.stringify(tree.getCheckedData({
                onlyId: false,
                onlyChild: false
            })));
            setTimeout(function(){$m},3500);
        }


        var toolbars = [{
                id: '9999', //按钮id
                iconCls: 'icon-bar-chart',
                cmd: 'test', //对应的权限标识
                params: {
                    pr1: 12345678
                }, //额外的参数
                disabled: true, //是否禁用
                text: '按钮3', //文本
                click: function (pr) { //点击事件，如果存在click，则字符串形式的handler不可用
                    alert(JSON.stringify(pr));
                }
            },
            {
                id: '1111', //按钮id
                iconCls: 'icon-reorder',
                cmd: 'test', //对应的权限标识
                params: {
                    pr1: 123,
                    p1: 456
                }, //额外的参数
                disabled: false, //是否禁用
                text: '按钮4', //文本
                handler: 'test4', //window.methodsObject对象中 test
                methodsObject: 'methodsObject'
            }
        ];
        var methodsObject = {
            test4: function (prs) {
                alert(JSON.stringify(prs));
            }
        }

        var tree;

        function reload() {
            tree.reload();
        }


        function pageLoaded() {
			var treeUrl = $B.getHttpHost()+ "/bui/api/json?flag=tree";
            simpleTree = new $B.Tree($("#simpleTree"), {
                data: getTreeData(3, 4), //'数据'               
                nodeParentIcon: 'k_tree_houses',//父节点图标关闭状态
                nodeParentOpenIcon: 'k_tree_houses',      //打开状态图标
                leafNodeIcon: 'k_tree_house',                //子节点图标 
                chkEmptyIcon:' fa-check-empty',            //不选
                chkAllIcon: ' fa-check',            //全选
                chkSomeIcon: 'fa-ok-squared',              //部分选
                url: treeUrl,
                checkbox: false,
                canClickParent: true, //点击事件时，是否可以点击父节点 
                disChecked: false, //是否禁用复选框 默认false
                toolbar: false,
                showLine: false,
                clickItemCls: 'k_tree_clicked_cls', //点击行颜色  
                plainStyle: true,
                clickCheck: true, //是否点击复选
                onlyNodeData: false, //回调api中的参数是否只需要当前节点的数据（不带children）
                tree2list: false, //回调api中的参数是否转为列表类型
                extParamFiled: ['f1'], //异步懒加载时候，可以定义再传一个字段作为参数，默认不设置只传pid 
                onTreeCreated: function () {
                    console.log("onTreeCreated 树创建完成！");
                },
                onClick: function (data, params) { //function (data) { },//点击事件                 
                    console.log("onClick =" + JSON.stringify(data) + "  params=" + JSON.stringify(params));
                },
                onloaded: function (data) { //加载完成事件
                    console.log("onloaded " + JSON.stringify(data));
                    alert(JSON.stringify(data));
                }
            });

            var tData = getTreeData(3, 4);
            console.log(tData);
            tree = new $B.Tree($("#fullTree"), {
                data: tData, //'数据'
                /*** 
                nodeParentIcon: 'fa-folder-empty',//父节点图标关闭状态
                nodeParentOpenIcon: 'fa-folder-open-empty',      //打开状态图标
                leafNodeIcon: 'fa-doc',                //子节点图标 
                chkEmptyIcon:' fa-check-empty',            //不选
                chkAllIcon: ' fa-check',            //全选
                chkSomeIcon: 'fa-ok-squared',              //部分选 ***/
                url: treeUrl,
                checkbox: true,
                canClickParent: true, //点击事件时，是否可以点击父节点 
                disChecked: false, //是否禁用复选框 默认false
                toolbar: true,
                showLine: true,
                clickItemCls: 'k_tree_clicked_cls', //点击行颜色  
                plainStyle: false,
                clickCheck: true, //是否点击复选
                onlyNodeData: false, //回调api中的参数是否只需要当前节点的数据（不带children）
                tree2list: false, //回调api中的参数是否转为列表类型
                extParamFiled: ['f1'], //异步懒加载时候，可以定义再传一个字段作为参数，默认不设置只传pid 
                onItemCreated: function (data, deep, params) {
                    console.log("onItemCreated " + JSON.stringify(params));
                    if (data.checked) {
                        this.children().children(".k_tree_text").css("color", "red");
                    }

                },
                onTreeCreated: function () {
                    console.log("onTreeCreated 树创建完成！");
                },
                onClick: function (data, params) { //function (data) { },//点击事件                 
                    console.log("onClick =" + JSON.stringify(data) + "  params=" + JSON.stringify(params));
                },
                onloaded: function (data) { //加载完成事件
                    console.log("onloaded " + JSON.stringify(data));
                    alert(JSON.stringify(data));
                },
                onCheck: function (data, params, checked) {
                    console.log("checked =" + checked + " " + JSON.stringify(data));
                    alert(JSON.stringify(data));
                },
                onOperated: function (params) {
                    console.log("onOperated  " + JSON.stringify(params));
                    alert(JSON.stringify(params));
                }
            });

            console.log(tree instanceof $B.Tree);


            new $B.Tree($("#tree1"), {
                data: getTreeData(3, 5), //'数据'
                clickCheck: true, //是否点击复选
                nodeParentIcon: 'fa-folder-empty', //父节点图标关闭状态
                nodeParentOpenIcon: 'fa-folder-open-empty', //打开状态图标
                leafNodeIcon: 'fa-doc', //子节点图标 
                chkEmptyIcon: ' fa-check-empty', //不选
                chkAllIcon: ' fa-check', //全选
                chkSomeIcon: 'fa-ok-squared', //部分选 ***/
                checkbox: true,
                fontIconColor: '#5F52E3',
                onClick: function (data, params) { //function (data) { },//点击事件                 
                    console.log("onClick =" + JSON.stringify(data) + "  params=" + JSON.stringify(params));
                }
            });

            new $B.Tree($("#tree2"), {
                data: getTreeData(3, 5), //'数据'
                nodeParentIcon: 'k_tree_icon_nodes', //父节点图标关闭状态
                nodeParentOpenIcon: 'k_tree_icon_nodes', //打开状态图标
                leafNodeIcon: 'k_tree_icon_node', //子节点图标 
                chkEmptyIcon: ' fa-check-empty', //不选
                chkAllIcon: ' fa-check', //全选
                chkSomeIcon: 'fa-ok-squared', //部分选 ***/
                checkbox: true,
                fontIconColor: '#52E383'
            });

            new $B.Tree($("#tree3"), {
                data: getTreeData(3, 5), //'数据'
                plainStyle: true,
                checkbox: false,
                showLine: true
                // nodeParentIcon: 'k_tree_houses',//父节点图标关闭状态
                // nodeParentOpenIcon: 'k_tree_houses',      //打开状态图标
                // leafNodeIcon: 'k_tree_house',                //子节点图标 
            });
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree'
            }
        });

        var loadModel = ['tree'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</div>
</html> 