<!DOCTYPE html>
<html>
    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>


    <style>
        body{
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
            min-height: 100%;
         }
         .clearfix {
            *zoom: 1;
         }
         .clearfix:before,
         .clearfix:after {
            display: table;
            line-height: 0;
            content: "";
         }
         .clearfix:after {
            clear: both;
         }
    </style>
</head>

<body>
<div id="main">
    <div id="_08220948717Z35RORX8Y" class="_section_div_" style="line-height: 21px; font-size: 14px; "><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: bold;">​特性：</span></div>
    <div id="_0822285236VV5BP4GS85" class="_section_div_" style="line-height: 30.8px; font-size: 14px; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 16px; padding-right: 0px;"><ul style="list-style-position: inside; padding: 0px 0px 0px 32px; margin: 0px; list-style-type: disc; font-size: 14px;"><li id="_0822405673LNFSLYB5X8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0822285236I775GS8FJG">面向后端开发人员的UI，采用JAVA后端开发者熟悉的面向对象编程风格，只需要定义一个标签，new 一个实例即可。</span></li><li id="_08221006972U1EIWRMCK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_08221006997GQLGYVZV9">面向JSON风格的UI，编程人员无需编写多余的HTML，也不需要定义非html标准的各种属性，只需要定义JSON参数，创建对象即可。</span></li><li id="_0822211099YZ5RPW8VQL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_08222110991V68AWU3RK">​传统与现代结合，将双向绑定、前端CURD封装、约定胜于配置等大大提升编程生产效率的思想引入到UI框架实现中，框架既有如ext、easyui等功能，又有如vue的双向绑定实现。</span></li><li id="_08223007788ZUY832YFR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0822300778J2ATAY5CYW">​UI框架在实现上，充分考虑与后端数据交互的API设计，统一交互接口，统一交互数据格式，避免其他框架编程中，各处分别编写不同的Ajax、接口数据格式不统一的问题。</span></li><li id="_0822330745Q3RCM81YUJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0822330745ALS2DXCI73">​封装全面，将前端DataGrid列表的增删改查封装为统一的CURD对象，利用双向绑定技术解放表单读写编程。</span></li><li id="_0822210385U7WJZZ7Y2E"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_08222103851OE3JPVK6D">​采用fontawesome丰富的矢量图标、结合主题css，可以轻松根据设计要求修改UI样式，即使你不是专业的前端开发也可以轻松实现样式主题。</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0822412739VFHJU136AQ">​</span></li><li id="_09135411694H1I6WP7FF" style="line-height: 21px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0913541170XIF979VX64">​采用config.js语言配置文件，可以轻松实现国际化语言调整。</span></li></ul></div>
    <div id="_0822405675USFRY9CBL5" class="_section_div_" style="line-height: 21px; font-size: 14px; "><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0822405675K1EE8KKSPC">​</span></div>
    <div id="_0822423753KTYLLICYBC" class="_section_div_" style="line-height: 21px; font-size: 14px; "><span class="" style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: bold;" id="_091348493622PXKZABDO">使用：</span></div>
    <div id="_08224249426QQAK49PWW" class="_section_div_" style="line-height: 21px; font-size: 14px; "><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0822424942DZQDP34H12">​</span></div>
    <div id="_0822424971Y8N3125552" class="_section_div_" style="line-height: 21px; font-size: 14px; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span class="" style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0913553225WJCOMMXMSO">1、引入资源</span></div>
    <div id="_0914043630R7PO9EMKPD" class="_section_div_" style="line-height: 21px; font-size: 14px; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span class="" style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_09140436301ZK4T6Y6X1">​</span></div>
    <div id="_0913503916G783FA89CX" class="_section_div_" style="line-height: 21px; font-size: 14px; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0913534568OVACWEN6CS">&nbsp; &nbsp; 特别说明，如果用到富文本，需要在main.css、bui-fonts.css上声明[ </span><span class="" style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: bold;" id="_0913534569CB4JL4XBAU">class="k_bui_css"&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_09135345712J9PGOA8YX">]</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0913503917VPFYWEZ1G8"><pre style="line-height:1.5em;background-color:#f2f2f2;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;">    &lt;link type="text/css" rel="stylesheet" class="k_bui_css" th:href="@{/static/theme/font/bui-fonts.css}"&gt;
        &lt;link type="text/css" rel="stylesheet" class="k_bui_css" th:href="@{/static/theme/default/main.css}"&gt;
        &lt;link type="text/css" rel="stylesheet"  th:href="@{/static/theme/default/icon.css}"&gt;
    
        &lt;script type="application/javascript" th:src="@{/static/lib/jquery.min.js}"&gt;&lt;/script&gt;
        &lt;script type="application/javascript" th:src="@{/static/lib/config.js}"&gt;&lt;/script&gt;
        &lt;script type="application/javascript" th:src="@{/static/lib/bui-0.0.1.js}"&gt;&lt;/script&gt;</pre></span></div>
    <div id="_08224250995XG45YWJML" class="_section_div_" style="line-height: 21px; font-size: 14px; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0822425000FWH3MEMT16">​</span></div>
    <div id="_09134435233W8B9222WF" class="_section_div_" style="line-height: 21px; font-size: 14px; cursor: text; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="_0913443525EX5T42DGW7" class="">​2、声明控件标签（以DataGrid为列）</span></div>
    <div id="_091404335053S4YQ8Z1S" class="_section_div_" style="line-height: 21px; font-size: 14px; cursor: text; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" class="" id="_0914043351O9UTFVUJ9N">​</span></div>
    <div id="_0913575537JBJKZF8SJ8" class="_section_div_" style="line-height: 21px; font-size: 14px; cursor: text; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" class="" id="_0913575538NY7LQ3LRJ6"><pre style="line-height:1.5em;background-color:#f2f2f2;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;">&lt;table id="datagrid"&gt;&lt;/table&gt;</pre></span></div>
    <div id="_0913575518NYY3QQFGG7" class="_section_div_" style="line-height: 21px; font-size: 14px; cursor: text; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" class="" id="_0913575519FD7XD6XLLM">​</span></div>
    <div id="_09135923096VO8U7Q63H" class="_section_div_" style="line-height: 21px; font-size: 14px; cursor: text; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" class="" id="_09135923097ZCC8MW3LR">​3、创建DataGrid控件实例</span></div>
    <div id="_09135940494KD751ZBOA" class="_section_div_" style="line-height: 21px; font-size: 14px; cursor: text; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" class="" id="_09135940502W5S31IY9A">​<pre style="line-height:1.5em;background-color:#f2f2f2;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;">            var args = {
                    data: dataList, // 数据
                    cols: cols, // 列配置
                    isTree: false,//是否是树形列表
                    title: "表格标题",
                    methodsObject: "toolMethods", //工具栏事件集合对象
                    loadImd: true, //是否立即加载
                    pageSize: 10,//分页大小
                    pgBtnNum: 5,//分页按钮数量
                    fillParent: true,//是否沾满父元素
                    oprCol: true, //是否需要操作列
                    oprColWidth: 200, //定义操作列宽
                    url: "api/json?flag=datagrid", // flag=datagrid datagridTree
                    checkBox: true, //是否需要复选框               
                    idField: 'id', //id字段名称        
                    toolbar: groupBTs, //toolbars  工具栏配置
                    toolbarOpts: { //工具栏样式配置
                        style: 'min', 
                        color: '#EDEDED',
                        iconColor: '#BCB9C9',
                        fontColor: '#666666'
                    },
                    pgposition: "bottom", //both bottom top  分页工具栏位置
                    iconCls: 'fa-table',
                    btnStyle: 'plain', //plain                
                    showBtnText: true, //是否显示按钮文本
                    sortField: {'order_index': 'desc',"fieldName": 'asc'}, //默认排序字段
                    setParams: function () { //设置查询时候附加的参数
                        return {};
                    },
                    onDbClickRow: function () { //双击一行时触发 fn(rowData)                    
                    },
                    onClickCell: function (field, value) { //单击一个单元格时触发                    
                    },
                    onCheck: function (isChked, data) { //复选事件                    
                    },
                    onLoaded: function (data) { //加载完成回调                   
                    },
                    onRowRender: function (data, rowIdx) { //行渲染事件                   
                    }
                };
               var dg = new $B.Datagrid($("#datagrid"), args); //创建dataGrid实例</pre></span></div>
    <div id="_0913575569UYCMO5ZM9I" class="_section_div_" style="line-height: 21px; font-size: 14px; cursor: text; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" class="" id="_0913575569E3HAPD9V92">​</span></div>
    <div id="_091357551762A967T1HE" class="_section_div_" style="line-height: 21px; font-size: 14px; cursor: text; text-indent: 0px; margin-top: 0px; margin-bottom: 0px; padding-left: 32px; padding-right: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" class="" id="_0913575519VF7GB2SVT1">​</span></div>
    
</div>
<script type="text/javascript">
   $(function(){
       
       var $main = $("#main");
       //收起，展开代码
       $main.children("._section_div_close_title").each(function(){
           var $c = $(this).css("cursor","pointer");
           $c.on("click",function(){
               var tag = $(this);
               var closeCount = parseInt(tag.attr("closed_count"));
               var next = tag.next("._section_div_");
               var isShow = next.css("display") === "none";
               while(closeCount > 0){
                   if(isShow){
                       next.show();
                   }else{
                       next.hide();
                   }
                   next = next.next();
                   closeCount--;
               }
           });
       });
   });
</script>
</body>

</html>