<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

    <style>
        body{
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
        }
        #wrap{
            width:90%;
            height: 100%;
            margin:0 auto ;
            position: relative;
        }
        #centerwrap{
            position: absolute;
            bottom: 150px;
            left:300px;
        }
    </style>
</head>

<body>
    <div class="form_main_wrap">
        <div class="form_condition"></div>
        <div class="form_grid">
            <table id="datagrid"></table>
        </div>
    </div>
</div>
<script type="application/javascript">
    var datas = {
    "totalSize": 4,
    "currentPage": 1,
    "resultList": [
        {
            "toolbar": [
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_2",
                    "text": "配置权限",
                    "cmd": "funcMrg",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-user",
                    "params": {
                        "privilage": "1",
                        "cmd": "funcMrg"
                    },
                    "click": "lineFuncMrgFn"
                },
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_4",
                    "text": "编辑",
                    "cmd": "update",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-share-2",
                    "params": {
                        "privilage": "1",
                        "cmd": "update"
                    },
                    "click": "lineUpdateFn"
                },
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_6",
                    "text": "删除",
                    "cmd": "delete",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-cancel-2",
                    "params": {
                        "privilage": "1",
                        "cmd": "delete"
                    },
                    "click": "lineDeleteFn"
                }
            ],
            "defaultRole": 1,
            "extFeildsMap": {},
            "createTime": "2018-10-21 17:12",
            "roleName": "业务人员",
            "id": "78599fe0d51111e8bf2f3c970e751c70",
            "roleStatus": 1
        },
        {
            "toolbar": [
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_2",
                    "text": "配置权限",
                    "cmd": "funcMrg",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-user",
                    "params": {
                        "privilage": "1",
                        "cmd": "funcMrg"
                    },
                    "click": "lineFuncMrgFn"
                },
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_4",
                    "text": "编辑",
                    "cmd": "update",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-share-2",
                    "params": {
                        "privilage": "1",
                        "cmd": "update"
                    },
                    "click": "lineUpdateFn"
                },
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_6",
                    "text": "删除",
                    "cmd": "delete",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-cancel-2",
                    "params": {
                        "privilage": "1",
                        "cmd": "delete"
                    },
                    "click": "lineDeleteFn"
                }
            ],
            "defaultRole": 0,
            "extFeildsMap": {},
            "createTime": "2018-10-21 17:07",
            "roleName": "系统管理员",
            "id": "b4c43270d51011e89e813c970e751c70",
            "roleStatus": 1
        },
        {
            "toolbar": [
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_2",
                    "text": "配置权限",
                    "cmd": "funcMrg",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-user",
                    "params": {
                        "privilage": "1",
                        "cmd": "funcMrg"
                    },
                    "click": "lineFuncMrgFn"
                },
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_4",
                    "text": "编辑",
                    "cmd": "update",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-share-2",
                    "params": {
                        "privilage": "1",
                        "cmd": "update"
                    },
                    "click": "lineUpdateFn"
                },
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_6",
                    "text": "删除",
                    "cmd": "delete",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-cancel-2",
                    "params": {
                        "privilage": "1",
                        "cmd": "delete"
                    },
                    "click": "lineDeleteFn"
                }
            ],
            "defaultRole": 0,
            "extFeildsMap": {},
            "createTime": "2018-10-21 22:08",
            "roleName": "普通用户",
            "id": "c83d4c40d53a11e8b5113c970e751c70",
            "roleStatus": 1
        },
        {
            "toolbar": [
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_2",
                    "text": "配置权限",
                    "cmd": "funcMrg",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-user",
                    "params": {
                        "privilage": "1",
                        "cmd": "funcMrg"
                    },
                    "click": "lineFuncMrgFn"
                },
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_4",
                    "text": "编辑",
                    "cmd": "update",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-share-2",
                    "params": {
                        "privilage": "1",
                        "cmd": "update"
                    },
                    "click": "lineUpdateFn"
                },
                {
                    "color": "",
                    "disabled": false,
                    "id": "line_btn_6",
                    "text": "删除",
                    "cmd": "delete",
                    "visualable": true,
                    "methodsObject": "toolMethods",
                    "iconCls": "fa-cancel-2",
                    "params": {
                        "privilage": "1",
                        "cmd": "delete"
                    },
                    "click": "lineDeleteFn"
                }
            ],
            "defaultRole": 0,
            "extFeildsMap": {},
            "createTime": "2018-10-21 22:08",
            "roleName": "测试用户",
            "id": "d0055530d53a11e8b5113c970e751c70",
            "roleStatus": 0
        }
    ]
};
    var curdObj,
        $win,
        height = 292,
        gridOpts = {"toolbar":[{"color":"","disabled":false,"id":"line_btn_2","text":"新增","cmd":"add","visualable":true,"methodsObject":"toolMethods","iconCls":"fa-plus-circled","params":{"privilage":"1","cmd":"add"},"click":"addFn"},{"color":"","disabled":false,"id":"line_btn_4","text":"编辑","cmd":"update","visualable":true,"methodsObject":"toolMethods","iconCls":"fa-doc-text","params":{"privilage":"1","cmd":"update"},"click":"updateFn"},{"color":"","disabled":false,"id":"line_btn_6","text":"删除","cmd":"delete","visualable":true,"methodsObject":"toolMethods","iconCls":"fa-trash","params":{"privilage":"1","cmd":"delete"},"click":"deleteFn"}],"oprCol":true,"colfield":0,"pageSize":15,"checkBox":true,"id":"role","methodsObject":"toolMethods","trBtnCount":3,"title":"","cols":[[{"formatter":"","field":"roleName","width":"180px","sortable":false,"title":"角色名称","align":"center"},{"formatter":"formatStatus","field":"roleStatus","width":"auto","sortable":false,"title":"角色状态","align":"center"},{"formatter":"","field":"createTime","width":"auto","sortable":false,"title":"创建时间","align":"center"},{"formatter":"formatDefaultRole","field":"defaultRole","width":"auto","sortable":false,"title":"是否默认角色","align":"center"}]],"url":"role/list"};//获取datagrid配置json
        //gridOpts.url = ctxPath + gridOpts.url;//url补充上ctxPath
        var title = "角色管理";//国际化语言
        gridOpts.title = title;
        gridOpts.data = datas;

    //打开form.html
    function _openForm(prs,isUpdate,rowData){
        var opts = {
            dataType:'html',
            title:title,
            width:700,
            height:height
        };
        if(isUpdate && rowData){
            opts.rowData = rowData;
        }
        curdObj.window(opts,isUpdate);
    };
    /******工具栏事件集合******/
    window.toolMethods = {
        addFn:function(prs){
            _openForm(prs);
        },
        updateFn:function(prs){
            _openForm(prs,true);
        },
        deleteFn:function(prs){
            curdObj.delChecked();
        },
        lineUpdateFn:function(prs){
            _openForm(prs,true,prs);
        },
        lineDeleteFn:function(prs){
            curdObj.deleteData([prs.id]);
        },
        lineFuncMrgFn:function(prs){
            $win = $B.window({
                title:'功能管理['+prs.roleName+"]",
                full:true,
                content: "test.html"
            });
        }
    };
    
    function formatStatus(cData,rData){
        if(cData === 1){
            return '启用';
        }
        return '禁用';
    }  
    function formatDefaultRole(cData,rData){
        if(cData === 1){
            return '是';
        }
        return '否';
    }
    function pageLoaded() { 
        var dg = $("#datagrid");
        curdObj = new $B.CURD(dg, gridOpts);
    };

    require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'plugin': '../javascript/plugin',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'accordion': '../javascript/accordion',
                'colorpicker': '../javascript/colorpicker',
                'combox': '../javascript/combox',
                'ctxmenu': '../javascript/ctxmenu',
                'datagrid': '../javascript/datagrid',
                'kedit': '../javascript/kedit',
                'labeltab': '../javascript/labeltab',
                'layout': '../javascript/layout',
                'pagination': '../javascript/pagination',
                'panel': '../javascript/panel',
                'resize': '../javascript/resize',
                'tab': '../javascript/tab',
                'toolbar': '../javascript/toolbar',
                'tree': '../javascript/tree',
                'upload': '../javascript/upload',
                'validate': '../javascript/validate'
            }
        });
        var loadModel = ['datagrid','tree'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
</script>
</body>
 </html>