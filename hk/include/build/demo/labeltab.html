 <!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" /> 
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

    <style>
        body {
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
        }

        #wrap {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <div id="wrap"></div>

    <script type="text/javascript">
        var cols = [
            [{
                    "title": "标题3标题3标题3",
                    "colspan": 3
                },
                {
                    "title": "标题1标题1标题1",
                    "colspan": 2,
                    "rowspan": 2
                },
                {
                    "title": "字段标题",
                    "colspan": 1,
                    "rowspan": 3,
                    "field": "createTime"
                }
            ],
            [{
                    "title": "标题",
                    "colspan": 2
                },
                {
                    "title": "标题2",
                    "colspan": 1,
                    "rowspan": 2,
                    "width": 100,
                    "sortable": true
                },
                {
                    "title": "标题1",
                    "colspan": 4
                }
            ],
            [{
                    "title": "菜单名称",
                    "field": "menuName",
                    "width": 200,
                    "align": "left",
                    "sortable": false,
                    "formatter": ""
                },
                {
                    "title": "行为",
                    "field": "content",
                    "width": "100px",
                    "align": "left",
                    "sortable": true,
                    "formatter": ""
                },
                {
                    "title": "操作人",
                    "field": "createUser",
                    "width": "100px",
                    "align": "center",
                    "sortable": true,
                    "formatter": "formatfn1"
                }, {
                    "title": "状态",
                    "field": "status",
                    "minWidth": "100px",
                    "align": "center",
                    "sortable": false,
                    "formatter": "formatfn"
                },
                {
                    "title": "数据3",
                    "field": "menuName",
                    "minWidth": "120px",
                    "align": "center",
                    "sortable": true,
                    "formatter": ""
                },
                {
                    "title": "字段标题",
                    "field": "createTime",
                    "width": "auto",
                    "align": "center"
                }
            ]
        ];
        var childrenBtns = [{
                "id": "line_btn_0",
                "text": "删除",
                "iconCls": "fa-trash",
                "params": {
                    "cmd": "delete",
                    "privilage": "1"
                },
                "visualable": true,
                "disabled": false,
                "click": function (prs) {
                    alert(JSON.stringify(prs));
                }
            },
            {
                "id": "line_btn_1",
                "text": "更新",
                "color": "",
                "methodsObject": "toolMethods",
                "params": {
                    "cmd": "update",
                    "privilage": "1"
                },
                "visualable": true,
                "disabled": false,
                "iconCls": "fa-doc-text"
            },
            {
                "id": "line_btn_2",
                "text": "管理子菜单",
                "methodsObject": "toolMethods",
                "params": {
                    "cmd": "update",
                    "privilage": "1"
                },
                "visualable": true,
                "disabled": false,
                "iconCls": "fa-share-2"
            }
        ];

        var groupBTs = [
            [{
                "text": "重新加载",
                "iconCls": "fa-search",
                "click": function (prs) {
                    dg.reload({
                        q: 123
                    });
                }
            }, {
                "text": "获取数据",
                "iconCls": "fa-file-word",
                "click": function (prs) {
                    alert(JSON.stringify(dg.getData()));
                }
            }],
            [{
                "text": "刷新当前页",
                "iconCls": "fa-arrows-ccw",
                "click": function (prs) {
                    dg.refresh();
                }
            }, {
                "text": "删除",
                "iconCls": "fa-cancel-2",
                "click": function (prs) {
                    alert("click2");
                }
            }],
            [{
                "text": "获取勾选的数据",
                "iconCls": "fa-ok-circled",
                "click": function (prs) {
                    var data = dg.getCheckedData();
                    alert(JSON.stringify(data));
                }
            }, {
                "text": "获取勾选的id集合",
                "iconCls": "fa-cancel-2",
                "click": function (prs) {
                    var data = dg.getCheckedId();
                    alert(JSON.stringify(data));
                }
            }],
            [{
                "text": "其他",
                "iconCls": "fa-down",
                "childrens": childrenBtns
            }]
        ];


        function pageLoaded() {
            var $contentWrap = $("#contentWrap");
            var $tableWrap = $("<div style='width:100%;height:100%;'><table></table></div>");
            var tab = new $B.Labeltab($("#wrap"), {
                onclick: function (title) {
                    if (title === "静态内容[表格]") {
                        if (!this.data("isInit")) {
                            var args = {
                                data: data, // data,  treeData
                                cols: cols, // treeCol cols
                                isTree: false,
                                title: "表格标题",
                                methodsObject: "toolMethods",
                                pageSize: 10,
                                pgBtnNum: 5,
                                fillParent: true,
                                oprCol: true, //是否需要操作列
                                oprColWidth: 200, //定义操作列宽
                                checkBox: true, //是否需要复选框               
                                idField: 'id', //id字段名称        
                                toolbar: groupBTs, //toolbars
                                toolbarOpts: {
                                    style: 'min', //工具栏按钮样式
                                    color: '#EDEDED',
                                    iconColor: '#BCB9C9',
                                    fontColor: '#666666'
                                },
                                pgposition: "bottom", //both bottom top
                                iconCls: 'fa-table',
                                btnStyle: 'plain', //plain                
                                showBtnText: true,
                                sortField: {
                                    'order_index': 'desc',
                                    "fieldName": 'asc'
                                }
                            };
                            dg = new $B.Datagrid($tableWrap.children("table"), args);
                            this.data("isInit", true);
                        }
                    }
                },
                onLoaded: function (title, data) {
                    if (data) {
                        //创建tree
                        var _this = this;
                        var treeUl = $("<ul/>").appendTo(this);
                        console.log("tree >>>>>>>>>>>>>>>>>>>>>>>");
                        tree = new $B.Tree(treeUl, {
                            data: data, //'数据'
                            nodeParentIcon: 'fa-folder-empty', //父节点图标关闭状态
                            nodeParentOpenIcon: 'fa-folder-open-empty', //打开状态图标
                            leafNodeIcon: 'fa-doc', //子节点图标 
                            chkEmptyIcon: ' fa-check-empty', //不选
                            chkAllIcon: ' fa-check', //全选
                            chkSomeIcon: 'fa-ok-squared', //部分选 
                            url: "/bui/api/json?flag=tree",
                            checkbox: false,
                            canClickParent: false, //点击事件时，是否可以点击父节点 
                            disChecked: false, //是否禁用复选框 默认false
                            toolbar: false,
                            showLine: true,
                            clickItemCls: 'k_tree_clicked_cls', //点击行颜色  
                            plainStyle: false,
                            clickCheck: false, //是否点击复选
                            onlyNodeData: true, //回调api中的参数是否只需要当前节点的数据（不带children）
                            tree2list: false, //回调api中的参数是否转为列表类型
                            onClick: function (data, params) { //function (data) { },//点击事件                 

                            }
                        });
                    }
                },
                tabs: [{
                        iconCls: 'fa-codeopen',
                        title: '静态内容[表格]',
                        actived: false,
                        content: $tableWrap
                    },
                    {
                        title: 'iframe加载',
                        iconCls: 'fa-file-word',
                        actived: true,
                        url: 'test.html',
                        dataType: 'iframe'
                    },
                    {
                        title: 'HTML片段',
                        actived: false,
                        url: 'fragment.html',
                        dataType: 'html'
                    },
                    {
                        iconCls: 'fa-desktop',
                        title: 'JSON标签请求',
                        actived: false,
                        url: '/bui/api/json?flag=datagridTree',
                        dataType: 'json'
                    }
                ]
            });
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'utils': '../javascript/utils',
                'plugin': '../javascript/plugin',
                'editConfig': '../javascript/keditConfig',
                'mutilUpload': '../javascript/upload',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree',
                "colorpicker": '../javascript/colorpicker',
                'panel': '../javascript/panel',
                'resize': '../javascript/resize',
                'edit': '../javascript/edit',
                'toolbar': '../javascript/toolbar',
                'labeltab': '../javascript/labeltab',
                'pagination': '../javascript/pagination',
                'datagrid': '../javascript/datagrid'
            }
        });

        var loadModel = ['labeltab', 'datagrid', 'tree'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</body>

</html>