 <!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body>
        <style>
                body {
                    box-sizing: border-box;
                    -moz-box-sizing: border-box;
                    -webkit-box-sizing: border-box;
                }
        
                #wrap {
                    width: 90%;
                    height: 100%;
                    margin: 0 auto;
                    position: relative;
                }
        
                #centerwrap {
                    position: absolute;
                    bottom: 150px;
                    left: 300px;
                }
            </style>
    <div class="section-content">
        <h1 class="section-title">远程ajax加载/checkbox多选/自定义样式/远程模糊查询</h1>
        <div style="height:400px" class="section-body clearfix">
            <div style="margin-bottom:5px;">
                <button onclick="getSelected()">获取选择的值</button>
                <button onclick="getSelectedId()">获取选择的ID</button>
                <button onclick="reset()">重置</button>
            </div>
            <div style="height:380px;width:100%;" class="clearfix">
                <div style="height:100%;width: 33%;float: left; ">
                    <input id="combox1" type="text" />
                </div>
                <div style="height:100%;width: 33%;float: right; ">
                    <input id="combox2" type="text" />
                </div>
                <div style="height:100%;width: 33%;float: right; ">
                    <input id="combox5" type="text" />
                </div>
            </div>
        </div>
    </div>


    <div class="section-content">
        <h1 class="section-title">本地模糊查询/简单列表样式/只读模式</h1>
        <div style="height:400px" class="section-body clearfix">
            <div style="height:380px;width:100%;" class="clearfix">
                <div style="height:100%;width: 33%;float: left; ">
                    <input id="combox3" type="text" />
                </div>
                <div style="height:100%;width: 33%;float: right; ">
                    <input id="combox4" type="text" />
                </div>
                <div style="height:100%;width: 33%;float: right; ">
                    <input id="combox6" type="text" />
                </div>
            </div>
        </div>
    </div>



    <script type="text/javascript">
        function getSelected() {
            var d = c1.getCheckedData();
            alert(JSON.stringify(d));

        }

        function getSelectedId() {
            var d = c1.getCheckedIds();
            alert(JSON.stringify(d));
        }

        function reset() {
            c1.reset();
            c2.reset();
        }

        var c1, c2, c3, c4;

        function pageLoaded() {
            var treeData = getTreeData(3, 3);
            console.log(JSON.stringify( treeData) );
            var args = {
                //data: treeData,//数据 参考树数据
                default: {
                    "id": '',
                    "text": "请您选择"
                },
                placeholder: '请您选择/输入', //默认选择项目
                url: "/bui/api/json?flag=tree",
                mutilchecked: true, //是否多选
                checkfather: false, // 单选的时候，是否可以选择父节点
                readonly: false, //不可以编辑                
                plainStyle: false, //\"true\" 为简单无图标样式
                textField: 'text', //菜单名称字段，默认为text
                idField: 'id', //菜单id字段,默认为id
                onCheck: function (data, params, checked) {
                    alert("onCheck" + JSON.stringify(data));
                },
                onClick: function (data) {
                    alert(JSON.stringify(data));
                }
            };
            args.initShow = true;
            args.search = 'remote'; //远程搜索
            c1 = new $B.Combox($("#combox1"), args);

            args.readonly = false;
            args.search = 'local'; //本地搜索
            args.data = comBoxJson.slice();
            args.mutilchecked = true;
            args.plainStyle = true;

            args.initShow = false;
            args.checkbox = false;
            args.mutilChecked = false;
            console.log("本地搜索本地搜索本地搜索本地搜索");
            c2 = new $B.Combox($("#combox2"), args);

            //普通数组类型
            var dataArray = [{
                    id: 111,
                    fileName: '中国北京'
                },
                {
                    id: 222,
                    fileName: '中国南宁'
                },
                {
                    id: 333,
                    fileName: '中国桂林'
                },
                {
                    id: 444,
                    fileName: '中国北海'
                },
                {
                    id: 555,
                    fileName: '中国钦州'
                },
                {
                    id: 666,
                    fileName: '中国柳州'
                }
            ];
            args.initShow = true;
            args.isTreeData = false; //非树形数据结构
            args.data = dataArray.slice();
            args.showLine = false;
            args.default = {
                "id": '',
                "fileName": "请您选择"
            };
            args.textField = 'fileName'; //菜单名称字段，默认为text
            args.idField = 'id'; //菜单id字段,默认为id 
            args.checkbox = true;
            args.checkfather = true;
            c3 = new $B.Combox($("#combox4"), args);

            args.initShow = false;


            args.initShow = false;
            args.forecePlainStyle = false;
            args.default = {
                "id": '',
                "text": "请您选择"
            };
            args.isTreeData = true; //非树形数据结构
            args.data = comBoxJson.slice();
            args.textField = 'text'; //菜单名称字段，默认为text
            args.idField = 'id'; //菜单id字段,默认为id 
            args.plainStyle = true;
            args.disChecked = false;
            args.showLine = false;
            args.nodeParentIcon = 'fa-folder-empty'; //父节点图标关闭状态
            args.nodeParentOpenIcon = 'fa-folder-open-empty'; //打开状态图标
            args.leafNodeIcon = 'fa-doc'; //子节点图标 
            args.chkEmptyIcon = ' fa-check-empty'; //不选
            args.chkAllIcon = ' fa-check'; //全选
            args.chkSomeIcon = 'fa-ok-squared'; //部分选 ***/
            c4 = new $B.Combox($("#combox3"), args);

            args.data = comBoxJson.slice();
            args.plainStyle = false;
            args.showLine = true;
            args.checkbox = true;
            args.fontIconColor = '#529DF5';
            new $B.Combox($("#combox5"), args);

            args.showLine = false;
            args.checkbox = false;
            args.textField = 'fileName'; //菜单名称字段，默认为text
            args.idField = 'id'; //菜单id字段,默认为id 
            args.default = {
                "id": '',
                "fileName": "只读模式"
            };

            args.placeholder = '只读模式'; //默认选择项目
            args.data = dataArray.slice();
            args.isTreeData = false; //非树形数据结构
            args.readonly = true;
            args.plainStyle = true;
            args.forecePlainStyle = true;
            new $B.Combox($("#combox6"), args);

            $("#mytd").keydown(function (e) {
                alert(e.which);
            });
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree',
                'combox': '../javascript/combox'
            }
        });

        var loadModel = ['combox'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</body>

 </html> 