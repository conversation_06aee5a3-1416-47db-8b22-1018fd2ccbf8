<!DOCTYPE html>
<html>

<head>
    <title>Bui 框架 富文本编辑器 SSM框架开发</title>
    <meta charset="UTF-8">
    <meta name="keywords"
        content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
    <meta name="description"
        content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="bookmark" href="/favicon.ico" />
    <meta name="renderer" content="webkit|ie-stand" />
    <style>
       body{
           width:98%;
           margin: 0 auto;
           padding: 12px 24px;
       }

    </style>

    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="../lib/require.js"></script>


</head>

<body class="k_box_size">
        <div>
                
            <button onclick="add()">新增内容，高宽变化</button>
            <button onclick="del()">删除内容，高宽变化</button>

        </div>

        <!-- <div style="width:500px;height:200px;">
            <div style="width:100%;height:100%;overflow: hidden">
                <div id="scroll_content">
                    <p>..............</p>
                    <p>..............</p>
                </div>
            </div>
        </div> -->



    <div class="section-content clearfix" style="margin:0;padding:0;">

        <div style="height:250px;width:500px;float:left;margin-right: 50px;margin-top:40px; border:1px solid #ccc;position: relative;" class="clearfix">            
            <div style="background:#6BABDF;color:#fff; line-height:32px;height:32px; position:absolute;top:0;z-index:2;width: 100%;">固定显示，自定义大小，颜色，动态增减内容</div>
            <div class="k_box_size" style="width:100%;height:100%;position: absolute;top:0;left:0;border-top:32px solid #fff;z-index:1;overflow:hidden">
                    <div id="content" style="width:900px;">
    <div id="k_2108462374CMHLI8DQJV" class="_section_div_" style="line-height: 36.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 16px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109394912CYHRJ41TCQ">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109394913CPG6CEC5UT">&#8203;<img id="k_2109394913H1581KGI9E" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABQUlEQVQoU52SrU/DUBTFz2kYZV0hAbH/hBAkjhEkWUKQcwgMI7TF1NBWLCGZQCFwEJghATwGAQKDmEYgMCQkLese6yVvH6QDxZ567973uznn5BITHE7AoA85jsyTyYaI1Yoivuua78tMtxtXRQoPYWi288NZr0u5UEiuSSyKyJ1Spapt4yNNkyaJmgheyalKEJjPI5CumyyR2S3AhWHxHpAXgFUAhgiUCLeiyLr8gfTF8+IKIOcA5/IyNEByLwiKTYAyBumH68ZrAC5IlIbNLxHuh2HxKA/oXj8IbTpN4xOSm1rSaKIIHpWy1hsNvo0F8RsYSIKWMq0/iuBJKWs1D9LzPldEelckZwF0sgy7gNEme2ckywAyQGpBYJ+OeXKceNsw5DDLeBBFpeOBx2QZkBaAG9O0dnyfnT9B/GczJlqjb7QChA5z09+EAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">&#8203;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109394914PM41296EPO">&#8203;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_211121109824DP6OFOG7">ui框架很多，UI风格都大</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111211099Q1NGZHRV7Y">体相似，但这</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111211000CPO2YEU3CO">只是表面的，要以</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21094206066ZZ2UAKKJA">一</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21094206063OFZIOLO4O">个从事前后端开发的[全栈]工程师的工作效率来评价ui框架的开发效率。</span></div><div id="k_21085024837HYJ7WWFX9" class="_section_div_" style="line-height: 36.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109395850KO3C5J7X31">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109395851V3X5R9599A">&#8203;<img id="k_2109395851XIXHCVHUIT" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABQUlEQVQoU52SrU/DUBTFz2kYZV0hAbH/hBAkjhEkWUKQcwgMI7TF1NBWLCGZQCFwEJghATwGAQKDmEYgMCQkLese6yVvH6QDxZ567973uznn5BITHE7AoA85jsyTyYaI1Yoivuua78tMtxtXRQoPYWi288NZr0u5UEiuSSyKyJ1Spapt4yNNkyaJmgheyalKEJjPI5CumyyR2S3AhWHxHpAXgFUAhgiUCLeiyLr8gfTF8+IKIOcA5/IyNEByLwiKTYAyBumH68ZrAC5IlIbNLxHuh2HxKA/oXj8IbTpN4xOSm1rSaKIIHpWy1hsNvo0F8RsYSIKWMq0/iuBJKWs1D9LzPldEelckZwF0sgy7gNEme2ckywAyQGpBYJ+OeXKceNsw5DDLeBBFpeOBx2QZkBaAG9O0dnyfnT9B/GczJlqjb7QChA5z09+EAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">&#8203;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109395852N3I1L4VOY5">&#8203;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21093958518X1GCE9X8Y">细节决定效率，量身定制决定适配度，一套面向PC端的高效的简洁</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109380466RSVSJC14DO">的UI框架，是我这个后端码农一直追求的理想，尝试过ext、easyui、elementui，最终发现它们都不是我想要的。</span></div><div id="k_2111155345HMUJZJ21L5" class="_section_div_" style="line-height: 36.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 20px; margin-top: 0px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111225259O33G6NI3VZ">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2111225261ZUQKTMUMTE">&#8203;<img id="k_2111225262RHV3CLSTCR" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABY0lEQVQ4T7WSv0scURSFv/NmhiDujBbxN0j8R4KFJGkFC7tYWIhlCAgxRRpBSRMQY2FpKZYBAxap0lsICZLSVRc1uroRs2/elQnZJsxGgvEVt3j3fue8d+8Vdziqx9VNsH7zyVQXvXstrXq0P43jpXDLaXNgpcyjgF8hFjB7m/rBOSG74KAnJLYlY9j5aKxC304pfMXBo5+JbQOJmvYkY+hrPaqOIzbM9D7L+18I+VK4uKzH+3NIixjzqefdRcw68PhvrgWnIvzgeMgn1x+ADgU3HxTWBGutb7TrqQxLLjnsJmbWZG8war+LJ8x37mZk34s+lD77Mq4+DWKrjfoX14xGK/QdlcJGrdIgjIBciPPXiAmhWTXd54C7Snn4TSi/D2d70KDWXSiHJF8Cniu4SeX6FMjzlIFTodB2VK3EeVRdlWPGGc8qfvDjbZv7a1T/BTZOsga+o5PeM6Hrf3K+rfjP/A2auowQtdRF6AAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">&#8203;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111225263K2CXTCMZU8">&#8203;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111295222LQSHPVPW3W">效率第一，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21112956264R7EMY6I4B">效益是</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21112956283XTMHF7BOB">目标，我们的</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111211454WNWC6QA4IU">团队不像BATJ那样土豪，养一群专业的人</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111203793SZJVH6X1RC">折腾前端，我们需要每一个人前端后都会，但希望前端</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111203795SZHXOPE7ZZ">开</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111203796QGKET2UUNU">发的知识成本要低</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111204636F7U6TE7L65">，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111251544JZDDABJQSD">同时UI、交互</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111251545LHVE5Q2OPQ">不落后于主流</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111204639BWCJBLYZZM">。</span></div><div id="k_2108564725WPVGXS8AKY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(152, 147, 250); padding-left: 12px; border-top: 1px solid rgb(88, 184, 14); border-right: none; border-bottom: none; border-left: 1px solid rgb(88, 184, 14); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_210858209328X74FQB19">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2108582094HD6IVW6KAE">&#8203;&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2208154269E9ZYIFULH5">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2208154271SBDDKTOD3E">&#8203;&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2208154273VEKYC2LWE5">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2208155919Q4OJ4MJDQJ">&#8203;<img id="k_2208155920ZFI8K37SGE" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAABMklEQVQ4T63TvUodURAA4G/QIo3kAcRSAr6AfYJgYSFikWijaArByjplLO0ECxXTxFgEEbEQRHtfQBIs1d4bO0k4YWSvrNd7xb/tlpnz7cyc2fAKTzSNUsoE3uMtzrAWESe1+ABm0YcGjiJiM+NRSsngOgbzvVZYJi5HxJdSylfMVx+4dXGMmUR2MdICNBP/4hT96G7TecFeIufofcFoLhL5g54XIFeJXLb0+lSvkcgvvHvqyVr+70QWsYA3z4CusZRIzmMbHzrcUCc7b+YQYzd7UUE/MfRIKIEDjEfEVX1js6IfGEbXA639wz4+JXCzsfXkqqINjHaAEtjBdBO4h9RaW8HHFiiBLczVgbZI7Yf7hslq3XP9v0fEVLs277TTmlBKWa0q2oqIz53m9CDy2L35D3kDZgdufHpmAAAAAElFTkSuQmCC" style="width:17px;height:17px;margin-left:1px ;margin-right:1px;">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_22081559223Y486GBWUF">&#8203;&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline; letter-spacing: 1px;" id="k_2108582704Y2SWIPVN6G">我理想中的ui框架是这样的</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2108582706OREO3TGLQE">&#8203;<img id="k_2108582706OIYC9G35SR" src="data:image/gif;base64,R0lGODlhGAAYAPZQAEDAHRZFDhlJEB1OFeokJPaJijWAJCtfIuskJBpKEpURETeFJjuiH4sDA/dbUMbfwKshITydJB5PFupkZL8mJt5QSDOBIrgmJjN+I6QcHIQZGfBLR9Vtbe5IQithIuElJe5CQMglJT+cJzt8LTV4J+kkJP6TitslJc8zM58gII8CAkiLOitdIjeVHzukH+9JR/6SiephYTN2JVOJRz61HyJUGnm6ae1APmOUWoK5dlWyPkGaK3fLYOxJSu5KRoIMDIwCAq4mJjqdIcntv4YKCh1OFD2rIa8uLe9KSokPD2S8TTmcIUqpMdlzcxlJEf1lWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAUAAFAALAAAAAAYABgAAAeYgFCCg4SFhoeIiYpQPYJJgwSLhUhPDj8NGheRkoMwT08VREAIMZyDJp9PRyolE6aCPqlPCh8or1AbshknIbcFL58dEBQct1AFIDcpQU3GUC4WHiwHLcZCSlA4CUM8O85QOQPfgw8S4wA0GOLOAEYGIjWEEZxMCwY6UEUjSwAMK84CbMggMeObgHGDnCAUFGAhlIYLITpUFAgAOw==" style="width:19px;height:19px;margin-left:1px ;margin-right:1px;">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2108582706OAQEYQOB51">&#8203;</span></div><div id="k_2108462427HHBRB8VMPY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109435125C487H9M5RI">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21094351264A3OIA9MQT">&#8203;<img id="k_2109435126U11D9FXGLI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22080039611LB5YL5Z6Q">&#8203;标准的HTML标签及属性，无额外自定义的标签属性</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109435125K1QIHLPEKF">。</span></div><div id="k_2109004860PL4ZW4WXAT" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109440403MNIROQS1JQ">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21094404036LVGF2X3BF">&#8203;<img id="k_2109440404CG6O3O4UDV" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_220801214683OQT32BB4">&#8203;不需要什么</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109440403J82T1DUGMN">编译、打包命令的环境，明明白白看得见、摸得着的html、css、JavaScript，普通文本编辑器也可以开发组件。</span></div><div id="k_2109051624Y98E3RI71Q" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109474957FXIU4WLYPW">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_210947495874A1595ZJM">&#8203;<img id="k_2109474958S2L5B8HI4S" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208022448C5SGLW54BX">&#8203;符合</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208022293Q45QFC267G">【表现(css) + 结构(html) + 行为(javascript)】分离的设计原则，不能用乱如意大利面的jsx。</span></div><div id="k_2109024663ON4A83D1EZ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109475792KEAZ3SD15K">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109475793KBN9VO45QJ">&#8203;<img id="k_21094757935BUAKWGS9D" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208032252UA8BK1DY3C">&#8203;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109475792XFK5IRXN7E">便于在运行环境（测试、生产）定位问题，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109195033EW31Y8N6TE">可以</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22080331623VNXXFAEXM">马上应对小的调整修改。不能</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208033167M5GF7X36OJ">：加个链</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109173332BU1Q9M1A3Z">接、改个颜色，要重新编译、打包、部署。</span></div><div id="k_2109152191JVE23B27ZQ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109480911NHF28NEN5E">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109480912NA9F8QF93S">&#8203;<img id="k_2109480912YQ7BNZFO6O" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208034508IJW3RLVODA">&#8203;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109480911BSI18X3W6L">使用方式要是经典的面向对象风格的</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(61, 101, 245); background-color: rgb(255, 255, 255);" id="k_2109173170D5E7D98B8C">（new一个实例）</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21091731713LZ5USTXE6">。不喜欢jquery组件套路风格，也不需要elementUi那种style。</span></div><div id="k_2109204595EK19O9817T" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109482649NMK5MIJDWN">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109482651Y9L3LWXOL5">&#8203;<img id="k_2109482651UUPGF5YF6H" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208041778KL4P47R31Z">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_220804269727GTZ8JP8O">设计上要有对接后端接口的考虑，要有统一处理的设计，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22080432775HX8BIT6EI">统一的请求出入口，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208043278NULSILTZ7P">统一的无权限，异常处理提示</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22080432793W1KIVYDNA">。</span></div><div id="k_2109245301C2ME5EVI79" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109484319FYMUD4OCG5">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109484320FVNHATFZC5">&#8203;<img id="k_2109484320U1ODALLMWH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208045702YIYS37D9F7">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208050650AGJJLXZXIE">能够解放</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208050651EGVU1S9FEX">前端curd、表单读写</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208052465XYQN49M7FN">重复性的工作，要有</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_220805246745699OZCXO">curd的通用封装，要有表单双向联动实现。</span></div><div id="k_2109292050V9EAT6JD3L" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109485533Y6YZ6ER5NP">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21094855341KIF25WXW9">&#8203;<img id="k_2109485534UXYF6CWSCI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold;" id="k_26095400839336Q7K2TZ">使用简单，只需要定义一个html标签，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_220806558231UV92E1H9">然后new一个组件实例即可，组件全面完整，能够覆盖常见表单UI需求。</span></div><div id="k_2109314992HPWIDCONJB" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21094906784NN1TK5743">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21094906792KVP9U5I1D">&#8203;<img id="k_21094906791ZG79SP3B1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208072497HLXM8XGFVJ">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208072859JBPGB9GDPW">只需要支持json参数方式创建即可，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208072861YTSZSKBTWA">可以通过后端控制json参数进而控制ui组件，特别是按钮权限可见性、可用性的问题，我不想写额外的ui权限逻辑。</span></div><div id="k_2109361723YULTMIEXZF" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109493444JDOZ5KOS9W">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(255, 255, 255);" id="k_22082006919HIEBUZABH">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2609593587IBMS6NUJGB">&#8203;<img id="k_2609593588W3ILLN83E3" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABi0lEQVQoU5WSv2sUARSEv3lrJIVaKArKwa279ydYaiEcCFZiUPAaRUxhY6oINpJKC1MFi6AWNgYsbASNtRJRsbMQxN29QyQQf4B6EDG378mtRMXOV8+8gW9G/Oep0Qdm/Xwa1yzQRpGAPiO/4Wk5h/ix+VeNuOzcQnGa0FDEK6SNiMgRLcGz2kZTpIPVsUlWZTO4zUs8rbeuH6f1/tPv1Cq7RtgFEffrrDiBcFmRv0Ts8ogj5OWbRry2e1vybcfdsHiusINBHHD8GFn1WFZ01kSsIN2LYM7xmQSdD3EU1Ie4Q+giFrO+v1iQlZ2PEA8JjVCcaRCAQKuexMlkFGlIN4HLnhXzY8NrYN0nvndtY3IBorcppl2sWJEvgnqOnyIvH8jK7Dph0xhXPH171fqdSx71Mln1giLrGrYkYlBv/3qYPR+GokpTiy3LBJ2GhnwR+GJhPaRzEENPmBqnNVgbKv323sQnbgfRFbJfXUYQFC4/S1Y9+VPc39N419rJaPKQue/zpH5EOqj+Xc5P0xi1DQAn1awAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(255, 255, 255);" id="k_260959358947HJT5XQK7">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208075555QJ8QFUBTVE">统</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208080853ZX1GTS68OU">一规范，支持</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_261001091295WZ8N4PPE">代码生</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_26100132534WO7W7IDIF">成器生成列表、新增/修改</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2610022386DDC5DJGWZA">这些通用的页面，我们希望能解放curd页面的基础工作</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_26100109158VM7OUUKW7">。</span></div><div id="k_210944567669BFSLQF24" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109494288GQANPF1ITV">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109494289WUDMPMC5PC">&#8203;<img id="k_2109494289KG7XPKDEA7" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208082909AW97SX664G">&#8203;满足</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21094942888PUU971KG9">常规需求，如国际化语言、自定义某些提示语、自定义字体图标、主题样式包那也是必须支持的。</span></div><div id="k_2109510826VTYSTRA7PI" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109535346R1YT2EMPD2">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109535348Y92D6CRTX1">&#8203;<img id="k_210953534894S3MDQKOI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22080840255NPX6MPRKE">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208083879DWI3545IOB">纯前端的，适用</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208124899JZAX24FS3U">于前后端分</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208124901VN4HNQL5K3">离开发，也可以通过</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110071263SOZXL4SRM3">后端实施某些配置来控制前端ui，比如将数据列表/工具栏做成后端xml配置方式。</span></div><div id="k_2208091517Q9QKIE2PHH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 20px; margin-top: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208132406LLGRXGU5U2">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2208132409IC9GHG4OPL">&#8203;<img id="k_2208132409R3GGFIG9G3" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABi0lEQVQoU5WSv2sUARSEv3lrJIVaKArKwa279ydYaiEcCFZiUPAaRUxhY6oINpJKC1MFi6AWNgYsbASNtRJRsbMQxN29QyQQf4B6EDG378mtRMXOV8+8gW9G/Oep0Qdm/Xwa1yzQRpGAPiO/4Wk5h/ix+VeNuOzcQnGa0FDEK6SNiMgRLcGz2kZTpIPVsUlWZTO4zUs8rbeuH6f1/tPv1Cq7RtgFEffrrDiBcFmRv0Ts8ogj5OWbRry2e1vybcfdsHiusINBHHD8GFn1WFZ01kSsIN2LYM7xmQSdD3EU1Ie4Q+giFrO+v1iQlZ2PEA8JjVCcaRCAQKuexMlkFGlIN4HLnhXzY8NrYN0nvndtY3IBorcppl2sWJEvgnqOnyIvH8jK7Dph0xhXPH171fqdSx71Mln1giLrGrYkYlBv/3qYPR+GokpTiy3LBJ2GhnwR+GJhPaRzEENPmBqnNVgbKv323sQnbgfRFbJfXUYQFC4/S1Y9+VPc39N419rJaPKQue/zpH5EOqj+Xc5P0xi1DQAn1awAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208132410FU7RIWWLO8">&#8203;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22081324078WHU7IMV7Z">主题易于修改，比如可以修改图标的颜色、大小、修改按钮大小规格，表格可以定义是否需要分割线等。</span></div>
    <P>ZHESWENBB这已经是是文本底部这已经是是文本底部这已经是是文本底部这已经是是文本底部这已经是是文本底部这已经是是文本底部</P>
                    </div>
            </div>
        </div>
        <div style="height:250px;width:500px;float:left;margin-right: 50px;margin-top:40px; border:1px solid #ccc;position: relative;" class="clearfix">            
                <div style="background:#6BABDF;color:#fff; line-height:32px;height:32px; position:absolute;top:0;z-index:2;width: 100%;">动态显示</div>
                <div class="k_box_size" style="width:100%;height:100%;position: absolute;top:0;left:0;border-top:32px solid #fff;z-index:1;overflow:hidden">
                        <div id="content2" style="width:750px;">
                            <p>ui框架很多，UI风格都大体相似，但这只是表面的，要以一个从事前后端开发的[全栈]工程师的工作效率来评价ui框架的开发效率</p>
                            <p>细节决定效率，量身定制决定适配度，一套面向PC端的高效的简洁的UI框架，是我这个后端码农一直追求的理想，尝试过ext、easyui、elementui，最终发现它们都不是我想要的。</p>
                            <p>效率第一，效益是目标，我们的团队不像BATJ那样土豪，养一群专业的人折腾前端，我们需要每一个人前端后都会</p>
                            <p>效率第一，效益是目标，我们的团队不像BATJ那样土豪，养一群专业的人折腾前端，我们需要每一个人前端后都会</p>
                            <p>效率第一，效益是目标，我们的团队不像BATJ那样土豪，养一群专业的人折腾前端，我们需要每一个人前端后都会</p>
                            <p>效率第一，效益是目标，我们的团队不像BATJ那样土豪，养一群专业的人折腾前端，我们需要每一个人前端后都会</p>
                            <p>效率第一，效益是目标，我们的团队不像BATJ那样土豪，养一群专业的人折腾前端，我们需要每一个人前端后都会</p>
                            <p>效率第一，效益是目标，我们的团队不像BATJ那样土豪，养一群专业的人折腾前端，我们需要每一个人前端后都会</p>
                            <p>效率第一，效益是目标，我们的团队不像BATJ那样土豪，养一群专业的人折腾前端，我们需要每一个人前端后都会</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                            <p>标准的HTML标签及属性，无额外自定义的标签属性。</p>
                        </div>
                </div>
        </div>

    </div>

    <script type="text/javascript">
        var count = 100;
        var myScrollbar;
        function add(){
            console.log("新增 " + count);
           var c = $("#content").append("<p> "+count+" 新增的： jQuery 是由美国人 John Resig 于 2006 年创建的一个开源项目，随着被人们的熟知，越来越多的程序高手加入其中，完善和壮大其项目内容 ；如今已发展成为集 JavaScript、CSS、DOM、Ajax 于一体的强大框架体系，它的主旨是 ：以更少的代码，实现更多的功能（Writeless，do more）。</p>");
           count ++; 
           myScrollbar.resetSliderPosByTimer();
        } 
        function del(){
           var c = $("#content2").children().first().remove();
            console.log("del");
            myScrollbar.resetSliderPosByTimer();
        }
    function pageLoaded() {            
            myScrollbar = $("#content").myscrollbar({
                slider: {
                    "background-color": '#646AF7',
                    "border-radius": "8px"
                },
                checkItv:0,
                size:"10px",
                ///axis:'y',
                //display:'auto',
                hightColor:"#C664F7",
                bar:{
                    "background-color": "#e4e4e4",
                    "border-radius": "8px",
                    "opacity": 0.4
                }   
            }).getMyScrollIns();

        $("#content2").myscrollbar({
                slider: {
                    "background-color": '#64FF2E',
                    "border-radius": "0"
                },               
                size:"6px",
                display:'auto',
                hightColor:"#39E300",
                bar:{
                    "background-color": "#585858",
                    "border-radius": "0",
                    "opacity": 0.4
                }   
            }).getMyScrollIns();
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'plugin': '../javascript/plugin'
            }
        });
        var loadModel = ['plugin'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</body>

</html>