 <!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

    <style>
        body{
             box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
         }
         td.label{
             width: 200px;
         }
         .section-body p{
             line-height: 25px;
             height: 25px;
         }
    </style>
</head>

<body>
    <div class="section-content">
        <h1 class="section-title">实体JSON填充到form表单中，实现双向联动，解放表单繁杂的数据读写开发</h1>
        <div style="height:200px" class="section-body clearfix">
            <p style="color:red">调用$B.bindForm(formJqueryTag,jsonData)api即可实现双向绑定联动，省去繁杂的表达式编写，只需要将标签id/name与Json数据对象属性对应上即可！</p>
            <p>$B.bindForm(formJqueryTag,jsonData,onChanged) api支持属性值变更通知，可及时获得修改通知</p>
            <p>$B.bindForm(formJqueryTag,jsonData)返回的mvvm实例，通过mvvm实例提供getJson()的api,可以轻松得到当前表单的json，省去编写表单序列化为JSON的代码</p>
            <p><button onclick="m_date()">修改data对象属性数据</button></p>
            <table style="margin-top:20px;" id="myform" class="form_table">
               <tr>
                    <td class="label">时间日期：</td>
                    <td style="width:250px;">
                        <input id="userDate" type="text" />  
                    </td>
                    <td>tree组件联动</td>
                </tr>
                <tr>
                    <td class="label">姓名：</td>
                    <td>
                        <input id="userName" type="text" />
                    </td>
                    <td rowspan="8" valign="top">
                        <ul id="userTree">
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td class="label">密码：</td>
                    <td>
                        <input id="userPwd" type="password" />
                    </td>
                </tr>
                <tr>
                    <td class="label">性别：</td>
                    <td>
                        <label class="k_radio_label k_radio_anim">
                            <input type="radio" name="sex" value="1" /><i class="k_radio_i"></i>男
                        </label>
                        <label class="k_radio_label k_radio_anim">
                            <input type="radio" name="sex" value="0" /><i class="k_radio_i"></i>女
                        </label>
                    </td>
                </tr>
                <tr>
                    <td class="label">爱好：</td>
                    <td>
                        <label class="k_checkbox_label k_checkbox_anim">
                            <input type="checkbox" name="hobby" value="1" /><i class="k_checkbox_i"></i>跑步
                        </label>
                        <label class="k_checkbox_label k_checkbox_anim">
                            <input type="checkbox" name="hobby" value="2" /><i class="k_checkbox_i"></i>钓鱼
                        </label>
                        <label class="k_checkbox_label k_checkbox_anim">
                            <input type="checkbox" name="hobby" value="3" /><i class="k_checkbox_i"></i>编程
                        </label>
                    </td>
                </tr>
                <tr>
                    <td class="label">户籍：</td>
                    <td>
                        <select id="userid">
                            <option value="0">广西钦州</option>
                            <option value="1">广西南宁</option>
                            <option value="2">广西桂林</option>
                            <option value="3">广西北海</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label">常住地：</td>
                    <td>
                        <input id="userAddr" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">技能：</td>
                    <td>
                        <input id="userTech" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">介绍：</td>
                    <td>
                        <textarea id="userDesp"></textarea>
                    </td>
                </tr>
            </table>
            <div>
                <button onclick="mySubmit(this)">提交表单</button>
            </div>
            <div id="message">

            </div>
        </div>
    </div>


    <script type="text/javascript">
        var userData = {
            userDate:'2019-05-01 05:05:05',
            userName: 'kevin.huang',
            userPwd: '000000',
            sex: 1,
            hobby: '1,3',
            userTech: 2,
            userid: 1,
            userAddr: '2,3',
            userTree:'0101,0202',
            userDesp: '介绍：这家伙是一个热爱编程工作的人！'
        };
        function m_date(){
            userData.userDate = '2016-06-06 08:08:08';
            userData.userName = "kevin";
            userData.sex = 1;
            userData.userDesp = "一个热爱编程工作的人";  
            userData.hobby = "1";
            userData.userid = "2";            
            userData.userAddr = "1,4";
            userData.userTech = 1;
            userData.userTree='0101,0102,0103';
        }
        var vm;

        function pageLoaded() {
            var dataArray = [{
                    id: '1',
                    fileName: '中国北京'
                },
                {
                    id: '2',
                    fileName: '中国南宁'
                },
                {
                    id: '3',
                    fileName: '中国桂林'
                },
                {
                    id: '4',
                    fileName: '中国北海'
                },
                {
                    id: '5',
                    fileName: '中国钦州'
                },
                {
                    id: '6',
                    fileName: '中国柳州'
                }
            ];
            var args = {
                default: {
                    "id": '',
                    "fileName": "请您选择"
                },
                data: dataArray,
                isTreeData: false,
                placeholder: '请您选择',
                checkbox: true,
                textField: 'fileName',
                mutilchecked: true, //是否多选
                checkfather: false, // 单选的时候，是否可以选择父节点
                readonly: true, //不可以编辑                
                plainStyle: false
            };
            new $B.Combox($("#userAddr"), args);

            args = {
                data: [{
                    id: 1,
                    text: 'java'
                }, {
                    id: 2,
                    text: 'javascript'
                }, {
                    id: 3,
                    text: 'c#'
                }],
                isTreeData: false,
                placeholder: '请您选择',
                mutilchecked: false, //是否多选          
                plainStyle: false,
                showLine: false,
                checkbox: false,
                mutilChecked: false,
                readonly: true,
                plainStyle: true,
                forecePlainStyle: true
            };
            new $B.Combox($("#userTech"), args);


            /***时间日期***/
            new $B.Calendar($("#userDate"),{
                fmt:'yyyy-MM-dd hh:mm:ss',
                readonly:true
            });


            var treeData = [{
                        id:'01',
                        text:'广西',
                        data:{},
                        closed:false,
                        children:[
                            {
                                id:'0101',
                                text:'南宁',                               
                                data:{}
                            },{
                                id:'0102',
                                text:'桂林',
                                data:{}
                            },{
                                id:'0103',
                                text:'柳州',
                                checked:true,
                                data:{}
                            }
                        ]
                    },{
                        id:'02',
                        text:'广东',
                        data:{},
                        closed:false,
                        children:[
                            {
                                id:'0201',
                                text:'广州',
                                data:{}
                            },{
                                id:'0202',
                                text:'东莞',                               
                                data:{}
                            },{
                                id:'0203',
                                text:'汕头',
                                checked:true,
                                data:{}
                            }
                        ]
                    }];


            new $B.Tree($("#userTree"),{
                checkbox:true,
                data:treeData
            });

            vm = $B.bindForm($("#myform"), userData, function (propObj, propName, newValue, oldValue) {
                var ms = "onChanged propName=" + propName + " newValue = " + newValue + "     ;   oldValue = " +   oldValue;
                console.log(ms);
                $("#message").prepend("<p style='border-bottom:1px dashed #666'>" + ms + "</p>");
            });
           
        }

        function mySubmit() {
            var formJson = vm.getJson();
            $("#message").prepend("<p style='border-bottom:1px dashed #666'>mv.getJson()提交：" + JSON.stringify(formJson) +"</p>");
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree',
                'combox': '../javascript/combox',
                'calendar':'../javascript/calendar',
                'mvvm': '../javascript/mvvm'
            }
        });
        var loadModel = ['$B', 'mvvm', 'utils','calendar', 'combox','tree'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
                // var $l = $('<label class="k_radio_label k_radio_anim"><input type="radio" name="sex" value="1" /><i class="k_radio_i"></i>男</label><label class="k_radio_label k_radio_anim"><input type="radio" name="sex" value="0" /><i class="k_radio_i"></i>女</label>');
                // $l.appendTo($("body"));
            });
        });
    </script>
</body>

</html> 