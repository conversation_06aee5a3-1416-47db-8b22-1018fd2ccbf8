<!DOCTYPE html>
<html>

<head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body>
        <style>
                body{
                         box-sizing: border-box;
                        -moz-box-sizing: border-box;
                        -webkit-box-sizing: border-box;
                     }
                     td.label{
                         width: 200px;
                     }
                     #message{
                         width: 100%;
                         height:20px;
                         line-height: 20px;                
                         overflow: hidden;
                     }
                     #message p{
                         padding: 0 6px;
                         border-bottom: 1px #cccccc solid;                 
                     }
                </style>
    <div id="mytemplete">
        <div id="message"></div>
        <div class="section-content">
            <h1 class="section-title">非表单标签绑定，将json数据（model）绑定到div、p等非表单标签上<span style="color:red">双向联动，解放表单读写开发！</span></h1>
            <div class="section-body clearfix">
                <h5>非表单标签绑定</h5>
                <div style="cursor:pointer;">p1属性值 ：{{this.data.p1}} </div>
                <div> p2属性值 ：{ {this.data.p2} } </div>
                <div> p3.name属性值 ：{{ this.data.p3.name }}</div>
                <div>{  {this.data.sum.a}} + {  {this.data.sum.b}  } = {{this.data.sum.a + this.data.sum.b}}</div>
                <div>{{this.data.sum.a}} - {{this.data.sum.b}} = {{this.data.sum.a - this.data.sum.b}} </div>
                <p style="{{ if(this.data.p1 * this.data.sum.b > 1000){ return 'color:red;font-weight:bold' ;} else {return '' ;} }}">
                    格式化修饰支持：{{this.data.p1}} * {{this.data.sum.b}} = {{this.data.p1 * this.data.sum.b}} 
                </p>
                <div>{{this.data.sum.a}} / {{this.data.sum.b}} = {{this.data.sum.a / this.data.sum.b}} </div>
                <div>{{this.data.sum.a}} % {{this.data.p2}} = {{this.data.sum.a % this.data.p2}} </div>
                <button onclick="modifyp3Name()">代码修改p3.name</button>
                <button onclick="modifyp1()">代码修改p1</button>
                <button onclick="modifyp2()">代码修改p2</button>
                <button onclick="modifyp_sum()">代码修改sum.a</button>
                <button onclick="m_sum()">代码修改sum对象</button>
            </div>
        </div>
        <div class="section-content">
            <h1 class="section-title">表单标签绑定，将json数据（model）绑定到input、radio等表单标签上<span style="color:red">双向联动，解放表单读写开发！</span></h1>
            <div class="section-body clearfix">
                <h5>表单标签绑定，developer：{{this.data.form.userName}}</h5>
                <table id="myform11" class="form_table">
                    <tr>
                        <td class="label">姓名：</td>
                        <td>
                            <input id="userName" type="text" title="{{this.data.form.userName}}" value="{{this.data.form.userName}}" />
                        </td>
                    </tr>
                    <tr>
                        <td class="label">密码：</td>
                        <td>
                            <input id="userPwd" type="password" value="{{this.data.form.userPwd}}" />
                        </td>
                    </tr>
                    <tr>
                        <td class="label">性别：</td>
                        <td>
                            男：<input type="radio" value="1" name="sex" checked="{{this.data.form.sex === 1 ? true : false}}" />
                            女：<input type="radio" value="0" name="sex" checked="{{this.data.form.sex === 0 ? true : false}}" />
                        </td>
                    </tr>
                    <tr>
                        <td class="label">爱好：</td>
                        <td>
                            跑步:<input type="checkbox" value="1" name="hobby" watcher="checkboxWather" express="{{this.checkboxExpress(this.data.form.hobby,el)}}" />&nbsp;&nbsp;
                            钓鱼:<input type="checkbox" value="2" name="hobby" watcher="checkboxWather" express="{{this.checkboxExpress(this.data.form.hobby,el)}}" />&nbsp;&nbsp;
                            编程:<input type="checkbox" value="3" name="hobby" watcher="checkboxWather" express="{{this.checkboxExpress(this.data.form.hobby,el)}}" />
                        </td>
                    </tr>
                    <tr>
                        <td class="label">性格：</td>
                        <td>
                            中性：<input type="radio" value="1" name="userAttr" checked="{{this.data.form.sex === 1 && this.data.form.flag === 1 ? true : false}}" />
                            温柔：<input type="radio" value="0" name="userAttr" checked="{{this.data.form.sex === 0 && this.data.form.flag === 1 ? true : false}}" />
                        </td>
                    </tr>

                    <tr>
                        <td class="label">户籍：</td>
                        <td>
                            <select id="userid">
                                <option value="1" selected="{{this.data.form.userid === 1 ? true:false}}">广西南宁</option>
                                <option value="2" selected="{{this.data.form.userid === 2 ? true:false}}">广西桂林</option>
                                <option value="3" selected="{{this.data.form.userid === 3 ? true:false}}">广西北海</option>
                            </select>
                            <span>《--相互联动--》</span>
                            <select>
                                <option value="1" selected="{{this.data.form.userid === 1 ? true:false}}">广西南宁</option>
                                <option value="2" selected="{{this.data.form.userid === 2 ? true:false}}">广西桂林</option>
                                <option value="3" selected="{{this.data.form.userid === 3 ? true:false}}">广西北海</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">介绍：</td>
                        <td>
                            <textarea id="userDesp">{{this.data.form.userDesp}}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">{{this.data.p1}} + {{this.data.p2}} = </td>
                        <td>
                            <input id="abc" readonly="readonly" value="{{this.data.p1 + this.data.p2}}" type="text" />
                        </td>
                    </tr>
                </table>
                <button onclick="modifyName()">修改用户名</button>
                <button onclick="modifySex()">修改性别</button>
                <button onclick="modifyUid()">修改户籍</button>
            </div>
        </div>
        <div class="section-content">
            <h1 class="section-title">自定义控件绑定，将json数据（model）绑定到自定义的ui控件上<span style="color:red">双向联动，解放表单读写开发！</span></h1>
            <div class="section-body clearfix">
                <h5>自定义控件的解析，监听(通过注入外部监听处理、表达式处理实现自定义控件的联动绑定)</h5>
                <table id="mydivform" class="form_table">
                    <tr id="diyCheckbox">
                        <td class="label">自定义控件CheckBox：</td>
                        <td>
                            <label class="k_checkbox_label k_checkbox_anim">
                                <input type="checkbox" name="regions" value="1" /><i class="k_checkbox_i"></i>南宁
                            </label>
                            <label class="k_checkbox_label k_checkbox_anim">
                                <input type="checkbox" name="regions" value="2" /><i class="k_checkbox_i"></i>桂林
                            </label>
                            <label class="k_checkbox_label k_checkbox_anim">
                                <input type="checkbox" name="regions" value="3" /><i class="k_checkbox_i"></i>钦州
                            </label>
                            <label class="k_checkbox_label k_checkbox_disabled" disabled="disabled">
                                <input type="checkbox" name="regions" value="4" disabled="disabled" /><i class="k_checkbox_i"></i>北海
                            </label>
                            <span class="selected_id"></span><a style="padding-left:30px;cursor:pointer;" onclick="selectAll()">全选</a><a
                                style="padding-left:30px;cursor:pointer;" onclick="unSelectAll()">全不选</a>
                        </td>
                    </tr>
                    <tr id="diyRadio">
                        <td class="label">自定义控件Radio：</td>
                        <td>
                            <label class="k_radio_label k_radio_anim">
                                <input type="radio" name="addrs" value="1" /><i class="k_radio_i"></i>金湖
                            </label>
                            <label class="k_radio_label k_radio_anim">
                                <input type="radio" name="addrs" value="2" /><i class="k_radio_i"></i>仙葫
                            </label>
                            <label class="k_radio_label k_radio_disabled">
                                <input type="radio" name="addrs" value="3" disabled="disabled" /><i class="k_radio_i"></i>南湖
                            </label>
                            <span class="selected_id"></span>
                        </td>
                    </tr>
                    <tr>
                        <td class="label">自定义combox控件：</td>
                        <td>
                            <input id="combox1" type="text" value="" watcher="comboxWatcher" express="{{this.comboxExpress(this.data.userSeletions,el)}}" />
                        </td>
                    </tr>
                    <tr>
                        <td class="label">当前选择：</td>
                        <td>{{this.data.userSeletions}}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>



    <script>
        function m_sum() {
            var rand = parseInt(Math.random() * 100);
            var rand1 = parseInt(Math.random() * 1000);
            options.data.sum = {
                a: rand,
                b: rand1
            };
        }
        function modifyp3Name() {
            var rand = parseInt(Math.random() * 100);
            options.data.p3.name = rand + "==kevin.huang " + (new Date());
        }
        function modifyp1() {
            var rand = parseInt(Math.random() * 100);
            options.data.p1 = rand;
        }
        function modifyp2() {
            var rand = parseInt(Math.random() * 100);
            options.data.p2 = rand;
        }
        function modifyp_sum() {
            var rand = parseInt(Math.random() * 1000);
            options.data.sum.a = rand;
            rand = parseInt(Math.random() * 100);
            options.data.sum.b = rand;
        }
    </script>
    <script>
        /*******对自定义checkbox ; radio进行绑定******/
        function unSelectAll() {
            options.data.regions = "";
        }
        function selectAll() {
            options.data.regions = "1;2;3;4";
        }

        function registeCheckbox(mvvm) {

            var wrap = $("#diyCheckbox");

            //设置表达式
            wrap.find(".k_checkbox_label").attr("watcher", "mycheckBoxWatcher").attr("express",
                "{{this.mycheckBoxExpress(this.data.regions,el)}}");
            wrap.find("span.selected_id").html("当前选择的id：{{this.data.regions}}");

            //先注册后编译
            //从watcher参数中得到对应的html标签，得到标签对应的json对象及绑定的属性
            mvvm.registeWatcher("mycheckBoxWatcher", function (watcher) {
                console.log("mycheckBoxWatcher >>>>>>>>>>>>>>>>>>>>>>>>>");
                var $el = watcher.$el;
                var propObject = watcher.propObjectArray[0];//绑定的json对象
                var propName = watcher.propPathArray[0];//绑定的属性
                $el.children("input").on("change", function () {//绑定标签事件，标签事件输入后更新propObject
                    var $input = $(this);
                    var propVal = propObject[propName];
                    var valArray = [];
                    if (propVal !== "") {
                        valArray = propVal.split(";");
                    }
                    var v = $input.val();
                    if ($input.prop("checked")) {
                        valArray.push(v);
                    } else {
                        var newArray = [];
                        for (var i = 0, len = valArray.length; i < len; ++i) {
                            if (v !== valArray[i]) {
                                newArray.push(valArray[i]);
                            }
                        }
                        valArray = newArray;
                    }
                    propObject[propName] = valArray.join(";");
                });
            });
            /**
             * 注册表达式解析处理
             * **/
            mvvm.registeExpress("mycheckBoxExpress", function (data, el) {
                console.log("mycheckBoxExpress 根据data 解析el表达式 >>>>>>>>>>>>>>>>>>>>>>>>>");
                var $el = $(el);
                $el.find("input[type=checkbox]").each(function () {
                    var box = $(this);
                    var v = box.val();
                    var isChecked = false;
                    if (data.indexOf(";") >= 0) {
                        var patt1 = new RegExp(";" + v + ";|;" + v + "|" + v + ";");
                        isChecked = patt1.test(data);
                    } else {
                        isChecked = data === v;
                    }
                    if (isChecked) {
                        box.attr("checked", "checked").prop("checked", true);
                    } else {
                        box.removeAttr("checked").prop("checked", false);
                    }
                });
            });
            mvvm.compile(wrap);

        }

        function registeRadio(mvvm) {
            var wrap = $("#diyRadio");
            wrap.find(".k_radio_label").attr("watcher", "myRadioWatcher").attr("express",
                "{{this.myRadioExpress(this.data.addrs,el)}}");
            wrap.find("span.selected_id").html("当前选择的id：{{this.data.addrs}}");

            //先注册后编译
            mvvm.registeWatcher("myRadioWatcher", function (watcher) {
                console.log("myRadioWatcher 绑定UI事件，修改属性值>>>>>>>>>>>>>>>>>>>>>>>>>");
                var $el = watcher.$el;
                var propObject = watcher.propObjectArray[0];
                var propName = watcher.propPathArray[0];
                $el.children("input").on("change", function () {
                    var v = $(this).val();
                    var oldValue = propObject[propName];
                    var isNumeric = $.isNumeric(oldValue);
                    if (isNumeric) {
                        propObject[propName] = parseInt(v);
                    } else {
                        propObject[propName] = v;
                    }
                });

            }).registeExpress("myRadioExpress", function (data, el) {
                console.log("myRadioExpress 根据data 解析el表达式  根据属性值data修改ui状态>>>>>>>>>>>>>>>>>>>>>>>>>");
                var $el = $(el);
                $el.find("input[type=radio]").each(function () {
                    var box = $(this);
                    var v = box.val();
                    if (v === (data + "")) {
                        box.attr("checked", "checked").prop("checked", true);
                    } else {
                        box.removeAttr("checked").prop("checked", false);
                    }
                });
            }).compile(wrap);
        }
        /*****对自定义的combox下拉框进行双向绑定*****/
        var c1;
        function createCombox() {
            var treeData = getTreeData(3, 4);
            var args = {
                data: treeData, //数据 参考树数据
                default: {
                    "id": '',
                    "text": "请您选择"
                },
                placeholder: '请您选择', //默认选择项目
                mutilchecked: true, //是否多选
                checkfather: false, // 单选的时候，是否可以选择父节点
                readonly: true, //不可以编辑                
                plainStyle: false, //\"true\" 为简单无图标样式
                textField: 'text', //菜单名称字段，默认为text
                idField: 'id', //菜单id字段,默认为id
                onCheck: function (data, params, checked) {
                    var ids = this.getCheckedIds();
                    var wather = this.wather;
                    var userSelIds = [];
                    for (var i = 0, len = ids.length; i < len; ++i) {
                        userSelIds.push(ids[i].id);
                    }
                    //通过wather更新userSeletions属性
                    wather.propObjectArray[0][wather.propPathArray[0]] = userSelIds.join("、");
                    console.log("根据选择的数据通知wather，更新数据属性");
                }
            };
            c1 = new $B.Combox($("#combox1"), args);
            /******
             * 利用options.registeWatchers 、options.registeExpress注册自定义的监听器，自定义的表达式解析处理
             * ******/
            //注册combox实例到mvvm上，在comboxWather、comboxExpress中可以使用this.options.combox 即可得到
            options.combox = c1;
            //注册标签上的watcher属性comboxWatcher到registerWatchers上
            options.registeWatchers = {
                comboxWatcher: function (wather) {
                    var combox = this.options.combox;
                    combox.wather = wather; //将监听器注册到combox上
                    var ids = combox.getCheckedIds();
                    var userSelIds = [];
                    for (var i = 0, len = ids.length; i < len; ++i) {
                        userSelIds.push(ids[i].id);
                    }
                    //通过wather更新userSeletions属性
                    wather.propObjectArray[0][wather.propPathArray[0]] = userSelIds.join("、");
                    console.log("comboxWatcher >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> comboxWatcher");
                }
            };
            options.registeExpress = {
                comboxExpress: function (data, el) { //通过数据反向修改combox，这里不做处理
                    var combox = this.options.combox;
                    if (combox) { //根据数据修改combox中的复选

                    }
                    console.log("comboxExpress >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> comboxExpress");
                }
            };
        }
    </script>

    <script>
        function modifyName() {
            var rand = parseInt(Math.random() * 100);
            options.data.form.userName = "kevin-" + rand;
        }

        function modifySex() {
            if (options.data.form.sex === 1) {
                options.data.form.sex = 0;
            } else {
                options.data.form.sex = 1;
            }
        }

        function modifyUid() {
            if (options.data.form.userid === 2) {
                options.data.form.userid = 1;
            } else {
                options.data.form.userid = 2;
            }
        }
    </script>


    <script type="text/javascript">
        var mvm, options;
        var $message;
        function pageLoaded() {
            $message = $("#message");
            options = {
                el: "mytemplete",
                data: {
                    userSeletions: "",
                    regions: '2;3',
                    addrs: 2,
                    form: {
                        userName: 'kevin.huang',
                        userPwd: '888888',
                        sex: 1,
                        hobby: '1,3',
                        userid: 2,
                        userAddr: 222,
                        userDesp: '锻炼好身体、学习好技能，保持一颗年轻的心！',
                        flag: 1,
                        extProp: {
                            test: 1111,
                            myprop: {
                                b: 12,
                                c: {
                                    v: 1
                                }
                            }
                        },
                        extProp2: {
                            test2: 1111,
                            pp: {
                                a: 122
                            }
                        }
                    },
                    p1: 123,
                    p2: 456,
                    sum: {
                        a: 888,
                        b: 999,
                    },
                    p3: {
                        name: 'kevin'
                    }
                },
                onChanged: function (propObj, propName, newValue, oldValue) {
                    //console.log("onChanged : 属性【" + propName + "】   newValue=" + newValue + "  oldValue=" + oldValue);
                    $message.html("<p>属性<span style='color:red'>" + propName + "发生变化，新值:" + newValue + ",旧值:" + oldValue + "</span></p>");
                    console.log("当前JSON:" + JSON.stringify(this.getJson()));
                }
            };
            //创建前注入自定义监听、表达式解析处理
            createCombox();
            var s = new Date();
            mvm = new $B.Mvvm(options);
            //创建后注入自定义监听、表达式解析处理
            registeCheckbox(mvm);
            registeRadio(mvm);
            var e = new Date();
            var diff = e - s;
            console.log("耗时：" + diff);
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree',
                'combox': '../javascript/combox',
                'mvvm': '../javascript/mvvm'
            }
        });

        var loadModel = ['mvvm', '$B', 'combox'];
        require(loadModel, function () {
            pageLoaded();
        });
    </script>
</body>

</html> 