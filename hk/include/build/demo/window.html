 <!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

    <style>
        body{
             box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
         }
    </style>
</head> 

<body>
    <div style="width:100%;margin:0 auto; ">
        <div>
            <input type="button" class="button-add" value="打开静态内容窗口" onclick="window1()" />
            <input type="button" class="button-add" value="打开远程内容窗口" onclick="window0()" />
            <input type="button" class="button-delete" value="打开确认窗口" onclick="window2()" />
            <input type="button" class="button-edit" value="打开信息窗口" onclick="window3()" />
            <input type="button" class="button-xls" value="打开错误窗口" onclick="window4()" />
            <input type="button" class="button-xls" id="successBtn" value="打开成功窗口" onclick="windowSuccess()" />
            <input type="button" class="button-xls" value="打开警告窗口" onclick="window5()" />
            <input type="button" class="button-xls" value="打开顶部提示窗口" onclick="window6('top')" />
            <input type="button" class="button-xls" value="打开右下角提示窗口" onclick="window6('bottom')" />
        </div>
    </div>

    <script type="text/javascript">
        var $toolbar, $, win, $resizeObj, $target;

        function pageLoaded() {
            $("#successBtn").trigger("click");
        }

        function window6(pos) {
            var icon = 'fa-volume-control-phone';
            if (pos === "top") {
                icon = "fa-wechat";
            }
            $B.window({
                width: 400,
                height: 200,
                iconCls: icon,
                title: '操作提示', //标题
                shadow: true, //是否需要阴影
                timeout: 2, //5秒钟后自动关闭，0表示不自动关闭
                mask: false, //是否需要遮罩层
                radius: true, //是否圆角
                header: true, //是否显示头部
                position: pos, // top :顶部中间，bottom：右下角
                content: '<p>您的操作成功了</p>',
                dataType: 'iframe', //当为url请求时，html/json/iframe
                //draggableHandler:'body',//拖动触发焦点
                draggable: true, //是否可以拖动
                //moveProxy:true,//代理拖动
                closeable: true, //是否关闭                   
                maxminable: false, //可变化小大
                collapseable: false, //上下收缩                   
                onClose: function () { //关闭前
                    console.log("可以关闭了..................");
                    return true;
                },
                onClosed: function () { //关闭后
                    console.log("window关闭了..................");
                }
            });
        }
        var win110;

        function window0() {
            if (win110) {
                win110.show();
                win110.setTitle("我是重新显示的窗口");
            } else {
                win110 = $B.window({
                    width: 500,
                    height: 200,
                    //full:true,//占满父容器
                    title: '普通窗口', //标题
                    closeType: 'hide', //可重新打开
                    shadow: true, //是否需要阴影
                    timeout: 0, //5秒钟后自动关闭，0表示不自动关闭
                    mask: true, //是否需要遮罩层
                    radius: true, //是否圆角
                    header: true, //是否显示头部
                    url: 'test.html',
                    dataType: 'iframe', //当为url请求时，html/json/iframe
                    //draggableHandler:'body',//拖动触发焦点
                    draggable: true, //是否可以拖动
                    //moveProxy:true,//代理拖动
                    closeable: true, //是否关闭                   
                    maxminable: true, //可变化小大
                    collapseable: true, //上下收缩
                    onResized: function () { //大小变化事件
                        console.log(" onResized  onResized  onResized");
                    },
                    onClose: function () { //关闭前
                        console.log("可以关闭了..................");
                        return true;
                    },
                    onClosed: function () { //关闭后
                        console.log("window关闭了..................");
                    }
                });
            }

        }


        function window1() {
            win = $B.window({
                width: 500,
                height: 200,
                title: '普通窗口', //标题
                shadow: true, //是否需要阴影
                timeout: 0, //5秒钟后自动关闭
                mask: true, //是否需要遮罩层
                radius: false, //是否圆角
                header: true, //是否显示头部
                content: '<div><p>静态内容窗口静态内容窗口静态内容窗口静态内容窗口静态内容窗口静态内容窗口</p><p>静态内容窗口静态内容窗口静态内容窗口静态内容窗口静态内容窗口</p></div>',
                //draggableHandler:'body',//拖动触发焦点
                draggable: true, //是否可以拖动
                //moveProxy:true,//代理拖动
                closeable: true, //是否关闭                   
                maxminable: true, //可变化小大
                collapseable: true, //上下收缩
                onResized: function () { //大小变化事件
                    console.log(" onResized  onResized  onResized");
                },
                onClose: function () { //关闭前
                    console.log("可以关闭了..................");
                    return true;
                },
                onClosed: function () { //关闭后
                    console.log("window关闭了..................");
                },
                toolbar: { //工具栏对象参考工具栏组件c
                    align: 'center', //对齐方式，默认是left 、center、right
                    style: 'normal', // plain / min  / normal /  big
                    showText: true, // min 类型可以设置是否显示文字
                    buttons: [{
                        iconCls: 'fa-ok-circled',
                        color: '#7676A2',
                        disabled: false, //是否禁用
                        text: '关闭', //文本
                        click: function (pr) { //点击事件，如果存在click，则字符串形式的handler不可用
                            win.close();
                        }
                    }, {
                        iconCls: 'fa-cancel-circled2',
                        color: '#7676A2',
                        text: '取消', //文本
                        click: function (pr) { //点击事件，如果存在click，则字符串形式的handler不可用
                            win.close();
                        }
                    }]
                }
            });
        }

        function window2() {
            $B.confirm({
                //position: 'top', // top :顶部中间，bottom：右下角       
                //message:'<span style="color:red;">您真的要删除数据吗？</span>',
                message: '您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？您真的要删除数据吗？',
                okFn: function () {
                    alert("ok");
                },
                noFn: function () {
                    alert("no");
                }
            });
        }

        function window3() {
            $B.message({
                title: '提示！',
                position: 'top', // top :顶部中间，bottom：右下角
                message: '提示信息或者创建html内容的函数！', //提示信息或者创建html内容的函数                
                width: 500, //宽度
                height: 150, //高度         
                timeout: 0,
                mask: true
            });
        }

        function window4() {
            $B.error("这是个美丽的错误！");
            // $B.error({
            //         width: 300,
            //         height: 120,
            //         title:'操作异常！',
            //         position: 'top', // top :顶部中间，bottom：右下角
            //         message:'这是个美丽的错误！', //提示信息或者创建html内容的函数 
            //         timeout:3,
            //         mask:false
            // });
        }

        function window5() {
            $B.alert("这是个温馨的警告信息！");
            // $B.alert({
            //         width: 300,
            //         height: 120,
            //         title:'警告信息！',
            //         position: 'bottom', // top :顶部中间，bottom：右下角
            //         message:'这是个警告信息信息！', //提示信息或者创建html内容的函数 
            //         timeout:2,
            //         mask:true
            // });
        }

        function windowSuccess() {
            //$B.success("这是个成功信息！");
            $B.success({
                width: 300,
                height: 120,
                title: '操作成功！',
                position: 'top', // top :顶部中间，bottom：右下角
                message: '这是个成功信息！', //提示信息或者创建html内容的函数 
                timeout: 2,
                mask: true
            });
        }
        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'resize': '../javascript/resize'
            }
        });
        var loadModel = ['utils', 'resize'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</div>
</html> 