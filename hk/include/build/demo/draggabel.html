<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

    <style>
        body {
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
        }
        .ap {
            line-height: 200px;
        }
    </style>

</head>

<body >
    <p id="message" style="width:850px;line-height: 24px;min-height: 30px;width: 100%;word-break: break-all;">

    </p>
    <p id="message1" style="width:850px;line-height: 24px;min-height: 30px;border-top: 1px solid #ccc;width: 100%;word-break: break-all;">

    </p>
    <div style="margin: 12px 0px;">
        <h2>一个元素绑定多个拖动实现，动态控制某个拖动不可用</h2>
        拖动：
        <select id="clazz_opts">
            <option value="draggable">draggable</option>
            <option value="draggablens">draggablens</option>
        </select>
        <button id="unbind">解除拖动</button>
        <button id="bind">绑定拖动</button>
        旋转角度：
        <select id="transformRotate">
            <option value="0">0</option>
            <option value="60">60</option>
            <option value="80">80</option>
            <option value="90">90</option>
            <option value="170">170</option>
            <option value="220">220</option>
            <option value="180">180</option>
            <option value="270">270</option>
        </select>
    </div>
    <!-- transform: rotate(90deg);-->

    <div id="dragdiv" style="width:350px;height:120px;background: #ccc;position: absolute; top:200px;left:50px;">
        <h1 id="title" style="line-height:22px;background-color: cadetblue;color:#fff;">我是可拖动的元素！！！！！！</h1>
        <p>任意区域可响应拖动</p>
        <div style="margin:0px auto;width: 80%;color:red;font-size: 20px" id="_angel"></div>
    </div>

    <div id="dragTitle" style="width:300px;height:150px;background: #ccc;position: absolute; top:300px;left:400px;">
        <h1 id="titleHandler" style="line-height:22px;background-color: cadetblue;color:#fff;">只有标题区域响应拖动</h1>
        <p>只有标题区域响应拖动</p>
    </div>

    <div id="dragProxy" style="width:300px;height:150px;background: #ccc;position: absolute; top:500px;left:200px;">
        <h1 style="line-height:22px;background-color: cadetblue;color:#fff;">代理移动</h1>
        <p>代理移动，利用一个空元素代理，避免内容庞大时候，拖动引起的浏览器大量计算！</p>
    </div>


    <div id="vdrag" style="width:100px;height:100px;background: #ccc;position: absolute; top:100px;left:550px;">
        <h1 style="line-height:22px;background-color: cadetblue;color:#fff;">只能水平移动</h1>
        <p>只能水平移动</p>
    </div>



    <div id="Tdrag" style="width:120px;height:150px;background: #ccc;position: absolute; top:500px;left:550px;">
        <h1 style="line-height:22px;background-color: #000000;color:#fff;">按下5秒才拖动</h1>
        <p style="color:red;" id="timer">倒计时:5</p>
    </div>

    <script type="text/javascript">
        var drag;

        function pageLoaded() {
            var $msg = $("#message");
            var $msg1 = $("#message1");
            var maxTop;
            var maxLeft;
            var opt1 = {
                nameSpace: 'draggable', //命名空间，一个对象可以绑定多种拖动方式
                which: undefined, //鼠标键码，是否左键1,右键3 才能触发拖动，默认左右键均可
                isProxy: false, //是否空代理
                holdTime: undefined, //按下鼠标500毫秒后才可以拖动
                handler: undefined, //触发拖动的对象
                cursor: 'move',
                disabled: false, //是否禁用拖动
                axis: undefined, // v or h  水平还是垂直方向拖动 ，默认全向
                onDragReady: function () { //鼠标按下时候的事件，返回true则往下执行，返回false则停止
                    console.log("onDragReady >>>>>>>>>>>>>>> return true");
                    $msg1.html("onDragReady return true");
                    return true;
                },
                onMouseUp: function (args) {
                    var state = args.state;
                    $msg1.html("没有发生拖动，触发onMouseUp事件！！！");
                    console.log("onMouseUp >>>>>>>>>>>>>>> " + JSON.stringify(state._data));
                },
                onStartDrag: function (args) { //开始拖动
                    var state = args.state;
                    var txt = ">>>>>>onStartDrag nameSpace = " + state.options.nameSpace + ":<br/> data=" +
                        JSON.stringify(state._data);
                    console.log(txt);
                    $msg1.html(txt);
                    var bodyWidth = $("body").width();
                    var bodyHeight = $("body").height();
                    maxTop = bodyHeight - state._data.height;
                    maxLeft = bodyWidth - state._data.width;
                },
                onDrag: function (args) { //拖动中
                    var state = args.state;
                    var data = state._data;
                    //拖动限制
                    if (data.top < 0) {
                        data.top = 0;
                    }
                    if (data.left < 0) {
                        data.left = 0;
                    }
                    if (data.top > maxTop) {
                        data.top = maxTop;
                    }
                    if (data.left > maxLeft) {
                        data.left = maxLeft;
                    }
                    var txt = "onDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(state._data);
                   // console.log(txt);
                    $msg1.html(txt);
                },
                onStopDrag: function (args) { //拖动结束
                    var state = args.state;
                    var txt = "onStopDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(state._data);
                    console.log(txt);
                    $msg1.html(txt);
                }
            };
            //绑定拖动1
            drag = $("#dragdiv").draggable(opt1);

            $("#transformRotate").on("change",function(){
                var r = $(this).val();
                drag.css("transform","rotate("+r+"deg)");
                console.log(JSON.stringify(drag.position() ) );
                drag.find("#_angel").html("当前旋转角度："+r);
            });

            var opt2 = {
                nameSpace: 'draggablens', //命名空间，一个对象可以绑定多种拖动方式
                which: undefined, //鼠标键码，是否左键,右键 才能触发拖动，默认左右键均可
                isProxy: false, //是否空代理
                handler: undefined, //触发拖动的对象
                disabled: false, //是否禁用拖动
                axis: undefined, // v or h  水平还是垂直方向拖动 ，默认全向
                onDragReady: undefined, //鼠标按下时候的事件，返回true则往下执行，返回false则停止
                onStartDrag: function (e) { //开始拖动
                    var state = e.state;
                    var txt = " onStartDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onDrag: function (e) { //拖动中
                    var state = e.state;
                    var txt = "onDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onStopDrag: function (e) { //拖动结束
                    var state = e.state;
                    var txt = "onStopDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                }
            };
            //绑定拖动2
            /*******/
            drag.draggable(opt2);

            //重新绑定
            $("#bind").click(function () {
                var ns = $("#clazz_opts").val();
                if (ns === "draggablens") {
                    drag.draggable(opt2);
                } else {
                    drag.draggable(opt1);
                }
            });
            //解除绑定
            $("#unbind").click(function () {
                var ns = $("#clazz_opts").val();
                drag.draggable("unbind", ns);
            });


           var dragTran = $("#dragTitle").draggable({
                handler: "#titleHandler", //触发拖动的对象 
                onStartDrag: function (e) { //开始拖动
                    var state = e.state;
                    var txt = " onStartDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onDrag: function (e) { //拖动中
                    var state = e.state;
                    var txt = "onDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onStopDrag: function (e) { //拖动结束
                    var state = e.state;
                    var txt = "onStopDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                }
            });
            
         


            $("#dragProxy").draggable({
                isProxy: true, //触发拖动的对象 
                onStartDrag: function (e) { //开始拖动
                    var state = e.state;
                    var txt = " onStartDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onDrag: function (e) { //拖动中
                    var state = e.state;
                    var txt = "onDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onStopDrag: function (e) { //拖动结束
                    var state = e.state;
                    var txt = "onStopDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                }
            });

            $("#vdrag").draggable({
                axis: "h", //只能水平移动
                onStartDrag: function (e) { //开始拖动
                    var state = e.state;
                    var txt = " onStartDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onDrag: function (e) { //拖动中
                    var state = e.state;
                    var txt = "onDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onStopDrag: function (e) { //拖动结束
                    var state = e.state;
                    var txt = "onStopDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onMouseUp: function (args) {
                    console.log("只能水平移动 只能水平移动onMouseUp >>>>>>>>>>>>>>>>>>>>>>>>>>>>> onMouseUp ");
                }
            });
            var tdarg = $("#Tdrag"); //
            var timer = tdarg.find("#timer");
            var timerIvt;
            tdarg.draggable({
                holdTime: 5000, //按下5秒才能拖动
                onDragReady: function () {
                    clearInterval(timerIvt);
                    var i = 5;
                    timerIvt = setInterval(function () {
                        timer.html("倒计时:" + i);
                        i--;
                        if (i < 0) {
                            clearInterval(timerIvt);
                        }
                    }, 1000);
                    return true;
                },
                onStartDrag: function (e) { //开始拖动
                    var state = e.state;
                    var txt = " onStartDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onDrag: function (e) { //拖动中
                    var state = e.state;
                    var txt = "onDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                },
                onStopDrag: function (e) { //拖动结束
                    var state = e.state;
                    var txt = "onStopDrag nameSpace = " + state.options.nameSpace + ":<br/> " + JSON.stringify(
                        state._data);
                    console.log(txt);
                    $msg.html(txt);
                    timer.html("倒计时:5");
                    clearInterval(timerIvt);
                },
                onMouseUp: function (args) {
                    timer.html("倒计时:5");
                    clearInterval(timerIvt);
                    console.log("onMouseUp >>>>>>>>>>>>>>>>>>>>>>>>>>>>> onMouseUp ");
                }
            });
        }
 
        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'plugin': '../javascript/plugin',
                '$B': '../javascript/basic',               
                'panel': '../javascript/panel',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils'
            }
        });
        var loadModel = ['plugin','$B'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</body>

 </html>