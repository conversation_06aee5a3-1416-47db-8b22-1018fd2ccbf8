<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

    <style type="stylesheet">
        body>div{ width: 100%; } #buttondiv{ height: 50px; margin-bottom:20px; }
    </style>

</head> 

<body id="_mmain">
        
    <div id="buttondiv">
        <button onClick="getPage()">获取当前页</button>
        <button onClick="update()">更新</button>
    </div>
    <div style="height:30px">
        <p id="msg"></p>
    </div>
    <div id="pgdiv"></div>

    <script type="text/javascript">
        var pg;
        function pageLoaded() {
            var msg = $("#msg");
            var opts = {
                total: 1980, //总数量
                pageSize: 20, //页大小
                buttons: 10, //页按钮数量
                position: 'center',
                summary: true,
                onClick: function (p, ps, startpg) {
                    console.log("您点击了第" + p + "页！当前页大小" + ps + ";开始页=" + startpg);
                    msg.html("您点击了第" + p + "页！当前页大小" + ps + ";开始页=" + startpg);
                    pg.update({
                        total: 1010,
                        startpg: startpg,
                        page: p
                    });
                }
            };
            pg = new $B.Pagination($("#pgdiv"), opts);
        }

        function getPage() {
            alert(pg.getCurPage());
        }

        function update() {
            pg.update({
                total: 1010
            });
        }
        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'plugin': '../javascript/plugin',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'validate': '../javascript/validate',
                'pagination': '../javascript/pagination'
            }
        });

        var loadModel = ['pagination'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</body>
 
</html>