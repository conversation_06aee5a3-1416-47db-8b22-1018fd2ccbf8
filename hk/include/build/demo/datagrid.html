<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

    <style>
        body {
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
        }

        #wrap {
            width: 90%;
            height: 100%;
            margin: 0 auto;
            position: relative;
        }

        #centerwrap {
            position: absolute;
            bottom: 150px;
            left: 300px;
        }
   
    </style>
</head>

<body>

    <div class="section-content">
        <h1 class="section-title">复杂表头/工具栏/分页/异步AJAX/行、单元格格式化--demo</h1>
        <div style="height:500px" class="section-body clearfix">
            <table id="datagrid"></table>
        </div>
    </div>

    <div class="section-content">
        <h1 class="section-title">静态数据+上下分页栏，自定义分割线--demo</h1>
        <div style="height:500px" class="section-body clearfix">
            <table id="datagrid1"></table>
        </div>
    </div>

    <script type="text/javascript">
        var dg;
        var cols = [
            [{
                    "title": "标题3标题3标题3",
                    "colspan": 3
                },
                {
                    "title": "标题1标题1标题1",
                    "colspan": 2,
                    "rowspan": 2
                },
                {
                    "title": "字段标题",
                    "colspan": 1,
                    "rowspan": 3,
                    "field": "createTime"
                }
            ],
            [{
                    "title": "标题",
                    "colspan": 2
                },
                {
                    "title": "标题2",
                    "colspan": 1,
                    "rowspan": 2,
                    "width": 100,
                    "sortable": true
                },
                {
                    "title": "标题1",
                    "colspan": 4
                }
                // ,
                // {
                //     "title": "字段标题",
                //     "colspan": 1,
                //     "field": "createTime"
                // }
            ],
            [{
                    "title": "菜单名称",
                    "field": "menuName",
                    "width": 200,
                    "align": "left",
                    "sortable": false,
                    "formatter": ""
                },
                {
                    "title": "行为",
                    "field": "content",
                    "width": "100px",
                    "align": "left",
                    "sortable": true,
                    "formatter": ""
                },
                {
                    "title": "操作人",
                    "field": "createUser",
                    "width": "100px",
                    "align": "center",
                    "sortable": true,
                    "formatter": "formatfn1"
                },
                // {
                //     "title": "IP",
                //     "field": "operateip",
                //     "width": "auto",
                //     "align": "center",
                //     "minWidth":100,
                //     "sortable": true,
                //     "formatter": ""
                // },
                // {
                //     "title": "时间",
                //     "field": "createTime",
                //     "minWidth": "90px",
                //     "align": "center",
                //     "sortable": true,
                //     "formatter": ""
                // },
                {
                    "title": "状态",
                    "field": "status",
                    "minWidth": "100px",
                    "align": "center",
                    "sortable": false,
                    "formatter": "formatfn"
                },
                {
                    "title": "数据3",
                    "field": "menuName",
                    "minWidth": "120px",
                    "align": "center",
                    "sortable": true,
                    "formatter": ""
                },
                {
                    "title": "字段标题",
                    "field": "createTime",
                    "width": "auto",
                    "align": "center"
                }
            ]
        ];
        var childrenBtns = [{
                "id": "line_btn_0",
                "text": "删除",
                "iconCls": "fa-trash",
                "params": {
                    "cmd": "delete",
                    "privilage": "1"
                },
                "visualable": true,
                "disabled": false,
                "click": function (prs) {
                    alert(JSON.stringify(prs));
                }
            },
            {
                "id": "line_btn_1",
                "text": "更新",
                "color": "",
                "methodsObject": "toolMethods",
                "params": {
                    "cmd": "update",
                    "privilage": "1"
                },
                "visualable": true,
                "disabled": false,
                "iconCls": "fa-doc-text"
            },
            {
                "id": "line_btn_2",
                "text": "管理子菜单",
                "methodsObject": "toolMethods",
                "params": {
                    "cmd": "update",
                    "privilage": "1"
                },
                "visualable": true,
                "disabled": false,
                "iconCls": "fa-share-2"
            }
        ];

        var groupBTs = [
            [{
                "text": "重新加载",
                "iconCls": "fa-search",
                "click": function (prs) {
                    dg.reload({
                        q: 123
                    });
                }
            }, {
                "text": "获取数据",
                "iconCls": "fa-file-word",
                "click": function (prs) {
                    alert(JSON.stringify(dg.getData()));
                }
            }],
            [{
                "text": "刷新当前页",
                "iconCls": "fa-arrows-ccw",
                "click": function (prs) {
                    dg.refresh();
                }
            }, {
                "text": "删除",
                "iconCls": "fa-cancel-2",
                "click": function (prs) {
                    alert("click2");
                }
            }],
            [{
                "text": "获取勾选的数据",
                "iconCls": "fa-ok-circled",
                "click": function (prs) {
                    var data = dg.getCheckedData();
                    alert(JSON.stringify(data));
                }
            }, {
                "text": "获取勾选的id集合",
                "iconCls": "fa-cancel-2",
                "click": function (prs) {
                    var data = dg.getCheckedId();
                    alert(JSON.stringify(data));
                }
            }],
            [{
                "text": "其他",
                "iconCls": "fa-down",
                "childrens": childrenBtns
            }]
        ];

        function pageLoaded() {
            var args = {
                // data: treeData, // data,  treeData
                cols: cols, // treeCol cols
                isTree: false,
                title: "表格标题",
                methodsObject: "toolMethods",
                loadImd: true, //是否立即加载
                pageSize: 10,
                pgBtnNum: 5,
                fillParent: true,
                oprCol: true, //是否需要操作列
                oprColWidth: 200, //定义操作列宽
                url: "/bui/api/json?flag=datagrid", // flag=datagrid datagridTree
                checkBox: true, //是否需要复选框               
                idField: 'id', //id字段名称        
                toolbar: groupBTs, //toolbars
                toolbarOpts: {
                    style: 'min', //工具栏按钮样式
                    color: '#EDEDED',
                    iconColor: '#BCB9C9',
                    fontColor: '#666666'
                },
                pgposition: "bottom", //both bottom top
                iconCls: 'fa-table',
                btnStyle: 'plain', //plain                
                showBtnText: true,
                sortField: {
                    'order_index': 'desc',
                    "fieldName": 'asc'
                }, //默认排序字段
                setParams: function () { //设置查询时候附加的参数
                    return {
                        "p": "p1"
                    };
                },
                onDbClickRow: function () { //双击一行时触发 fn(rowData)
                    alert("onDbClickRow " + JSON.stringify(this.data("data")));
                },
                onClickCell: function (field, value) { //单击一个单元格时触发fn(field, value)
                    alert("onClickCell " + field + "  " + value);
                },
                onCheck: function (isChked, data) { //复选事件 
                    alert("onCheck " + isChked + " data=" + JSON.stringify(data));
                },
                onLoaded: function (data) { //加载完成回调
                    //console.log("onLoaded" + JSON.stringify(data));
                },
                onRowRender: function (data, rowIdx) { //行渲染事件
                    if (data.menuName.indexOf("0") > 0) {
                        this.children().children().css({
                            "font-weight": "bold",
                            "color": "#D1A700"
                        });
                    }
                }
            };
            dg = new $B.Datagrid($("#datagrid"), args);
            args = {
                data: data,
                oprCol: false,
                pageSize: 10,
                url: "/bui/api/json?flag=datagrid", // flag=datagrid datagridTree
                splitColLine: 'k_datagrid_td_h_line', //是否需要td列分割线 k_datagrid_td_v_line k_datagrid_td_h_line k_datagrid_td_none_line
                pgposition: "both", //both bottom top
                cols: cols[2], // treeCol cols
                checkBox: false
            };
            console.log("static table>>>>>>>>>>>>>>>");
            new $B.Datagrid($("#datagrid1"), args);

        };

        function _fclick() {
            alert("格式化单元格事件！");
            return false;
        }

        function formatfn(cellData, rowData, field) {
            if (field === "status") {
                if(cellData === 1){
                    return "<a onclick='_fclick()' ><i style='color:#008B1F' class='fa fa-ok-circled'></i></a>";
                }else{
                    return "<a onclick='_fclick()' ><i style='color:red' class='fa fa-cancel-2'></i></a>";
                }
                
            }
            // if (cellData === 1 || cellData === "1") {
            //     return "ok";
            // }
            // return "no"
        }
        window.toolMethods = {
            lineTestFn: function (prs) {
                //alert("lineDetailFn " + JSON.stringify(prs));
                $B.window({
                    width:600,
                    height:300,
                    content:JSON.stringify(prs)
                });
            },
            lineDleteFn: function (prs) {
                $B.confirm({
                    message :'确认删除？',
                    okFn:function(){
                        alert("lineDleteFn " + JSON.stringify(prs));
                    }
                });
                
            },
            lineDetailFn: function (prs) {
                dg.openInner(this, {
                    content: 'test.html',
                    type: 'iframe', //如果是url请求的时候，type=html/iframe,
                    onLoaded: function () { //如果是url加载，会触发加载完成事件
                        console.log("loading is ok");
                    }
                });
            }
        };

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'tree': '../javascript/tree',
                'pagination': '../javascript/pagination',
                'datagrid': '../javascript/datagrid'
            }
        });

        var loadModel = ['datagrid'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</body>

 </html>