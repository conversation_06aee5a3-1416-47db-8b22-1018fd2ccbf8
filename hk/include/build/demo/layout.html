<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>


    <style>
        div.k_tree_clicked_cls {
            background-color: #2F4050;
            color: #ffffff;
        }

        div.k_tree_clicked_cls div {
            color: #ffffff;
        }
    </style>

</head>

<body>

    <script type="text/javascript">
        function pageLoaded() {

            var treeUl = $("<ul />");

            var layout = new $B.Layout($("body"), {
                onCreated: function (params) { //function(){} 布局创建完成事件,
                    console.log("onCreated " + JSON.stringify(params));
                },
                onLoaded: function (data, params) { //加载完成
                    console.log("onLoaded data = " + JSON.stringify(data) + " params=" + JSON.stringify(
                        params));
                },
                items: [ //参考items说明
                    {
                        width: 230,
                        title: '系统面板', //标题
                        expandable: true,
                        iconCls: 'fa-cog-alt', //样式
                        content: treeUl
                    }, {
                        header: true, //是否需要标题栏
                        iconCls: 'fa-file-code',
                        width: 'auto', //宽度
                        title: 'tab模块',
                        content: 'test.html',
                        dataType: 'iframe'
                    }, {
                        width: 200,
                        title: '系统面板', //标题
                        expandable: false,
                        iconCls: 'fa-cubes', //样式
                        content: 'fragment.html',
                        dataType: 'html',
                        toolbar: { //工具栏对象参考工具栏组件c
                            align: 'center', //对齐方式，默认是left 、center、right
                            style: 'min', // plain / min  / normal /  big
                            showText: true, // min 类型可以设置是否显示文字
                            buttons: [{
                                iconCls: 'fa-check',
                                text: '测试', //文本
                                click: function (pr) { //点击事件，如果存在click，则字符串形式的handler不可用
                                    alert(JSON.stringify(pr));
                                }
                            }, {
                                iconCls: 'fa-cancel-2',
                                text: '按钮2', //文本
                                handler: 'test', //window.methodsObject对象中 test
                                methodsObject: 'methodsObject'
                            }]
                        }
                    }
                ]
            });

            tree = new $B.Tree(treeUl, {
                data: getTreeData(3, 3), //'数据'
                nodeParentIcon: 'fa-folder-empty', //父节点图标关闭状态
                nodeParentOpenIcon: 'fa-folder-open-empty', //打开状态图标
                leafNodeIcon: 'fa-doc', //子节点图标 
                chkEmptyIcon: ' fa-check-empty', //不选
                chkAllIcon: ' fa-check', //全选
                chkSomeIcon: 'fa-ok-squared', //部分选 
                url: "/bui/api/json?flag=tree",
                checkbox: false,
                canClickParent: false, //点击事件时，是否可以点击父节点 
                disChecked: false, //是否禁用复选框 默认false
                toolbar: false,
                showLine: true,
                clickItemCls: 'k_tree_clicked_cls', //点击行颜色  
                plainStyle: false,
                clickCheck: false, //是否点击复选
                onlyNodeData: true, //回调api中的参数是否只需要当前节点的数据（不带children）
                tree2list: false, //回调api中的参数是否转为列表类型
                onClick: function (data, params) { //function (data) { },//点击事件                 
                    console.log("onClick =" + JSON.stringify(data) + "  params=" + JSON.stringify(params));
                    layout.load({
                        url: 'list.html', //url地址
                        dataType: 'iframe',
                        title: data.text
                    }, 1);
                }
            });

        }

        var methodsObject = {
            test: function (pr) {
                alert(JSON.stringify(pr));
            }
        };

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'plugin': '../javascript/plugin',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'accordion': '../javascript/accordion',
                'colorpicker': '../javascript/colorpicker',
                'combox': '../javascript/combox',
                'ctxmenu': '../javascript/ctxmenu',
                'datagrid': '../javascript/datagrid',
                'kedit': '../javascript/kedit',
                'labeltab': '../javascript/labeltab',
                'layout': '../javascript/layout',
                'pagination': '../javascript/pagination',
                'panel': '../javascript/panel',
                'resize': '../javascript/resize',
                'tab': '../javascript/tab',
                'toolbar': '../javascript/toolbar',
                'tree': '../javascript/tree',
                'upload': '../javascript/upload',
                'validate': '../javascript/validate'
            }
        });

        var loadModel = ['layout', 'tree'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</body>

 </html>