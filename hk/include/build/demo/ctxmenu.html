 <!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body>
    <h1>
        请点击鼠标右键！！！
    </h1>
    <h1>
        请点击鼠标右键！！！
    </h1>
    <h1 id="h1tile">
        请点击我！！！
    </h1>
    <script type="text/javascript">
        function pageLoaded() {
            new $B.Ctxmenu(
                [{
                    text: '右键菜单项目1',
                    iconCls: 'fa-chart-bar',
                    click: function () {
                        alert(this.text());
                    },
                    items: [{
                        text: '子项目1',
                        iconCls: 'fa-laptop',
                        click: function () {
                            alert(this.text());
                        }
                    }, {
                        text: '子项目2',
                        iconCls: 'fa-print',
                        click: function () {
                            alert(this.text());
                        }
                    }, {
                        text: '子项目3',
                        iconCls: 'fa-zoom-in',
                        click: function () {
                            alert(this.text());
                        }
                    }, {
                        text: '子项目4',
                        iconCls: 'fa-mail',
                        click: function () {
                            alert(this.text());
                        }
                    }]
                }, {
                    text: '右键菜单项目2',
                    iconCls: 'fa-floppy',
                    click: function () {
                        alert(this.text());
                    }
                }, {
                    text: '右键菜单项目3',
                    iconCls: 'fa-paper-plane-empty',
                    click: function () {
                        alert(this.text());
                    },
                    items: [{
                        text: '子项目1',
                        iconCls: 'icon-cogs',
                        click: function () {
                            alert(this.text());
                        }
                    }, {
                        text: '子项目1',
                        iconCls: 'icon-cogs',
                        click: function () {
                            alert(this.text());
                        }
                    }, {
                        text: '子项目2',
                        iconCls: 'icon-cogs',
                        click: function () {
                            alert(this.text());
                        },
                        items: [{
                                text: '子项目11',
                                iconCls: 'icon-cogs'
                            },
                            {
                                text: '子项目2',
                                iconCls: 'icon-cogs'
                            }, {
                                text: '子项目2',
                                iconCls: 'icon-cogs'
                            }
                        ]
                    }]
                }, {
                    text: '右键菜单项目4',
                    iconCls: 'fa-doc-text',
                    click: function () {
                        alert(this.text());
                    }
                }]);
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'panel': '../javascript/panel',
                'toolbar': '../javascript/toolbar',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'ctxmenu': '../javascript/ctxmenu'
            }
        });
        var loadModel = ['ctxmenu'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</body>

 </html>