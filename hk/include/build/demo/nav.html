<!DOCTYPE html>
<html>

<head>
    <title>Bui 框架 富文本编辑器 SSM框架开发</title>
    <meta charset="UTF-8">
    <meta name="keywords"
        content="富文本编辑器、日期时间控件、菜单导航、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
    <meta name="description"
        content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="bookmark" href="/favicon.ico" />
    <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

</head>

<body>
    <div id="msg" style="height:30px; line-height: 30px; width:400px;overflow:hidden;position:absolute;top:0;z-index:2;"> </div>
    <div id="msg2" style="height:30px; line-height: 30px;  width:400px;overflow:hidden;position:absolute;top:0;left:500px;z-index:2;"> </div>
    <div style="width:100%;height:100%;z-index:1;position:absolute;top:0;border-top:35px solid #fff;" class="k_box_size">
        <div style="width:400px;padding:20px 50px;height: 100%;float:left;border:1px solid #ccc" class="clearfix k_box_size">
                <div id="m1" style="width:100%;height:100%"></div>
        </div>
        <div style="width:320px;padding:20px 50px;height: 100%;float:left;margin-left:80px;border:1px solid #ccc" class="clearfix k_box_size">
             <div id="m2"  style="width:100%;height:100%">

             </div>
        </div>
    </div>

    <script type="text/javascript">

        var ms = [
            {
                "deep": 1,
                "data": {
                    "controller": "",
                    "id": "cd6ddac0cf4211e89b063c970e751c70",
                    "isEnable": 1,
                    "isLeaf": 0,
                    "isVisible": 1,
                    "menuIconCss": "fa-user-circle",
                    "menuText": "个人工作台",
                    "orderIndex": 1,
                    "pageName": "",
                    "params": "",
                    "pid": "0"
                },
                "children": [
                    {
                        "deep": 2,
                        "data": {
                            "controller": "user",
                            "id": "6fbbe810d08a11e89e803c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-th-list",
                            "menuText": "个人待审批",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=user",
                            "pid": "cd6ddac0cf4211e89b063c970e751c70"
                        },
                        "pid": "cd6ddac0cf4211e89b063c970e751c70",
                        "id": "6fbbe810d08a11e89e803c970e751c70",
                        "text": "个人待审批"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "",
                            "id": "469c88b0d08d11e890a93c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 0,
                            "isVisible": 1,
                            "menuIconCss": "fa-sliders",
                            "menuText": "我的流程",
                            "orderIndex": 2,
                            "pageName": "",
                            "params": "",
                            "pid": "cd6ddac0cf4211e89b063c970e751c70"
                        },
                        "children": [
                            {
                                "deep": 3,
                                "data": {
                                    "controller": "menu",
                                    "createTime": 1550032332000,
                                    "extFeildsMap": {},
                                    "id": "476d3c90d14511e8af943c970e751c70",
                                    "isEnable": 1,
                                    "isLeaf": 1,
                                    "isVisible": 1,
                                    "menuIconCss": "fa-user",
                                    "menuText": "子菜单",
                                    "orderIndex": 1,
                                    "pageName": "index",
                                    "params": "gridid=menu",
                                    "pid": "469c88b0d08d11e890a93c970e751c70"
                                },
                                "pid": "469c88b0d08d11e890a93c970e751c70",
                                "id": "476d3c90d14511e8af943c970e751c70",
                                "text": "子菜单"
                            },
                            {
                                "deep": 3,
                                "data": {
                                    "controller": "dict",
                                    "createTime": 1550032351000,
                                    "extFeildsMap": {},
                                    "id": "4da5a7d0130f11e986c53c970e751c70",
                                    "isEnable": 1,
                                    "isLeaf": 1,
                                    "isVisible": 1,
                                    "menuIconCss": "fa-database",
                                    "menuText": "这个是一个1232434菜单菜单123123123123123131234",
                                    "orderIndex": 1,
                                    "pageName": "index",
                                    "params": "gridid=dict",
                                    "pid": "469c88b0d08d11e890a93c970e751c70"
                                },
                                "pid": "469c88b0d08d11e890a93c970e751c70",
                                "id": "4da5a7d0130f11e986c53c970e751c70",
                                "text": "这个是一个1232434菜单菜单123123123123123131234"
                            }
                        ],
                        "closed": true,
                        "pid": "cd6ddac0cf4211e89b063c970e751c70",
                        "id": "469c88b0d08d11e890a93c970e751c70",
                        "text": "我的流程"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "",
                            "createTime": 1550032370000,
                            "extFeildsMap": {},
                            "id": "be47b510d14111e880ff3c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 0,
                            "isVisible": 1,
                            "menuIconCss": "fa-mail",
                            "menuText": "模块1",
                            "orderIndex": 5,
                            "pageName": "",
                            "params": "",
                            "pid": "cd6ddac0cf4211e89b063c970e751c70"
                        },
                        "children": [
                            {
                                "deep": 3,
                                "data": {
                                    "controller": "",
                                    "createTime": 1550032380000,
                                    "extFeildsMap": {},
                                    "id": "0569ab80d14511e8af943c970e751c70",
                                    "isEnable": 1,
                                    "isLeaf": 0,
                                    "isVisible": 1,
                                    "menuIconCss": "fa-mail",
                                    "menuText": "子模块",
                                    "orderIndex": 1,
                                    "pageName": "",
                                    "params": "",
                                    "pid": "be47b510d14111e880ff3c970e751c70"
                                },
                                "children": [
                                    {
                                        "deep": 4,
                                        "data": {
                                            "controller": "menu",
                                            "createTime": 1550032409000,
                                            "extFeildsMap": {},
                                            "id": "30912f90d14511e8af943c970e751c70",
                                            "isEnable": 1,
                                            "isLeaf": 1,
                                            "isVisible": 1,
                                            "menuIconCss": "fa-tag",
                                            "menuText": "子菜单",
                                            "orderIndex": 1,
                                            "pageName": "index",
                                            "params": "gridid=menu",
                                            "pid": "0569ab80d14511e8af943c970e751c70"
                                        },
                                        "pid": "0569ab80d14511e8af943c970e751c70",
                                        "id": "30912f90d14511e8af943c970e751c70",
                                        "text": "子菜单"
                                    }
                                ],
                                "closed": true,
                                "pid": "be47b510d14111e880ff3c970e751c70",
                                "id": "0569ab80d14511e8af943c970e751c70",
                                "text": "子模块"
                            }
                        ],
                        "closed": true,
                        "pid": "cd6ddac0cf4211e89b063c970e751c70",
                        "id": "be47b510d14111e880ff3c970e751c70",
                        "text": "模块1"
                    }
                ],
                "pid": "0",
                "id": "cd6ddac0cf4211e89b063c970e751c70",
                "text": "个人工作台个人工作台个人工作台个人工作台个人工作台个人工作台个人工作台"
            },
            {
                "deep": 1,
                "data": {
                    "controller": "",
                    "createTime": 1545409460000,
                    "extFeildsMap": {},
                    "id": "18c5f310cf4111e8ada53c970e751c70",
                    "isEnable": 1,
                    "isLeaf": 0,
                    "isVisible": 1,
                    "menuIconCss": "fa-cog",
                    "menuText": "系统管理",
                    "orderIndex": 1,
                    "pageName": "",
                    "params": "",
                    "pid": "0"
                },
                "children": [
                    {
                        "deep": 2,
                        "data": {
                            "controller": "org",
                            "createTime": 1550032108000,
                            "extFeildsMap": {},
                            "id": "05b2d030d14a11e8bee43c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-cog-alt",
                            "menuText": "机构管理",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=org",
                            "pid": "18c5f310cf4111e8ada53c970e751c70"
                        },
                        "pid": "18c5f310cf4111e8ada53c970e751c70",
                        "id": "05b2d030d14a11e8bee43c970e751c70",
                        "text": "机构管理"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "user",
                            "createTime": 1550032121000,
                            "extFeildsMap": {},
                            "id": "dd68a280d14911e8bee43c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-user",
                            "menuText": "用户管理",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=user",
                            "pid": "18c5f310cf4111e8ada53c970e751c70"
                        },
                        "pid": "18c5f310cf4111e8ada53c970e751c70",
                        "id": "dd68a280d14911e8bee43c970e751c70",
                        "text": "用户管理"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "role",
                            "createTime": 1550032146000,
                            "extFeildsMap": {},
                            "id": "1cefe8f0d14a11e8bee43c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-users",
                            "menuText": "角色管理",
                            "orderIndex": 3,
                            "pageName": "index",
                            "params": "gridid=role",
                            "pid": "18c5f310cf4111e8ada53c970e751c70"
                        },
                        "pid": "18c5f310cf4111e8ada53c970e751c70",
                        "id": "1cefe8f0d14a11e8bee43c970e751c70",
                        "text": "角色管理"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "func",
                            "createTime": 1550032187000,
                            "extFeildsMap": {},
                            "id": "4bcb9b60d14a11e8bee43c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-layout",
                            "menuText": "功能管理",
                            "orderIndex": 4,
                            "pageName": "index",
                            "params": "gridid=func",
                            "pid": "18c5f310cf4111e8ada53c970e751c70"
                        },
                        "pid": "18c5f310cf4111e8ada53c970e751c70",
                        "id": "4bcb9b60d14a11e8bee43c970e751c70",
                        "text": "功能管理"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "menu",
                            "createTime": 1539747881000,
                            "extFeildsMap": {},
                            "id": "c3690e60d14911e8bee43c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-list",
                            "menuText": "菜单管理",
                            "orderIndex": 5,
                            "pageName": "index",
                            "params": "gridid=menu",
                            "pid": "18c5f310cf4111e8ada53c970e751c70"
                        },
                        "pid": "18c5f310cf4111e8ada53c970e751c70",
                        "id": "c3690e60d14911e8bee43c970e751c70",
                        "text": "菜单管理"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "log",
                            "createTime": 1550032231000,
                            "extFeildsMap": {},
                            "id": "7ecd29c0d14a11e8bee43c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-file-word",
                            "menuText": "日志管理",
                            "orderIndex": 6,
                            "pageName": "index",
                            "params": "gridid=log",
                            "pid": "18c5f310cf4111e8ada53c970e751c70"
                        },
                        "pid": "18c5f310cf4111e8ada53c970e751c70",
                        "id": "7ecd29c0d14a11e8bee43c970e751c70",
                        "text": "日志管理"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1550032268000,
                            "extFeildsMap": {},
                            "id": "ce2b3870d9e411e895fb3c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-th-list",
                            "menuText": "字典管理",
                            "orderIndex": 10,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "18c5f310cf4111e8ada53c970e751c70"
                        },
                        "pid": "18c5f310cf4111e8ada53c970e751c70",
                        "id": "ce2b3870d9e411e895fb3c970e751c70",
                        "text": "字典管理"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "api",
                            "createTime": 1552980606000,
                            "extFeildsMap": {},
                            "id": "c9b1e4204a1811e9ba303c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-share-1",
                            "menuText": "开发接口调试",
                            "orderIndex": 12,
                            "pageName": "index",
                            "params": "",
                            "pid": "18c5f310cf4111e8ada53c970e751c70"
                        },
                        "pid": "18c5f310cf4111e8ada53c970e751c70",
                        "id": "c9b1e4204a1811e9ba303c970e751c70",
                        "text": "开发接口调试"
                    }
                ],
                "pid": "0",
                "id": "18c5f310cf4111e8ada53c970e751c70",
                "text": "系统管理"
            },
            {
                "deep": 1,
                "data": {
                    "controller": "",
                    "createTime": 1550032079000,
                    "extFeildsMap": {},
                    "id": "89bdf940cf4211e89b063c970e751c70",
                    "isEnable": 1,
                    "isLeaf": 0,
                    "isVisible": 1,
                    "menuIconCss": "fa-chart-bar",
                    "menuText": "流程管理",
                    "orderIndex": 3,
                    "pageName": "",
                    "params": "",
                    "pid": "0"
                },
                "children": [
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1550032453000,
                            "extFeildsMap": {},
                            "id": "428cc2d012e611e986c53c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-cog-alt",
                            "menuText": "这个是一个菜单1232菜单",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "428cc2d012e611e986c53c970e751c70",
                        "text": "这个是一个菜单1232菜单"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1546911592000,
                            "extFeildsMap": {},
                            "id": "4b3abef012e611e986c53c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-area-chart",
                            "menuText": "这个是一个菜单sdfsd菜单222",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "4b3abef012e611e986c53c970e751c70",
                        "text": "这个是一个菜单sdfsd菜单222"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1550059844000,
                            "extFeildsMap": {},
                            "id": "c29aa630109411e98a1b3c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-user",
                            "menuText": "这个是一个菜单菜单111",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "c29aa630109411e98a1b3c970e751c70",
                        "text": "这个是一个菜单菜单111"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1550032439000,
                            "extFeildsMap": {},
                            "id": "39d48d8012e611e986c53c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-mail",
                            "menuText": "这个是sdfsdfsdffs单222",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "39d48d8012e611e986c53c970e751c70",
                        "text": "这个是sdfsdfsdffs单222"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1550032468000,
                            "extFeildsMap": {},
                            "id": "545d9cf012e611e986c53c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-th-list",
                            "menuText": "这个是一sdfs个1232434菜单菜单",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "545d9cf012e611e986c53c970e751c70",
                        "text": "这个是一sdfs个1232434菜单菜单"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "menu",
                            "createTime": 1539745147000,
                            "extFeildsMap": {},
                            "id": "a6601ff0d14b11e8832f3c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-file",
                            "menuText": "测试",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=menu",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "a6601ff0d14b11e8832f3c970e751c70",
                        "text": "测试"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1550032430000,
                            "extFeildsMap": {},
                            "id": "3085e49012e611e986c53c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-user",
                            "menuText": "这个是一个菜单菜单006",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "3085e49012e611e986c53c970e751c70",
                        "text": "这个是一个菜单菜单006"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1546656686000,
                            "extFeildsMap": {},
                            "id": "cb6774f0109411e98a1b3c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-area-chart",
                            "menuText": "这个是一个菜单菜单222",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "cb6774f0109411e98a1b3c970e751c70",
                        "text": "这个是一个菜单菜单222"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1550032423000,
                            "extFeildsMap": {},
                            "id": "259e9a4012e611e986c53c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-th-list",
                            "menuText": "这个是一个菜单菜单8889898000",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "259e9a4012e611e986c53c970e751c70",
                        "text": "这个是一个菜单菜单8889898000"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1546656657000,
                            "extFeildsMap": {},
                            "id": "abbc4f40109411e98a1b3c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-area-chart",
                            "menuText": "这个是一个菜单菜单",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "abbc4f40109411e98a1b3c970e751c70",
                        "text": "这个是一个菜单菜单"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1550059855000,
                            "extFeildsMap": {},
                            "id": "dcb9d040109411e98a1b3c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-cog",
                            "menuText": "这个是一个123243菜单菜单",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "dcb9d040109411e98a1b3c970e751c70",
                        "text": "这个是一个123243菜单菜单"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1546911631000,
                            "extFeildsMap": {},
                            "id": "62b6b57012e611e986c53c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-area-chart",
                            "menuText": "这个是一个菜单菜单122",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "62b6b57012e611e986c53c970e751c70",
                        "text": "这个是一个菜单菜单122"
                    },
                    {
                        "deep": 2,
                        "data": {
                            "controller": "dict",
                            "createTime": 1550032480000,
                            "extFeildsMap": {},
                            "id": "d4040bf0109411e98a1b3c970e751c70",
                            "isEnable": 1,
                            "isLeaf": 1,
                            "isVisible": 1,
                            "menuIconCss": "fa-cog-alt",
                            "menuText": "这个是一个菜单菜单8889898",
                            "orderIndex": 1,
                            "pageName": "index",
                            "params": "gridid=dict",
                            "pid": "89bdf940cf4211e89b063c970e751c70"
                        },
                        "pid": "89bdf940cf4211e89b063c970e751c70",
                        "id": "d4040bf0109411e98a1b3c970e751c70",
                        "text": "这个是一个菜单菜单8889898"
                    }
                ],
                "pid": "0",
                "id": "89bdf940cf4211e89b063c970e751c70",
                "text": "流程管理"
            }
        ];
        function pageLoaded() {

            var m1 = $("<div></div>").appendTo($("#m1")); 
            new $B.Menu( m1,{
    				data: ms,
    				onClick:function(_data,isParent){
    					$("#msg").html("导航菜单1 你点击了：" +_data.menuText);
    				}
            });  
            
            var ul = $("<ul/>").appendTo($("#m2"));
            new $B.Menu(ul,{
                data: ms,
                ellipsis:true,
				onClick:function(_data,isParent){
                    $("#msg2").html("导航菜单2 你点击了：" +_data.menuText);
            }});

        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'menu': '../javascript/menu'
            }
        });

        var loadModel = ['utils', 'menu'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</body>

</html>