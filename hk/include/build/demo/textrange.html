<!DOCTYPE html>
<html>

<head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、手风琴、拾色器、多文件上传、弹窗组件、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <meta charset="UTF-8">

    <style>

    </style>
</head>

<body>
    <div id="main">
        <div> <span style="float:left;margin:0 20px;">代码修饰：</span><span id="bold" cmd="font-weight: bold" tabindex="0"
                title="加粗代码" style="display:block;width:20px;height:20px;float:left;margin:0 2px;margin-top:3px;cursor:pointer;font-weight: bold;text-align:center;line-height:20px;">B</span><span
                id="normal" cmd="font-weight: normal" title="取消加粗" tabindex="0" style="display:block;width:20px;height:20px;float:left;margin:0 2px;margin-top:3px;cursor:pointer;margin-left:20px;line-height:20px;text-align:center;">B</span><span
                id="red" cmd="color:red" title="字体红色" tabindex="0" style="display:block;width:20px;height:20px;float:left;margin:0 2px;margin-top:3px;cursor:pointer;margin-left:20px;line-height:20px;text-align:center;color:red;">A</span><span
                id="greed" cmd="color:#0FE000" title="绿色" tabindex="0" style="display:block;width:20px;height:20px;float:left;margin:0 2px;margin-top:3px;cursor:pointer;margin-left:20px;line-height:20px;text-align:center;color:#0FE000;">A</span><span
                id="blank" cmd="color:#666666" title="淡黑色" tabindex="0" style="display:block;width:20px;height:20px;float:left;margin:0 2px;margin-top:3px;cursor:pointer;margin-left:20px;line-height:20px;text-align:center;color:#666666;">A</span><span
                id="blinkred" cmd="color:#FF00D1" title="粉红色" tabindex="0" style="display:block;width:20px;height:20px;float:left;margin:0 2px;margin-top:3px;cursor:pointer;margin-left:20px;line-height:20px;text-align:center;color:#FF00D1;">A</span><span
                id="yello" cmd="color:#FFD600" title="黄色" tabindex="0" style="display:block;width:20px;height:20px;float:left;margin:0 2px;margin-top:3px;cursor:pointer;margin-left:20px;line-height:20px;text-align:center;color:#FFD600;">A</span>
        </div>
        <br />
        <div>
            <pre id="_code_txt" style="width: 600px;height: 300px;border:1px dashed #ccc" contenteditable="true" spellcheck="false">


            </pre>
        </div>

        <script>
            (function () {
                var doc = document;
                var range, seletionObj,input;
                function rebuildRange() {
                    if (range) {
                        console.log("恢复 rande 》》》》");
                        try {
                            seletionObj.removeAllRanges();
                        } catch (e) { }
                        seletionObj.addRange(range);
                    }
                }
                function saveRange() {
                    range = undefined;
                    if (window.getSelection) {
                        seletionObj = window.getSelection();
                    } else if (document.selection) {
                        seletionObj = document.selection.createRange();
                    }
                    if (seletionObj.getRangeAt && seletionObj.rangeCount > 0) {
                        range = seletionObj.getRangeAt(0);
                    } else { // 老版本浏览器
                        if (seletionObj.anchorNode) {
                            range = document.createRange();
                            range.setStart(seletionObj.anchorNode, seletionObj.anchorOffset);
                            range.setEnd(seletionObj.focusNode, seletionObj.focusOffset);
                        }
                    }
                    console.log("saveRange >>>>>>>>>saveRange");
                }
                input = doc.getElementById("_code_txt");
                input.onmouseleave = saveRange;
                var $bold = doc.getElementById("bold");
                $bold.onmouseup = function () {
                    setTimeout(function(){
                        rebuildRange();
                    },10);                                        
                };               
            })();
        </script>
    </div>
</body>

</html>