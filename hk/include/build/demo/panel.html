<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>

    <style>
        body{
             box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
         }
        .ap{
            line-height: 200px;
        }
    </style>

</head>

<body>

    <div class="section-content">
        <h1 class="section-title">固定面板/浮动面板；远程内容加载/静态内容；面板工具栏配置(依赖toolbar)</h1>
        <div style="height:400px;width:100%" class="section-body clearfix">
            <div style="margin-bottom:5px;">
                <button type="button" onclick="load('iframe','test.html')">重新加载</button>
                <button type="button" onclick="updateContent()">更新静态内容</button>
                <button type="button" onclick="setTitle()">设置标题</button>
                <button type="button" id="flowPanel">显示浮动面板</button>

            </div>
            <div style="height:380px;width:100%;" class="clearfix">
                <div style="height:100%;width: 48%;float: left; ">
                    <div id="panel1"></div>
                </div>
                <div style="height:100%;width: 48%;float: right; ">
                    <div id="panel2"></div>
                </div>
            </div>
        </div>
    </div>
    <div>
    </div>
    <div id="panel"></div>


    <script type="text/javascript">
        function closed() {
            panel.close();
        }

        function load(f, url) {
            panel.load({
                url: url,
                dataType: f
            })
        }

        function updateContent() {
            panel.updateContent("<p>我是更新后的静态内容</p>");
        }

        function setTitle() {
            panel.setTitle("new title");
        }

        function resize() {
            panel.resize({
                width: 500,
                height: 250
            });
        }

        var panel;

        function pageLoaded() {

            panel = new $B.Panel($("#panel1"), {
                width: '100%',
                height: '100%',
                title: '公告文件面板', //标题
                iconCls: 'fa-mail', //图标cls，对应icon.css里的class
                shadow: false, //是否需要阴影
                radius: false, //是否圆角
                header: true, //是否显示头部
                content: 'test.html',
                dataType: 'iframe', //当为url请求时，html/json/iframe
                draggable: false, //是否可以拖动
                moveProxy: false, //是否产生一个可移动的空div代理
                closeable: false, //是否关闭
                expandable: false, //可左右收缩
                maxminable: false, //可变化小大
                collapseable: true, //上下收缩
                onLoaded: function () { //加载后
                    console.log("onLoaded");
                },
                onClosed: function () { //关闭后
                    console.log("onClosed");
                },
                onExpand: function (pr) { //左右收缩后
                    console.log("onExpand " + pr);
                },
                onCollapse: function (pr) { //上下收缩后
                    console.log("onCollapse " + pr);
                }
            });
            var $editDiv = $("<div><p>您可以在这里创建一个Bui-Editor富文本输入控件！</p></div>");
            new $B.Panel($("#panel2"), {
                width: '100%',
                height: '100%',
                title: '新闻通知面板', //标题
                iconCls: 'fa-chat', //图标cls，对应icon.css里的class
                shadow: false, //是否需要阴影
                radius: false, //是否圆角                
                header: true, //是否显示头部
                content: $editDiv,
                draggable: false, //是否可以拖动
                moveProxy: false, //是否产生一个可移动的空div代理
                closeable: false, //是否关闭
                expandable: false, //可左右收缩
                maxminable: false, //可变化小大
                collapseable: true, //上下收缩
                onLoaded: function () { //加载后
                    console.log("onLoaded");
                },
                onClosed: function () { //关闭后
                    console.log("onClosed");
                },
                onExpand: function (pr) { //左右收缩后
                    console.log("onExpand " + pr);
                },
                onCollapse: function (pr) { //上下收缩后
                    console.log("onCollapse " + pr);
                },
                toolbar: { //工具栏对象参考工具栏组件c
                    align: 'right', //对齐方式，默认是left 、center、right
                    style: 'min', // plain / min  / normal /  big
                    showText: true, // min 类型可以设置是否显示文字
                    buttons: [{
                        iconCls: 'fa-floppy',
                        params: {
                            pr1: 123
                        }, //额外的参数
                        text: '提交', //文本
                        click: function (pr) { //点击事件，如果存在click，则字符串形式的handler不可用
                            alert(JSON.stringify(pr));
                        }
                    }, {
                        iconCls: 'fa-edit-1',
                        params: {
                            pr1: 8888
                        }, //额外的参数
                        text: '发送', //文本
                        click: 'test', //window.methodsObject对象中 test
                        methodsObject: 'methodsObject'
                    }]
                }
            });
            var myTree;

            var pp1 = new $B.Panel($("#panel"), {
                width: 600,
                height: 300,
                title: '浮动面板[JSON请求]', //标题
                iconCls: 'fa-window-maximize', //图标cls，对应icon.css里的class
                shadow: true, //是否需要阴影
                radius: true, //是否圆角
                header: true, //是否显示头部
                url: '/bui/api/json?flag=tree',
                dataType: 'json', //当为url请求时，html/json/iframe
                draggableHandler: 'header', //拖动触发焦点
                draggable: true, //是否可以拖动
                closeType: 'hide', // 关闭类型 hide(隐藏，可重新show)/ destory 直接从dom中删除
                moveProxy: false, //是否产生一个可移动的空div代理
                closeable: true, //是否关闭
                expandable: true, //可左右收缩
                maxminable: true, //可变化小大
                collapseable: true, //上下收缩
                onResized: function (pr) {
                    console.log("onResized " + pr);
                }, //大小变化事件
                onLoaded: function (data) { //加载后
                    var dom = this;
                    dom.children().remove();
                    var $treeDom = $("<div><ul id='_tree' /></div>").appendTo(dom).children("ul");
                    myTree = new $B.Tree($treeDom, {
                        data: data,
                        checkbox: true,
                        clickCheck: true,
                        nodeParentIcon: 'fa-folder-empty', //父节点图标关闭状态
                        nodeParentOpenIcon: 'fa-folder-open-empty', //打开状态图标
                        leafNodeIcon: 'fa-doc', //子节点图标 
                        chkEmptyIcon: ' fa-check-empty', //不选
                        chkAllIcon: ' fa-check', //全选
                        chkSomeIcon: 'fa-ok-squared', //部分选 ***/
                        url: '/bui/api/json?flag=tree'
                    });
                },
                onClosed: function () { //关闭后
                    console.log("onClosed");
                    flowBtn.show();
                },
                onExpanded: function (pr) { //左右收缩后
                    console.log("onExpand " + pr);
                },
                onCollapsed: function (pr) { //上下收缩后
                    console.log("onCollapse " + pr);
                },
                toolbar: { //工具栏对象参考工具栏组件c
                    align: 'center', //对齐方式，默认是left 、center、right
                    style: 'normal', // plain / min  / normal /  big
                    showText: true, // min 类型可以设置是否显示文字
                    buttons: [{
                        iconCls: 'fa-floppy',
                        params: {
                            pr1: 123
                        }, //额外的参数
                        disabled: false, //是否禁用
                        text: '测试', //文本
                        click: function (pr) { //点击事件，如果存在click，则字符串形式的handler不可用
                            if (myTree) {
                                alert(JSON.stringify(myTree.getCheckedData({
                                    onlyId: false,
                                    onlyChild: false
                                })));
                            }
                        }
                    }, {
                        iconCls: 'fa-cancel-circled',
                        text: '关闭', //文本
                        click: function () {
                            pp1.close();
                        }
                    }]
                },
                onDragReady: function (e) {
                    console.log(" onDragReady 鼠标按下时候准备拖动前，返回true则往下执行，返回false则停止 ");
                    return true;
                }, //鼠标按下时候准备拖动前，返回true则往下执行，返回false则停止
                onStartDrag: function (e) {
                    console.log(" onStartDrag 开始拖动事件 开始拖动事件 >>>>>>>>>>>>>>>>>>");
                }, //开始拖动事件
                onDrag: function (e) {
                    console.log(" onDrag 拖动中事件 ");
                }, //拖动中事件
                onStopDrag: function (e) {
                    console.log(" onStopDrag 拖动结束事件 拖动结束事件");
                } //拖动结束事件
            });
            var flowBtn = $("#flowPanel").hide().click(function () {
                pp1.show();
                flowBtn.hide();
            });

        };
        var methodsObject = {
            test: function (pr) {
                alert(JSON.stringify(pr));
            }
        };

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                'utils': '../javascript/utils',
                'plugin': '../javascript/plugin',
                'mutilUpload': '../javascript/upload',
                '$B': '../javascript/basic',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree',
                "colorpicker": '../javascript/colorpicker',
                'panel': '../javascript/panel',
                'resize': '../javascript/resize',
                'toolbar': '../javascript/toolbar'
            }
        });

        var loadModel = ['panel',  'tree'];
        require(loadModel, function () {
            $(function () {
                $B.debug("pageLoaded invoke.....");
                pageLoaded();
            });
        });
    </script>
</body>

</html>