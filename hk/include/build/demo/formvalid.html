<!DOCTYPE html>
<html>

    <head>
        <title>Bui 框架 富文本编辑器 SSM框架开发</title>
        <meta charset="UTF-8">
        <meta name="keywords" content="富文本编辑器、树形列表、树形控件、树控件、手风琴、拾色器、多文件上传、弹窗组件、模态窗口、双向联动表单、右键菜单、jquery拖动、draggable、datagrid、tree、mvvm、combox、tab" />
        <meta name="description" content="Bui editor ui框架是一套前后端分离，包含富文本编辑器、双向联动表单、树形列表、树形控件、模态窗口、手风琴、拾色器、多文件上传、弹窗组件等全功能UI框架。还包括Spring mvc spring mybatis高效开发框架" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="bookmark" href="/favicon.ico" />
        <meta name="renderer" content="webkit|ie-stand" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/blue/main.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../theme/font/bui-fonts.min.css" />
    <link type="text/css" rel="stylesheet" lang="stylesheet" href="../css/site.css" />
    <script type="text/javascript" src="datas.js"></script>
    <script type="text/javascript" src="../lib/require.js"></script>


    <style>
        body{
             box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
         }
         td.label{
             width: 200px;
         }
         .section-body p{
             line-height: 20px;
         }
         .catalog_item{

         }

    </style>
</head>

<body>

    <div class="section-content">

        <h1 class="section-title">通过</h1>
        <div style="height:200px" class="section-body clearfix">
            <p></p>

            <table id="myform" class="form_table">
                <tr>
                    <td class="label">姓名：</td>
                    <td>
                        <input id="userName" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">中文名：</td>
                    <td>
                        <input id="zhName" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">密码：</td>
                    <td>
                        <input id="userPwd" type="password" />
                    </td>
                </tr>
                <tr>
                    <td class="label">性别：</td>
                    <td>
                        <label class="k_radio_label k_radio_anim">
                            <input type="radio" name="sex" value="1" /><i class="k_radio_i"></i>男
                        </label>
                        <label class="k_radio_label k_radio_anim">
                            <input type="radio" name="sex" value="0" /><i class="k_radio_i"></i>女
                        </label>
                    </td>
                </tr>
                <tr>
                    <td class="label">电子邮件：</td>
                    <td>
                        <input id="userEmail" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">电话：</td>
                    <td>
                        <input id="userPhone" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">座机：</td>
                    <td>
                        <input id="telphone" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">年龄：</td>
                    <td>
                        <input id="age" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">年薪：</td>
                    <td>
                        <input id="salary" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">常住地：</td>
                    <td>
                        <input id="userAddr" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">技能：</td>
                    <td>
                        <input id="userTech" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">博客：</td>
                    <td>
                        <input id="myweb" type="text" />
                    </td>
                </tr>
                <tr>
                    <td class="label">介绍：</td>
                    <td>
                        <textarea id="userDesp"></textarea>
                    </td>
                </tr>
            </table>
            <div>
                <button onclick="mySubmit(this)">提交表单</button>
            </div>
            <div id="message"></div>
        </div>
    </div>


    <script type="text/javascript">
        var userData = {
            userName: 'kevin.huang',
            userPwd: '000000',
            zhName: '雪山飞狐',
            sex: 1,
            userEmail: '',
            telphone: '0771-777777',
            age: 18,
            salary: 100000.1,
            userTech: 2,
            userid: 1,
            userAddr: '',
            userDesp: '介绍：这家伙是一个热爱编程工作的人！',
            myweb: 'http:127.0.0.1/myweb'
        };
        var vm;
        var validObj;

        function pageLoaded() {
            var dataArray = [{
                id: '1',
                fileName: '中国北京'
            },
            {
                id: '2',
                fileName: '中国南宁'
            },
            {
                id: '3',
                fileName: '中国桂林'
            },
            {
                id: '4',
                fileName: '中国北海'
            },
            {
                id: '5',
                fileName: '中国钦州'
            },
            {
                id: '6',
                fileName: '中国柳州'
            }
            ];
            var args = {
                default: {
                    "id": '',
                    "fileName": "请您选择"
                },
                data: dataArray,
                isTreeData: false,
                placeholder: '请您选择',
                checkbox: true,
                textField: 'fileName',
                mutilchecked: true, //是否多选
                checkfather: false, // 单选的时候，是否可以选择父节点
                readonly: true, //不可以编辑                
                plainStyle: false
            };
            new $B.Combox($("#userAddr"), args);

            args = {
                data: [{
                    id: 1,
                    text: 'java'
                }, {
                    id: 2,
                    text: 'javascript'
                }, {
                    id: 3,
                    text: 'c#'
                }],
                isTreeData: false,
                placeholder: '请您选择',
                mutilchecked: false, //是否多选          
                plainStyle: false,
                showLine: false,
                checkbox: false,
                mutilChecked: false,
                readonly: true,
                plainStyle: true,
                forecePlainStyle: true
            };
            new $B.Combox($("#userTech"), args);

            //绑定双向关联表单
            var $from = $("#myform");
            vm = $B.bindForm($from, userData, function (propObj, propName, newValue, oldValue) {
                var ms = "onChanged propName=" + propName + " newValue = " + newValue + "     ;   oldValue = " +
                    oldValue;
                console.log(ms);
                $("#message").prepend("<p style='border-bottom:1px dashed #666'>" + ms + "</p>");
            });
            var url = $B.getHttpHost()+ "/bui/api/json?flag=valid"
            //绑定客户端验证
            validObj = new $B.Validate({
                userName: [{ rule: 'require' }, { rule: 'wchar' }, { rule: { minlength: 2 } }, { rule: { remote: url } }],
                userPwd: [{ rule: 'require' }, { rule: { minlength: 6 } }],
                zhName: [{ rule: 'require' }, { rule: 'chchar' }, { rule: { minlength: 2 } }],
                userEmail: [{ rule: 'email' }],
                userPhone: [{ rule: 'phone' }],
                telphone: [{ rule: 'telphone' }],
                age: [{ rule: 'digits' }],
                salary: [{ rule: { range: [10000, 100000] } }],
                userTech: [{ rule: 'require', msg: '请选择' }],
                userAddr: [{ rule: 'require', msg: '该项必须选择' }],
                userDesp: [{ rule: { minlength: 10 } }, { rule: { maxlength: 20 } }]
            }, $from);

        }

        function mySubmit() {
            if (validObj.valid()) {
                var formJson = vm.getJson();
                $("#message").prepend("<p style='border-bottom:1px dashed #666'>mv.getJson()提交：" + JSON.stringify(formJson) + "</p>");
            } else {
                alert("验证没有通过！");
            }
        }

        require.config({
            waitSeconds: 0,
            baseUrl: "",
            paths: {
                'jquery': '../lib/jquery.min',
                'config': '../javascript/config',
                '$B': '../javascript/basic',
                'panel': '../javascript/panel',
                'plugin': '../javascript/plugin',
                'toolbar': '../javascript/toolbar',
                'utils': '../javascript/utils',
                'tree': '../javascript/tree',
                'combox': '../javascript/combox',
                'mvvm': '../javascript/mvvm',
                'valid': '../javascript/validate'
            }
        });
        var loadModel = ['$B', 'mvvm', 'utils', 'combox', 'valid'];
        require(loadModel, function () {
            $(function () {
                pageLoaded();
            });
        });
    </script>
</body>

</html>