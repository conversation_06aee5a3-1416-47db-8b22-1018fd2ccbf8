<div style="position:relative">

<div id="k_25200830231X18EHPX1B" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 255, 253); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(160, 32, 240); border-image: initial; padding-left: 12px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253);" id="k_25201103034NR6DK7U88">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_252011030413IQNHUVCS">​<img id="k_2520110304HFDTK2I9OY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABVElEQVQoU5WRv0sccRDFP++7nhAshFNuBfMHWGmhpBfShAiCjaBgJwmChVgqWmkR0VRCDoKNnFhopYUgCBZCGgsLOxubeLuKhQrij52RNRHOw8ap3+fNvDfinaN/elcJOhUYF3x2KEocWMZiCnsge/FVLo4jBuUsI47N2Q1w79AF9AkWq8YP0G0OqRXvaQhsOJQTYwl097I1hl4FVlzMJRm/Qa44+Lyg/d4Yv0RXryO5SoEJiS9ZxsgFOlMc+bY7W6mp/Fb+Vry7IVB5NIYv0KHaIt8xWE0zVd4CWvCOQmDNjbEE/VFb8GWDm9SYAj3WQyV8QBGTIWPoDJ0qxvsJLMgYraL9WqCIfyxE/MI5SozZ3DAHmhTy2vjq8PPB2GyEvKlPRMw6/PWMbylKnmv9X+GHOPBdMI0oPr/SuQbWzZg5R9Wax9Ue4Y0xtBs0Ayfn6KY+0xPgDH4N4O/JMAAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253);" id="k_2520110304MHU6I2969B">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253);" id="k_2520111490KTLORMOYL4">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253);" id="k_2520111491PLLGOFJ37F">介绍</span></div><div id="k_25200837845Z6JW25MA9" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 32px; width: 793px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2520163168BHYP8I4HDH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_252016316931A3N8P633">​<img id="k_2520163169FGNG9PVG2J" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABH0lEQVQ4T7WSsUrDcBDGvy9a3RwcBPEBOvgUXUyyOBSqkGurg5s49AlEcBF8A0ELzT8GMrmYROwr2MUH6BO4CRLM/ySDImpaKHjLwR2/+7jvjlgwuCCHmWCQBocOnROSByN39PxdpBbspt0miAcAm1atRH6UzAcVlFxuCe4rNG2sNtrD1vBtLhikQZtgCOCVpBt64dNPL9jJOutVMfGSlyr38t6GtfaR4LZSz41nTv8ykJLKFMCS4zh+ZYBkcknlQKmTAsXO58BfipLKHcldKCaW9ozKG5IrVu1e5Ef3dedif9zfKotyTLKp0HeCywq9Nq45AqG1YNWQXHxYxCTXFDottWzFflytUBtfd5RMjgleKHRgPHM176P+5+VmqX4AOjxpD6EyF28AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2520163169MMUK29Z87G">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2520163168Q5OKE6ZTY1">win</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2520154085IU1MWIPQDA">dow组件内部采用panel组件构建，拥有panel对应的api，参数。</span></div><div id="k_2520124761Q8EZGRIVRQ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 32px; width: 793px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_252016408214JEV5IK8Z">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2520164083C73TNDHKP1">​<img id="k_2520164083S498QLU6KH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABH0lEQVQ4T7WSsUrDcBDGvy9a3RwcBPEBOvgUXUyyOBSqkGurg5s49AlEcBF8A0ELzT8GMrmYROwr2MUH6BO4CRLM/ySDImpaKHjLwR2/+7jvjlgwuCCHmWCQBocOnROSByN39PxdpBbspt0miAcAm1atRH6UzAcVlFxuCe4rNG2sNtrD1vBtLhikQZtgCOCVpBt64dNPL9jJOutVMfGSlyr38t6GtfaR4LZSz41nTv8ykJLKFMCS4zh+ZYBkcknlQKmTAsXO58BfipLKHcldKCaW9ozKG5IrVu1e5Ef3dedif9zfKotyTLKp0HeCywq9Nq45AqG1YNWQXHxYxCTXFDottWzFflytUBtfd5RMjgleKHRgPHM176P+5+VmqX4AOjxpD6EyF28AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2520164083TUF9PET9RJ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2520164082JHB4RET2PI">window组件是Bui窗口类（$B.alert、$B.message、$B.confirm等）的基础。</span></div><div id="k_2520140038GO18AHEH9W" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 32px; width: 793px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2520165085E7BSAW7DMO">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2520165087TJYTXGOPWB">​<img id="k_2520165087OCCPDJQXBB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABH0lEQVQ4T7WSsUrDcBDGvy9a3RwcBPEBOvgUXUyyOBSqkGurg5s49AlEcBF8A0ELzT8GMrmYROwr2MUH6BO4CRLM/ySDImpaKHjLwR2/+7jvjlgwuCCHmWCQBocOnROSByN39PxdpBbspt0miAcAm1atRH6UzAcVlFxuCe4rNG2sNtrD1vBtLhikQZtgCOCVpBt64dNPL9jJOutVMfGSlyr38t6GtfaR4LZSz41nTv8ykJLKFMCS4zh+ZYBkcknlQKmTAsXO58BfipLKHcldKCaW9ozKG5IrVu1e5Ef3dedif9zfKotyTLKp0HeCywq9Nq45AqG1YNWQXHxYxCTXFDottWzFflytUBtfd5RMjgleKHRgPHM176P+5+VmqX4AOjxpD6EyF28AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2520165087UPPYPDPX6R">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_252016508648AMLYEBBQ">window组件支持自定义</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2520154086QP3OCH4KBL">按钮(依赖</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_25201527724KAY2RMAH7">工具栏)、支持字体图标定义、支持窗口位置定义、定时关闭定义。</span></div><div id="k_2520083844YCGPN59RCG" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520083844GWO3L6O1RN">​</span></div><div id="k_2520083861NXAQ642E84" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 255, 253); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(160, 32, 240); border-image: initial; margin-top: 0px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253); font-weight: 400; font-style: normal; text-decoration: none solid rgb(153, 0, 51); vertical-align: baseline;" id="k_2520115197EB6WPIRLKQ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_25201151988L8Y9VJW9O">​<img id="k_25201151986BYJDUQEWZ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABVElEQVQoU5WRv0sccRDFP++7nhAshFNuBfMHWGmhpBfShAiCjaBgJwmChVgqWmkR0VRCDoKNnFhopYUgCBZCGgsLOxubeLuKhQrij52RNRHOw8ap3+fNvDfinaN/elcJOhUYF3x2KEocWMZiCnsge/FVLo4jBuUsI47N2Q1w79AF9AkWq8YP0G0OqRXvaQhsOJQTYwl097I1hl4FVlzMJRm/Qa44+Lyg/d4Yv0RXryO5SoEJiS9ZxsgFOlMc+bY7W6mp/Fb+Vry7IVB5NIYv0KHaIt8xWE0zVd4CWvCOQmDNjbEE/VFb8GWDm9SYAj3WQyV8QBGTIWPoDJ0qxvsJLMgYraL9WqCIfyxE/MI5SozZ3DAHmhTy2vjq8PPB2GyEvKlPRMw6/PWMbylKnmv9X+GHOPBdMI0oPr/SuQbWzZg5R9Wax9Ue4Y0xtBs0Ayfn6KY+0xPgDH4N4O/JMAAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253); font-weight: 400; font-style: normal; text-decoration: none solid rgb(153, 0, 51); vertical-align: baseline;" id="k_2520115198QIBKLA8T41">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253); font-weight: 400; font-style: normal; text-decoration: none solid rgb(153, 0, 51); vertical-align: baseline;" id="k_25201151976QMYMMQWCR">&nbsp;API 及 构造参数说明</span></div><div id="k_2520084879Q7IBFHU424" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190325202025WRVEVT82U5KW"><tbody><tr id="row_0"><td id="k_25202025914PB2V58DEB" tabindex="0" row="0" col="0" w="1307" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1309px; background-color: rgb(242, 244, 252);" colspan="3" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(242, 244, 252);" id="k_25202025923XVVO8RGPV"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 244, 252);" id="k_2520202592PYPVYVNHVY">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 244, 252);" id="k_2520211752EYOOVAUT7U">​$B.window(opts)&nbsp; opts 参数说明</span></p></td></tr><tr id="row_1"><td id="k_2520202594RMEMX6QZH9" tabindex="0" row="1" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px; background-color: rgb(242, 244, 252); text-align: center;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(242, 244, 252);" id="k_25202025954FHNGECMF9"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 244, 252); text-align: center;" id="k_2520202595RNPKN65FGW">​参数</span></p></td><td id="k_2520202595K6MDXZHJ5A" tabindex="0" row="1" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px; background-color: rgb(242, 244, 252); text-align: center;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(242, 244, 252);" id="k_2520202595AMRC2LTRFC"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 244, 252); text-align: center;" id="k_2520202595A1XU4Y1VBH">参数值​</span></p></td><td id="k_2520202596OQLLFG3F4T" tabindex="0" row="1" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px; background-color: rgb(242, 244, 252); text-align: center;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(242, 244, 252);" id="k_25202025964OSOE9WDW5"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 244, 252); text-align: center;" id="k_2520202596CBOKEGK9GS">说明​</span></p></td></tr><tr id="row_2"><td id="k_2520202596UHKBSEII3N" tabindex="0" row="2" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202596MCX6FRX16A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25202025961RBDZBQX4B">​​iconCls</span></p></td><td id="k_2520202597DSK9G8ZJVC" tabindex="0" row="2" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202597W1SVKB3X87"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202597V8IAEZ79DY">​默认值：​fa-window-restore</span></p></td><td id="k_25202025989RNFCW9UBW" tabindex="0" row="2" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202598C1QIX3D4RU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202598PRGU8Z4RPI">​​FontAwesomese图标定义</span></p></td></tr><tr id="row_3"><td id="k_2520202598KFWL89J42O" tabindex="0" row="3" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202598TISEGQABVC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25202025989M2CQODAFH">​​full</span></p></td><td id="k_2520202599M1UKIZ2AB9" tabindex="0" row="3" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25202025998LVL9XL8QD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202599WYZYC3KKEZ">​​默认值：false ，如果设置true，则忽略width/height配置</span></p></td><td id="k_2520202599O4HA3R97AZ" tabindex="0" row="3" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202599T6JRYQGA8C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25202025999U8WIMIAZE">​​是否自动占满父元素空间</span></p></td></tr><tr id="row_4"><td id="k_25202025004Y28HPMCPO" tabindex="0" row="4" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202500PIXOTH5MYB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202500JY46LC4PU3">​​width</span></p></td><td id="k_2520202501FYF8L7O7JY" tabindex="0" row="4" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202501RTBDCEPMI9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202501WL4UBP5VHT">​​默认值：600</span></p></td><td id="k_2520202501FRT8XJFXQA" tabindex="0" row="4" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25202025012WF86XBD2H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202501G336GV4SC4">​​宽度</span></p></td></tr><tr id="row_5"><td id="k_25202025024U35GQB9VB" tabindex="0" row="5" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202502CKUIF7C1O4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25202025028PX9WK7Q3O">​​height</span></p></td><td id="k_25202025037RKV9QL7RT" tabindex="0" row="5" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202503DG2MFYLGSY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202503UWWI5BTPGA">​​默认值：300</span></p></td><td id="k_2520202503CMVOH5MGZ4" tabindex="0" row="5" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202503X2F9I8T6AI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202503MQRIKWBT3Z">​​高度</span></p></td></tr><tr id="row_6"><td id="k_2520202504W71NG9UBJ2" tabindex="0" row="6" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202504VFZHTS2WX4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25202025045K3W15JQYY">​​title</span></p></td><td id="k_2520202504AVD6X2N13X" tabindex="0" row="6" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25202025048HEESIK7F4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202504L98QWZWSG5">​</span></p></td><td id="k_25202025051PJ2XAEFD1" tabindex="0" row="6" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202505HWWWTW1OX9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202506RLNEOFVPUT">​​窗口标题</span></p></td></tr><tr id="row_7"><td id="k_25202025065MPFWA1GPM" tabindex="0" row="7" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202506JC7LHFEIWB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202506MNORUWQU2I">​​closeType</span></p></td><td id="k_25202025073QDYLVJ45D" tabindex="0" row="7" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202507HKMHMFPK6R"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202507KVQ98X17A5">​​默认值：destory</span></p></td><td id="k_2520202508IPVIKXURRS" tabindex="0" row="7" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_252020250889MG25MS2Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202508JXN4ZT2OGG">​​关闭类型，关闭类型 hide(隐藏，可重新show)/ destory 直接从dom中删除</span></p></td></tr><tr id="row_8"><td id="k_25202025098W3MXQTNLB" tabindex="0" row="8" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202509JRUVGXP4P9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202509USUNH62JEG">​​shadow</span></p></td><td id="k_25202025094DTHJW26QQ" tabindex="0" row="8" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202509Z1O34VI5Y3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202509K7454MSBWI">​​默认值：true</span></p></td><td id="k_25202025106DVL9HCPS4" tabindex="0" row="8" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25202025104DN8X94I2Z"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25202025104PUC3GUHW9">​​是否需要阴影</span></p></td></tr><tr id="row_9"><td id="k_2520202511PJZFYHY69S" tabindex="0" row="9" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202511VQFUTD44BM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202511FKXBCO5PEY">​​timeout</span></p></td><td id="k_2520202512CBKGXGVSMT" tabindex="0" row="9" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202512C89BB3LJ3A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202512CBZ9JYYDTJ">​​默认值：0</span></p></td><td id="k_25202025127YTKL1T5E1" tabindex="0" row="9" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202512EZTHBURA8X"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202512LS8INSZTTG">​​延时自动关闭，单位秒，0表示不自动关闭</span></p></td></tr><tr id="row_10"><td id="k_2520202513EN9XX3OUF4" tabindex="0" row="10" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25202025131QD1SJNYXK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202513WY9J9FS3KX">​​mask</span></p></td><td id="k_25202025141B83CIZSEY" tabindex="0" row="10" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25202025144ORBJZ7P5H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202514ZK6747DRTW">​​默认值：true</span></p></td><td id="k_2520202515ODRDM88BGU" tabindex="0" row="10" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202515GR6GJH5AIF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202516UQFITRA2NM">​​是否需要蒙版遮罩</span></p></td></tr><tr id="row_11"><td id="k_2520202516KGEK2JZBWB" tabindex="0" row="11" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202516HPPQQFIL37"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202516ZX58N585J3">​​radius</span></p></td><td id="k_2520202517ZXISP1ILEV" tabindex="0" row="11" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25202025173GMFDPTQ16"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202517ZXAIODDJGH">​​默认值：false</span></p></td><td id="k_2520202518ON7J5AJ3T9" tabindex="0" row="11" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202518DK6J63IASI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202518P35FAB1AGM">​​是否需要圆角（2px）</span></p></td></tr><tr id="row_12"><td id="k_25202025184BD1UAHMYJ" tabindex="0" row="12" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202518R6DKGX4KIW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202518JF4RVFEJA5">​​header</span></p></td><td id="k_252020252023XPIRRQCE" tabindex="0" row="12" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202520WQ3O5OCMQ6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202520G3L9B9N875">​​默认值：true</span></p></td><td id="k_2520202521IW2K1RECTG" tabindex="0" row="12" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25202025211XXPAY5J59"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202521ZK949XGGEK">​​是否需要显示头部</span></p></td></tr><tr id="row_13"><td id="k_2520202522OCHG1CUKYF" tabindex="0" row="13" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202522BCY5SSYQ6W"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202522TDW3PMOFWO">​​content</span></p></td><td id="k_2520202522ORTXH6SRV7" tabindex="0" row="13" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202522X8Q3QGB6CY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202522LMTJAQ6QEB">​</span></p></td><td id="k_2520202523N5OR8BUCRV" tabindex="0" row="13" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202523OGC6UBT9A1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25221929687R8W1ZP2E4">窗口静态html文本内容</span></p></td></tr><tr id="row_14"><td id="k_2522192058ZBE9C5ZK56" tabindex="0" row="14" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522192059VM39TN9L9R"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522192060I8UYZI9RC2">​url</span></p></td><td id="k_2522192062J49JZAVCWY" tabindex="0" row="14" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522192063EHB1O1MACH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522192063KYQ197U4C2">​</span></p></td><td id="k_252219206553N83UEU3O" tabindex="0" row="14" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25221920664X22V8VEPI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522192067JZW3UCHN1X">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522193459AHU459F61M">​远程数据请求地址</span></p></td></tr><tr id="row_15"><td id="k_2000075139UG1ZGESJ5M" class="k_box_size" tabindex="0" row="15" col="0" index="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2000075140FP3BAYNUUF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2000075140APP6OU59FR">​opacity</span></p></td><td id="k_2000075143I6QQ2TKDR7" class="k_box_size" tabindex="0" row="15" col="1" index="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_20000751432TQ9QB9EPM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_20000751447TE648PIYJ">​​默认值：undefined</span></p></td><td id="k_2000075147GC678Z1ZAM" class="k_box_size" tabindex="0" row="15" col="2" index="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2000075148K3XM5YQ2W2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2000075148AYOZRVJGEQ">​遮罩层透明度设置</span></p></td></tr><tr id="row_16"><td id="k_2520202524E315L2VWI9" tabindex="0" row="16" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" class="k_box_size" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_252020252481LIBTWX8C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202524K3F56F98J8">​​dataType</span></p></td><td id="k_25202025246M2FFG85SK" tabindex="0" row="16" col="1" w="452" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" class="k_box_size" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202524J147RO5J5Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202524349DGHBBN2">​​默认值：html ；配合content配置为url时候使用</span></p></td><td id="k_2520202525K498GNW9N7" tabindex="0" row="16" col="2" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" class="k_box_size" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2520202525GOL9JHSKBA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520202525QU6ACH5LVY">​​远程请求数据类型，取值[html、iframe、json]</span></p></td></tr><tr id="row_17"><td id="k_2522145254OD98FZJH4Y" tabindex="0" row="17" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522145255UIOUJIZ7JO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522145255TKUXW7R1IR">​​draggableHandler</span></p></td><td id="k_2522145257M8C6Q1OL6D" tabindex="0" row="17" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522145258181V71Z1PA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25222016824IYAEHS5PE">默认值：undefined</span></p></td><td id="k_2522145260HJ842W2USL" tabindex="0" row="17" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522145261W8ZJGB78WW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522145261GYY9GE4S8C" class="">​​定义响应拖动的区域，取值[header、body]，header表示标题部位响应拖动</span></p></td></tr><tr id="row_18"><td id="k_25221451925XT1GIJTPU" tabindex="0" row="18" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522145193X1OIGPP6BN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522145194TD7D72FPQP">​​draggable</span></p></td><td id="k_2522145196IKSZCBALOD" tabindex="0" row="18" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25221451964Q16DZF9S3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522145197CQ2W6UXG4X">​​默认值：true</span></p></td><td id="k_2522145199Q8AQ799T33" tabindex="0" row="18" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_252214510063ZWP4YHNS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522145100B7JQRQLUJW">​​是否启用拖动</span></p></td></tr><tr id="row_19"><td id="k_2522144984A74Z6L4N8K" tabindex="0" row="19" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_252214498542HKQIHFSB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522144986QOVB4P8L23">​​moveProxy</span></p></td><td id="k_2522144987EEF4I6YT2W" tabindex="0" row="19" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522144988QQO2V9MPJZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522144989IO5LSCGDKL">​​默认值：false</span></p></td><td id="k_2522144990YI4PM9D12W" tabindex="0" row="19" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522144991GWGJFCTWTE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522144992N3BHFOG99I">​​是否启用代理拖动</span></p></td></tr><tr id="row_20"><td id="k_2522144887P5JGDGA6IR" tabindex="0" row="20" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522144888T9W49V7Y83"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522144889U8QVP6FB9M">​​position</span></p></td><td id="k_2522144891Q5OM3MJXH8" tabindex="0" row="20" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size k_edit_selected_td_shadow"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522144891US17L1TOWT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25221448925MXI8VJJ7L">​​默认值：undefined，页面中间显示</span></p></td><td id="k_2522144894O5WEF8I8S2" tabindex="0" row="20" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522144894HE5BUXLLCS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522144895RBAZC22RDE">​​定义窗口显示位置，取值:top(顶部中间)、bottom(底部右下角)，固定位置：{top: x ,left :y }</span></p></td></tr><tr id="row_21"><td id="k_25221447909OVJ9MMQG8" tabindex="0" row="21" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522144791JBCHRUFGO2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522144792NB57E4TCA5">​​closeable</span></p></td><td id="k_252214479474ZVRBGM3E" tabindex="0" row="21" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522144795F32PESIVEE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25221702902XMBX15C7Y">默认值：true</span></p></td><td id="k_252214479795ZMMTZII2" tabindex="0" row="21" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522144798AGLGCCULCX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25221447999AFY9OJ673">​​是否启用右上角关闭按钮</span></p></td></tr><tr id="row_22"><td id="k_2522165232HC5A49C5KC" tabindex="0" row="22" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25221652339GC6X1XXYP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522165233APFC6T26V8">​​maxminable</span></p></td><td id="k_2522165235SFLOQ25PJI" tabindex="0" row="22" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522165236VXNF61E4MR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522165237N818MRP7XF">​​默认值：true</span></p></td><td id="k_2522165238GWJXFFGECL" tabindex="0" row="22" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522165239E5OTTDMRQL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522165240L2FQLTWIM1">​​是否启用扩大缩小功能</span></p></td></tr><tr id="row_23"><td id="k_25221651678A27UGT3S1" tabindex="0" row="23" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522165168YKMU6F1RM1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522165169LBGPC9FBBS">​​collapseable</span></p></td><td id="k_25221651712XYLX34DJ6" tabindex="0" row="23" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25221651728VFFW18M54"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522165172BOQ7K495P7">​​默认值：true</span></p></td><td id="k_2522165174A2BAOMVRSU" tabindex="0" row="23" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522165175BAOG94HQHE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25221651755XJBUZ8D41">​​是否启用上下收缩</span></p></td></tr><tr id="row_24"><td id="k_2522201195ODDOUMZGXB" tabindex="0" row="24" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522201196SEQ3K412WJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522201197S8Z2S38D9Z">​​toolbar</span></p></td><td id="k_25222011009FZ4XAHDQX" tabindex="0" row="24" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25222011011CRF9659F6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25222011015ZNSPU44I4">​​默认值：undefined</span></p></td><td id="k_2522201103P4MEXMDK6Q" tabindex="0" row="24" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25222011041SU166HCOT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522201105GCKH8LABF4">​按钮工具栏定义，​请参考toolbar组件说明</span></p></td></tr><tr id="row_25"><td id="k_25222044023XA93S5PU2" tabindex="0" row="25" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522204403B4KMHG84WC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522204404IM86ISRT7D">​​createToolsFn</span></p></td><td id="k_25222044066NKUYTWLZP" tabindex="0" row="25" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522204406OS7VXIZQ1G"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522204407HCJCR28UO9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_252221345865TQ7SHGK7">​​​createToolsFn = function(){&nbsp; return [] };</span></p></td><td id="k_2522204409M5QGTJVEVD" tabindex="0" row="25" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522204410ABFAXZNDFP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25222044113LCMMX3U4B">​​创建工具栏的函数，该函数返回toolbar定义数组，忽略toolbar配置</span></p></td></tr><tr id="row_26"><td id="k_2522165072ULGEEGN52M" tabindex="0" row="26" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 229px;" w="227" h="53" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25221650724KX4KQZLOF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522195039J9V6B8MKEU">onResized</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522205224VXH3HUOORS">&nbsp;</span></p></td><td id="k_2522165075B93ONERS28" tabindex="0" row="26" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 454px;" w="452" h="53" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522165076RJU4CN2FPO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25221650764FW58CFW82">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25222058541WH7H64IZS">​ ​onResized = function(pr) {}&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2522211720CUSQAXJ145"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522211721NFGNHHSFNP">pr：min / max&nbsp;&nbsp;</span></p></td><td id="k_2522165078EDX1U2JL88" tabindex="0" row="26" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 626px;" w="624" h="53" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522165079UHNIITW1VL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522165080LKBVUG4ZEX">​​扩大、缩小回调</span></p></td></tr><tr id="row_27"><td id="k_2522222385B6HJMGXW2L" tabindex="0" row="27" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 229px;" w="227" h="53" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522222386T1QM53NO89"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25222229677SERQJRRV1">onLoaded</span></p></td><td id="k_2522222388LZI147BPEV" tabindex="0" row="27" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 454px;" w="452" h="53" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25222223893I6FCFHKOI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522223663OBFVL5B5WF">onLoaded</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522223664YMVSZZZ5MK">=</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25222236655L26T9HM6V">function (data){}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_25222242649VDQTWI3AY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522224265SCIXADV8FD">​</span></p></td><td id="k_2522222391397DF9F67A" tabindex="0" row="27" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 626px;" w="624" h="53" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522222392J32VY5Y4VI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522222393XL9CCSWHY5">​​加载完成回调</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2522225928OJ4FEVOZSA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522225929GBHMVBX5NA">​​如果是JSON数据请求，data则为返回的数据</span></p></td></tr><tr id="row_28"><td id="k_25222222218544HMQ4OU" tabindex="0" row="28" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522222222LZUJTB3TGS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522231235ECXANSRJZH">onClose</span></p></td><td id="k_2522222225G3OW2LDKVG" tabindex="0" row="28" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522222226UJBMOZYE58"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522231607PQ8W25T95P">onClose</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25222316083BDY52SSY3">=</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522231609OKFPOMQT3M"> function (){}</span></p></td><td id="k_2522222229AIXCXQ6CUQ" tabindex="0" row="28" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522222230S449BGKDQL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522222231GHDTXK47XJ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522232953O3DZ72JUFU">​关闭前回调，​return true则执行关闭，return false不执行关闭</span></p></td></tr><tr id="row_29"><td id="k_2522164995K14QE261XL" tabindex="0" row="29" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522164996WRWCVX6S5Q"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522234478H4XGRV1YYB">onClosed</span></p></td><td id="k_25221649997IB3RBOIR8" tabindex="0" row="29" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522165000SQJ1F2N3GL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522234830K5KWZSVVP3">onClosed</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522234831X1Y6AWMDD4">=</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522234832OEI69H8UVV"> function (){}</span></p></td><td id="k_25221650033OJISYIUMC" tabindex="0" row="29" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25221650039I6OU8QJGT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522165004KSAWQMTNXL">​​关闭后回调</span></p></td></tr><tr id="row_30"><td id="k_2522240227DWFGRYODZN" tabindex="0" row="30" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 229px;" w="227" h="53" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25222402284FD9Q82GMZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522241662YZ1E87TEGO">onCollapsed</span></p></td><td id="k_2522240230DVCO1GABXX" tabindex="0" row="30" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 454px;" w="452" h="53" index="1" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522240231DEUNDH2NEN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522242076ZJU8I6PD4J">onCollapsed</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522242077GOF8A311RJ">=</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_252224207889ND1DXE9C"> function (flag){}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2522243208YB98ZGY8AT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522243209KREXJ8R6MG">​​flag:up / down</span></p></td><td id="k_2522240233FSB1E1EF6X" tabindex="0" row="30" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 626px;" w="624" h="53" index="2" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_25222402344RVGXL91FB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522240235I4B5P8FBI7">​​上下收缩回调</span></p></td></tr><tr id="row_31"><td id="k_1923315172CHWJW129WB" class="k_box_size" tabindex="0" row="31" col="0" index="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_19233151737N7U8E3EEL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_19233151745TFZ5OFW6P">​​onStartDrag</span></p></td><td id="k_1923315176CVR2O89MR1" class="k_box_size" tabindex="0" row="31" col="1" index="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_1923315177X9NAFUKCDR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_1923315177N2YAAVG2N5">​</span></p></td><td id="k_1923315179ZQ9UYYPMXQ" class="k_box_size" tabindex="0" row="31" col="2" index="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_1923315180XX1HUCXXK1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_1923315181BBEG8AN7FO">​​开始拖动回调，请参考《拖动 Draggable》</span></p></td></tr><tr id="row_32"><td id="k_19233150097JGZUWLVXG" class="k_box_size" tabindex="0" row="32" col="0" index="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_19233150109WD5DHE62K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_1923315010KHIYQYJN54">​​onDrag</span></p></td><td id="k_1923315012PSWFOCB8GI" class="k_box_size" tabindex="0" row="32" col="1" index="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_1923315013S5O166SP9U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_1923315014T4UPIFYXVX">​</span></p></td><td id="k_1923315016RYPB3Q8GXM" class="k_box_size" tabindex="0" row="32" col="2" index="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_1923315017QPR56JQZJQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_1923315017TWRPSKYURE">​​拖动中回调，请参考《拖动 Draggable》</span></p></td></tr><tr id="row_33"><td id="k_19233149544EYKBTP16E" class="k_box_size" tabindex="0" row="33" col="0" index="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_1923314955TL7OTJYZ4Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_19233149577HF7U3X7VI">​​onStopDrag</span></p></td><td id="k_19233149602HCN93B8J6" class="k_box_size" tabindex="0" row="33" col="1" index="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 454px;" w="452" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_1923314961AYZNTW8TTT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_1923314962VG8TEZOX45">​</span></p></td><td id="k_1923314966HDY956447D" class="k_box_size" tabindex="0" row="33" col="2" index="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;" w="624" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_1923314967L8WUXV7L22"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_1923314968LRMY4RN6RK">​​拖动结束回调，请参考《拖动 Draggable》</span></p></td></tr></tbody></table></div><div id="k_2520084836ZHY8CLY362" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520084836MY2GNULMFJ">​</span></div><div id="k_2522274880TQ79QAZTBB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 26px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" contenteditable="false"><table style="border-collapse: collapse; width: auto; height: auto; " id="201903252228239Z1XV6HYADGY"><tbody><tr id="row_0"><td id="k_2522282321526A7PZQT5" tabindex="0" row="0" col="0" w="1309" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1311px; background-color: rgb(240, 255, 253);" colspan="3" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 255, 253);" id="k_2522282322ZKV5DRDNMZ"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 253);" id="k_2522282323UZMWLLGTZX">​实例 API</span></p></td></tr><tr id="row_1"><td id="k_2522282331IT4JWH9NRI" tabindex="0" row="1" col="0" w="1309" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1311px; background-color: rgba(0, 0, 0, 0);" colspan="3" index="0" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2522282332AFCJ1DWEUO"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2522300613XA1HHXD4I8">请参考 Panel 组件的 实例API说明</span></p></td></tr></tbody></table></div><div id="k_2522274995RWLJ52WU9F" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522274996AV8SOR8TNU">​</span></div><div id="k_2520084894GCTMB6MUEM" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 255, 253); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(160, 32, 240); border-image: initial; margin-top: 0px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253); font-weight: 400; font-style: normal; text-decoration: none solid rgb(153, 0, 51); vertical-align: baseline;" id="k_2520114292A96OX9DJZ1">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2520114293HPAXR5ZW8K">​<img id="k_25201142931N21BTXJ5J" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABVElEQVQoU5WRv0sccRDFP++7nhAshFNuBfMHWGmhpBfShAiCjaBgJwmChVgqWmkR0VRCDoKNnFhopYUgCBZCGgsLOxubeLuKhQrij52RNRHOw8ap3+fNvDfinaN/elcJOhUYF3x2KEocWMZiCnsge/FVLo4jBuUsI47N2Q1w79AF9AkWq8YP0G0OqRXvaQhsOJQTYwl097I1hl4FVlzMJRm/Qa44+Lyg/d4Yv0RXryO5SoEJiS9ZxsgFOlMc+bY7W6mp/Fb+Vry7IVB5NIYv0KHaIt8xWE0zVd4CWvCOQmDNjbEE/VFb8GWDm9SYAj3WQyV8QBGTIWPoDJ0qxvsJLMgYraL9WqCIfyxE/MI5SozZ3DAHmhTy2vjq8PPB2GyEvKlPRMw6/PWMbylKnmv9X+GHOPBdMI0oPr/SuQbWzZg5R9Wax9Ue4Y0xtBs0Ayfn6KY+0xPgDH4N4O/JMAAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253); font-weight: 400; font-style: normal; text-decoration: none solid rgb(153, 0, 51); vertical-align: baseline;" id="k_2520114293B4TJEA4IP5">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 255, 253); font-weight: 400; font-style: normal; text-decoration: none solid rgb(153, 0, 51); vertical-align: baseline;" id="k_2520114292XTB8AR9A69">&nbsp;Demo</span></div><div id="k_2520084927YYP7JYAN4Q" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: 1px dotted rgb(160, 32, 240); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-bottom: 18px; margin-top: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255);" id="k_2522332834HGJ83I8MVH">$B.window( opts&nbsp; )&nbsp; &nbsp;普通窗口</span></div><div id="k_2522305157BGQSHAZVCB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522305158SWW7CMXWGR"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">$B</span><span style="background: none; color: rgb(167, 167, 167);">.</span><span style="background: none; color: rgb(175, 175, 125);">window</span><span style="background: none; color: rgb(167, 167, 167);">({</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">width:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">400</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">height:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">200</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"fa-wechat"</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'操作提示'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//标题</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">shadow:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否需要阴影</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">timeout:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">2</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//5秒钟后自动关闭，0表示不自动关闭</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">mask:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否需要遮罩层</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">radius:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否圆角</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">header:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否显示头部</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">position:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">pos</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// top :顶部中间，bottom：右下角</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">content:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'&lt;p&gt;您的操作成功了&lt;/p&gt;'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">dataType:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'iframe'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//当为url请求时，html/json/iframe</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(61, 108, 40); background: none;">//draggableHandler:'body',//拖动触发焦点</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">draggable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否可以拖动</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(61, 108, 40); background: none;">//moveProxy:true,//代理拖动</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">closeable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否关闭                   </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">maxminable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//可变化小大</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">collapseable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//上下收缩                   </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onClose</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> () { </span><span style="color: rgb(61, 108, 40); background: none;">//关闭前</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"可以关闭了.................."</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(152, 89, 147); background: none;">return</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onClosed</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> () { </span><span style="color: rgb(61, 108, 40); background: none;">//关闭后</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"window关闭了.................."</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">  });</span></div></div></pre></span></div><div id="k_2522305172VJ2I24NG2L" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: 1px dotted rgb(160, 32, 240); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-top: 30px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_25223556432KZHB7RU2Y">$B.message( opts)&nbsp; &nbsp;提示信息</span></div><div id="k_2522392999FIICR3Q8QV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 30px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_2522392901CRE6UJSXE9"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">            </span><span style="background: none; color: rgb(111, 175, 209);">$B</span><span style="background: none; color: rgb(167, 167, 167);">.</span><span style="background: none; color: rgb(175, 175, 125);">message</span><span style="background: none; color: rgb(167, 167, 167);">("这是一个提示信息，5秒后关闭!" , 5 </span><span style="color: rgb(167, 167, 167);">);</span></div></pre></span></div><div id="k_2522311445SS9IDU8M7W" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522311446NAUMBWBCKB"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; color: rgb(0, 137, 255);">  <span style="background: none; color: rgb(165, 165, 255);">      /**信息提示框参数</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">        *args={</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                title:'请您确认',</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                iconCls:'图标样式',</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                message:'提示信息！', //提示信息或者创建html内容的函数</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                contentIcon:'内容区域的图标',</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                iconColor:'',//内容区域图标颜色</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                width: ,//宽度</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                height:,//高度         </span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                timeout:自动消失时间,</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                mask://是否需要遮罩,</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                toolbar:[],//工具栏的按钮</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">                createToolsFn://创建工具按钮的函数，优先调用创建函数</span></div><div style="background: none;"><span style="color: rgb(165, 165, 255); background: none;">        }</span></div></div><div style="background: none;"><span style="background: none; color: rgb(165, 165, 255);">        ***/​</span><span style="background: none; color: rgb(122, 122, 122);"><span style="background: none; color: rgb(0, 137, 255);"> </span>           </span></div><div style="background: none;"><span style="background: none; color: rgb(66, 130, 164);">        $B</span><span style="background: none; color: rgb(122, 122, 122);">.</span><span style="background: none; color: rgb(130, 130, 80);">message</span><span style="background: none; color: rgb(122, 122, 122);">({</span></div><div style="color: rgb(122, 122, 122); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">title:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'提示！'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">position:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'top'</span><span style="color: rgb(122, 122, 122); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// top :顶部中间，bottom：右下角</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">message:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'提示信息或者创建html内容的函数！'</span><span style="color: rgb(122, 122, 122); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//提示信息或者创建html内容的函数                </span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">width:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(91, 116, 78); background: none;">500</span><span style="color: rgb(122, 122, 122); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//宽度</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">height:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(91, 116, 78); background: none;">150</span><span style="color: rgb(122, 122, 122); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//高度         </span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">timeout:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(91, 116, 78); background: none;">0</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">mask:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">            });</span></div></div></pre></span></div><div id="k_2522311501IPV7QFB8RR" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: 1px dotted rgb(160, 32, 240); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-top: 12px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_2522311502HBZFZP9P4W">​$B.success( opts )&nbsp; &nbsp; 成功提示</span></div><div id="k_25224219138YCQXJSQ15" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25224219141EV8E18UBN"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">           </span><span style="background: none; color: rgb(111, 175, 209);">$B</span><span style="background: none; color: rgb(167, 167, 167);">.</span><span style="background: none; color: rgb(175, 175, 125);">success</span><span style="background: none; color: rgb(167, 167, 167);">(" 您的提交已经成功处理！ " , 3</span><span style="color: rgb(167, 167, 167);">);</span></div></pre></span></div><div id="k_2522434715SHHHOHM9XI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_252243471619TH5X574T">​</span></div><div id="k_2522315816UQB2GR171O" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; color: rgb(0, 137, 255);">​      /**成功信息参数</div><div style="color: rgb(122, 122, 122); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">        *arg={</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                message:'提示内容',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                width:,</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                height:,</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                iconCls:'图标样式',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                timeout:自动消失时间,</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                title:'标题',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                mask://是否需要遮罩</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">        }</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">        ***/</span></div><div style="background: none;"><span style="color: rgb(61, 108, 40); background: none;"><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">      </span><span style="color: rgb(111, 175, 209); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">success</span><span style="color: rgb(167, 167, 167); background: none;">({</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">width:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">300</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">height:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">120</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'操作成功！'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">position:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'top'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// top :顶部中间，bottom：右下角</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">message:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'这是个成功信息！'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//提示信息或者创建html内容的函数 </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">timeout:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">2</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">mask:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            });</span></div></div></span></div><div style="background: none;"><span style="color: rgb(61, 108, 40); background: none;"><br style="background: none;"></span></div></div></pre></span></div><div id="k_2522403472LTI83RGI56" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522403473BIR6ZGGDMP">​</span></div><div id="k_2522315886MKDOQMBYTE" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: 1px dotted rgb(160, 32, 240); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-top: 12px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_2522315887BS1B1CVTFB">$B.alert( opts )​&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;普通提示信息</span></div><div id="k_2522322042VGTJX1PDDM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522322043IUKIRG8LC6"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(111, 175, 209); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">alert</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"这是个温馨的警告信息！"</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div></pre></span></div><div id="k_252244445887VO85EY1S" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25224444592M56EPHIRD">​</span></div><div id="k_2522451747LDHC5J6HIB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522451748N7KESHSPYZ"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(122, 122, 122);"> <span style="background: none; color: rgb(0, 137, 255);">       </span></span><span style="background: none; color: rgb(0, 137, 255);">/**警告信息</span></div><div style="color: rgb(122, 122, 122); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">        *arg={</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                message:'提示内容',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                width:,</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                height:,</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                iconCls:'图标样式',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                timeout:自动消失时间,</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                title:'标题',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                mask://是否需要遮罩</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">        }</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">        ***/</span></div><div style="background: none;"><span style="color: rgb(61, 108, 40); background: none;"><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">        </span><span style="color: rgb(61, 108, 40); background: none;">$B.alert({</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(61, 108, 40); background: none;">        width: 300,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(61, 108, 40); background: none;">         height: 120,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(61, 108, 40); background: none;">        title:'警告信息！',</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(61, 108, 40); background: none;">         position: 'bottom', // top :顶部中间，bottom：右下角</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(61, 108, 40); background: none;">         message:'这是个警告信息信息！', //提示信息或者创建html内容的函数 </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(61, 108, 40); background: none;">         timeout:2,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(61, 108, 40); background: none;">        mask:true</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">         </span><span style="color: rgb(61, 108, 40); background: none;">});</span></div></div></span></div></div></pre></span></div><div id="k_2522444429T4SBW2AB7S" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25224444319KPKDBNU12">​</span></div><div id="k_25224444264OOS4BUKIJ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25224444283SKE73RURS">​</span></div><div id="k_2522322099GUZYJ8Y5BT" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: 1px dotted rgb(160, 32, 240); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-top: 12px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_25223220008SG4NJJXXQ">​$B.error( opts )&nbsp; &nbsp; &nbsp; &nbsp; 错误提示信息</span></div><div id="k_252231587136PIOHHCZZ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_25223158751VXKOOSM9A"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(111, 175, 209); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">error</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"这是个美丽的错误！"</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div></pre></span></div><div id="k_2522463793BCNL6E5Z7C" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522463794PG1VRG13U7">​</span></div><div id="k_2522463850LWLOV978WR" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_252246385273R98EYWXS"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; color: rgb(0, 137, 255);">​     /**message 错误信息对话框</div><div style="color: rgb(122, 122, 122); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">         * opts = {</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                width: 400,</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                height: 150,</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                title: config.errorTitle, //标题</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                iconCls: 'fa-cancel-2', //图标cls，对应icon.css里的class</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                shadow: true, //是否需要阴影</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                timeout: 0, //5秒钟后自动关闭</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                mask: true, //是否需要遮罩层</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                contentIcon: 'fa-cancel-circled',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                iconColor: '#F7171C',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                draggableHandler: 'header'</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">        }</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">        * ***/</span></div><div style="background: none;"><span style="color: rgb(61, 108, 40); background: none;"><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">        </span><span style="color: rgb(111, 175, 209); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">alert</span><span style="color: rgb(167, 167, 167); background: none;">({</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">width:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">300</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">height:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">120</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(161, 100, 75); background: none;">'警告信息！'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">position:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'bottom'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// top :顶部中间，bottom：右下角</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">message:</span><span style="color: rgb(161, 100, 75); background: none;">'这是个警告信息信息！'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//提示信息或者创建html内容的函数 </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">timeout:</span><span style="color: rgb(136, 161, 123); background: none;">2</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">mask:</span><span style="color: rgb(41, 111, 169); background: none;">true</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            });</span></div></div></span></div></div></pre></span></div><div id="k_25224638877UJ3OUKD9G" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522463888JPTXX5MTOU">​</span></div><div id="k_2520085094HV63BZQRMY" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: 1px dotted rgb(160, 32, 240); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-top: 12px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_2522361744MRXV25F64G">$B.confirm( opts )&nbsp; &nbsp;确认对话框</span></div><div id="k_2520085035R1VMF79SF3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 27px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2520085036LBDAN4SZDL"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none; color: #A5A5FF;">        /** 确认提示框</div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">         * args={</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                title:'请您确认',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                iconCls:'图标样式',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                message:'提示信息！',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                toolbar:[],//工具栏，如果传入，则不生成默认的按钮</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                contentIcon:'内容区域的图标',</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                width: ,//宽度</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                height:,//高度</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                okIcon = "fa-ok-circled",</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                okText = "确认",</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                noIcon = "fa-ok-circled",</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                noText = "取消",</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                okFn:fn, //确认回调</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                noFn:fn, //否定回调</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                contentFn:,//创建内容的函数，返回jq标签</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">                createToolsFn://创建工具按钮的函数，当不存在toolbar的时候生效，有默认的createToolsFn，this为工具类的jq对象容器</span></div><div style="background: none;"><span style="color: rgb(0, 137, 255); background: none;">        }</span></div></div><div style="background: none; color: rgb(0, 137, 255);">        * ***/​</div><div style="background: none;"><br style="background: none;"></div><div style="background: none;"><div style="color: rgb(122, 122, 122); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">     </span><span style="color: rgb(66, 130, 164); background: none;">$B</span><span style="color: rgb(122, 122, 122); background: none;">.</span><span style="color: rgb(130, 130, 80); background: none;">confirm</span><span style="color: rgb(122, 122, 122); background: none;">({</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">               <span style="color: rgb(255, 0, 209); background: none;"> </span></span><span style="color: rgb(61, 108, 40); background: none;"><span style="color: rgb(255, 0, 209); background: none;"><span style="color: rgb(0, 137, 255); background: none;">//position: 'top', // top :顶部中间，bottom：右下角</span> </span>      </span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">message:</span><span style="color: rgb(161, 100, 75); background: none;">'&lt;span style="color:red;"&gt;您真的要删除数据吗？&lt;/span&gt;'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(130, 130, 80); background: none;">okFn</span><span style="color: rgb(66, 130, 164); background: none;">:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(122, 122, 122); background: none;"> () {</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(130, 130, 80); background: none;">alert</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"ok"</span><span style="color: rgb(122, 122, 122); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(130, 130, 80); background: none;">noFn</span><span style="color: rgb(66, 130, 164); background: none;">:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(122, 122, 122); background: none;"> () {</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(130, 130, 80); background: none;">alert</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"no"</span><span style="color: rgb(122, 122, 122); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                }</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">     });</span></div></div></div></pre></span></div><div id="k_2522074583NLKQZZHT1W" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2522074585I12KL6AVBV">​</span></div><div id="k_2523012850X9BD6HGJXN" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2523012851XPGOXFPVQ8">​</span></div><div id="k_1923314312VG4J27PKAT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_1923314314LUQP3E7XK7">​</span></div><div id="k_2000074370SWRO2PN4GC" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2000074371T8AMMGC3OR">​</span></div>
</div>