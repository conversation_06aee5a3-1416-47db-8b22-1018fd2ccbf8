<div style="position:relative">

    <div id="k_2418012296DHB3IP7HFW" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 250, 255); border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(153, 0, 51); border-image: initial; padding-left: 15px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 250, 255);" id="k_2418045034MGZJ8WZ9NY">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_24180450382NC61S9JAV">​<img id="k_24180450393AY85T9OSZ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAA8klEQVQ4T7WSwUqCQRRGz71Dm0KREvQ5EtcueggFW7SIXqJauhGCQhciSIs2Cb5AT9SqpEQhYv4bRmPT/w+2qdnNcA/33O+OEJ0qdugcfYGj8GzG9D3j7Bl5jWslXKpYwzluBBZLz/ECeSphB7tKb5Vxvr4XwC/oUjzXpnRC4VYwQHguVvAYd/gEHROBCtDadDTupK52i3CSmqmONc0xEJjn9aWmNkrNEEwyz5Uq3bx+EvxVX+kVwBq2J8ow1gdmb57TOfISAiuA25Jc5/An4L0I7Xi5wMPS080v/UfHHLBRSSX9P+A+Vt5Rxgn9749jTD8AO06uup3TOPgAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 250, 255);" id="k_2418045041EG6YDCWMA9">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 250, 255);" id="k_24180458386KHYGSYR5S">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 3, 55); background-color: rgb(247, 250, 255);" id="k_2418045839MXQHKNJERL">&nbsp;构造参数</span></div><div id="k_2418024492I1P84MOR7A" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324180610MAFSJR9T7YKL"><tbody><tr id="row_0"><td id="k_2418061065U7QJ1OEI9D" tabindex="0" row="0" col="0" w="1343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1345px; background-color: rgb(252, 255, 255);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(252, 255, 255);" id="k_24180610667IFX48LNMR"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255);" id="k_2418061068NOZE882JKC">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255);" id="k_2418064105QOGH2UCWWB">​var layout = new $B.Layout($("body"),items);&nbsp; items 参数说明</span></p></td></tr><tr id="row_1"><td id="k_2418061080RCPUDF3YQB" tabindex="0" row="1" col="0" w="259" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 261px; background-color: rgb(252, 255, 255); text-align: center; font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_2418061081Q3NHBVFEPZ"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_241806108278ZZIT3JQT">​参数名</span></p></td><td id="k_2418061085FOXK9UWHVC" tabindex="0" row="1" col="1" w="486" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 488px; background-color: rgb(252, 255, 255); text-align: center; font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_2418061087O4HXES1O2U"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_2418061088SXF9SUGMOB">参数值​</span></p></td><td id="k_2418061091548WILU1VB" tabindex="0" row="1" col="2" w="594" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 596px; background-color: rgb(252, 255, 255); text-align: center; font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_24180610942VDINCT43E"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_24180610955NFZZRSWER">说明​</span></p></td></tr><tr id="row_2"><td id="k_2418061098UDEFDKRG5J" tabindex="0" row="2" col="0" w="259" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 261px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061099CTSJCX3V3A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061000PANUXXNAN3">​​title</span></p></td><td id="k_2418061005GX6WI9AKON" tabindex="0" row="2" col="1" w="486" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 488px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241806100681IWT72YIT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061007EMWM45FX6A">​​默认值：空字符串</span></p></td><td id="k_24180610095NRGRFOADP" tabindex="0" row="2" col="2" w="594" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 596px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061010DVM2TH9TJ2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061011I3H2XWYIGB">​​标题</span></p></td></tr><tr id="row_3"><td id="k_2418061014SU14HWI6HP" tabindex="0" row="3" col="0" w="259" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 261px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061014TF6YAL5IFA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061015756VVHTR31">​​width</span></p></td><td id="k_2418061016VENEY37BOO" tabindex="0" row="3" col="1" w="486" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 488px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061017M5OII4TILI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241806101827G86QUCA1">​​默认值：auto</span></p></td><td id="k_2418061020ZK593JO1O3" tabindex="0" row="3" col="2" w="594" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 596px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061021FZKOJI8NK7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24180858947E3O8NQXAW">宽度</span></p></td></tr><tr id="row_4"><td id="k_2418061026TW7SP5IUYB" tabindex="0" row="4" col="0" w="259" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 261px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061027BFUDPLRZME"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061028RRBPV6HPS2">​​expandable</span></p></td><td id="k_24180610309Z6YJ25819" tabindex="0" row="4" col="1" w="486" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 488px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24180610303WMBWF2KYS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061031HD1PLH673W">​​默认值：false</span></p></td><td id="k_2418061033THEDGMEZPD" tabindex="0" row="4" col="2" w="594" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 596px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061033VA7QEYOPV9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061034Q24N3WPJFY">​​是否可左右收缩</span></p></td></tr><tr id="row_5"><td id="k_2418061035ZSNLPJ9EI7" tabindex="0" row="5" col="0" w="259" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 261px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061036JAQLEZUIJH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061037OPV2F7C1P8">​​iconCls</span></p></td><td id="k_2418061039KAQRIZNQED" tabindex="0" row="5" col="1" w="486" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 488px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061040KHNL6KISFA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061041WHDG5YLEXW">​​默认值：undefined</span></p></td><td id="k_2418061043LJKKMVG5NO" tabindex="0" row="5" col="2" w="594" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 596px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061044JF8S9S9TTW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24180610456BOMDZZY9G">​​图标样式</span></p></td></tr><tr id="row_6"><td id="k_2418061046XTGUXM45UY" tabindex="0" row="6" col="0" w="259" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 261px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24180610477KJI3ZO6UX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061048CIHVP949RQ">​​header</span></p></td><td id="k_2418061049NPSZ2D3CKN" tabindex="0" row="6" col="1" w="486" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 488px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418061050PIIOJLIZDS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061051UJQJWGQJ32">​​默认值：true</span></p></td><td id="k_2418061053L2JDQQ1TZ6" tabindex="0" row="6" col="2" w="594" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 596px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24180610535T4MR8KZQS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418061054DCWS1NZAMN">​​是否需要标题</span></p></td></tr><tr id="row_7"><td id="k_2418100999TBCEOG732K" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 261px;" w="259" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24181009006Y7M39PG7Z"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418100901515TY6ENXE">​​content</span></p></td><td id="k_2418100904BEYNC3EXU8" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 488px;" w="486" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418100905VDEPWEV4M8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418100906XTJHNUIN7B">​​默认值：undefined</span></p></td><td id="k_24181009076EA99UUJY5" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 596px;" w="594" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24181009086YWGO6UM62"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418100909ICVJ5FB414">​​静态HTML内容或者jquery标签对象 / 远程请求地址</span></p></td></tr><tr id="row_8"><td id="k_2418101139PFSWQC325D" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 32px; width: 261px;" w="259" h="30"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418101140VPUTPJFLQI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24181011417KREA8ELRZ">​​dataType</span></p></td><td id="k_2418101143EA54MTTWH4" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 32px; width: 488px;" w="486" h="30"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418101144K4B6OLDVUY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241810114549N8S1NLR7">​​默认值：html</span></p></td><td id="k_2418101148DJD1SIRXOS" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 32px; width: 596px;" w="594" h="30"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418101149W2189TOFD7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418101150M85Q76RVY5">​​请求数据类型，配合content配置使用，可取值【html / iframe / json】</span></p></td></tr><tr id="row_9"><td id="k_2418115519NLBTHPDTST" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 261px;" w="259" h="78"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24181155207TMSZODVBX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24181155213C7KOP294R">​​onLoaded</span></p></td><td id="k_2418115523PEOTA689TO" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 488px;" w="486" h="78"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24181155243MQDLTA2NH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418115524NU5H5O24K3">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418121642M84LHAOY8G">​onLoaded =&nbsp; function (data,params){}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418123954GLS2VBZESY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418123954ETDJYEYA27">​​data：远程加载返回的数据</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418124485R7RNC5CM2H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418124486A2HD2PY92X">params：标识当前加载面板的参数，如{"i":1,"title":"tab模块"}</span></p></td><td id="k_2418115526FLWOBE4ABQ" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 596px;" w="594" h="78"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418115527BHHQPWXALF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418115527KVAO8A9OP6">​​加载完成回调</span></p></td></tr><tr id="row_10"><td id="k_2418125573HQ2XMA9ZD9" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 261px;" w="259" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418125574FLGSKSM3TL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418130619X3NKC9F4IB">onCreated</span></p></td><td id="k_2418125579TUGO2Q77QE" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 488px;" w="486" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24181255804TKMRPMEMX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24181255823LV1IB8I6Y">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418131070YC46EMP9S9">​onCreated&nbsp; = function (params){}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418131914DJ4V3OP8EC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418131915D2CY3EJ4NZ">​​params：标识当前加载面板的参数，如{"i":1,"title":"tab模块"}</span></p></td><td id="k_2418125584MMUSF9IGX5" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 596px;" w="594" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418125584XFAK5KQ7G2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418125585TN4MJX72O7">​​创建完成回调</span></p></td></tr></tbody></table></div><div id="k_24180244895QMAJX21T5" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24180244913S6RDZGWWJ">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_241814346132TYZFMQLT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324181440J9RPW9SHFL2C"><tbody><tr id="row_0"><td id="k_2418144025KBWLJ2ZDWC" tabindex="0" row="0" col="0" w="1297" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1299px; background-color: rgb(252, 255, 255);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(252, 255, 255);" id="k_2418144026YYJ3YG88ZY"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); letter-spacing: 1px;" id="k_2418153136E4XJPPI5JJ">实例API&nbsp;</span></p></td></tr><tr id="row_1"><td id="k_2418145220VOKRJBCKWN" tabindex="0" row="1" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 274px; background-color: rgb(252, 255, 255); text-align: center; font-size: 16px;" w="272" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_24181452215EMHVQUCDI"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_24181452214T4CRP2NVU">名称​</span></p></td><td id="k_2418145223OTLMV3B4FC" tabindex="0" row="1" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 450px; background-color: rgb(252, 255, 255); text-align: center; font-size: 16px;" w="448" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_2418145224NQU5YTG39J"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_2418145225R7C46Y2VL2">参数​</span></p></td><td id="k_2418145226Y6WOLNTQJ5" tabindex="0" row="1" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 575px; background-color: rgb(252, 255, 255); text-align: center; font-size: 16px;" w="573" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_24181452275ZFT2A3XGT"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_2418145227VYXEE66GYI">说明​</span></p></td></tr><tr id="row_2"><td id="k_2418145059VQI6NIEMWO" tabindex="0" row="2" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 274px;" w="272" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24181450602PKNHF3F5V"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418145061ARZ7ZUJE6I">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24181825228ITL1NKJMM">​resize()</span></p></td><td id="k_2418145063NKAO92KQUO" tabindex="0" row="2" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 450px;" w="448" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418145063512FOYCMDP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418145064PZOFQ3A8NV">​</span></p></td><td id="k_24181450661LSMVMTKO5" tabindex="0" row="2" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 575px;" w="573" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418145066YXMXYH7YTN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418145067272MAST88T">​​根据父容器重置布局宽度</span></p></td></tr><tr id="row_3"><td id="k_24181440397DVG3OR7XL" tabindex="0" row="3" col="0" w="272" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 274px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418144040BRFJ3H9H3A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418184617SVCMGKO559">setTitle</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418184619JR7ACQEQ9W">(args, panelIdx)</span></p></td><td id="k_2418144043BB144TZC5X" tabindex="0" row="3" col="1" w="448" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 450px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418144044JDXRGL4NXQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418144045WMPLH2PC4I">​​args：{title:'标题',iconCls:'图标样式'} 或者 "标题"</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418190604QUT7VZRIN9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418190605VENV1BG3GC">panelIdx：布局项下标</span></p></td><td id="k_2418144048BPOKG16EEK" tabindex="0" row="3" col="2" w="573" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 575px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24181440496CKFVBT2SK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418144050KY197IGZE8">​​更新设置布局标题</span></p></td></tr><tr id="row_4"><td id="k_2418181091W6EPDMSPGP" tabindex="0" row="4" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 274px;" w="272" h="178"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418181092Z2KOLIYRFW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418193101IWDXDSPO2J">load</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418193103XBPMAUFBBO">(args, panelIdx)</span></p></td><td id="k_24181810942EPO9O46LE" tabindex="0" row="4" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 450px;" w="448" h="178"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418181095UOLKXRQO5H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418181096EMVSDERNHQ">​​args：{</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418201721P572TZ8QDI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418201724TJZLBT2MVO">&nbsp; &nbsp; url: null,//url地址</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418195756PQEJDW3TCT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418195757B443D4U7UB">&nbsp; &nbsp; dataType:'html/json/iframe',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418200196OMSEX6SOC9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418200824UN818IJBXR">&nbsp; &nbsp; title:'</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241820082576CD1Z89MJ">设置该项会改</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418200826Y6J4CXA5GF">变标题，可是不设置',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418201064LFLL36B541"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24182010657EYCD47MBC">&nbsp; &nbsp; iconCls:'设置新的图标，可是不设置'</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418202195R81RSL7A1Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418202196THXCEUZMS5">}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418195297CQTJOBIZF3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418195298F3399W715I">panelIdx：布局项下标&nbsp;&nbsp;</span></p></td><td id="k_2418181097EMRJLOLRBS" tabindex="0" row="4" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 575px;" w="573" h="178"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418181098GUSZEDIQ3P"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418181099TDGMZGX5XH">​​加载远程内容</span></p></td></tr><tr id="row_5"><td id="k_2418144057MAL2FW4KJX" tabindex="0" row="5" col="0" w="272" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 274px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418144058WJ8CUTHBYL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24182234638OITUHWGZT">updateContent</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24182234655VD7UCA84T">(content, panelIdx)</span></p></td><td id="k_2418144070E3AT713IS5" tabindex="0" row="5" col="1" w="448" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 450px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418144071AO255O44Y7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418144072NXNE5DWU48">​​content：待更新的内容</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2418241822C7TTAJBLTY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418241823SLBQUUBKNF">panelIdx：布局项下标</span></p></td><td id="k_2418144074I3WAZ19MM1" tabindex="0" row="5" col="2" w="573" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 575px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418144075KP9VDCGUW8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418144076MQMLKPVB7J">​​更新内容</span></p></td></tr><tr id="row_6"><td id="k_2418144079ATJUBM2EWL" tabindex="0" row="6" col="0" w="272" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 274px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418144080195YJDE88Q"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418243922CVZG2GINDA">destroy</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418243925AWI7ZLTOBM">()</span></p></td><td id="k_2418144083FHRFUA6H6G" tabindex="0" row="6" col="1" w="448" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 450px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418144087BNS9WDVECS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24181440882TE4CH2JFM">​</span></p></td><td id="k_2418144092GKUTF6V9B7" tabindex="0" row="6" col="2" w="573" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 575px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2418144093PBK6X4V1EH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241814409397KFR84P76">​​销毁布局</span></p></td></tr></tbody></table></div><div id="k_24180244932PR6B1Q4CU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418024494IIFIYN2FHB">​</span></div><div id="k_2418024573T8WJPUTGB4" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 250, 255); padding-left: 15px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(153, 0, 51); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 3, 55); background-color: rgb(247, 250, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 3, 55); vertical-align: baseline;" id="k_24180553013SRODI5OXM">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2418055305RH5GMFJCHV">​<img id="k_2418055306HV4ADXCHPW" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAA8klEQVQ4T7WSwUqCQRRGz71Dm0KREvQ5EtcueggFW7SIXqJauhGCQhciSIs2Cb5AT9SqpEQhYv4bRmPT/w+2qdnNcA/33O+OEJ0qdugcfYGj8GzG9D3j7Bl5jWslXKpYwzluBBZLz/ECeSphB7tKb5Vxvr4XwC/oUjzXpnRC4VYwQHguVvAYd/gEHROBCtDadDTupK52i3CSmqmONc0xEJjn9aWmNkrNEEwyz5Uq3bx+EvxVX+kVwBq2J8ow1gdmb57TOfISAiuA25Jc5/An4L0I7Xi5wMPS080v/UfHHLBRSSX9P+A+Vt5Rxgn9749jTD8AO06uup3TOPgAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 3, 55); background-color: rgb(247, 250, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 3, 55); vertical-align: baseline;" id="k_24180553083DUZ25SZAE">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 3, 55); background-color: rgb(247, 250, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 3, 55); vertical-align: baseline;" id="k_2418055302T8IM8DUANB">&nbsp;Demo</span></div><div id="k_2418024566E1SFWSOAIB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;"><br></span></div><div id="k_2418254480MQZQL3VU45" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24182631684CC8QRXTQ3">1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24182544816CUXZACECQ">定义容器标签</span></div><div id="k_2418255388IBJCNTHFH9" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24182554851I131332BC"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">body</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="background: none; color: rgb(83, 83, 83);">&lt;/</span><span style="background: none; color: rgb(41, 111, 169);">body</span><span style="background: none; color: rgb(83, 83, 83);">&gt;</span><span style="background: none; color: rgb(102, 102, 102);"></span></div></pre></span></div><div id="k_2418255497YQWUD9DW6D" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24182604758PEO5F6AMV">​2、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2418255499BUP2E1KDN1"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_241825540617Y6FMHAAT">​定义布局项目</span></div><div id="k_2418260485HYBHLQU2DU" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2418260530S482EFOOS6"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);"> <span style="background: none; color: rgb(255, 0, 5);">var </span></span><span style="background: none; color: rgb(111, 175, 209);"><span style="background: none; color: rgb(255, 0, 5);">items</span> =</span><span style="background: none; color: rgb(167, 167, 167);"> [ </span><span style="color: rgb(167, 167, 167);">{</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">width:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">230</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'系统面板'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//标题</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">expandable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-cog-alt'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//样式</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">content:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">treeUl</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }, {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">header:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否需要标题栏</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-file-code'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">width:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'auto'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//宽度</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'tab模块'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">content:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'test.html'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">dataType:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'iframe'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }, {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">width:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">200</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'系统面板'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//标题</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">expandable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-cubes'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//样式</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">content:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fragment.html'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">dataType:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'html'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">toolbar:</span><span style="color: rgb(167, 167, 167); background: none;"> { </span><span style="color: rgb(61, 108, 40); background: none;">//工具栏对象参考工具栏组件c</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                            </span><span style="color: rgb(111, 175, 209); background: none;">align:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'center'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//对齐方式，默认是left 、center、right</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                            </span><span style="color: rgb(111, 175, 209); background: none;">style:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'min'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// plain / min  / normal /  big</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                            </span><span style="color: rgb(111, 175, 209); background: none;">showText:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// min 类型可以设置是否显示文字</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                            </span><span style="color: rgb(111, 175, 209); background: none;">buttons:</span><span style="color: rgb(167, 167, 167); background: none;"> [{</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                                </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-check'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                                </span><span style="color: rgb(111, 175, 209); background: none;">text:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'测试'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//文本</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                                </span><span style="color: rgb(175, 175, 125); background: none;">click</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">pr</span><span style="color: rgb(167, 167, 167); background: none;">) { </span><span style="color: rgb(61, 108, 40); background: none;">//点击事件，如果存在click，则字符串形式的handler不可用</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                                    </span><span style="color: rgb(175, 175, 125); background: none;">alert</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">pr</span><span style="color: rgb(167, 167, 167); background: none;">));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                                }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                            }, {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                                </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-cancel-2'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                                </span><span style="color: rgb(111, 175, 209); background: none;">text:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'按钮2'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//文本</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                                </span><span style="color: rgb(111, 175, 209); background: none;">handler:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'test'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//window.methodsObject对象中 test</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                                </span><span style="color: rgb(111, 175, 209); background: none;">methodsObject:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'methodsObject'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                            }]</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">     ];</span></div></div></pre></span></div><div id="k_2418260541NT161S9RPP" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2418260542K2UL39A1JW">3、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2418260543S6AMNHUWW2">​创建对象</span></div><div id="k_2418254483W1U89TG3M5" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2418254484O9PW4UVR5M"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">            </span><span style="background: none; color: rgb(41, 111, 169);">var</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">layout</span><span style="background: none; color: rgb(167, 167, 167);"> = </span><span style="background: none; color: rgb(41, 111, 169);">new</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(33, 156, 131);">$B</span><span style="background: none; color: rgb(167, 167, 167);">.</span><span style="background: none; color: rgb(33, 156, 131);">Layout</span><span style="background: none; color: rgb(167, 167, 167);">(</span><span style="background: none; color: rgb(175, 175, 125);">$</span><span style="background: none; color: rgb(167, 167, 167);">(</span><span style="background: none; color: rgb(161, 100, 75);">"body"</span><span style="background: none; color: rgb(167, 167, 167);">), {</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onCreated</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">params</span><span style="color: rgb(167, 167, 167); background: none;">) { </span><span style="color: rgb(61, 108, 40); background: none;">//function(){} 布局创建完成事件,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onCreated "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">params</span><span style="color: rgb(167, 167, 167); background: none;">));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onLoaded</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(111, 175, 209); background: none;">params</span><span style="color: rgb(167, 167, 167); background: none;">) { </span><span style="color: rgb(61, 108, 40); background: none;">//加载完成</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onLoaded data = "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">) + </span><span style="color: rgb(161, 100, 75); background: none;">" params="</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="background-image: none; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: rgb(167, 167, 167);"> </span><span style="background-image: none; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: rgb(111, 175, 209);">params</span><span style="background-image: none; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; color: rgb(167, 167, 167);">));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">items:</span><span style="color: rgb(255, 0, 5); background: none;"> items</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            });</span></div></div></pre></span></div><div id="k_2418261121RB4MGZR24J" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2418261122RVTZYM6SWT">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_24180245801A77PG7TAC" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24180245812T44VF8ZJ9">​</span></div>

</div>