<div style="position:relative">
<div id="k_2321224968G8YL28WD9D" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(252, 242, 244); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(245, 88, 232); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 242, 244); letter-spacing: 3px;" id="k_23212929328YOV5WCO4W">介绍</span></div><div id="k_2321271476XI1KN18F3Y" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 670px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 18px; margin-top: 20px; border-top: 1px solid rgb(160, 32, 240); border-right: 1px solid rgb(160, 32, 240); border-bottom: none; border-left: 1px solid rgb(160, 32, 240); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321364984I5ER4285UA">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_232136498729U7Q8WEBE">​<img id="k_2321364987YVEBCD7G43" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABYUlEQVQoU23SP0jbQRjG8e9zQWglkWIXcRIRdBCKg0NxsFKNf+kgOJesHTrYxcGt3boVCk6VDh2aVVATREVwagc3UXDpICK49JcMMfWe8suftmrf6e54P9w9L6dcSUeIQaAKXIIOCfFTkuEbE/ziTqmrHIrgOdA69gzSQNpjfG57uZKniHDbKVviVVD4aFyX/QapABppoTrofTIVV9tIuR2eKmoT9Mj2SZRfB7Qu1NtGsgs/p/mS7tW1TbelXUlPbEdJ7xqN9qqk0FofJDfOM0dN6UEzB0vNd/pHlF8qak1SOowUX5DxeDLJaQPkSiwgFYUeNg1fo+K+0AehDnD1Bi9W85QbgD0e5OraEnr2N6xXQONCL+4DIFtiOKANpL5/JvQZexTRc53xWO05Z80bWtVZpjeD1jDz7cDgCnDs4Nlkkqtb4I/cI5utMws8jvA9iH7MUGWKt/8Hd/9DazBSGP0NqYqTWMYYOmwAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321364989HP7G5XHMQR">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321364985R29XVE8F4X">支持自定义复杂表</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23213622841LZPAYII7O">头，支持列配置：固定宽度、最小宽度、排序配置。</span></div><div id="k_232133203653AUDCNQIP" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 670px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 18px; margin-top: 0px; border-top: none; border-right: 1px solid rgb(160, 32, 240); border-bottom: none; border-left: 1px solid rgb(160, 32, 240); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321370024Q65Y2X6BRD">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_23213700262257H5TL12">​<img id="k_2321370027LL2JFBNFRO" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABYUlEQVQoU23SP0jbQRjG8e9zQWglkWIXcRIRdBCKg0NxsFKNf+kgOJesHTrYxcGt3boVCk6VDh2aVVATREVwagc3UXDpICK49JcMMfWe8suftmrf6e54P9w9L6dcSUeIQaAKXIIOCfFTkuEbE/ziTqmrHIrgOdA69gzSQNpjfG57uZKniHDbKVviVVD4aFyX/QapABppoTrofTIVV9tIuR2eKmoT9Mj2SZRfB7Qu1NtGsgs/p/mS7tW1TbelXUlPbEdJ7xqN9qqk0FofJDfOM0dN6UEzB0vNd/pHlF8qak1SOowUX5DxeDLJaQPkSiwgFYUeNg1fo+K+0AehDnD1Bi9W85QbgD0e5OraEnr2N6xXQONCL+4DIFtiOKANpL5/JvQZexTRc53xWO05Z80bWtVZpjeD1jDz7cDgCnDs4Nlkkqtb4I/cI5utMws8jvA9iH7MUGWKt/8Hd/9DazBSGP0NqYqTWMYYOmwAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23213700282ZVFI52FPW">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321370025KY8WCU22Y4">可通过后端控制相关JSON实现灵活的动态表头。</span></div><div id="k_23213043964TYG4E8N7F" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 670px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 18px; margin-top: 0px; border-top: none; border-right: 1px solid rgb(160, 32, 240); border-bottom: none; border-left: 1px solid rgb(160, 32, 240); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321370902I1LL8VNHLO">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_23213709059FFDPNLB5I">​<img id="k_2321370906NYIAUAWAFZ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABYUlEQVQoU23SP0jbQRjG8e9zQWglkWIXcRIRdBCKg0NxsFKNf+kgOJesHTrYxcGt3boVCk6VDh2aVVATREVwagc3UXDpICK49JcMMfWe8suftmrf6e54P9w9L6dcSUeIQaAKXIIOCfFTkuEbE/ziTqmrHIrgOdA69gzSQNpjfG57uZKniHDbKVviVVD4aFyX/QapABppoTrofTIVV9tIuR2eKmoT9Mj2SZRfB7Qu1NtGsgs/p/mS7tW1TbelXUlPbEdJ7xqN9qqk0FofJDfOM0dN6UEzB0vNd/pHlF8qak1SOowUX5DxeDLJaQPkSiwgFYUeNg1fo+K+0AehDnD1Bi9W85QbgD0e5OraEnr2N6xXQONCL+4DIFtiOKANpL5/JvQZexTRc53xWO05Z80bWtVZpjeD1jDz7cDgCnDs4Nlkkqtb4I/cI5utMws8jvA9iH7MUGWKt/8Hd/9DazBSGP0NqYqTWMYYOmwAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321370907FGT55U8H8X">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321370904YFP15LOK9Q">支持行、列渲染拦截处理，可根据数据格式化行、列数据、样式。</span></div><div id="k_23213043964TYG4E8N7F" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 670px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 18px; margin-top: 0px; border-top: none; border-right: 1px solid rgb(160, 32, 240); border-bottom: none; border-left: 1px solid rgb(160, 32, 240); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23213721351EC3L2MAXW">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2321372137Z6E854JSZZ">​<img id="k_2321372138GKZF5ZBDEF" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABYUlEQVQoU23SP0jbQRjG8e9zQWglkWIXcRIRdBCKg0NxsFKNf+kgOJesHTrYxcGt3boVCk6VDh2aVVATREVwagc3UXDpICK49JcMMfWe8suftmrf6e54P9w9L6dcSUeIQaAKXIIOCfFTkuEbE/ziTqmrHIrgOdA69gzSQNpjfG57uZKniHDbKVviVVD4aFyX/QapABppoTrofTIVV9tIuR2eKmoT9Mj2SZRfB7Qu1NtGsgs/p/mS7tW1TbelXUlPbEdJ7xqN9qqk0FofJDfOM0dN6UEzB0vNd/pHlF8qak1SOowUX5DxeDLJaQPkSiwgFYUeNg1fo+K+0AehDnD1Bi9W85QbgD0e5OraEnr2N6xXQONCL+4DIFtiOKANpL5/JvQZexTRc53xWO05Z80bWtVZpjeD1jDz7cDgCnDs4Nlkkqtb4I/cI5utMws8jvA9iH7MUGWKt/8Hd/9DazBSGP0NqYqTWMYYOmwAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321372139RMV2MQR5WP">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321372136TWXBHGRDUQ">结合toolbar组件，支持动态工具栏配置，可轻松构建工具栏组。</span></div><div id="k_2321335895GHJDSQP8X3" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 670px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 18px; margin-top: 0px; border-top: none; border-right: 1px solid rgb(160, 32, 240); border-bottom: none; border-left: 1px solid rgb(160, 32, 240); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321372942YIAJ131S4D">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_23213729449WBN6RO3PP">​<img id="k_2321372944J4K2DJM24E" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABYUlEQVQoU23SP0jbQRjG8e9zQWglkWIXcRIRdBCKg0NxsFKNf+kgOJesHTrYxcGt3boVCk6VDh2aVVATREVwagc3UXDpICK49JcMMfWe8suftmrf6e54P9w9L6dcSUeIQaAKXIIOCfFTkuEbE/ziTqmrHIrgOdA69gzSQNpjfG57uZKniHDbKVviVVD4aFyX/QapABppoTrofTIVV9tIuR2eKmoT9Mj2SZRfB7Qu1NtGsgs/p/mS7tW1TbelXUlPbEdJ7xqN9qqk0FofJDfOM0dN6UEzB0vNd/pHlF8qak1SOowUX5DxeDLJaQPkSiwgFYUeNg1fo+K+0AehDnD1Bi9W85QbgD0e5OraEnr2N6xXQONCL+4DIFtiOKANpL5/JvQZexTRc53xWO05Z80bWtVZpjeD1jDz7cDgCnDs4Nlkkqtb4I/cI5utMws8jvA9iH7MUGWKt/8Hd/9DazBSGP0NqYqTWMYYOmwAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321372945NGJ7Q5AE3X">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23213729429HUT2WBVIL">支持分割线定制，可配置表格分割线风格（无分割线、仅有水平分割线、仅有重置分割线）。</span></div><div id="k_2321335895GHJDSQP8X3" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 670px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 18px; margin-top: 0px; border-top: none; border-right: 1px solid rgb(160, 32, 240); border-bottom: none; border-left: 1px solid rgb(160, 32, 240); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23213737475IRYBL3AD8">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2321373752158MGWSNN4">​<img id="k_2321373753J8FL22E3PE" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABYUlEQVQoU23SP0jbQRjG8e9zQWglkWIXcRIRdBCKg0NxsFKNf+kgOJesHTrYxcGt3boVCk6VDh2aVVATREVwagc3UXDpICK49JcMMfWe8suftmrf6e54P9w9L6dcSUeIQaAKXIIOCfFTkuEbE/ziTqmrHIrgOdA69gzSQNpjfG57uZKniHDbKVviVVD4aFyX/QapABppoTrofTIVV9tIuR2eKmoT9Mj2SZRfB7Qu1NtGsgs/p/mS7tW1TbelXUlPbEdJ7xqN9qqk0FofJDfOM0dN6UEzB0vNd/pHlF8qak1SOowUX5DxeDLJaQPkSiwgFYUeNg1fo+K+0AehDnD1Bi9W85QbgD0e5OraEnr2N6xXQONCL+4DIFtiOKANpL5/JvQZexTRc53xWO05Z80bWtVZpjeD1jDz7cDgCnDs4Nlkkqtb4I/cI5utMws8jvA9iH7MUGWKt/8Hd/9DazBSGP0NqYqTWMYYOmwAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23213737546QJQHPL7TD">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23213737487FQRCMDGZB">支持分页工具栏配置，可配置分页工具栏按钮数量、分页大小、分页栏位置。</span></div><div id="k_2321335895GHJDSQP8X3" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 670px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 18px; margin-top: 0px; border-top: none; border-right: 1px solid rgb(160, 32, 240); border-bottom: 1px solid rgb(160, 32, 240); border-left: 1px solid rgb(160, 32, 240); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321374699C5AFQSPQH1">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2321374601AQ5G1GO9RL">​<img id="k_2321374601QN11Y22Y2A" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABYUlEQVQoU23SP0jbQRjG8e9zQWglkWIXcRIRdBCKg0NxsFKNf+kgOJesHTrYxcGt3boVCk6VDh2aVVATREVwagc3UXDpICK49JcMMfWe8suftmrf6e54P9w9L6dcSUeIQaAKXIIOCfFTkuEbE/ziTqmrHIrgOdA69gzSQNpjfG57uZKniHDbKVviVVD4aFyX/QapABppoTrofTIVV9tIuR2eKmoT9Mj2SZRfB7Qu1NtGsgs/p/mS7tW1TbelXUlPbEdJ7xqN9qqk0FofJDfOM0dN6UEzB0vNd/pHlF8qak1SOowUX5DxeDLJaQPkSiwgFYUeNg1fo+K+0AehDnD1Bi9W85QbgD0e5OraEnr2N6xXQONCL+4DIFtiOKANpL5/JvQZexTRc53xWO05Z80bWtVZpjeD1jDz7cDgCnDs4Nlkkqtb4I/cI5utMws8jvA9iH7MUGWKt/8Hd/9DazBSGP0NqYqTWMYYOmwAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321374602SOXFXMMXIW">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321374699K9T3CJ5W2I">支持行、单元格事件（点击、双</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2321362289OWX6C8NWXM">击），支持拖动改变列宽。</span></div><div id="k_2321271481FTB6W49PD1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23212714825Z6GW2KVMA">​</span></div><div id="k_23212714479Z452324TA" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(252, 242, 244); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(245, 88, 232); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 242, 244); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline; letter-spacing: 1px;" id="k_2321271448DXI5693LW5">API及构造参数</span></div><div id="k_2321272413VLNSMVU7MV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;" contenteditable="false"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190323213858YVELITABO2OL"><tbody><tr id="row_0"><td id="k_2321385849J8P9JJCIO2" tabindex="0" row="0" col="0" w="1291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1293px; background-color: rgb(240, 244, 255);" colspan="3" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 244, 255);" id="k_2321385850NWN475FDF7"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 244, 255);" id="k_2321385851N9TGQTBCM1">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 244, 255);" id="k_2321403445TFQE6TYU3O">​var dg = new $B.Datagrid($("#datagrid"),opts);&nbsp; &nbsp; opts说明</span></p></td></tr><tr id="row_1"><td id="k_2321385858RNPYRTM4TY" tabindex="0" row="1" col="0" w="207" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px; background-color: rgb(240, 244, 255); text-align: center; font-size: 16px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(240, 244, 255);" id="k_23213858596FRLGQSVH7"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 244, 255); text-align: center;" id="k_2321385860HSKD5DGW7N">​参数名</span></p></td><td id="k_2321385861OKVWAW23MD" tabindex="0" row="1" col="1" w="474" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px; background-color: rgb(240, 244, 255); text-align: center; font-size: 16px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(240, 244, 255);" id="k_2321385862EI2YLCC5I8"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 244, 255); text-align: center;" id="k_23213858649MQARJIFEA">​参数值</span></p></td><td id="k_2321385867935O41NOFO" tabindex="0" row="1" col="2" w="606" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px; background-color: rgb(240, 244, 255); text-align: center; font-size: 16px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(240, 244, 255);" id="k_23213858688JBUY9QEFV"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 244, 255); text-align: center;" id="k_23213858703L9C58ZRJ9">​说明</span></p></td></tr><tr id="row_2"><td id="k_2321385873CYLNBPPDR6" tabindex="0" row="2" col="0" w="207" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23213858751G6QZEN251"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385876SLKMTVCGSX">​​url</span></p></td><td id="k_2321385880IQHAJFLREN" tabindex="0" row="2" col="1" w="474" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385883M8WKTJ2TYQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385883NZIE8Z5XI2">​​远程加载url</span></p></td><td id="k_2321385888FORAOC5AON" tabindex="0" row="2" col="2" w="606" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23213858902E5GK9R4EK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410190681H2XI1C5KJX">返回格式：</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410190682G2RYYIA5N4">{"resultList": [{},...],"totalSize":100,"currentPage":1}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2321435073AIPHR1BA83"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321435074CPFVDV99S4">​树形：​请参考tree组件的数据格式</span></p></td></tr><tr id="row_3"><td id="k_2321385893UY3TNOEESW" tabindex="0" row="3" col="0" w="207" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385894NBMJWBV368"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23213858944DPBHFWCBW">​​data</span></p></td><td id="k_2321385897V5IZMBQLW9" tabindex="0" row="3" col="1" w="474" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385898CFEZ7TNH2V"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385899NBA4YKDEIE">​在页面定义的静态数据</span></p></td><td id="k_2321385801KXZN433TMB" tabindex="0" row="3" col="2" w="606" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385802TWLZYXQOYO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385803Y8U4HNV818">​​列表：{"resultList": [{},...],"totalSize":100,"currentPage":1}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2321434720U2PUG9VNCQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321440009GZPJAIQOOA">树形：</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321440010V8K87REB74">请参考tree组件的数据格式</span></p></td></tr><tr id="row_4"><td id="k_2321385806WNBQCOJ4HE" tabindex="0" row="4" col="0" w="207" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385806H5MKG69GAA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23213858075W6FB61CX7">​​cols</span></p></td><td id="k_2321385809ABEGT71J6A" tabindex="0" row="4" col="1" w="474" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385810CKCGXUFXTX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321454020FSH8C1I54H">请参考《</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 5, 5); background-color: rgb(255, 255, 255);" id="k_2321454021U46UAQDW2L">cols配置说明</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321454022CT3ZLQYH9P">》</span></p></td><td id="k_23213858122HJFV1Y2PF" tabindex="0" row="4" col="2" w="606" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23213858133KVFU382FO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385813DQ1FX4MVPR">​列配置</span></p></td></tr><tr id="row_5"><td id="k_23213858155C8ESWE1T9" tabindex="0" row="5" col="0" w="207" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23213858168TCAJKC9GB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385817C8R6RDBNVZ">​​isTree</span></p></td><td id="k_2321385819L6SJ1ICB3M" tabindex="0" row="5" col="1" w="474" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385820NPQCHIDCD9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385821SIFT1W8OS3">​​默认值：false</span></p></td><td id="k_2321385823W3JP8YZ411" tabindex="0" row="5" col="2" w="606" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385823698G5N5ETE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23213858243X6QISJ39I">​是否为树形列表</span></p></td></tr><tr id="row_6"><td id="k_2321385827BCZQGNJ3AM" tabindex="0" row="6" col="0" w="207" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385828MPN953OZGG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23213858295LIHI1SXQM">​​treeIconColor</span></p></td><td id="k_2321385830YWWAG4J31P" tabindex="0" row="6" col="1" w="474" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385831L8ZR9DZAXG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321473031VODOQYJW98">默认值：undefined&nbsp;</span></p></td><td id="k_2321385833BRHSA86PKZ" tabindex="0" row="6" col="2" w="606" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385834W8CGGTFAI3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385834VPJAEUVST4">​​树形图标颜色，不设置则采用默认的字体颜色</span></p></td></tr><tr id="row_7"><td id="k_23213858364SJBL5NJ2A" tabindex="0" row="7" col="0" w="207" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321385837NGH4S1XGBX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385837Z4GJG19QVA">​​title</span></p></td><td id="k_2321385839NMG8KTAXW5" tabindex="0" row="7" col="1" w="474" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23213858408BRD1FQGTH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385840XFRM8PKRHL">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23214821923J4TRZRDYH">​默认值：undefined</span></p></td><td id="k_23213858423JXDG96JOJ" tabindex="0" row="7" col="2" w="606" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_232138584217934J4Y82"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321385843YK9AGV1P5J">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321483685R15JCXZ9OB">​表格标题，为空则不出现标题</span></p></td></tr><tr id="row_8"><td id="k_2321491078KZEVN7U2C4" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491079GULADBYXBK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491080ZRML1GQBQP">​​iconCls</span></p></td><td id="k_2321491083TYHBORY3F6" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491084F6CEII5J45"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232150025398Q9AI5ZVW">默认值：undefined</span></p></td><td id="k_2321491086C2KK9AT7LW" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491087D171QIX4L3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491088ZKB25JJI82">​​表格表头小图标，font-awesome字体图标</span></p></td></tr><tr id="row_9"><td id="k_2321491606GCHPM1YRO4" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491607EUZ2RMB6UT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491608QP38D1NRVP">​​methodsObject</span></p></td><td id="k_2321491611WY2324ZH2H" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491612RJ2XVUA2UW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491612M46Q7C4EKX">​​默认值：undefined</span></p></td><td id="k_23214916144FB4P3KBMV" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491615T3OALL1JQ4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491616Y3W4X1BGL3">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23215014709IG46L8K1V">​与toolbar里的配置相同意义</span></p></td></tr><tr id="row_10"><td id="k_2321491196DVYXOXGMH8" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491197UJ1DS7U5EU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491198S12H71JT45">​​loadImd</span></p></td><td id="k_2321491100NTVGMV6M6N" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491100N1A3XDHBXL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491101QJYR213UET">​​默认值：false</span></p></td><td id="k_23214911034KGEKHFW9U" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23214911032I8HQ6YFLB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491104CGXOL9NZEX">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321531843J5REXBURS6">​当配置了Url，而且静态data树形为空的时候，是否立即加载数据</span></p></td></tr><tr id="row_11"><td id="k_23214914947JHGKRRS5K" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491495P7ZOZ3KB86"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491496TXKYASHRYY">​​pageSize</span></p></td><td id="k_2321491499EMEPSTL4AQ" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491400G2DTZRC6TB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491401EDM77BKDX4">​​默认值：30</span></p></td><td id="k_2321491404VUWPTZZWMI" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23214914058MDLQ2J383"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23214914068NVDF3IQSU">​页大小</span></p></td></tr><tr id="row_12"><td id="k_2321535507YKVDA6K1AI" tabindex="0" row="12" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535507LA4ZO9EKNV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23215355085ME3RY38GA">​​pageList</span></p></td><td id="k_2321535511HTKGNH8YEX" tabindex="0" row="12" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535511KRFTU3UNS3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535512L3YK5VFZGA">​​默认值：[15, 25, 30, 35, 40, 55, 70, 90, 100]</span></p></td><td id="k_2321535514WH3PK1Z3LT" tabindex="0" row="12" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23215355143U2E85CY9H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535515W4U9LNCENI">​​分页大小列表</span></p></td></tr><tr id="row_13"><td id="k_2321535627EOZCDNNSCB" tabindex="0" row="13" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535628ZKGXYFC9Y5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535629RM384SQWYY">​​pgBtnNum</span></p></td><td id="k_2321535632Q8CQEHBPOI" tabindex="0" row="13" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535633YRMDE68AOZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535634ZPZ47ONZW9">​​默认值：10</span></p></td><td id="k_2321535638UZZSZT19J5" tabindex="0" row="13" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535639GQRUYN74G3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535639WNSNQK4S2T">​​分页数字按钮的数量</span></p></td></tr><tr id="row_14"><td id="k_2321535434OZGGFYAZ3K" tabindex="0" row="14" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535435CJ5JX7ZL4K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535436CA2KLWJ14I">​​pgposition</span></p></td><td id="k_2321535438DP7E5NM15U" tabindex="0" row="14" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535439TBFMEELOAY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535439PKYH4KDZSO">​​默认值：bottom，可取值：both、bottom、top</span></p></td><td id="k_2321535441MU3BTWFLG6" tabindex="0" row="14" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23215354419AMP3Q5Q52"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23215354425IFE9WUK1D">分页栏位置</span></p></td></tr><tr id="row_15"><td id="k_2321535389MGB4NODUFX" tabindex="0" row="15" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535390KIDV4FLNAS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535391WUEV4IUK7R">​​oprCol</span></p></td><td id="k_232153539381CKG3BQ1F" tabindex="0" row="15" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535394Q9IA7USSVV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23215353942NWSTGI5IF">​​默认值：true</span></p></td><td id="k_2321535396N2SZ8SY1K3" tabindex="0" row="15" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535397BG9U262ZG8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535398K95XFBFLQX">​​是否需要操作列</span></p></td></tr><tr id="row_16"><td id="k_2321535173SABTMZ6BXD" tabindex="0" row="16" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23215351748EQZ7IPB95"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23215351759O4WG28DA6">​​oprColWidth</span></p></td><td id="k_2321535177FOVTC6PT4I" tabindex="0" row="16" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535177XXOMCJVTLJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535178RTFOPEFUJZ">​​默认值：auto</span></p></td><td id="k_2321535180IM29ESTC45" tabindex="0" row="16" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321535181M2YFAS1KTN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321535181A1CZXP6OXO">​​操作列宽度</span></p></td></tr><tr id="row_17"><td id="k_2321564099FOODWR4FTS" tabindex="0" row="17" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564099BHTJJSSKXN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321564000H8SAROW46D">​​checkBox</span></p></td><td id="k_2321564002CMKHJ2JQW4" tabindex="0" row="17" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564003JFQXJMW8MJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321564004GVCR48SEIM">​​默认值：true</span></p></td><td id="k_2321564006159R6SCBOE" tabindex="0" row="17" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564006ZXYF26NG8G"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321564007CER18HU21D">​​是否需要复选框</span></p></td></tr><tr id="row_18"><td id="k_23215641312ETWAK5MPB" tabindex="0" row="18" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23215641324MF3GRQ2SI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23215641324IPXCGFR21">​​idField</span></p></td><td id="k_232156413486RZQL1IUW" tabindex="0" row="18" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564135ULZ2M15NI6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321564136ZGXODIDUGO">​​默认值：id</span></p></td><td id="k_2321564139ZOKB3VE92P" tabindex="0" row="18" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564140GN837IOGIF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321564141WLWGUCK7CL">​id字段名</span></p></td></tr><tr id="row_19"><td id="k_2321564489IJE6H75Z3E" tabindex="0" row="19" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564490281XBFKH9O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321564491OWRD7DMM83">​​btnStyle</span></p></td><td id="k_2321564494625VQDTFHD" tabindex="0" row="19" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564495OBWZM7S8OF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23215644962CGFGF6MEZ">​​默认值：plain ；可取值：plain / min / normal / big</span></p></td><td id="k_2321564498VLT7CNRLKX" tabindex="0" row="19" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23215644994DOHW5P11E"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321564400GDFGXKFQPR">​​表格上方的工具栏按钮的风格配置</span></p></td></tr><tr id="row_20"><td id="k_2321564293DQNQJECCLY" tabindex="0" row="20" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564294LSNSBHCQ1W"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321564295LDYL1EUT3O">​​toolbar</span></p></td><td id="k_2321564215B32SCKMWBL" tabindex="0" row="20" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564216GDHWEHYCVQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321564217LHHUCEWH3K">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321582478WZKRYXBWYX">​请参考toolbar组件的配置说明</span></p></td><td id="k_23215642196YJXOM2437" tabindex="0" row="20" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321564220LHIHLVRILT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232156422183TKMPRYYC">​</span></p></td></tr><tr id="row_21"><td id="k_2321491287MKQ9P4LOGV" tabindex="0" row="21" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" w="207" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491287V1NMXUGB5X"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491288MEG1O9MV4R">​​toolbarOpts</span></p></td><td id="k_2321491291BSN8FY6U9T" tabindex="0" row="21" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" w="474" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491292XP3CE3EHXX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321491293KY8RO2W4LX">​取值:&nbsp;​​{ style: 'min',&nbsp;​color: '#EDEDED',&nbsp;​iconColor: '#BCB9C9',&nbsp;​fontColor: '#666666'&nbsp;}</span></p></td><td id="k_2321491296AAYLH13IS1" tabindex="0" row="21" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" w="606" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321491297VOT8H6MPUR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23214912986BFG7DPP8H">​​表格行内工具栏的样式配置</span></p></td></tr><tr id="row_22"><td id="k_2321592394L3J2JZF4JW" tabindex="0" row="22" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321592395PTMZV5G2PB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321592396UIZ8XJXEDU">​​showBtnText</span></p></td><td id="k_2321592399NDH5LRCTHL" tabindex="0" row="22" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23215923006HPQM6FPOE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321592301AHVECKKGR3">​​默认值：true ,false则只显示图标</span></p></td><td id="k_23215923046H4QMEU5O1" tabindex="0" row="22" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321592305Y4CCBWSE7U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232159230699WFDNRAJJ">​​表格行内工具栏是否显示按钮文本</span></p></td></tr><tr id="row_23"><td id="k_2321592752C39WBIC5YN" tabindex="0" row="23" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" w="207" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321592752LOCQYWHT99"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321592753DSASHRTSB6">​​sortField</span></p></td><td id="k_23215927551FWGZJGG2C" tabindex="0" row="23" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" w="474" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23215927567VMB75XDZA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321592757NEH3WH4AKB">​​​默认值:undefined&nbsp;</span></p></td><td id="k_23215927586DRUXQDEZ7" tabindex="0" row="23" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" w="606" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321592759MVJBAT32XG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321592760HE686PJQFU">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322035939YTHBIL4ZGQ">设置查询排序字段:&nbsp; [ {'</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(255, 255, 255);" id="k_2322035940C2WQCX6K49">filed1</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322041309YZT2BVJ8OT">' : 'desc' } , { '</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(255, 255, 255);" id="k_2322041310CDUELY6S8I">filed2</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322041312LJ8GPSAAXH">' : 'asc'} ]</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321592759MVJBAT32XG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322042274DKPGTARAI5">后端取值：{ '</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2322042275HS7RDKCNMR">col_sort_</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(255, 255, 255);" id="k_2322043734X1PFM9YGSK">filed1</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322043735EZSGSPVXYB">' : 'desc' , '</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2322043101DJKHLOWP9B">col_sort_</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(255, 255, 255);" id="k_2322044393UXT6KOQ552">filed2</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23220517848ILPCDQ2LV">' : 'asc' } 注意 [</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2322051785JXOT6JF5X9">col_sort_</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322051786E5A1MW6X3C">]前缀</span></p></td></tr><tr id="row_24"><td id="k_2321592662XYS86R7OMC" tabindex="0" row="24" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" w="207" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321592663PNLAIXVU7L"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321592663DP7EPFMQ64">​​splitColLine</span></p></td><td id="k_2321592665QKN6IEWMAC" tabindex="0" row="24" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" w="474" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321592666QT69JIYZ3E"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23215926673SGDHO4UJE">​​默认值：k_datagrid_td_all_line</span></p></td><td id="k_2321592669QNNYE6A2CX" tabindex="0" row="24" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" w="606" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321592670AQ43QSIXM7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321592671HCG9TWX8XJ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322061409ORRHDMNGNW">​分割线配置</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_232206139729KODQV5P3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322061398TQBRYBYKOH">可取值：k_datagrid_td_v_line(只有重置线)、k_datagrid_td_h_line(只有水平线)、k_datagrid_td_none_line(无分割线)</span></p></td></tr><tr id="row_25"><td id="k_23215924316MELTKTMFQ" tabindex="0" row="25" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 209px;" w="207" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2321592431T5TWVN3IYE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321592432HZSSU5P4OY">​​fillParent</span></p></td><td id="k_23215924366I1JV5848N" tabindex="0" row="25" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 476px;" w="474" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_232159243661D1CGS5UW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23215924377V92YH238U">​​默认:false</span></p></td><td id="k_2321592440FJ16PKCQOY" tabindex="0" row="25" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 608px;" w="606" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23215924414TSYT3LFUE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321592442EFSVOIMQVQ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322073653LWUXXQGTC9">​是否适配父元素高度，​true则datagrid的高度沾满父元素高度，不受数据量影响高度</span></p></td></tr><tr id="row_26"><td id="k_2322112987QJ9L4KDM6D" tabindex="0" row="26" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 209px;" w="207" h="103" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322112988KQTUTPI18X"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322112988B3O1PI3AML">​​onDbClickRow</span></p></td><td id="k_232211299029ATOUA5EM" tabindex="0" row="26" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 476px;" w="474" h="103" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322112991GSBQT4O62B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322112992GR2H5BFCL3">​默认值：undefined</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322120347MW5PM7B7CE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322120348Q4FKM3D6WZ">onDbClickRow = function(){&nbsp; &nbsp;&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322123540BOZTDNKQWH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322124256JNQK94DM1H">​&nbsp; alert("onDbClickRow " + JSON.stringify(this.data("data")));&nbsp;&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322123615KRGWPZAHMI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322123616HVR5FWI9UT">}</span></p></td><td id="k_2322112993KFX8WRDAX7" tabindex="0" row="26" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 608px;" w="606" h="103" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322112994Q18JKTCFRS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23221129946KRVUXRET8">​行双击事件</span></p></td></tr><tr id="row_27"><td id="k_2322112704QFX5SXGCQQ" tabindex="0" row="27" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" w="207" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322112704HXQLLML5EK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322112705HQXC63J7FH">​​setParams</span></p></td><td id="k_2322112707Y6TY88E6E8" tabindex="0" row="27" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" w="474" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23221127082D2628LHYR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322143817SL6THA7OCV">默认值：undefined</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322131848D77JMXIV9L"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322131850ZOJNJ9QUJ7">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322133428PFRJAMB1ON">​setParams&nbsp; = function () { return { "p": "p1" }; }</span></p></td><td id="k_2322112710KDMTTOVV49" tabindex="0" row="27" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" w="606" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322112711KMGVXOTY5D"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322112712568QVBA1R1">​​每次查询都调用该函数，将函数返回的参数附加到请求参数中</span></p></td></tr><tr id="row_28"><td id="k_2322112505IRSSXW8BBE" tabindex="0" row="28" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" w="207" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322112505X6423ETJF7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322112506F9S46FMVDJ">​​onRowRender</span></p></td><td id="k_2322112509ULH8DKBE5P" tabindex="0" row="28" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" w="474" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322112510XZH9EKJ5OX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232215453777CPAHX9FG">默认值：undefined</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322112510XZH9EKJ5OX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322151769LI8TA9FOQX">​onRowRender = function (data, rowIdx) {}</span></p></td><td id="k_23221125134F8OUD3CAF" tabindex="0" row="28" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" w="606" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23221125144RJWLVF6KX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232211251559Z1H783TC">​行渲染事件，应用于根据数据对行做ui效果渲染</span></p></td></tr><tr id="row_29"><td id="k_2322153000XHLYAHAZZL" tabindex="0" row="29" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" w="207" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322153001YKSGDKUPKU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23221530013FBUP4Q7DH">​​onClickCell</span></p></td><td id="k_2322153003N713RKS1OJ" tabindex="0" row="29" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" w="474" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322153004KSXM76U2R6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232215300588EXTPOOMR">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322165935YXWTMORHG2">​默认值：undefined</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322153004KSXM76U2R6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23221553493CCQZU7GEU">​​​onClickCell =&nbsp;​function (field, value){}</span></p></td><td id="k_2322153007JRJH2UR24J" tabindex="0" row="29" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" w="606" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322153008EDKK9HE25G"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322153009ZZUE4T8LT9">​​单元格点击事件</span></p></td></tr><tr id="row_30"><td id="k_2322153251D5NNG89QVM" tabindex="0" row="30" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" w="207" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322153252N3ZWMI7QB3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23221728839AJQS1XKGT">onCheck</span></p></td><td id="k_2322153255NSLM7XZXF5" tabindex="0" row="30" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" w="474" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322153256SM5PAQPL6O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23221801118J8GWA7QEE">默认值：undefined</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322170290V1RCVE2CRL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322170291V6NQNZMTUH">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322172423K8V9F9ZBY7">​​onCheck = function (isChked, data){}</span></p></td><td id="k_2322153258DMUB3D7CF7" tabindex="0" row="30" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" w="606" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23221532591BSA9YPOV6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322153259HW7F1SBKNE">​​复选事件</span></p></td></tr><tr id="row_31"><td id="k_2322174842AOBTA6KF73" tabindex="0" row="31" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 209px;" w="207" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322174843HNGSSBZ8TQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322174843191351RYEC">​​onLoaded</span></p></td><td id="k_2322174845SGWG8XIBRQ" tabindex="0" row="31" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 476px;" w="474" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322174846EONEVUNYBQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322174847RCNSKSCT32">​​默认值：undefined</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322180400NOFAAJVN7Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23221804014DGGXAEPB2">​onLoaded = function(data){}</span></p></td><td id="k_2322174849RXHWRB976W" tabindex="0" row="31" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 608px;" w="606" h="53" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322174850HIACJKKPSO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322174851WN9UBGJUSC">​加载完成事件回调</span></p></td></tr></tbody></table></div><div id="k_2321272470VBTNRV7FN5" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321272471IUKUQRA8OY">​</span></div><div id="k_232210583493SYIH8XAB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322105834ZA6YFERZIH">​</span></div><div id="k_23221059651D45E4KLSX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 42px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" contenteditable="false"><table style="border-collapse: collapse; width: auto; height: auto; " id="2019032322193297I1I18XCHF7"><tbody><tr id="row_0"><td id="k_2322193200DJFO8488CY" tabindex="0" row="0" col="0" w="1293" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1295px; background-color: rgb(240, 244, 255);" colspan="3" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 244, 255);" id="k_23221932012P92KUBOV3"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 244, 255);" id="k_23221932025QGKHAZNRW">​cols配置说明</span></p></td></tr><tr id="row_1"><td id="k_23221932118YGOS7ELRR" tabindex="0" row="1" col="0" w="212" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 214px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193212FHI7762MKQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23221932135V7YYRH858">​​title</span></p></td><td id="k_2322193215E8ZKJALKO1" tabindex="0" row="1" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193216KF6H4CR1IS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193217MQFXNAVK6L">​</span></p></td><td id="k_2322193219B2CTDUZK12" tabindex="0" row="1" col="2" w="605" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 607px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193220Z5BEQOT7RD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193221NVNP5MBKUL">​​列标题</span></p></td></tr><tr id="row_2"><td id="k_2322193223NUT1Y11R7R" tabindex="0" row="2" col="0" w="212" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 214px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193224C4W1H7H778"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193225G42G7Q6E34">​​field</span></p></td><td id="k_2322193227QDB8AXX6FA" tabindex="0" row="2" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193228N5WHEGHVY3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193229NALY4D139I">​</span></p></td><td id="k_2322193231Z2IM25SR84" tabindex="0" row="2" col="2" w="605" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 607px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193232K5EAWBYS71"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193233YIT1L8QTMD">​​列字段名称</span></p></td></tr><tr id="row_3"><td id="k_2322193235AZWDYLBZPU" tabindex="0" row="3" col="0" w="212" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 214px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193236IG9HVQP8UP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23221932372G6J548GZB">​​width</span></p></td><td id="k_2322193239QV3RQWTGN2" tabindex="0" row="3" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193240HDAXUDNE4E"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193241DR2EN7FVHQ">​​默认值：auto</span></p></td><td id="k_232219324341OEM2E8F8" tabindex="0" row="3" col="2" w="605" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 607px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193244KJEHDL98B9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193245KNPN1YILXB">​列宽度</span></p></td></tr><tr id="row_4"><td id="k_23221932472EL1GY1LMA" tabindex="0" row="4" col="0" w="212" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 214px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193248E953J78F65"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193250IACO3P7KQV">​​minWidth</span></p></td><td id="k_2322193252DLYLN6BJZI" tabindex="0" row="4" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23221932532VYUBRNUA7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322264596ZOT8PUZ2JG">默认值：undefined</span></p></td><td id="k_2322193256ADICY63UHH" tabindex="0" row="4" col="2" w="605" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 607px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193257XJZT7G92QO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193258HS1UNVRD2U">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322255597CF5NZTIL6T">​当设置了固定列宽，该项无效，设置列的最小宽度</span></p></td></tr><tr id="row_5"><td id="k_2322193260VLJISQL4FL" tabindex="0" row="5" col="0" w="212" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 214px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_232219326145NI219GH1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23221932625XJDG6DSEM">​​align</span></p></td><td id="k_2322193264JUTG5MX8HP" tabindex="0" row="5" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193265SXB256NT4R"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193265KWOOS5X2DV">​​默认值：center，可取值：center 、right、left</span></p></td><td id="k_2322193267BHR6EAD1FJ" tabindex="0" row="5" col="2" w="605" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 607px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23221932673P8C7AQJF1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193268CTRURCNLNQ">​​对齐方式</span></p></td></tr><tr id="row_6"><td id="k_23221932708WTR1FQCG5" tabindex="0" row="6" col="0" w="212" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 214px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193271D6GNKG5STP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193272PEZCBKA7ZQ">​​sortable</span></p></td><td id="k_232219327321CKELZR3O" tabindex="0" row="6" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193274J13P6A1JXQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322265631SYX1BDEJLH">默认值：</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322265632IP2XQT746T">false</span></p></td><td id="k_2322193276O2CLVBOTNW" tabindex="0" row="6" col="2" w="605" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 607px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322193277Y5KOVAGNYB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193278VN3TNMDYTD">​​true则该列支持鼠标点击排序</span></p></td></tr><tr id="row_7"><td id="k_2322193279LPZNW2WEKD" tabindex="0" row="7" col="0" w="212" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 214px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_232219328082L9W7DTRS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193280F57YUM3E4G">​​formatter</span></p></td><td id="k_2322193282PYGIC12S1Q" tabindex="0" row="7" col="1" w="472" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 474px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23221932821R2XPJ856K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193283FW2SXCPVDU">​​默认值值：undefined；</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322272233HP2ILSL6T6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23222759183QAIAC5SIS">formatter = function(</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322275920MWAHHZUBFO">cellData, rowData, fieldName</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322275921QVJNGOL2BJ">){}</span></p></td><td id="k_2322193284GYTWLIZ5BG" tabindex="0" row="7" col="2" w="605" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 607px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23221932855BLB52AYSX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322193285RY9ENQOGJU">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322280101WWCYJ6KSPO">​列渲染格式函数,​cellData = [单元格值], rowData=[行数据], fieldName=[字段名]</span></p></td></tr></tbody></table></div><div id="k_232127245564485H579E" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2321272456VCMPNO43XV">​</span></div><div id="k_23222932453T5W9FGDCE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322293246GSEHTDKE4E">​</span></div><div id="k_2322284261N2WJLR2I15" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190323222856KSFRVK8WKEBZ"><tbody><tr id="row_0"><td id="k_232228566591X2WS8OS7" tabindex="0" row="0" col="0" w="1295" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1297px; background-color: rgb(240, 244, 255); color: rgb(153, 0, 51);" colspan="3" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 244, 255); color: rgb(153, 0, 51);" id="k_2322285666CRP9K2CD7R"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(240, 244, 255);" id="k_2322302025C9C6QDI3WL">实例API</span></p></td></tr><tr id="row_1"><td id="k_23222856764QCGFJDMOC" tabindex="0" row="1" col="0" w="242" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 244px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285677JRPLAJNEPO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322313633GQQMT4ZU3F">getCheckedId</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23223136355FAYB4263E">(split)</span></p></td><td id="k_2322285680FWN57PRST9" tabindex="0" row="1" col="1" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285680WWKX6JBSFL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23223216778QFCV1XZO5">split : 分隔符 ，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322322393WDF5RMZOS2">split默认值 [ ;&nbsp; ]</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322323416SN6MH4I46N">&nbsp;分号</span></p></td><td id="k_2322285683TGICCUJSQ3" tabindex="0" row="1" col="2" w="763" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 765px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285683VDKJC5WAEA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285684KRMXI8Y9EU">​​获取复选id</span></p></td></tr><tr id="row_2"><td id="k_2322285685V19W54KR3I" tabindex="0" row="2" col="0" w="242" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 244px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285686CGOXLPLDSX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285687UCYZZF33A3">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322330407GHF8J2LJJ1">​getCheckedData()</span></p></td><td id="k_2322285688NGSRVTC61D" tabindex="0" row="2" col="1" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285688QSWWBTQI9Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23222856891ZD1WWHJ6G">​</span></p></td><td id="k_232228569079IXB1BPEF" tabindex="0" row="2" col="2" w="763" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 765px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285691KLD4F218IL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285691289KJ5892F">​​获取复选的数据，返回值为数组[]</span></p></td></tr><tr id="row_3"><td id="k_0122101473OFDAI2IECJ" tabindex="0" row="3" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 244px; background-color: rgba(0, 0, 0, 0);" w="242" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0122101474BXAG3HOGF6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0122101475AIAVODAMPU">​reload(args)</span></p></td><td id="k_0122101478CZPJNZHKEQ" tabindex="0" row="3" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px; background-color: rgba(0, 0, 0, 0);" w="286" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0122101478XYMEQRD71Z"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01221014796RAT4TVP6N">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01221057044CMYIACX9A">args : 查询参数</span></p></td><td id="k_012210148148UZ19P7LK" tabindex="0" row="3" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 765px; background-color: rgba(0, 0, 0, 0);" w="763" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0122101482IYP53BUXNB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0122101485N8G1TO7W4Z">​根据参数重新查询，并回到第一页</span></p></td></tr><tr id="row_4"><td id="k_23222856937WCLRMG7ZO" tabindex="0" row="4" col="0" w="242" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 244px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285693RTMQ9YI61D"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285695Q2HEEYMTUW">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322333482H71YYR9GX2">​refresh(args)</span></p></td><td id="k_23222856966VBQDO4VX5" tabindex="0" row="4" col="1" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_232228569769BQUUN7HL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285697ORDKOQNZ4Z">​​args : 查询参数</span></p></td><td id="k_23222856991BGCSWHIZ9" tabindex="0" row="4" col="2" w="763" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 765px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23222856009F8WM4BNL7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_012214094821N1NF2L91">刷新当前页，注意与reload有区别，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0122140949NFOR5ZYASU">reload会回到第一页</span></p></td></tr><tr id="row_5"><td id="k_232228560237O7JZAVNL" tabindex="0" row="5" col="0" w="242" h="153" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 155px; width: 244px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23222856026X1KGGOAVD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285603T2I2TOXJYK">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322335662D6T6SMHEZ1">​openInner(​tr, args)</span></p></td><td id="k_2322285605GZTWN72TR6" tabindex="0" row="5" col="1" w="286" h="153" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 155px; width: 288px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285605F9COJ115TM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285607FTH275J3IC">​tr:目标行</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322342741WRNZDSIRRC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322342742P8DX9V51WL">args:是远程请求的配置参数</span></p></td><td id="k_2322285608JKEKW2YGBF" tabindex="0" row="5" col="2" w="763" h="153" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 155px; width: 765px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285609SRXQZJ759O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23222856109S3GQ9495L">​​开行内嵌入内容，可拥于请求远程一个详情页面</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_232234502658XV16OYSH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322345027OEYFK7NP9Y">​​openInner(tr,&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322351337PICXFC54YH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322351338VYHUBR4S2Z">&nbsp; {&nbsp; &nbsp;content: 'http://localhost/myui/demo/test.html',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322351746T7N3PGRGX9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322360227Y75NC4JVB9">&nbsp; &nbsp; &nbsp; type: 'iframe', //如果是url请求的时候</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322360228EZ8NFLET6X">，type=html/iframe</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_23223521807DNPZBEK3C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322355770ZRPE9OM34B">&nbsp; &nbsp; &nbsp; onLoaded: function () {</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322353733RVOKN8SAV2">console.log("loading is ok");</span><span style="font-family: &quot;Microsoft Yahei&quot;;">}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2322362017OUAE1UYIQD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23223620174GFU41W674">});&nbsp;</span></p></td></tr><tr id="row_6"><td id="k_23222856114OKSR4OILU" tabindex="0" row="6" col="0" w="242" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 244px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285612AAS3H6RAJG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285612IPOSHBDYJF">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322373100PIMZZOK9XV">​getData()</span></p></td><td id="k_2322285613GXKV3ZGJ3V" tabindex="0" row="6" col="1" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23222856141YETW6MOGP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285615ZBQ42TSIQO">​</span></p></td><td id="k_2322285616JE2IHWUIL4" tabindex="0" row="6" col="2" w="763" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 765px; background-color: rgba(0, 0, 0, 0);" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2322285616ZB828VL76M"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322285617QNDW2IRHO1">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322374133Q8AHD7N1AE">​获取列表数据，返回数组</span></p></td></tr></tbody></table></div><div id="k_2322284187SJ6VF96FC3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2322284188B1ZZVZZSSR">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_2410183062OFAT1C8AXL" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(252, 242, 244); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(245, 88, 232); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; font-weight: 400; color: rgb(102, 102, 102); background-color: rgb(252, 242, 244); font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2410183064SAOZMH6EHF">Datagrid数据格式说明​</span></div><div id="k_2410183060Q9O43XCBC1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;;" id="k_2410183061YHJ3CT2M4D">​</span></div><div id="k_2410183158NXONVWQE8X" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 39px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;;" id="k_2410183159L184BDC1F3"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">{</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; "resultList": [{</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;id: '111'</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;f1 : '字段1'</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;},{</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; id: '2222'</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; f1 : '字段值'</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;},{</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;}],</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; "totalSize":100,</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">&nbsp; &nbsp; &nbsp; "currentPage":1</span></div><div style="background: none;"><span style="background: none; font-family: &quot;Microsoft Yahei&quot;; white-space: normal;">}</span></div></div></pre></span></div><div id="k_2410183112Q1QM6R9KZZ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;;" id="k_2410183113H31C4DL9T9">​</span></div><div id="k_2410183278NRSHVS7CJ5" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;;" id="k_2410183279A5MDGLH1C7">​</span></div><div id="k_2321272670T8ADHZEOE5" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(252, 242, 244); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(245, 88, 232); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 242, 244); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2321303732Z27U5ULTEV">Demo</span></div><div id="k_2321271480T5LBP6JGKA" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24095700972SD1HBLGVV">​1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2322402862FT8KOEA8BX">定义table标签</span></div><div id="k_2409570009CJE12Q3LBM" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_240957043537HCN4GPRP"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(83, 83, 83); background: none;">&lt;</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(41, 111, 169); background: none;">table</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">=</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(161, 100, 75); background: none;">"datagrid"</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(41, 111, 169); background: none;">table</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(83, 83, 83); background: none;">&gt;</span></div></div></pre></span></div><div id="k_2409570444ZISUPSGPSI" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24095726341YRFW4TTR7">​2、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24095722538K94ODJNDM">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2409570445SKFN5UHUP2"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2409570445IJANEQF34U">​定义列col配置</span></div><div id="k_2409572643Z4G6824OUI" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24095727651R28VOS7U4"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(86, 156, 214);">var</span>&nbsp;<span style="color: rgb(156, 220, 254);">cols</span>&nbsp;= [</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;[{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"标题3标题3标题3"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"colspan"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">3</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"标题1标题1标题1"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"colspan"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">2</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"rowspan"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">2</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"字段标题"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"colspan"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">1</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"rowspan"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">3</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"field"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"createTime"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;],</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;[{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"标题"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"colspan"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">2</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"标题2"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"colspan"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">1</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"rowspan"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">2</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"width"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">100</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"sortable"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"标题1"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"colspan"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">4</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;],</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;[{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"菜单名称"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"field"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"menuName"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"width"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(181, 206, 168);">200</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"align"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"left"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"sortable"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">false</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"formatter"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">""</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"行为"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"field"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"content"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"width"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"100px"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"align"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"left"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"sortable"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"formatter"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">""</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"操作人"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"field"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"createUser"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"width"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"100px"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"align"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"center"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"sortable"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"formatter"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"formatfn1"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"状态"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"field"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"status"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"minWidth"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"100px"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"align"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"center"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"sortable"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">false</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"formatter"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"formatfn"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"数据3"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"field"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"menuName"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"minWidth"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"120px"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"align"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"center"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"sortable"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"formatter"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">""</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},&nbsp;&nbsp;&nbsp;&nbsp;</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"title"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"字段标题"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"field"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"createTime"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"width"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"auto"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"align"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"center"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;]</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">];</div></pre></span></div><div id="k_2409572775XCJ39QWD2Z" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24095745298N6ZMSIT5P">​3、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2409572775YITFNKTMUR"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2409572776ZP2LSQD3YJ">​定义工具栏</span></div><div id="k_2409574538LHGUPH5DJ4" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2409574659BUG3MSYXCK"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(86, 156, 214);">var</span>&nbsp;<span style="color: rgb(156, 220, 254);">childrenBtns</span>&nbsp;= [{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"id"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"line_btn_0"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"删除"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"fa-trash"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"params"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"cmd"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"delete"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"privilage"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"1"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"visualable"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"disabled"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">false</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"click"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">function</span>&nbsp;(<span style="color: rgb(156, 220, 254);">prs</span>) {</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;alert</span>(<span style="color: rgb(78, 201, 176);">JSON</span>.<span style="color: rgb(220, 220, 170);">stringify</span>(<span style="color: rgb(156, 220, 254);">prs</span>));</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"id"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"line_btn_1"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"更新"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"color"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">""</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"methodsObject"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"toolMethods"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;"params"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"cmd"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"update"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"privilage"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"1"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"visualable"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"disabled"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">false</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"fa-doc-text"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"id"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"line_btn_2"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"管理子菜单"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"methodsObject"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"toolMethods"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"params"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"cmd"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"update"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"privilage"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"1"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"visualable"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"disabled"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">false</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"fa-share-2"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">];</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; background: none;"><div style="background: none;"><span style="color: rgb(41, 111, 169); background: none;">var</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">groupBTs</span>&nbsp;= [</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;[{</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"重新加载"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"fa-search"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;"click"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">prs</span>) {</div><div style="background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dg</span>.<span style="color: rgb(175, 175, 125); background: none;">reload</span>({</div><div style="background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;q:</span>&nbsp;<span style="color: rgb(136, 161, 123); background: none;">123</span></div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;});</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}, {</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"获取数据"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"fa-file-word"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"click"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">prs</span>) {</div><div style="background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;alert</span>(<span style="color: rgb(33, 156, 131); background: none;">JSON</span>.<span style="color: rgb(175, 175, 125); background: none;">stringify</span>(<span style="color: rgb(111, 175, 209); background: none;">dg</span>.<span style="color: rgb(175, 175, 125); background: none;">getData</span>()));</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}],</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;[{</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"刷新当前页"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"fa-arrows-ccw"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"click"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">prs</span>) {</div><div style="background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;dg</span>.<span style="color: rgb(175, 175, 125); background: none;">refresh</span>();</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}, {</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"删除"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"fa-cancel-2"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"click"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">prs</span>) {</div><div style="background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;alert</span>(<span style="color: rgb(161, 100, 75); background: none;">"click2"</span>);</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}],</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;[{</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"获取勾选的数据"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"fa-ok-circled"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"click"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">prs</span>) {</div><div style="background: none;"><span style="color: rgb(41, 111, 169); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;var</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">data</span>&nbsp;=&nbsp;<span style="color: rgb(111, 175, 209); background: none;">dg</span>.<span style="color: rgb(175, 175, 125); background: none;">getCheckedData</span>();</div><div style="background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;alert</span>(<span style="color: rgb(33, 156, 131); background: none;">JSON</span>.<span style="color: rgb(175, 175, 125); background: none;">stringify</span>(<span style="color: rgb(111, 175, 209); background: none;">data</span>));</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}, {</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"获取勾选的id集合"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"fa-cancel-2"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"click"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">prs</span>) {</div><div style="background: none;"><span style="color: rgb(41, 111, 169); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;var</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">data</span>&nbsp;=&nbsp;<span style="color: rgb(111, 175, 209); background: none;">dg</span>.<span style="color: rgb(175, 175, 125); background: none;">getCheckedId</span>();</div><div style="background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;alert</span>(<span style="color: rgb(33, 156, 131); background: none;">JSON</span>.<span style="color: rgb(175, 175, 125); background: none;">stringify</span>(<span style="color: rgb(111, 175, 209); background: none;">data</span>));</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}],</div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;[{</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"text"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"其他"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"iconCls"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">"fa-down"</span>,</div><div style="background: none;"><span style="color: rgb(161, 100, 75); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"childrens"</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">childrenBtns</span></div><div style="background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}]</div><div style="background: none;">];</div></div></div></pre></span></div><div id="k_240957466994CE2WGS8L" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2409574670CT4JPO8GI7">4、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2409574671QWA37YMWZ3">​创建datagrid实例</span></div><div id="k_240957226482C15HM98B" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2409572265O7CGBQEDWX"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(86, 156, 214);">var</span>&nbsp;<span style="color: rgb(156, 220, 254);">args</span>&nbsp;= {</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(106, 153, 85);">&nbsp;&nbsp;&nbsp;&nbsp;// data: treeData, // data, treeData</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;cols:</span>&nbsp;<span style="color: rgb(156, 220, 254);">cols</span>,&nbsp;<span style="color: rgb(106, 153, 85);">// treeCol cols</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;isTree:</span>&nbsp;<span style="color: rgb(86, 156, 214);">false</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;title:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"表格标题"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;methodsObject:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"toolMethods"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;loadImd:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//是否立即加载</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;pageSize:</span>&nbsp;<span style="color: rgb(181, 206, 168);">10</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;pgBtnNum:</span>&nbsp;<span style="color: rgb(181, 206, 168);">5</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;fillParent:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;oprCol:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//是否需要操作列</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;oprColWidth:</span>&nbsp;<span style="color: rgb(181, 206, 168);">200</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//定义操作列宽</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;url:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'http://localhost/myui/demo/Handler.ashx?flag=datagrid'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">// flag=datagrid datagridTree</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;checkBox:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//是否需要复选框</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;idField:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'id'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//id字段名称</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;toolbar:</span>&nbsp;<span style="color: rgb(156, 220, 254);">groupBTs</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//toolbars</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;toolbarOpts:</span>&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;style:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'min'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//工具栏按钮样式</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;color:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'#EDEDED'</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iconColor:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'#BCB9C9'</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fontColor:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'#666666'</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;pgposition:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"bottom"</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//both bottom top</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;iconCls:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'fa-table'</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;btnStyle:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'plain'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//plain</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;showBtnText:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;sortField:</span>&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'order_index'</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'desc'</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"fieldName"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'asc'</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},&nbsp;<span style="color: rgb(106, 153, 85);">//默认排序字段</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;setParams</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">function</span>&nbsp;() {&nbsp;<span style="color: rgb(106, 153, 85);">//设置查询时候附加的参数</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(197, 134, 192);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return</span>&nbsp;{</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"p"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"p1"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;};</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;onDbClickRow</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">function</span>&nbsp;() {&nbsp;<span style="color: rgb(106, 153, 85);">//双击一行时触发 fn(rowData)</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;alert</span>(<span style="color: rgb(206, 145, 120);">"onDbClickRow "</span>&nbsp;+&nbsp;<span style="color: rgb(78, 201, 176);">JSON</span>.<span style="color: rgb(220, 220, 170);">stringify</span>(<span style="color: rgb(86, 156, 214);">this</span>.<span style="color: rgb(220, 220, 170);">data</span>(<span style="color: rgb(206, 145, 120);">"data"</span>)));</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;onClickCell</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">function</span>&nbsp;(<span style="color: rgb(156, 220, 254);">field</span>,&nbsp;<span style="color: rgb(156, 220, 254);">value</span>) {&nbsp;<span style="color: rgb(106, 153, 85);">//单击一个单元格时触发fn(field, value)</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;alert</span>(<span style="color: rgb(206, 145, 120);">"onClickCell "</span>&nbsp;+&nbsp;<span style="color: rgb(156, 220, 254);">field</span>&nbsp;+&nbsp;<span style="color: rgb(206, 145, 120);">" "</span>&nbsp;+&nbsp;<span style="color: rgb(156, 220, 254);">value</span>);</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;onCheck</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">function</span>&nbsp;(<span style="color: rgb(156, 220, 254);">isChked</span>,&nbsp;<span style="color: rgb(156, 220, 254);">data</span>) {&nbsp;<span style="color: rgb(106, 153, 85);">//复选事件</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;alert</span>(<span style="color: rgb(206, 145, 120);">"onCheck "</span>&nbsp;+&nbsp;<span style="color: rgb(156, 220, 254);">isChked</span>&nbsp;+&nbsp;<span style="color: rgb(206, 145, 120);">" data="</span>&nbsp;+&nbsp;<span style="color: rgb(78, 201, 176);">JSON</span>.<span style="color: rgb(220, 220, 170);">stringify</span>(<span style="color: rgb(156, 220, 254);">data</span>));</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;onLoaded</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">function</span>&nbsp;(<span style="color: rgb(156, 220, 254);">data</span>) {&nbsp;<span style="color: rgb(106, 153, 85);">//加载完成回调</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(106, 153, 85);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;//console.log("onLoaded" + JSON.stringify(data));</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;onRowRender</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">function</span>&nbsp;(<span style="color: rgb(156, 220, 254);">data</span>,&nbsp;<span style="color: rgb(156, 220, 254);">rowIdx</span>) {&nbsp;<span style="color: rgb(106, 153, 85);">//行渲染事件</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(197, 134, 192);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if</span>&nbsp;(<span style="color: rgb(156, 220, 254);">data</span>.<span style="color: rgb(156, 220, 254);">menuName</span>.<span style="color: rgb(220, 220, 170);">indexOf</span>(<span style="color: rgb(206, 145, 120);">"0"</span>) &gt;&nbsp;<span style="color: rgb(181, 206, 168);">0</span>) {</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(86, 156, 214);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;this</span>.<span style="color: rgb(220, 220, 170);">children</span>().<span style="color: rgb(220, 220, 170);">children</span>().<span style="color: rgb(220, 220, 170);">css</span>({</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"font-weight"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"bold"</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(206, 145, 120);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"color"</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(206, 145, 120);">"#D1A700"</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;});</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;}</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">};</div><div style="color: rgb(255, 0, 5); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none; font-weight: bold;">var&nbsp; dg&nbsp;=&nbsp;new&nbsp;$B.Datagrid($("#datagrid"),&nbsp;args);</div></pre></span></div><div id="k_2322394876NL4U67P4AJ" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2322394877GN1LL7X466">​</span></div><div id="k_2322394859EGCG9K51UH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23223948617ZG5HHYSI6">​</span></div><div id="k_2409563620C1Q2PLZ76B" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2409563622GO6GFNN9SL">​</span></div><div id="k_24101826141RSL3QCC3U" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410182617KMRHBFOU5S">​</span></div><div id="k_0122100742VMPHOINGIH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0122100743NAY7ZF1CR8">​</span></div><div id="k_0123313941O978XBZA4Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0123313943GDNMQS2STI">​</span></div><div id="k_24102136929QQNXR55LX" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 388px; height: 175px; position: absolute; top: 2449px; left: 273px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_24102136941WNBC9X8AI" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;" class="k_editor_float_input k_box_size"><div id="k_2410213699X4NEAMM14T" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410213600IPLBTKVYE6">​说明：</span></div><div id="k_2410214503KHPTMPFR9B" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410214504R4CGUCVLAJ">​resultList:&nbsp; 实体json数据列表，</span></div><div id="k_2410220883SY1QJTDCHX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410220884I5P9MMJJS3">​totalSize:&nbsp; 总页数</span></div><div id="k_2410222107FKV79U3M3I" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_24102221089J24DQP374">​currentPage: 当前页</span></div><div id="k_241022454558W29O3NKX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410224547KR95E7ME62">​</span></div><div id="k_2410224721FXEK2AW13X" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none;" id="k_241023158552KB8XQBOW">提示：树形列表数据格式 请参考 tree 组件数据格式说明</span></div></div></div>
</div>