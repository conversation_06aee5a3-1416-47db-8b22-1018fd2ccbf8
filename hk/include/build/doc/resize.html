<div style="position:relative">

<div id="k_2222072723XH2GGLQB8Q" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(243, 237, 250); border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(0, 230, 12); border-image: initial; margin-top: 12px; margin-bottom: 20px; text-indent: 0px; padding-left: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250);" id="k_22221324828IOPT9E4TH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2222132483F2TQ7ZE14T">​<img id="k_2222132483BS1SL812U4" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABTklEQVQ4T7WSvUoDQRSFz9lZxMxqqwjmRysfwE70DdRCTJXGQoQ8gW1A0NpKECEvEBt7LcRGAtaiYDaFYqGNaxJ1Z65sNJJfNhCc8s6935xzzxAjHo44j/8HaF9tO5BEkLaH/dTGKvAenF2ABYIHQSYsgLDtoKEApLMvkJCCkyBj8u2QXoBgTPsqR+EOIQtCTBJs9glECJQCbbYwhSCqdQDGq1h2rSoCnB+UTgQBcPHumixm8fIH8HyVg+CIoBcXrYg8W8ds1NO4agISPpYcq0okp4cY9q1rsvUkrn8sPEJ7n+oMwAqEN4A9ta49rwP3SOI1SiFaYhMsuP1yw9WPJO5aD1FX1Tot94wT5hspXHYraAEEKBsJNxtzqHTEOFFRx4amGPnpJ/8XsGYRbtQyeOruoa5isZZCeZB3XcFMTeOtFVsPIG5pcfexPzEO8A3zyXkRwtOTJQAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250);" id="k_22221324846NVZDI4YVP">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250);" id="k_2222132482LVROZXRETK">介绍</span></div><div id="k_222210570951NINCCSUS" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 39px; width: 678px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: 1px solid rgb(212, 180, 250); border-right: 1px solid rgb(212, 180, 250); border-bottom: none; border-left: 1px solid rgb(212, 180, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_22222431553HB2N4I1IB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_222224315683P2VCUI1C">​<img id="k_22222431562W5BJCOR5X" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA9UlEQVQ4T62TvUoDQRSFv7NklShWVla+hemSjWDpAwRcLOwsLG0sTBXIQ6TZRXwBbWxc8BkEGzGVlVYWknHJFfwBN5qdwDjt3PNx5twzIvAoUM//AW4y1yphKLEFNAGHcWdRdNLda1zMc/rhoMjejpH1v4Szs6XBKLmPD9XXdPZSRVbuwvQMsVaTRwk6TdJ48BuQuytgxxum2YPFS9vdnsY/Z1Xkk0fQhh/AS6So104blxXAdeaeJNa9AHgFO0jS5fMqIJ+MhTa9AJvjoFgwAzNuVyzutPb1XM0gdAufPXBHBgOJ1T+eUt+Db0FQE70B1gwEf6Z34rVdERtra+AAAAAASUVORK5CYII=" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_22222431575OTEDYD1XL">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222243155MORMJQBNHN">Bui缩放框组件</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222235294WU9EED4G3Y">可应用于某个标签对象的</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222235296EXRLASY17B">缩</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222240854NQY63TBCTT">放，</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222240854BYC9S6GAOI">并且支持等比例缩放设置。</span></div><div id="k_2222150518PSMROSBNYH" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 39px; width: 678px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(212, 180, 250); border-bottom: none; border-left: 1px solid rgb(212, 180, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_22222439331TCUBVWH52">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2222243935FPY84SV5VE">​<img id="k_2222243935CL13ZA4EM1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA9UlEQVQ4T62TvUoDQRSFv7NklShWVla+hemSjWDpAwRcLOwsLG0sTBXIQ6TZRXwBbWxc8BkEGzGVlVYWknHJFfwBN5qdwDjt3PNx5twzIvAoUM//AW4y1yphKLEFNAGHcWdRdNLda1zMc/rhoMjejpH1v4Szs6XBKLmPD9XXdPZSRVbuwvQMsVaTRwk6TdJ48BuQuytgxxum2YPFS9vdnsY/Z1Xkk0fQhh/AS6So104blxXAdeaeJNa9AHgFO0jS5fMqIJ+MhTa9AJvjoFgwAzNuVyzutPb1XM0gdAufPXBHBgOJ1T+eUt+Db0FQE70B1gwEf6Z34rVdERtra+AAAAAASUVORK5CYII=" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222243935OOOH2R4JW8">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_22222439338RI1V2EXNE">Bui缩放框支持自定义缩放框边线、缩放锚点的样式。</span></div><div id="k_2222172028HHGYTGC13A" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 39px; width: 678px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(212, 180, 250); border-bottom: none; border-left: 1px solid rgb(212, 180, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222245017R5RGTQE9ZY">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2222245018Z3UMJRWFYB">​<img id="k_2222245018LHF7H4IUZY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA9UlEQVQ4T62TvUoDQRSFv7NklShWVla+hemSjWDpAwRcLOwsLG0sTBXIQ6TZRXwBbWxc8BkEGzGVlVYWknHJFfwBN5qdwDjt3PNx5twzIvAoUM//AW4y1yphKLEFNAGHcWdRdNLda1zMc/rhoMjejpH1v4Szs6XBKLmPD9XXdPZSRVbuwvQMsVaTRwk6TdJ48BuQuytgxxum2YPFS9vdnsY/Z1Xkk0fQhh/AS6So104blxXAdeaeJNa9AHgFO0jS5fMqIJ+MhTa9AJvjoFgwAzNuVyzutPb1XM0gdAufPXBHBgOJ1T+eUt+Db0FQE70B1gwEf6Z34rVdERtra+AAAAAASUVORK5CYII=" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_22222450185R8C1J8EFO">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_22222450177KVFEKSYF7">Bui缩放框支持动态禁用/启用缩放边线、缩放锚点。</span></div><div id="k_2222200334C586Z8TJTH" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 39px; width: 678px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(212, 180, 250); border-bottom: 1px solid rgb(212, 180, 250); border-left: 1px solid rgb(212, 180, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_222225087644ZRUP6CFB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2222250877MY6Y122JGK">​<img id="k_2222250877ZJCVMYTDAH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA9UlEQVQ4T62TvUoDQRSFv7NklShWVla+hemSjWDpAwRcLOwsLG0sTBXIQ6TZRXwBbWxc8BkEGzGVlVYWknHJFfwBN5qdwDjt3PNx5twzIvAoUM//AW4y1yphKLEFNAGHcWdRdNLda1zMc/rhoMjejpH1v4Szs6XBKLmPD9XXdPZSRVbuwvQMsVaTRwk6TdJ48BuQuytgxxum2YPFS9vdnsY/Z1Xkk0fQhh/AS6So104blxXAdeaeJNa9AHgFO0jS5fMqIJ+MhTa9AJvjoFgwAzNuVyzutPb1XM0gdAufPXBHBgOJ1T+eUt+Db0FQE70B1gwEf6Z34rVdERtra+AAAAAASUVORK5CYII=" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222250877LGCVAYZCPE">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222250876CEYUQ26Z9N">Bui缩放框支持动态绑</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222235297J3JSWQTOBK">定/解除缩放目标对</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_222224085557LZ7XMJPX">象，便于</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222240856VUFOP8ZUHV">缩放组件采用单列方式进行应用。</span></div><div id="k_2222105825OLOYHPCA5G" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222105825YGHCXTGDUI">​</span></div><div id="k_2222105881LV6KTIBAK2" class="_section_div_" style="line-height: 40.2286px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(243, 237, 250); padding-left: 20px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(0, 230, 12); border-image: initial; margin-top: 12px; margin-bottom: 20px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 168, 3); vertical-align: baseline;" id="k_22222651417KZG1SSH4L">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2222265142HESRGHKHEY">​<img id="k_2222265142J5C8C2MTZQ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABTUlEQVQ4T7WSvUpDQRCFz+yikqitoigxe1P4AHaib6AWwVRpLETIE9gGBK2tBBHyArGx10JsJGAt5m4S8AcRBVFUMDsjNzEhv1whuOXszLdnzlnCgIcGnMf/A7T1NoUQ4bi/30ttqALlJ7YBZAHssSlmQeBW0J8ARNgVQRWQIzZ+phXSDRAMa2vSILUlgnmQjBOo1icQIVDejb1uYOLpPai1A2x8SUHnCDD90qlBBGdu5CuFmbvnJkDbRFqAAwJGw6IVyCNrJBHzL+qAireoHPIEmvzDcIXBKZjSZX2F++mo/oycCGiZSK5E6JjJnWLou4jZ25cghcDEXw+uWWQFnr1pPEQoJdYUyw4TZ2BK550KmgCRglNuHfFyuS1GZb1D1sgF+/SSXwfIqlPVJOYqD509BN8swLOFvruXY1OIfrw1YusGhLkWch/6E8P4P7ktfxH7vQb3AAAAAElFTkSuQmCC" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 168, 3); vertical-align: baseline;" id="k_2222265142JZKBGWQIBS">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 168, 3); vertical-align: baseline;" id="k_2223012054LBTTWQDNZM">AP</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 168, 3); vertical-align: baseline;" id="k_2223012055QPF4RA7IAR">I及构造参数</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 168, 3); vertical-align: baseline;" id="k_2223012055SRP9QJJDR4">说明</span></div><div id="k_2222105831UO15GY513P" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: 730px; text-align: left; background-color: rgb(168, 168, 250); padding-left: 12px; border-top: 1px dotted rgb(168, 168, 250); border-right: 1px dotted rgb(168, 168, 250); border-bottom: none; border-left: 1px dotted rgb(168, 168, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250);" id="k_2222323604657BVQ6RLF">构造参数&nbsp; [</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250);" id="k_2222323604J2Z3DRRM7T">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250);" id="k_2222331583SUTLVLAZ3U"> </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250);" id="k_22223315838KD4R3C1RG">&nbsp;var resize = new $B.Resize</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250);" id="k_22223001473ONR1K4ZGY">( </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 5, 5); background-color: rgb(168, 168, 250);" id="k_22223001489DYLW4KYHG">args</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250);" id="k_22223022633T2JERSA1W"> )&nbsp; </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250);" id="k_2222302263B5HEDZZ9WL">&nbsp;]</span></div><div id="k_2222270816J7Z1TWBTJW" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190322223136Z121V1BNMRH4"><tbody><tr id="row_0"><td id="k_2222313663Y8RA23MSF3" tabindex="0" row="0" col="0" w="178" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 180px; background-color: rgb(168, 168, 250); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(168, 168, 250); color: rgb(255, 255, 255);" id="k_22223136644I7G6AXJQY"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250); text-align: center;" id="k_2222313664EY91U9WRC4">​参数名称</span></p></td><td id="k_2222313665G3N6COOXT1" tabindex="0" row="0" col="1" w="562" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 564px; background-color: rgb(168, 168, 250); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(168, 168, 250); color: rgb(255, 255, 255);" id="k_2222313665X5KOHT6A19"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250); text-align: center;" id="k_22223136651KQN97ME1T">参数值​</span></p></td><td id="k_22223136668SXYH3D1IZ" tabindex="0" row="0" col="2" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px; background-color: rgb(168, 168, 250); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(168, 168, 250); color: rgb(255, 255, 255);" id="k_2222313666NMSHXIDW7Q"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250); text-align: center;" id="k_2222313666NH6AL7VSZO">说明​</span></p></td></tr><tr id="row_1"><td id="k_22223136676PWD4RDYQD" tabindex="0" row="1" col="0" w="178" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 180px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313667C92OC6JLCB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222313667MKWEZERPMM">​​target</span></p></td><td id="k_2222313668G14E7E9R6O" tabindex="0" row="1" col="1" w="562" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 564px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313668VGVBGUCN66"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222384082ZV9CQGIJRY">默认值：undefined，可以通过</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(21, 0, 255); background-color: rgb(255, 255, 255);" id="k_2222384082NW154WZCH8">[</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(21, 0, 255); background-color: rgb(255, 255, 255);" id="k_22223840837QX62OKMEG">Instance]</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(21, 0, 255); background-color: rgb(255, 255, 255);" id="k_2222384633GAYODH5SX6">.setTarget(target)</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222384633RW34P5TJFP">进行设置</span></p></td><td id="k_2222313669R6R895YGEX" tabindex="0" row="1" col="2" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313669V5ZWT4YUME"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222313669SC2O4WUZGI">​​需要缩放的标签jquery对象</span></p></td></tr><tr id="row_2"><td id="k_2222313670RW8682QRBD" tabindex="0" row="2" col="0" w="178" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 180px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313670DSULUQIJ8R"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22223136701MAJT8LE7E">​​zoomScale</span></p></td><td id="k_2222313671QKXFKSVUY8" tabindex="0" row="2" col="1" w="562" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 564px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313671JIODEUC1X5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22223136717NOHVLMSTC">​​默认值：false</span></p></td><td id="k_22223136727SQHLX7HL3" tabindex="0" row="2" col="2" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_22223136729JPR4XDIXE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_222231367244CQEAEGWH">​​是否等比例缩放</span></p></td></tr><tr id="row_3"><td id="k_2222313673DFQVKSPG7Y" tabindex="0" row="3" col="0" w="178" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 180px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313673MOZCWY3UMA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222313673YGMHEGFRI4">​​poitStyle</span></p></td><td id="k_2222313674XH8YP5VHU3" tabindex="0" row="3" col="1" w="562" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 564px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313674FXFQCQI3NV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222313674JSPIO4VIVG">​​默认值:{ color: '#FF292E', icon: 'fa-circle-thin', "font-size": "8px" }</span></p></td><td id="k_222231367527QUHK9X8G" tabindex="0" row="3" col="2" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313675JACGB6THIH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22223136751I29S1PAES">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222395448YOWC5VIKN7">​缩放锚点样式配置</span></p></td></tr><tr id="row_4"><td id="k_2222313676JFZFFLPG6Z" tabindex="0" row="4" col="0" w="178" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 180px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313676H8LOQV5ITR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222313676QDUO8CS5ZF">​​lineStyle</span></p></td><td id="k_2222313677DUONXLL4PC" tabindex="0" row="4" col="1" w="562" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 564px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313677FZ4T2FYLZW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_222231367731ZWX2GVQB">​​默认值:{ "border-color": "#FF292E", "border-size": "1px", "border-style": "solid" }</span></p></td><td id="k_2222313678QPFKKXK4Y7" tabindex="0" row="4" col="2" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313678YD5UNMW3IE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222313678WDSRHOI87Y">​​缩放框4边样式</span></p></td></tr><tr id="row_5"><td id="k_2222405081AABCS5J3ME" tabindex="0" row="5" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 180px;" w="178" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222405081GBCPFRQ11S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222412951H63JZMBWLT">onDragReady</span></p></td><td id="k_2222405084C89HCDDGLO" tabindex="0" row="5" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 564px;" w="562" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_222240508433MUNOMSRO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222440339ZE25JQZITC">默认值：undefined&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2222430797OLGC8VUNPU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22224307974SWLCL8QYL">onDragReady = function(args) ，​args：请参考draggable拖动API</span></p></td><td id="k_22224050858VCX3QOGAY" tabindex="0" row="5" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px;" w="286" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222405085MVRNKX5UUV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222405085RV5NUQ52SM">​准备拖动前回调函数，返回true执行拖动，false不执行拖动</span></p></td></tr><tr id="row_6"><td id="k_2222404963TKHS4M2DUM" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 180px;" w="178" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222404963IPCT8T5S1K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22224049633FIFEV34M3">​​dragStart</span></p></td><td id="k_2222404964GZSS7QQN2B" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 564px;" w="562" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222404964PZDX9TQJN2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222404964ZLHS47IPEQ">​默认值：undefined&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_22224410385BFN9FD4KL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222441778L5PAH8FQFF">dragStart</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222441779CTF39NIBKJ"> = function(args) ，args：请参考draggable拖动API</span></p></td><td id="k_2222404965OTRTB8WETM" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px;" w="286" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222404965QBGVSXKW9L"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222455462E9Q56M7V28">开始拖动事件</span></p></td></tr><tr id="row_7"><td id="k_2222404888B529OKS917" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 180px;" w="178" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222404888HZ973ZGVQF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222404888MCC7KY9PWP">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222444131H9OZJS29YK">​onDrag</span></p></td><td id="k_2222404890PN7S54ZR74" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 564px;" w="562" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222404890986HRCEEHY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222404890ASFUQVZLOP">​​默认值：undefined&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2222445451G6T31X8MPD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222445452VH1RW93ZB3">onDrag = function(args) ，args：请参考draggable拖动API</span></p></td><td id="k_2222404891PBN1L3AQD3" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px;" w="286" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222404891GBTWFI2PTJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22224557434KIYNGI8TJ">拖动中事件</span></p></td></tr><tr id="row_8"><td id="k_2222313679PO16RK6SIS" tabindex="0" row="8" col="0" w="178" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 180px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313679N4TLXMTHOC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22223136795LDXRHH3Z7">​​dragEnd</span></p></td><td id="k_2222313680DDQQ6HE9IL" tabindex="0" row="8" col="1" w="562" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 564px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_22223136808WC43TEUAU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222313680ATC14I8V8F">​​​默认值：undefined&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2222453177USNVTBQ2OH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222453178GGF95VPW2O">dragEnd = function(args) ，args：请参考draggable拖动API</span></p></td><td id="k_2222313681THFBEQMMGI" tabindex="0" row="8" col="2" w="286" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 288px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222313681Z4E4GTAD1B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222460157J2N8J7RP7Q">拖动结束事件</span></p></td></tr></tbody></table></div><div id="k_2222284315BWJZ5R58KY" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_22222843156NKD861EBE">​</span></div><div id="k_2222284396KZXTJOS8J3" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2222284397MMKLNIQT8J">​</span></div><div id="k_22222709178TYW6FRXTE" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: 730px; text-align: left; background-color: rgb(168, 168, 250); padding-left: 12px; border-top: 1px dotted rgb(168, 168, 250); border-right: 1px dotted rgb(168, 168, 250); border-bottom: none; border-left: 1px dotted rgb(168, 168, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2222284821YVRXHDY2YV">实例</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2222284821XIEJK1C2CR">API</span></div><div id="k_2222105890TMPA1AAV6V" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190322224757OP5GM7IKZBX3"><tbody><tr id="row_0"><td id="k_2222475750FO7MZLCDTK" tabindex="0" row="0" col="0" w="228" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px; background-color: rgb(168, 168, 250); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(168, 168, 250); color: rgb(255, 255, 255);" id="k_22224757516BYLBLL9AV"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250); text-align: center;" id="k_22224757517UJ5JG683X">​名称</span></p></td><td id="k_2222475752YZC3JTVK6W" tabindex="0" row="0" col="1" w="510" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px; background-color: rgb(168, 168, 250); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(168, 168, 250); color: rgb(255, 255, 255);" id="k_2222475752NF2YRQ1N16"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250); text-align: center;" id="k_2222475752C285BT1S1B">参数​</span></p></td><td id="k_2222475753W586RUQKFQ" tabindex="0" row="0" col="2" w="290" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px; background-color: rgb(168, 168, 250); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(168, 168, 250); color: rgb(255, 255, 255);" id="k_2222475753JAWFJ3FK7R"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250); text-align: center;" id="k_2222475753KJZJNGT2FZ">​说明</span></p></td></tr><tr id="row_1"><td id="k_2222475754ZKLPGSE7K1" tabindex="0" row="1" col="0" w="228" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 230px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475754SVOQ274W1P"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22224933677XKYNB2IWT">setStyle(&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222493368SN2E8Z9IIH">pointStyle,lineStyle&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222493369H93JRLZ32Q">)</span></p></td><td id="k_22224757549M65CQ889D" tabindex="0" row="1" col="1" w="510" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 512px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475754AQMXRMGK66"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222475754URU5DZE7XB">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222494012P2DTHO4B6D">​pointStyle ： {拖动锚点样式}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2222493726A4C67GGDAC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222493726P45W5EAFLZ">lineStyle ：{拖动边线样式}</span></p></td><td id="k_2222475755YDUGLXSDSO" tabindex="0" row="1" col="2" w="290" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 292px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475755XW5VCOUQUY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222475755Z2OIYNQEWQ">​设置缩放框样式</span></p></td></tr><tr id="row_2"><td id="k_2222475757DB71JVSCBO" tabindex="0" row="2" col="0" w="228" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475757FOMRUYKLWG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222475758X2DJVA1BFI">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_222250254178ID92SVCB">​zoomScale( isZoom )</span></p></td><td id="k_22224757595XP2ZO7CXO" tabindex="0" row="2" col="1" w="510" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475759X6NI4QWUST"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222475759PLC61SDIVA">​isZoom :&nbsp; true / false</span></p></td><td id="k_2222475760NDD9LBLLO1" tabindex="0" row="2" col="2" w="290" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475760EXN2XQRXSS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222475760PWM3N1QFKB">​设置是否等比例缩放</span></p></td></tr><tr id="row_3"><td id="k_2222475760KNDRXG9G23" tabindex="0" row="3" col="0" w="228" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475760FQDQRQCOYJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222475760OWRBTXTPVT">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_222251381679HZS64NKC">​setTarget( target )</span></p></td><td id="k_2222475761PEA2DPPRDB" tabindex="0" row="3" col="1" w="510" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475761H5MJWBC9HU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_222247576132G4BJ5DA1">​target ： 缩放目标，jquery dom对象</span></p></td><td id="k_2222475762H2U6ZGRDLK" tabindex="0" row="3" col="2" w="290" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475762ZGRV6JNOWV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222475762JHHKP47K8X">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222531766V8QOEA427X">设置缩放目标，但</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222531768UUIBDY3RZ4">不会触发show</span></p></td></tr><tr id="row_4"><td id="k_2222475763RD8PBN8XZ4" tabindex="0" row="4" col="0" w="228" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475763SX9641RLO5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222475763F6GTDY51YQ">​​bind(target)</span></p></td><td id="k_2222475764P2AQTRZVU5" tabindex="0" row="4" col="1" w="510" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475764UQQRSLIY9F"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222475764V1HZT9BEUJ">​​​target ： 缩放目标，jquery dom对象</span></p></td><td id="k_222247576515ILTW8XL7" tabindex="0" row="4" col="2" w="290" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222475765FZA967IEGK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_222247576543P9M2JMP4">​绑定缩放对象，缩放框会立即show</span></p></td></tr><tr id="row_5"><td id="k_22225228431B4F8GJZPS" tabindex="0" row="5" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 130px; width: 230px;" w="228" h="128"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222522843OKKN71UMM2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225228433YCSFMYOB7">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222533939GK8FY8KB6K">​disable( flag )&nbsp;</span></p></td><td id="k_2222522844KZA3KCSZGX" tabindex="0" row="5" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 130px; width: 512px;" w="510" h="128"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222522844BTL7PKS6D9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222522844URNKS212O2">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222541875QDYMWXN5LJ">&nbsp;* flag = line/point/left/right ，不传值则禁用所有&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2222543592189D6EY2HF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222543592GI2UJ99TZK">&nbsp;* line:&nbsp; 禁用线&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2222543758RCF41TP3R8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222543759MRYBYLS1DU">&nbsp;* point:&nbsp; 禁用点&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2222543961TUS3KU96SQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222543962RKOTWIAJRU">&nbsp;* left： 禁用上边线，左边线，左边点&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2222544241JWFVR9YUNL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222544242P42KWKUUTN">&nbsp;* right: 禁用下边线，右边线，右边</span></p></td><td id="k_22225228458G867PZ87R" tabindex="0" row="5" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 130px; width: 292px;" w="290" h="128"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_222252284541RMMVMZ9H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222522845PVCGYUAW9C">​禁用边线、锚点拖动</span></p></td></tr><tr id="row_6"><td id="k_2222545256PKLHEZCQJ7" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px;" w="228" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222545256J7BFM8UCNE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222545256VGHBCQ6JJB">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222552926CEN3K311YN">​enable( flag )</span></p></td><td id="k_2222545257ODOKB664EC" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px;" w="510" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222545257GBWW23DCBR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222545257FLV6JN7GP1">​参数意义 同上</span></p></td><td id="k_2222545258X254V6WW91" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px;" w="290" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222545258G8DOFNNRHN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225452587F4OTA5YST">​启用边线，锚点拖动</span></p></td></tr><tr id="row_7"><td id="k_2222545186B4JZ2SAIPG" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px;" w="228" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222545186P3LCK5LPEJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222545186QVITNII2YS">​​unbind()</span></p></td><td id="k_2222545187P6Q9OVYZ8D" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px;" w="510" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222545187ZJW5SD7DXC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225451875HK6RGPFM9">​</span></p></td><td id="k_2222545188Z6CKK983ST" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px;" w="290" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222545188EWA5UE1CLA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222545188TMN3DU1FFT">​解除缩放目标</span></p></td></tr><tr id="row_8"><td id="k_2222522694EXBLLXKW94" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px;" w="228" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222522694XPG6WZ9QB5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222522694G5DTFF837B">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222570109WXXQ148L76">​show( target )&nbsp;</span></p></td><td id="k_22225226962N4YY8VHO4" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px;" w="510" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222522696LB776YOETF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222581585KCI8T68WXJ">target: 缩放目标，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222581585IL8AR19K5G">传值则先执行 setTarget( target )</span></p></td><td id="k_2222522698EWXBYQJC4K" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px;" w="290" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222522698IWFR9EVKJK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222522698V9FHM2NJBE">​显示缩放框</span></p></td></tr><tr id="row_9"><td id="k_2222575791TFRLY3LEGN" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 230px;" w="228" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222575791A1DDULBUR6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225757918WJKXKEX56">​​hide(target)</span></p></td><td id="k_22225757937L6L142MMY" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 512px;" w="510" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222575793R68F99NGLY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225757939DO6YGNGAA">​target: 缩放目标</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_22225840231I3UFT8UVY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225840237N2S2SMKQU">如传值target则当前缩放框里的target等于参数target才进行隐藏</span></p></td><td id="k_2222575794KG84AAXVD8" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 292px;" w="290" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_222257579421EG482IWW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225757946WVLWDCBD8">​​隐藏缩放框</span></p></td></tr><tr id="row_10"><td id="k_2222575604OXH7R64IFI" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px;" w="228" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222575604LELT433NTY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222575604RFB5UVJATX">​​isHide()</span></p></td><td id="k_2222575605YBHDLVF57C" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px;" w="510" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222575605BI1X1CJXFR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225756051M1HIE2O8W">​</span></p></td><td id="k_2222575607X48H1B7NA3" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px;" w="290" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222575607HXLEI1S4GE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225756072MGG8YEA76">​是否是隐藏状态</span></p></td></tr><tr id="row_11"><td id="k_22225912519DPI9NCK1N" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px;" w="228" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222591251ZBJDGBLUL2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222591251E8N2MSEXZG">​​isShow()</span></p></td><td id="k_2222591253ECBSBHDOLH" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px;" w="510" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222591253ZWVSBW5BDN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222591253GSZ9UCYMA9">​</span></p></td><td id="k_2222591254J4M6CDMPWB" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px;" w="290" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_22225912547HGQVFHATZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222591254P98ZV1PJNK">​是否是显示状态</span></p></td></tr><tr id="row_12"><td id="k_22225954389ZPRA4R3WS" tabindex="0" row="12" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 230px;" w="228" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222595439WKQUR1RZNF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22225954391HVFOBOA7T">​​destroy()</span></p></td><td id="k_2222595440TGJ8IZ16TA" tabindex="0" row="12" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 512px;" w="510" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222595440A9LD51TFR8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222595440G7OD45D9O2">​</span></p></td><td id="k_2222595441FJUZ6424A4" tabindex="0" row="12" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 292px;" w="290" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2222595441X2N4SPGBGR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222595441P5D7NK2KCC">​销毁对象</span></p></td></tr></tbody></table></div><div id="k_2222271634BV5JM5ISAJ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222271634BT126TZ9MG">​</span></div><div id="k_222300374856EXR7DQZI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2223003748NA6LT14QB3">​</span></div><div id="k_2223003737XOFJTFPFXV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(243, 237, 250); padding-left: 20px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(0, 230, 12); border-image: initial; margin-top: 12px; margin-bottom: 20px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(3, 168, 3);" id="k_2223015391ZREK69916K">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2223015392DPQFR6E2AB">​<img id="k_2223015392YSBGR1SRS4" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABTUlEQVQ4T7WSvUpDQRCFz+yikqitoigxe1P4AHaib6AWwVRpLETIE9gGBK2tBBHyArGx10JsJGAt5m4S8AcRBVFUMDsjNzEhv1whuOXszLdnzlnCgIcGnMf/A7T1NoUQ4bi/30ttqALlJ7YBZAHssSlmQeBW0J8ARNgVQRWQIzZ+phXSDRAMa2vSILUlgnmQjBOo1icQIVDejb1uYOLpPai1A2x8SUHnCDD90qlBBGdu5CuFmbvnJkDbRFqAAwJGw6IVyCNrJBHzL+qAireoHPIEmvzDcIXBKZjSZX2F++mo/oycCGiZSK5E6JjJnWLou4jZ25cghcDEXw+uWWQFnr1pPEQoJdYUyw4TZ2BK550KmgCRglNuHfFyuS1GZb1D1sgF+/SSXwfIqlPVJOYqD509BN8swLOFvruXY1OIfrw1YusGhLkWch/6E8P4P7ktfxH7vQb3AAAAAElFTkSuQmCC" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(3, 168, 3);" id="k_22230153937C36JVZF7A">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 168, 3); background-color: rgb(243, 237, 250); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(3, 168, 3);" id="k_2223015391BNBRM2AXDH">Demo</span></div><div id="k_2223003771V5KXXH49YU" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_222302585691M798MJ6V">​1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2223003771Z9Q3VSLSPQ">​定义需要缩放的div</span></div><div id="k_2223025870HGZPZMQF5P" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: 857px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2223025958D5VD2IQV1L"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">    </span><span style="background: none; color: rgb(83, 83, 83);">&lt;</span><span style="background: none; color: rgb(41, 111, 169);">div</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">id</span><span style="background: none; color: rgb(167, 167, 167);">=</span><span style="background: none; color: rgb(161, 100, 75);">"box"</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">style</span><span style="background: none; color: rgb(167, 167, 167);">=</span><span style="background: none; color: rgb(161, 100, 75);">"</span><span style="background: none; color: rgb(161, 100, 75);">position:absolute;top:83px;left:570px; width:200px;height:200px;background-color:#A58BF7;"</span><span style="background: none; color: rgb(83, 83, 83);">&gt;</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">        </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">h1</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">我是缩放框 div</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">h1</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">    </span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></div></div></pre></span></div><div id="k_22230316238E2HBHBAHV" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2223031623J88VDEBRLY">​</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;">​</span></div><div id="k_2223025964T39QQZ6OYL" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2223032284N67MW49IXZ">​2、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2223025965AVNQGE13MN"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2223025965ZHAVDGYM66">​创建缩放组件实例</span></div><div id="k_2223032292Q8UL4DF9ZV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: 854px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2223032292ILC2EWX9B2"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">resize</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(41, 111, 169); background: none;">new</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(33, 156, 131); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(33, 156, 131); background: none;">Resize</span><span style="color: rgb(167, 167, 167); background: none;">({</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">               <span style="color: rgb(255, 0, 5); background: none;"> </span></span><span style="background: none; color: rgb(255, 0, 5);">target: $("#box") ,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">zoomScale:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//等比例缩放</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">dragStart</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">$msg</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">html</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"dragStart "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"dragStart = "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"resizeData"</span><span style="color: rgb(167, 167, 167); background: none;">)));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onDrag</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">$msg</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">html</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onDrag "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onDrag = "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"resizeData"</span><span style="color: rgb(167, 167, 167); background: none;">)));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">dragEnd</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">$msg</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">html</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"dragEnd "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"dragEnd = "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"resizeData"</span><span style="color: rgb(167, 167, 167); background: none;">)));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            });</span></div></div></pre></span></div><div id="k_2223032493ZD81FXABDB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22230324942A729OF2PY">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_2222105904ZNI5OHBA19" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2222105905H9FZ3ZFCVX">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_2223074017CEFOF5PFNG" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 724px; height: 35px; position: absolute; top: 1409px; left: 155px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_2223074019U7DDESO5NB" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;"><div id="k_2223074019HZ4LKS4VZ8" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2223085909611AEXWZ7I">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2223085910N7DLNYUHQ8">​<img id="k_2223085910W4LZUSIAEO" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABdElEQVQ4T6WSv0tWURjHP997z1mCEJ2ipaHJ/6DJJRzMoSJaaigUt36gOAoRNIpSukViQ0FDZIMKintb0N4kNNYguJznvY/c8/7A977vS0bPds/zvZ/ncw6PGFEOE4TwPrfNHgl+D4tqJKAs5yiKd7lfVfNqtbYuDHC4QgifkKY6P30npduC4yZkwMBBxPgEeNMIPyOlTYGfPx8GuE6MX3H/hfvrHJaeI13tWPwcCXAIhPACaYWq2qDVWsrhslyjKJ7i/gqzlwLrQvoMHCaJcR+4BnwkpYUcjPEt8CBbmc0IfgwAOtNXs267BgH1aX0ts+WuRc/AY7yB+x7S+F8Af5BuKaVv+XkyFC71NEctRv95thOctgEh3AS+IF3u5dw/Y7aYv0NYR7p3rncC3JXZkRzGCGEb6U5j+PA36IbcdzB7LC/L+0gfkGIfwH0Xs3qhaoNNpNlGP+H+UB7CAdL0xa7eSLkf1gYbFEV70r9WVdWr/X91Buf0jNJxBjzaAAAAAElFTkSuQmCC" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2223085910BJ6R61831P">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background: none;" id="k_2223085909V94BFCY2AQ">强烈建议您采用 单列模式，通过实例 setTarget( target ) 实现一个对象，控制多个缩放目标</span></div></div></div>
</div>