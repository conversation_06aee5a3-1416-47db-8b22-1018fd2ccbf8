<div style="position:relative">
     
    <div id="k_2420390941A7B7OWZVRV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 252, 252); border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(3, 118, 240); border-image: initial; padding-left: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(11, 11, 219); background-color: rgb(247, 252, 252); letter-spacing: 1px;" id="k_2420431468R2VAFVLNEN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2420431471TDMUR3ONNE">​<img id="k_2420431472B972DUV5ZP" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAB60lEQVQ4T6WTO2iUQRSFvzvzBxNEzHOzIprS2KRRhDSijQgpDCL4hDQBgzbRoDYWKgg2bhd8BkGIDzRYiY9KCwuxMEUKY6GxEP9djY24sdiZIxPdsMZKnGrgnu+eU5xr/MezRrYNrV/h2CdjK9AEVCUeLUSmvmHzy31+w8qKnoOIEkb7X2HEbIiMfoYnYKrPDWTdnv2Iy2a8IXBW8KyMfV+N2lo8g4hTQDORoRx7vgQXUJ9z3JHxthY4Oo99XO7ciTZ5zzhQs8ChHJtLGis6nROMKrKngj1tBDvQ2sxxgsh1PP0GVwXD5WATv2Cve4JWCxyowTrnGLBIqQatmWfcYJfEXYtclOOawYs8Mgb2I8GPJeZCpJR5rgDbJCYxugx2CF4SGK7Cp5WeyaQtR46BLaTYN2W0VwNDzbDFOy5h9KRYdbCMzaxBPdFzy8T0knO302ngiEUGc3jVBTvTAhl5ckxgWlRAu51nKoqRSrSUEOtEmzPHfRnTChyuQKUA/UBewd4lUQfamHluIJpDZO8XbHYRBmVFxxjGBcEDBc5UYAYsplkBtjvPeYlejJFy4Ha9KPWGtXSnBXDSjFWIDxjvEX2LjRNfMY7ngUmwWkPD6l9ZB/Q2OYaBAYwNiNeChxaZqBejsQd/HMa/HthP6UzIEPPDU94AAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24204319188QM1PL3JAW">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(11, 11, 219); background-color: rgb(247, 252, 252); letter-spacing: 1px;" id="k_2420431469P6GBWP54XI">API及构造参数</span></div><div id="k_2420412440NJIKWMOV8F" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420412442E4H9XEKMOO">​</span></div><div id="k_242043432698R24UP694" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324204406CSTIQEC57EUF"><tbody><tr id="row_0"><td id="k_2420440602AQ3UWS82ZZ" tabindex="0" row="0" col="0" w="1343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1345px; background-color: rgb(247, 247, 252);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 247, 252);" id="k_2420440602J93K98GVEK"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252);" id="k_24204406036XDDJBPEGA">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252);" id="k_2420471678IOHOASGLEO">​var $tab = new $B.Tab($("#tab"),opts);&nbsp; &nbsp;opts参数说明</span></p></td></tr><tr id="row_1"><td id="k_2420440612DP59BFO6RW" tabindex="0" row="1" col="0" w="226" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 228px; background-color: rgb(247, 247, 252); text-align: center; font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_24204406132B5LPA47US"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_24204406141TZUXG75TU">参数名​</span></p></td><td id="k_2420440615ODIZX5TRV8" tabindex="0" row="1" col="1" w="477" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 479px; background-color: rgb(247, 247, 252); text-align: center; font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420440616ZTEOWHS2E9"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420440617QXSGR7SL13">参数值​</span></p></td><td id="k_2420440620ADGWGJRC2I" tabindex="0" row="1" col="2" w="636" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 638px; background-color: rgb(247, 247, 252); text-align: center; font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420440621ZANGLCC8RY"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420440622WBTBVN8PSP">说明​</span></p></td></tr><tr id="row_2"><td id="k_24204406269KARG3LN4T" tabindex="0" row="2" col="0" w="226" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 228px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420440627V1C4Z5ZR7H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420440628C7LVUIADM1">​​tabs</span></p></td><td id="k_24204406329126B5TIIA" tabindex="0" row="2" col="1" w="477" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 479px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420440634QARJ7OTFQF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420573138ED2EHIY2TE">数组，请参考《</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_242057313981O6GAA676">tabs说明</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420573140R7XJUH4U89">》</span></p></td><td id="k_2420440638I9XJ9F5EY6" tabindex="0" row="2" col="2" w="636" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 638px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420440639HYLZJTMWUA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420440640KEEMIKMUH8">​定义tab项</span></p></td></tr><tr id="row_3"><td id="k_2420440642YACTZEI1FE" tabindex="0" row="3" col="0" w="226" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 228px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24204406433CAQU5I1OD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24204406454TIABWSGSR">​​cxtmenu</span></p></td><td id="k_2420440648NV127UTR9P" tabindex="0" row="3" col="1" w="477" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 479px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420440648XP6CM3A4R8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420481521Z9LB21BJ96">默认值：true&nbsp;</span></p></td><td id="k_2420440652RG8R8B3LW7" tabindex="0" row="3" col="2" w="636" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 638px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420440652FR5EW86YDJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420440653EO2BL2GDSI">​是否需要右键菜单，​依赖于ctxmenu组件</span></p></td></tr><tr id="row_4"><td id="k_2420495669SLYEQQQ385" tabindex="0" row="4" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 228px;" w="226" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24204956708G5OLWDZZ4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420502601MYGYFI1CZ1">onLoaded</span></p></td><td id="k_2420495672XTC5ELZBR7" tabindex="0" row="4" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 479px;" w="477" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420495673NPVAJ2LMFC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24205014123GFQCA5QUW">onLoaded =&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420513527TAJEOSFCAQ">function (title){}&nbsp; </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420513529M7MH7HXP8F">&nbsp; &nbsp;[&nbsp;title：标签页名称 ]</span></p></td><td id="k_2420495676DJN1MNFBTZ" tabindex="0" row="4" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 638px;" w="636" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420495677V16ZRACXDL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420495677OLCQP5ZAZE">​​远程加载完成回调</span></p></td></tr><tr id="row_5"><td id="k_2420495565WYFSI9X45N" tabindex="0" row="5" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 228px;" w="226" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420495566XZLKD22323"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420512096D3KJ9MFRZD">onClosed</span></p></td><td id="k_2420495569F45QDF8EX3" tabindex="0" row="5" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 479px;" w="477" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420495570XVPI2VX84E"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420495570Y7QO6B6EV4">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420521324SVJIWISPCA">onClosed = function (title){}&nbsp; &nbsp; &nbsp;&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420521325CJK8LGFXU8">[&nbsp;title：标签页名称 ]</span></p></td><td id="k_24204955724U2E9M8QQ8" tabindex="0" row="5" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 638px;" w="636" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420495573LJNKHTZNQF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420495573PXH9VZKGFF">​​标签页关闭回调</span></p></td></tr><tr id="row_6"><td id="k_2420495446DUQKRKBQ92" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 228px;" w="226" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420495447F7X6KUCYE8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420515965STX19BLUOB">onClick</span></p></td><td id="k_2420495450NFQZBOXRO1" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 479px;" w="477" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24204954503NRF6KFJ6M"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420520400A8ER3D289Y">onClick =&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420520403TPBHSDL943">function(title){}&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;​[&nbsp;title：标签页名称 ]</span></p></td><td id="k_24204954534SJN62KL68" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 638px;" w="636" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_242049545316GR3VMVUV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420495454W8ME54TH6N">​​title：标签页名称</span></p></td></tr></tbody></table></div><div id="k_2420412532U25ULL9ZNY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420412533FJSG33SG3N">​</span></div><div id="k_2420483770BAWAZU66Z6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 37px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="2019032420531372SGLICNF3YG"><tbody><tr id="row_0"><td id="k_2420531311LWEH4JRK9O" tabindex="0" row="0" col="0" w="1335" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1337px; background-color: rgb(247, 247, 252);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 247, 252);" id="k_2420531312T1CZ3E59JQ"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252);" id="k_2420531313PTEL9EQ6M5">​tabs说明</span></p></td></tr><tr id="row_1"><td id="k_2420531322WSOMDSB5BH" tabindex="0" row="1" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420531323RFTJYB4469"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420531324HAG23I4SUQ">参数名​</span></p></td><td id="k_2420531326W8D4T2WZ5P" tabindex="0" row="1" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_242053132753BV6DKDAF"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420531327A6SHRXZ392">参数值​</span></p></td><td id="k_2420531329ZR8CHBEA2E" tabindex="0" row="1" col="2" w="632" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 634px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_24205313303PT2PWJSCV"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420531331EF12PAY4Q8">说明​</span></p></td></tr><tr id="row_2"><td id="k_2420531332LTNIQDLTPP" tabindex="0" row="2" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531333YVUORVPPKT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531338ZTAK16M1VD">​​title</span></p></td><td id="k_2420531340ZNS52E4NHX" tabindex="0" row="2" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531341OV1RRGH4D7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531342DUZJ44DXJS">​</span></p></td><td id="k_2420531344BMSQKLT1JN" tabindex="0" row="2" col="2" w="632" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 634px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24205313442YKWSQW23L"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24205313455OQKH9BDEV">​​标签页标题</span></p></td></tr><tr id="row_3"><td id="k_2420531347IPUKW9GGWR" tabindex="0" row="3" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_242053134853EVE3LCPG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531349R3LIPI5NWT">​​data</span></p></td><td id="k_2420531350XSRYIU45AT" tabindex="0" row="3" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531351LA7IQHE3II"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531351NNEBQ1IIR2">​​如{id:123}</span></p></td><td id="k_2420531353P1UIHB44XN" tabindex="0" row="3" col="2" w="632" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 634px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531353Z3CYDR9HI9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24205313548VGKOWIJ7T">​​标签页附带的数据</span></p></td></tr><tr id="row_4"><td id="k_2420531355C97L97G2WP" tabindex="0" row="4" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531356FXYFPWU9Z3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531356O2L5KPEUYI">​​tabCount</span></p></td><td id="k_2420531358JU63QWMR65" tabindex="0" row="4" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531358KPNXTC8YV5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531359YOMJYTYLV4">​​默认无限制</span></p></td><td id="k_24205313603KXAIOND95" tabindex="0" row="4" col="2" w="632" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 634px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531361YQKB3AZB45"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531361O4CYL5B1WF">​​打开的标签数量</span></p></td></tr><tr id="row_5"><td id="k_2420531363ZK5JS4RF11" tabindex="0" row="5" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531363YKDXALLQ49"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531364QZP2HW42JJ">​​iconCls</span></p></td><td id="k_24205313663NHSCN7YWX" tabindex="0" row="5" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531366CAXLAATRPH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531367B2LSXRGQ5Z">​</span></p></td><td id="k_24205313689MSZODO3XL" tabindex="0" row="5" col="2" w="632" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 634px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531369QSJCJ1FY84"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531370CAMIIPVSTI">​​标签页图标样式，font-awesome字体图标</span></p></td></tr><tr id="row_6"><td id="k_2420531371GQU33RGGXX" tabindex="0" row="6" col="0" w="227" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531372Y6LVIR8938"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531372OKZMZYE2IP">​​closeable</span></p></td><td id="k_2420531374VBKDS2BCQZ" tabindex="0" row="6" col="1" w="472" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420531374548ZBSNVZU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531375CWRCUGOHRP">​​默认值：false</span></p></td><td id="k_2420531378FY9FQ8B9AG" tabindex="0" row="6" col="2" w="632" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 634px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24205313792T72GRQ8ZI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420531379WWE8QVHRFP">​</span></p></td></tr><tr id="row_7"><td id="k_24205609205HIHRRXLNP" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_242056092192NRIRT87V"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420560922PPKYUJAY34">​​actived</span></p></td><td id="k_2420560924DU3XNYND2A" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;" w="472" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420560925UR6C9G6RRS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_242056092669RKJE34X2">​​默认值：false</span></p></td><td id="k_24205609276QTW62KRTW" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 634px;" w="632" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420560928N4MPZV427T"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420560929G4ZS7FROWW">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420563799V8VLUXUUBQ">​是否激活状态</span></p></td></tr><tr id="row_8"><td id="k_2420560860D332N7YDFX" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420560861WDXQG4DLJ9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420560862NQUQ49TFUP">​​content</span></p></td><td id="k_24205608648IZ5KKYA2J" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;" w="472" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420560865FI76EQUBGF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420560865SC7XW7P2YC">​​静态HTML / 远程请求url</span></p></td><td id="k_24205608671G9C21EQGK" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 634px;" w="632" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420560868KEO32QDQMF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_242056086944IH36MK7M">​</span></p></td></tr><tr id="row_9"><td id="k_2420560643O1QUO7S63T" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 229px;" w="227" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420560644FXSJA7UB8I"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420560645YH3PPJ9CJC">​​dataType</span></p></td><td id="k_2420560652APAUE3S321" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 474px;" w="472" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24205606531SCIFXYHH5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420560654T2MAVYYKW2">​​默认值：html</span></p></td><td id="k_2420560657UXZVOF6YF4" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 634px;" w="632" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420560658YFXAQBF615"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420560659YNQVBBD5VN">​​远程请求类型，取值[html、iframe、json]</span></p></td></tr></tbody></table></div><div id="k_2420483743DVLF5W6SQU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24204837438CLCADVBGJ">​</span></div><div id="k_2420572365FUHSKM31Y2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24205723652JAQ1E1RDM">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_2420580546GBKFED24GU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 37px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324205805L7B63NXM45E9"><tbody><tr id="row_0"><td id="k_2420580552OOGRFIS193" tabindex="0" row="0" col="0" w="1335" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1337px; background-color: rgb(247, 247, 252);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 247, 252);" id="k_2420580554S39UJF9M85"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252);" id="k_2420580555MUBHHE6ESP">​实例 API</span></p></td></tr><tr id="row_1"><td id="k_2420580572Q8UNY8BNYO" tabindex="0" row="1" col="0" w="443" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 445px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_242058057423IDGMVGAZ"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420580575CQRHJD41AJ">API名称​</span></p></td><td id="k_24205805795MXTHM6SVZ" tabindex="0" row="1" col="1" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420580580RWYGOD1CLB"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420580582RZ7KU2CXOI">​参数</span></p></td><td id="k_24205805854THLO4USID" tabindex="0" row="1" col="2" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420580586POK5UVX78W"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420580587WF7E3L9TRF">说明​</span></p></td></tr><tr id="row_2"><td id="k_2420580591NEOR76M5ZN" tabindex="0" row="2" col="0" w="443" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 445px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580592I7FPWWKYJ1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420585662KJIXISTXJQ">active</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420585664HBNRFD2CE3">(title)</span></p></td><td id="k_2420580596HXCM9O6S5S" tabindex="0" row="2" col="1" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580598YS12X4AQGE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420580599WINDL7N7BG">​​title：标签页名称</span></p></td><td id="k_2420580501XOAUJVUBHQ" tabindex="0" row="2" col="2" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580502VEA3IFET8K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24205805036G5CV6AJH7">​​激活某一个标签页</span></p></td></tr><tr id="row_3"><td id="k_24205805064QUIDJ58LY" tabindex="0" row="3" col="0" w="443" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 445px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580507XCYW57PFNH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24205926531DGURILVZY">reload</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420592655P7HDJIVD38">(title)</span></p></td><td id="k_2420580510YZLN2VJOD9" tabindex="0" row="3" col="1" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580511UWT2TE7KRX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420595395R17HOFNUNE">同上</span></p></td><td id="k_2420580514T5XD42W59A" tabindex="0" row="3" col="2" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24205805163GOXGXQQU7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420580517OARSIJGY3O">​​刷新某个标签页</span></p></td></tr><tr id="row_4"><td id="k_2420580520NXF3X7SHLL" tabindex="0" row="4" col="0" w="443" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 445px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580521XNITID8CSO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24205949618PHTYMJ6XZ">close</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420594963R3PXHRU765">(title)</span></p></td><td id="k_2420580524P6CG2OJB8T" tabindex="0" row="4" col="1" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24205805253C23ZIWKMV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420580526PFPRZSDJWV">​​同上</span></p></td><td id="k_2420580529M3O1NNPRX5" tabindex="0" row="4" col="2" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580530CNES2DHJTP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420580530WT8Y3KERS4">​​关闭某个标签页</span></p></td></tr><tr id="row_5"><td id="k_2420580534VO3M4VKYUW" tabindex="0" row="5" col="0" w="443" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 445px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580535PDKRBF94MB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2421010512TNSS8IH5RA">add</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2421010513NJI9HXOQ8P">(opt)</span></p></td><td id="k_2420580539CDWLQ1W8JU" tabindex="0" row="5" col="1" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24205805407XJJ8ZLF6O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24210124008JZVJRFU4I">opt：请参考</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2421012403B7SBD8A9HM">《​&nbsp;​tabs说明》</span></p></td><td id="k_24205805433H9WTTNALV" tabindex="0" row="5" col="2" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580544ZIUJBWSTDF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420580545M59XFS3187">​​新增一个标签页</span></p></td></tr><tr id="row_6"><td id="k_2420580547SL6NFID5FK" tabindex="0" row="6" col="0" w="443" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 445px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24205805484G8IO1UWC3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2421014803UR9HSS4I4J">getActived</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2421014805JMKYLZ7AQ5">()</span></p></td><td id="k_2420580550TCQGWEDJDK" tabindex="0" row="6" col="1" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580551LC5TW163IP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24205805526QFI8AJVYP">​​获取当前激活的标签(返回对应的li标签)</span></p></td><td id="k_24205805533F4176ADP8" tabindex="0" row="6" col="2" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420580554WNB7HTD21S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420580555LQ4A3P3WKF">​​如获取数据： tabIns.getActived().data("itdata")</span></p></td></tr></tbody></table></div><div id="k_2420572313A8B3DPDLOZ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420572314LWJY6PBWPR">​</span></div><div id="k_2420483864IYU8DXYZPE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420483865BQALQXO8YT">​</span></div><div id="k_24204125439HZPR65MR6" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 252, 252); padding-left: 20px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(3, 118, 240); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(11, 11, 219); background-color: rgb(247, 252, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(11, 11, 219); vertical-align: baseline;" id="k_24204340249KR933WQEY">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2420434027W8WNSBROMX">​<img id="k_2420434028R1HN8HDUQY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAB60lEQVQ4T6WTO2iUQRSFvzvzBxNEzHOzIprS2KRRhDSijQgpDCL4hDQBgzbRoDYWKgg2bhd8BkGIDzRYiY9KCwuxMEUKY6GxEP9djY24sdiZIxPdsMZKnGrgnu+eU5xr/MezRrYNrV/h2CdjK9AEVCUeLUSmvmHzy31+w8qKnoOIEkb7X2HEbIiMfoYnYKrPDWTdnv2Iy2a8IXBW8KyMfV+N2lo8g4hTQDORoRx7vgQXUJ9z3JHxthY4Oo99XO7ciTZ5zzhQs8ChHJtLGis6nROMKrKngj1tBDvQ2sxxgsh1PP0GVwXD5WATv2Cve4JWCxyowTrnGLBIqQatmWfcYJfEXYtclOOawYs8Mgb2I8GPJeZCpJR5rgDbJCYxugx2CF4SGK7Cp5WeyaQtR46BLaTYN2W0VwNDzbDFOy5h9KRYdbCMzaxBPdFzy8T0knO302ngiEUGc3jVBTvTAhl5ckxgWlRAu51nKoqRSrSUEOtEmzPHfRnTChyuQKUA/UBewd4lUQfamHluIJpDZO8XbHYRBmVFxxjGBcEDBc5UYAYsplkBtjvPeYlejJFy4Ha9KPWGtXSnBXDSjFWIDxjvEX2LjRNfMY7ngUmwWkPD6l9ZB/Q2OYaBAYwNiNeChxaZqBejsQd/HMa/HthP6UzIEPPDU94AAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(11, 11, 219); background-color: rgb(247, 252, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(11, 11, 219); vertical-align: baseline;" id="k_2420434029DEP9TI1RYP">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(11, 11, 219); background-color: rgb(247, 252, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(11, 11, 219); vertical-align: baseline;" id="k_2420434025C91BVRAB4O">Demo</span></div><div id="k_2420412553DSEJEJOQBB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420412554NW37QIANMX">​</span></div><div id="k_24210311272EMAXZ9HOY" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421035835S3EWLCXYSQ">1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421031128RZ9QPCPP5H">​定义容器标签</span></div><div id="k_242103195391QEP4N32Y" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24210320199T4K4EQETE">​</span></div><div id="k_2421034765RWPDE994E6" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_242103476697A2KSPL4F"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(167, 167, 167); background: none;">        </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"tab"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></pre></span></div><div id="k_24210323886JS4YICLFM" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421032389RS6W7QS1B4">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421032390SSA44CPLGR">​</span></div><div id="k_2421032030T59VZSSONR" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421033319KZOURPNUL8">​2、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421032031BW2YY6Q2X9"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421032032ONFYPDJA7C">​定义tabs项</span></div><div id="k_2421033326O6AA1JXDVK" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421033382X4JK3MZ8UL">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421033326SZ7ENZXEWV">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421033327GS55FW1BKU">​</span></div><div id="k_2421035245BEDCBMKNM4" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421035246WHVEI5OUJV"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);"> var </span><span style="background: none; color: rgb(111, 175, 209);">tabs =</span><span style="background: none; color: rgb(167, 167, 167);"> [{</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'tab标题标题1'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-database'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">closeable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">actived:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">content:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"&lt;div style='height: 500px;width: 800px'&gt;&lt;p&gt;我是静态内容，这里可以是html标签内容！&lt;/p&gt;&lt;/div&gt;"</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }, {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'IFRAM内容'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">closeable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-chart-bar'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">actived:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'test.html'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">dataType:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'iframe'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }, {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'HTML片段'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">closeable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-docs'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">actived:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fragment.html'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">dataType:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'html'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }, {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'JSON标签请求'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-print'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">closeable:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">actived:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'/bui/api/json?flag=datagridTree'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">dataType:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'json'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }</span><span style="color: rgb(167, 167, 167);">];</span></div></div></pre></span></div><div id="k_2421034144N1RK3TJZYK" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421034145UETD87A57P">​</span></div><div id="k_2421033392DCF982GGWO" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421033393YGJ3Z8Z8N6">3、创建实例</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421033393Y7XBTF1MES">​</span></div><div id="k_2420412724EN8I5VXMNZ" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420412724P474UIRWW9">​</span></div><div id="k_2421035421XRO7XK5C7H" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2421035422ZPNWZQ65T2"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">      var </span><span style="background: none; color: rgb(111, 175, 209);">$tab</span><span style="background: none; color: rgb(167, 167, 167);"> = </span><span style="background: none; color: rgb(41, 111, 169);">new</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(33, 156, 131);">$B</span><span style="background: none; color: rgb(167, 167, 167);">.</span><span style="background: none; color: rgb(33, 156, 131);">Tab</span><span style="background: none; color: rgb(167, 167, 167);">(</span><span style="background: none; color: rgb(175, 175, 125);">$</span><span style="background: none; color: rgb(167, 167, 167);">(</span><span style="background: none; color: rgb(161, 100, 75);">"#tab"</span><span style="background: none; color: rgb(167, 167, 167);">), {</span></div><div style="background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">cxtmenu:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//右键菜单</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onLoaded</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">title</span><span style="color: rgb(167, 167, 167); background: none;">) { </span><span style="color: rgb(61, 108, 40); background: none;">//加载后</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">title</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">" 加载完成......."</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(152, 89, 147); background: none;">if</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(41, 111, 169); background: none;">arguments</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">length</span><span style="color: rgb(167, 167, 167); background: none;"> == </span><span style="color: rgb(136, 161, 123); background: none;">2</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"JSON 加载完成:"</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(41, 111, 169); background: none;">arguments</span><span style="color: rgb(167, 167, 167); background: none;">[</span><span style="color: rgb(136, 161, 123); background: none;">1</span><span style="color: rgb(167, 167, 167); background: none;">]));</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">html</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"JSON 加载完成:"</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(41, 111, 169); background: none;">arguments</span><span style="color: rgb(167, 167, 167); background: none;">[</span><span style="color: rgb(136, 161, 123); background: none;">1</span><span style="color: rgb(167, 167, 167); background: none;">]));</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onClosed</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">title</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onclosed ="</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">title</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onClick</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">title</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(175, 175, 125); background: none;">alert</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"你点击了"</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">title</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">tabs:</span><span style="background: none;"><font> <span style="font-weight: bold;">tabs</span></font></span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">            });</span></div></div></pre></span></div><div id="k_2420434870SBTXVT7FTQ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24204348722SUYWC5CJA">​</span></div>
     
</div>