<div style="position:relative">
<div id="k_2211161107PRD8GHIDIV" class="_section_div_" style="line-height: 32.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 248, 252); margin-top: 12px; margin-bottom: 12px; text-indent: 36px; border-top: 1px dotted rgb(238, 0, 255); border-right: none; border-bottom: none; border-left: 1px dotted rgb(238, 0, 255); border-image: initial;"><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(238, 0, 255); background-color: rgb(247, 248, 252); font-weight: normal;" id="k_22112632015QVQ24L68G">介绍</span></div><div id="k_2211162559H25JTLD7ZL" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 67px; width: 680px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: 1px solid rgb(175, 216, 250); border-right: 1px solid rgb(175, 216, 250); border-bottom: none; border-left: 1px solid rgb(175, 216, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211310746JRHY4TR7LE">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2211310749KQS9TQI3HI">​<img id="k_2211310749FVHC4FM6JO" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAByElEQVQ4T52SQUhUURSG///ead5LyIIWpQ1lYbSrwMVoQrgJ2oi7MDKQmcFNBUWghKJuIlpEC2sj0xBoJO4ibONG2tQsBHETgeQEkzJELSRl3vjuPXFfvRjJ1ZzlOec753D+n2gwGHODPeu+rnJAiHsgzkV5wWcKnhpfZl8una7W74jAoY5Si0nKHMjLEOyAUv4DMgWiCSLvdY3908ttmzHMG+kfzb7amgVwFaLG9O6BZ9PLrTuuIdP9/RDM9gQU7lLkXdU2D7wqHt1yNWa6SlkSebEYLXw89QigxFPvtIsXHN7UxgsmAA6LIFf40PYiArOXSguAtNfoXTlSaam45NQag1y6nBIdvo2GWN6GsgWAa7Vf/rWZ1ePbbuMnEl+MyH1Nvo76BLcU8RzARQArRuS6Jp+I4IyyB3vyxWOV/UDXHMcKTaJXheqnSQZzAp79B2a7SvOgXHCneqG1f8+LNjkoX0yVb3ZvnExKsOjk0TWv3z2Pmc71QSjmYTHunpNLfzthVTipbGLSQU6TTOfXB1R4uOc59XIIOGo9mYrFHurYaDLJ3TGhDP8nR8MGqLdcoqr7hHZEiPORVoJVinoc+ubNvpZrxOe/AcCw6Q/V6dWJAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211310751JL23NYFX7A">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211310747H1FJEBULSE">为解决前端开发中</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_221129393027K49ZXDWY">繁琐的表单读</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211293931ODKTXS76KA">写编程工作，Bui根据表单开发场景开发了一个mini-mvvm。</span></div><div id="k_221116348585ZC8FAKKK" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 67px; width: 680px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(175, 216, 250); border-bottom: none; border-left: 1px solid rgb(175, 216, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211311968T44DXW1GGV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_221131197184KC8Z2OEO">​<img id="k_221131197214L9MZ7PKH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAByElEQVQ4T52SQUhUURSG///ead5LyIIWpQ1lYbSrwMVoQrgJ2oi7MDKQmcFNBUWghKJuIlpEC2sj0xBoJO4ibONG2tQsBHETgeQEkzJELSRl3vjuPXFfvRjJ1ZzlOec753D+n2gwGHODPeu+rnJAiHsgzkV5wWcKnhpfZl8una7W74jAoY5Si0nKHMjLEOyAUv4DMgWiCSLvdY3908ttmzHMG+kfzb7amgVwFaLG9O6BZ9PLrTuuIdP9/RDM9gQU7lLkXdU2D7wqHt1yNWa6SlkSebEYLXw89QigxFPvtIsXHN7UxgsmAA6LIFf40PYiArOXSguAtNfoXTlSaam45NQag1y6nBIdvo2GWN6GsgWAa7Vf/rWZ1ePbbuMnEl+MyH1Nvo76BLcU8RzARQArRuS6Jp+I4IyyB3vyxWOV/UDXHMcKTaJXheqnSQZzAp79B2a7SvOgXHCneqG1f8+LNjkoX0yVb3ZvnExKsOjk0TWv3z2Pmc71QSjmYTHunpNLfzthVTipbGLSQU6TTOfXB1R4uOc59XIIOGo9mYrFHurYaDLJ3TGhDP8nR8MGqLdcoqr7hHZEiPORVoJVinoc+ubNvpZrxOe/AcCw6Q/V6dWJAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211311974RUI82JDUUJ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211311969ZA2R6F94O5">mini-mvvm的目的是解</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211244543BCD2G8IX3L">放表单读写编程，当然也支持逻辑运算绑定表达式。</span></div><div id="k_2211274708NES5A4EEXU" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 67px; width: 680px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(175, 216, 250); border-bottom: 1px solid rgb(175, 216, 250); border-left: 1px solid rgb(175, 216, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211312960DK8E9VC8S6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2211312964RSSZPM42VI">​<img id="k_2211312965SZGW219SHH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAByElEQVQ4T52SQUhUURSG///ead5LyIIWpQ1lYbSrwMVoQrgJ2oi7MDKQmcFNBUWghKJuIlpEC2sj0xBoJO4ibONG2tQsBHETgeQEkzJELSRl3vjuPXFfvRjJ1ZzlOec753D+n2gwGHODPeu+rnJAiHsgzkV5wWcKnhpfZl8una7W74jAoY5Si0nKHMjLEOyAUv4DMgWiCSLvdY3908ttmzHMG+kfzb7amgVwFaLG9O6BZ9PLrTuuIdP9/RDM9gQU7lLkXdU2D7wqHt1yNWa6SlkSebEYLXw89QigxFPvtIsXHN7UxgsmAA6LIFf40PYiArOXSguAtNfoXTlSaam45NQag1y6nBIdvo2GWN6GsgWAa7Vf/rWZ1ePbbuMnEl+MyH1Nvo76BLcU8RzARQArRuS6Jp+I4IyyB3vyxWOV/UDXHMcKTaJXheqnSQZzAp79B2a7SvOgXHCneqG1f8+LNjkoX0yVb3ZvnExKsOjk0TWv3z2Pmc71QSjmYTHunpNLfzthVTipbGLSQU6TTOfXB1R4uOc59XIIOGo9mYrFHurYaDLJ3TGhDP8nR8MGqLdcoqr7hHZEiPORVoJVinoc+ubNvpZrxOe/AcCw6Q/V6dWJAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211312967LC3FCP5XGM">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211312962CNJNMF5PMW">mini-mvvm适用于表单读写场景、及J</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211293934MW8ZPE83KA">SON绑定到页面的显示场景。</span></div><div id="k_2211215877KBYQR9IQZC" class="_section_div_" style="line-height: 32.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 248, 252); padding-left: 0px; border-top: 1px dotted rgb(238, 0, 255); border-right: none; border-bottom: none; border-left: 1px dotted rgb(238, 0, 255); border-image: initial; margin-top: 25px; margin-bottom: 20px; text-indent: 36px;"><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(238, 0, 255); background-color: rgb(247, 248, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(238, 0, 255); vertical-align: baseline;" id="k_2211535339W5IOOSE7ND">绑定Demo-1[ </span><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(168, 168, 250); background-color: rgb(247, 248, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(238, 0, 255); vertical-align: baseline;" id="k_2211535340GNTVHM4AAD">new $B.mvvm(opts) 方式</span><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(238, 0, 255); background-color: rgb(247, 248, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(238, 0, 255); vertical-align: baseline;" id="k_2211535341JXUZY1YD8U"> ]</span></div><div id="k_2211215832GFEUIKYVLS" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211421558GU4XVUOJU7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2211421560Z6R99LUXDF">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2211423630JYS9JWN3H9">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2211424261J6FUXN4CYV">​<img id="k_2211424262W3EPZHNOCH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAzUlEQVQ4T82TMQ4BYRCF3/yTbKOkcQSlAyhFKXQancoFZJP985I9hkLiFg4gUYiCVlTiAAqFZHdEouLHioKpZ76Z915G8GXJl/P4QwDJSp7nMzNbq+qQ5OmVzAcJJKMsy8Yi0jezqaoOSJ6fQYIefAKRJElGAGqBDRGApoiUX11yBaxEpP4mzq1zrkVyd9/3NEbvfc/MJgAOANppmm5CS4IA733nNnxU1S7JZWET4ziuqupcRErOuTbJxUcxAhCSDQD7kObCHhT9kd//wgV6K08Rzda8JwAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22114242632498DQTID9">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2212032615WPITFG5D13">编写具有绑定表达式的表单[ </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255);" id="k_2212032616UCPFLQHS56">myform</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2212032617HN9452DHF5"> ]：</span></div><div id="k_2211345613UGVDQ4QBQ9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 94px; width: 876px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2211345613MNLXEQN46Q"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #d4d4d4;"><span style="color: rgb(102, 102, 102);"><div style="color: rgb(102, 102, 102);"></div></span></span>&lt;div id="myform"&gt;</div><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #808080;">      &lt;</span><span style="color: #569cd6;">div  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">style</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"</span><span style="color: #ce9178;">"</span><span style="color: #808080;">&gt;</span><span style="color: #d4d4d4;">p1属性值 ：{{this.data.p1}} </span><span style="color: #808080;">&lt;/</span><span style="color: #569cd6;">div</span><span style="color: #808080;">&gt;</span></div><div style="background-color: rgb(30, 30, 30); line-height: 19px; font-family: Consolas, &quot;Courier New&quot;, monospace; color: rgb(212, 212, 212);"><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #808080;">      &lt;</span><span style="color: #569cd6;">div</span><span style="color: #808080;">&gt;</span><span style="color: #d4d4d4;">{  {this.data.sum.a}} + {  {this.data.sum.b}  } = {{this.data.sum.a + this.data.sum.b}}</span><span style="color: #808080;">&lt;/</span><span style="color: #569cd6;">div</span><span style="color: #808080;">&gt;</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #808080;">      &lt;</span><span style="color: #569cd6;">div</span><span style="color: #808080;">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">input </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">id</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"userName" </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">type</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"text" </span><span style="color: rgb(156, 220, 254);">title</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"{{this.data.form.userName}}" </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">value</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"{{this.data.form.userName}}"</span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(128, 128, 128);">/&gt;</span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">div</span><span style="color: rgb(128, 128, 128);">&gt;</span></div><div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">      &lt;</span><span style="color: #569cd6;">div</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;">               男：</span><span style="color: #808080;">&lt;</span><span style="color: #569cd6;">input  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">type</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"radio"  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">value</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"1"  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">name</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"sex"  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">checked</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"{{this.data.form.sex === 1 ? true : false}}"</span><span style="color: #d4d4d4;"></span><span style="color: #808080;">/&gt;</span></div><div><span style="color: #d4d4d4;">               女：</span><span style="color: #808080;">&lt;</span><span style="color: #569cd6;">input  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">type</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"radio"  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">value</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"0"  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">name</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"sex"  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">checked</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"{{this.data.form.sex === 0 ? true : false}}"</span><span style="color: #d4d4d4;"></span><span style="color: #808080;">/&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">       &lt;/</span><span style="color: #569cd6;">div</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #808080;">&lt;/div&gt;</span></div></div></div></div></div></div></pre></span></div><div id="k_22113318129U235O7QD2" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211331813GNQGYKMI6D">​</span></div><div id="k_2211414226GPEIW891K5" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211422275ZBQQY9PJ36">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2211422278CHN4CMXZ4U">​<img id="k_2211422279D2S9V8FAUY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAzUlEQVQ4T82TMQ4BYRCF3/yTbKOkcQSlAyhFKXQancoFZJP985I9hkLiFg4gUYiCVlTiAAqFZHdEouLHioKpZ76Z915G8GXJl/P4QwDJSp7nMzNbq+qQ5OmVzAcJJKMsy8Yi0jezqaoOSJ6fQYIefAKRJElGAGqBDRGApoiUX11yBaxEpP4mzq1zrkVyd9/3NEbvfc/MJgAOANppmm5CS4IA733nNnxU1S7JZWET4ziuqupcRErOuTbJxUcxAhCSDQD7kObCHhT9kd//wgV6K08Rzda8JwAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211422281K6YVLFNEBP">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211563987II9N4SORHI">准备JSON实体数据[ </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255);" id="k_22115639891QE96U89H4">formData</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211563990ZHEJKA6JUP"> ]</span></div><div id="k_2211345853D9PVSQG8WQ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 93px; width: 879px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2211345854PVLV1BHTY8"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="color: rgb(212, 212, 212);"><span style="color: #9cdcfe;">     var formData = </span><span style="color: #d4d4d4;">{</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">             form:</span><span style="color: #d4d4d4;"> {</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                      userName:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'kevin.huang'</span><span style="color: #d4d4d4;">,</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                      sex:</span><span style="color: #d4d4d4;"></span><span style="color: #b5cea8;">1</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;">               },</span></div><div style=""><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">               p1:</span><span style="color: rgb(212, 212, 212);"></span>"<span style="color: rgb(255, 0, 209);">这是一个mvvm表单</span>"<span style="color: rgb(212, 212, 212);">,</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">               sum:</span><span style="color: #d4d4d4;"> {</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                   a:</span><span style="color: #d4d4d4;"></span><span style="color: #b5cea8;">888</span><span style="color: #d4d4d4;">,</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                   b:</span><span style="color: #d4d4d4;"></span><span style="color: #b5cea8;">999</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;">              }</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;">     }；</span></div></div></pre></span></div><div id="k_2211333632GRTREFO1W1" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);"><br></span></div><div id="k_2211414517KFYO6OWMG9" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_22114227046F5439NFAB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2211422706S4ZW5VKINY">​<img id="k_2211422707I45DQ2ZHZT" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAzUlEQVQ4T82TMQ4BYRCF3/yTbKOkcQSlAyhFKXQancoFZJP985I9hkLiFg4gUYiCVlTiAAqFZHdEouLHioKpZ76Z915G8GXJl/P4QwDJSp7nMzNbq+qQ5OmVzAcJJKMsy8Yi0jezqaoOSJ6fQYIefAKRJElGAGqBDRGApoiUX11yBaxEpP4mzq1zrkVyd9/3NEbvfc/MJgAOANppmm5CS4IA733nNnxU1S7JZWET4ziuqupcRErOuTbJxUcxAhCSDQD7kObCHhT9kd//wgV6K08Rzda8JwAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2211501190UU7FMEWEME">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_221150097097T44YNPC4">&nbsp;创建Mvmm实例实现双向联动</span></div><div id="k_22115058604CRZ44KGD5" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 93px; width: 876px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211505861O8IGIE8L67"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;">           var </span><span style="color: #9cdcfe;">options</span><span style="color: #d4d4d4;"> = {</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                       el:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">"<span style="color: rgb(255, 0, 5); font-weight: bold;">myfrom</span>"</span><span style="color: #d4d4d4;">,  //  表单容器id</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                       data:</span><span style="color: #d4d4d4;"><span style="color: rgb(255, 0, 5); font-weight: bold;">formData</span></span><span style="color: rgb(212, 212, 212);">,  //  表单json数据</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #dcdcaa;">                       onChanged</span><span style="color: #9cdcfe;">:</span><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">function</span><span style="color: #d4d4d4;"> (</span><span style="color: #9cdcfe;">propObj</span><span style="color: #d4d4d4;">, </span><span style="color: #9cdcfe;">propName</span><span style="color: #d4d4d4;">, </span><span style="color: #9cdcfe;">newValue</span><span style="color: #d4d4d4;">, </span><span style="color: #9cdcfe;">oldValue</span><span style="color: #d4d4d4;">) {</span><span style="color: rgb(212, 212, 212);"></span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                              $message</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">html</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"&lt;p&gt;属性"</span><span style="color: #d4d4d4;"> + </span><span style="color: #9cdcfe;">propName</span><span style="color: #d4d4d4;"> + </span><span style="color: #ce9178;">"-新值:"</span><span style="color: #d4d4d4;"> + </span><span style="color: #9cdcfe;">newValue</span><span style="color: #d4d4d4;"> + </span><span style="color: #ce9178;">",旧值:"</span><span style="color: #d4d4d4;"> + </span><span style="color: #9cdcfe;">oldValue</span><span style="color: #d4d4d4;"> + </span><span style="color: #ce9178;">"&lt;/p&gt;"</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #4ec9b0;">                              console</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">log</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"当前JSON:"</span><span style="color: #d4d4d4;"> + </span><span style="color: #4ec9b0;">JSON</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">stringify</span><span style="color: #d4d4d4;">(</span><span style="color: #569cd6;">this</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">getJson</span><span style="color: #d4d4d4;">()));</span></div><div><span style="color: #d4d4d4;">                }</span></div><div><span style="color: #d4d4d4;">            };</span></div><div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #9cdcfe;">           var   mvm</span><span style="color: #d4d4d4;"> = </span><span style="color: #569cd6;">new</span><span style="color: #d4d4d4;"></span><span style="color: #4ec9b0;">$B</span><span style="color: #d4d4d4;">.</span><span style="color: #4ec9b0;">Mvvm</span><span style="color: #d4d4d4;">(</span><span style="color: #9cdcfe;">options</span><span style="color: #d4d4d4;">);   //创建mvvm实例</span></div></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #d4d4d4;">           var json = mvm.getJson() ;   //获取 可以用于提交的 json</span></div></div></pre></span></div><div id="k_2211505867FGV8QD82E1" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2211505868JFQCW72MMY">​</span></div><div id="k_22115059373K8BVF3NEE" class="_section_div_" style="line-height: 36.45px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 248, 252); padding-left: 0px; border-top: 1px dotted rgb(238, 0, 255); border-right: none; border-bottom: none; border-left: 1px dotted rgb(238, 0, 255); border-image: initial; margin-top: 12px; margin-bottom: 12px; text-indent: 36px;"><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(238, 0, 255); background-color: rgb(247, 248, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(238, 0, 255); vertical-align: baseline;" id="k_2212061685CE4XYNZM4Y">绑定Demo-2[&nbsp; </span><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(168, 168, 250); background-color: rgb(247, 248, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(238, 0, 255); vertical-align: baseline;" id="k_2212111820VILK6RYWUN">$B.bindForm()</span><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(168, 168, 250); background-color: rgb(247, 248, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(238, 0, 255); vertical-align: baseline;" id="k_2212111821HS4B1B7AB8"> 方式</span><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(238, 0, 255); background-color: rgb(247, 248, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(238, 0, 255); vertical-align: baseline;" id="k_2212061687Y9IMNWKL2R">&nbsp; ]</span></div><div id="k_2212045559NCRKYGLBD9" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 37px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212101550F8DYGSNP73">demo-1 比较繁琐，需要编写绑定表达式，为此框架封装了一个更便利的&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(140, 164, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212101551D2DHIZPSBY"> &nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(140, 164, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212100507DH7KXN717D">$B.bin</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(140, 164, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212101554KSPEMEHUX7">dForm( form, dataObj, onchangeFn )&nbsp; </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212101555BENNYBP65S">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22121008507NM22F5Z1I">API.</span></div><div id="k_2212045608RE2VQ7N98F" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 92px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212241375ZLMEDJOHHH">第一步：定义表单[</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 222, 36); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212242103BOPBFMR19D"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 222, 36); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212242105MLE4UO8LXD">myform</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 222, 36); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212242106Y826ZH9Z1B"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22122413787MP35D2LAU">]：</span></div><div id="k_2212124957CZ9RZ6F6WF" class="_section_div_" style="line-height: 36.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 92px; width: 873px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212124958XPHCOBYY2W"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">  &lt;</span><span style="color: #569cd6;">table </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">style</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"</span><span style="color: #ce9178;">margin-top:20px;"  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">id</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"<span style="color: rgb(15, 224, 0); font-weight: bold;">myform</span>"  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"form_table"</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">      &lt;</span><span style="color: #569cd6;">tr</span><span style="color: #808080;">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">td  </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">class</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"label"</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(212, 212, 212);">姓名：</span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">td</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">td</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">input</span><span style="color: rgb(255, 0, 5);"> id="userName"  </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">type</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"text"</span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(128, 128, 128);">/&gt;</span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">td</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">tr</span><span style="color: rgb(128, 128, 128);">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">      &lt;</span><span style="color: #569cd6;">tr</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">            &lt;</span><span style="color: #569cd6;">td  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"label"</span><span style="color: #808080;">&gt;</span><span style="color: #d4d4d4;">性别：</span><span style="color: #808080;">&lt;/</span><span style="color: #569cd6;">td</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">            &lt;</span><span style="color: #569cd6;">td</span><span style="color: #808080;">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">label   </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">class</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"k_radio_label k_radio_anim"</span><span style="color: rgb(128, 128, 128);">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">                            &lt;</span><span style="color: #569cd6;">input  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">type</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"radio"</span><span style="color: rgb(255, 0, 5);"> name="sex"   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">value</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"1"</span><span style="color: #d4d4d4;"></span><span style="color: #808080;">/&gt;&lt;</span><span style="color: #569cd6;">i  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"k_radio_i"</span><span style="color: #808080;">&gt;&lt;/</span><span style="color: #569cd6;">i</span><span style="color: #808080;">&gt;</span><span style="color: #d4d4d4;">男 </span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">label</span><span style="color: rgb(128, 128, 128);">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">                  &lt;</span><span style="color: #569cd6;">label   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"k_radio_label k_radio_anim"</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">                           &lt;</span><span style="color: #569cd6;">input   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">type</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"radio" </span><span style="color: #d4d4d4;"></span><span style="color: rgb(255, 0, 5);">name="sex"  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">value</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"0"</span><span style="color: #d4d4d4;"></span><span style="color: #808080;">/&gt;&lt;</span><span style="color: #569cd6;">i   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"k_radio_i"</span><span style="color: #808080;">&gt;&lt;/</span><span style="color: #569cd6;">i</span><span style="color: #808080;">&gt;</span><span style="color: #d4d4d4;">女 </span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">label</span><span style="color: rgb(128, 128, 128);">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">          &lt;/</span><span style="color: #569cd6;">td</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">     &lt;/</span><span style="color: #569cd6;">tr</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">     &lt;</span><span style="color: #569cd6;">tr</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">          &lt;</span><span style="color: #569cd6;">td   </span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"label"</span><span style="color: #808080;">&gt;</span><span style="color: #d4d4d4;">爱好：</span><span style="color: #808080;">&lt;/</span><span style="color: #569cd6;">td</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">          &lt;</span><span style="color: #569cd6;">td</span><span style="color: #808080;">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">label  </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">class</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"k_checkbox_label k_checkbox_anim"</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">input  </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">type</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"checkbox"</span><span style="color: rgb(255, 0, 5);"> name="hobby"  </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">value</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"1"</span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(128, 128, 128);">/&gt;</span></div><div><span style="color: rgb(128, 128, 128);">                               &lt;</span><span style="color: rgb(86, 156, 214);">i  </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">class</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"k_checkbox_i"</span><span style="color: rgb(128, 128, 128);">&gt;&lt;/</span><span style="color: rgb(86, 156, 214);">i</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(212, 212, 212);">跑步</span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">label</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(212, 212, 212);"></span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">             &lt;</span><span style="color: #569cd6;">label  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"k_checkbox_label k_checkbox_anim"</span><span style="color: #808080;">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">input</span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">type</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"checkbox"  </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(255, 0, 5);">name="hobby"</span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">value</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"3"</span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(128, 128, 128);">/&gt;</span></div><div><span style="color: rgb(128, 128, 128);">                              &lt;</span><span style="color: rgb(86, 156, 214);">i  </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">class</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"k_checkbox_i"</span><span style="color: rgb(128, 128, 128);">&gt;&lt;/</span><span style="color: rgb(86, 156, 214);">i</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(212, 212, 212);">编程</span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">label</span><span style="color: rgb(128, 128, 128);">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">         &lt;/</span><span style="color: #569cd6;">td</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">     &lt;/</span><span style="color: #569cd6;">tr</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">     &lt;</span><span style="color: #569cd6;">tr</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">            &lt;</span><span style="color: #569cd6;">td   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"label"</span><span style="color: #808080;">&gt;</span><span style="color: #d4d4d4;">户籍：</span><span style="color: #808080;">&lt;/</span><span style="color: #569cd6;">td</span><span style="color: #808080;">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">td</span><span style="color: rgb(128, 128, 128);">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">                 &lt;</span><span style="color: #569cd6;">select</span><span style="color: rgb(255, 0, 5);"> id="userid"</span><span style="color: #808080;">&gt; </span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">option </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">value</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"0"</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(212, 212, 212);">广西钦州</span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">option</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(128, 128, 128);">&lt;</span><span style="color: rgb(86, 156, 214);">option </span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(156, 220, 254);">value</span><span style="color: rgb(212, 212, 212);">=</span><span style="color: rgb(206, 145, 120);">"1"</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(212, 212, 212);">广西南宁</span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">option</span><span style="color: rgb(128, 128, 128);">&gt;</span><span style="color: rgb(212, 212, 212);"></span><span style="color: rgb(128, 128, 128);">&lt;/</span><span style="color: rgb(86, 156, 214);">select</span><span style="color: rgb(128, 128, 128);">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">            &lt;/</span><span style="color: #569cd6;">td</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">     &lt;/</span><span style="color: #569cd6;">tr</span><span style="color: #808080;">&gt;</span><span style="color: rgb(212, 212, 212);"></span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">  &lt;/</span><span style="color: #569cd6;">table</span><span style="color: #808080;">&gt;</span></div></div></pre></span></div><div id="k_2212110305VI3S5L9UCE" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 92px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_221211030618DBC23FUS">​</span></div><div id="k_2212244271UKLX8XQJDS" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 92px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212254610E7DC7YM649">第二步：定义json实体数据 [ </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 222, 36); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212254612HLO9JQKHPS">userData</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22122546126N8RVT8BT8"> ]</span></div><div id="k_2212125115UF3LRAEWJ7" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 92px; width: 871px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212125116MCJXDZCURK"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">  var   </span><span style="color: #d4d4d4;"></span><span style="color: rgb(15, 224, 0); font-weight: bold;">userData</span><span style="color: #d4d4d4;"> = {</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">         userName:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'kevin.huang'</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">         userPwd:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'000000'</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">         sex:</span><span style="color: #d4d4d4;"></span><span style="color: #b5cea8;">1</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">         hobby:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'1,3'</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">         userTech:</span><span style="color: #d4d4d4;"></span><span style="color: #b5cea8;">2</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">         userid:</span><span style="color: #d4d4d4;"></span><span style="color: #b5cea8;">1</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">         userAddr:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">''</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">         userDesp:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'介绍：这家伙是一个热爱编程工作的人！'</span></div><div><span style="color: #d4d4d4;">     };</span></div></div></pre></span></div><div id="k_2212244572I8C8JCAAIY" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 92px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22122445738NUD2YLIM4">​</span></div><div id="k_2212110510QBHHQARTLL" class="_section_div_" style="line-height: 41.6px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 92px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212110511PCGQY2RIKN">​第三步：调用​$B.bindForm()实现绑定双向绑定</span></div><div id="k_2212045600M1ZQ8LKDXV" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 92px; width: 869px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2212045601SXGD85FEKJ"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;">      var vm ; // 定义vm对象</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #569cd6;">      function   </span><span style="color: #d4d4d4;"></span><span style="color: #dcdcaa;">pageLoaded</span><span style="color: #d4d4d4;">() {</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">               vm</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">$B</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">bindForm</span><span style="color: #d4d4d4;">(</span><span style="color: rgb(255, 0, 5);">$("#myform"</span><span style="color: #d4d4d4;"><span style="color: rgb(255, 0, 5);">)</span>, </span><span style="color: rgb(15, 224, 0); font-weight: bold;">userData</span><span style="color: #d4d4d4;">, </span><span style="color: #569cd6;">function</span><span style="color: #d4d4d4;"> (</span><span style="color: rgb(0, 137, 255);">propObj, propName, newValue, oldValue</span><span style="color: #d4d4d4;">) {</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">               var   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">ms</span><span style="color: #d4d4d4;"> = </span><span style="color: #ce9178;">"onChanged propName="</span><span style="color: #d4d4d4;"> + </span><span style="color: #9cdcfe;">propName</span><span style="color: #d4d4d4;"> + </span><span style="color: #ce9178;">" newValue = "</span><span style="color: #d4d4d4;"> + </span><span style="color: #9cdcfe;">newValue</span><span style="color: #d4d4d4;"> + </span><span style="color: #ce9178;">"     ;   oldValue = "</span><span style="color: #d4d4d4;"> +   </span><span style="color: #9cdcfe;">oldValue</span><span style="color: #d4d4d4;">;</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: rgb(0, 137, 255);">               console.log("数据发送变化" + ms);</span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;">            });</span><span style="color: rgb(212, 212, 212);"></span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;">        }</span></div><font><br></font><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">       function   </span><span style="color: #d4d4d4;"></span><span style="color: #dcdcaa;">mySubmit</span><span style="color: #d4d4d4;">() { // 提交表单 </span></div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">              var     </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">formJson</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">vm</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">getJson</span><span style="color: #d4d4d4;">();</span></div><div style=""><span style="color: rgb(212, 212, 212);"></span><font>              console.log("执行提交 </font><span style="color: rgb(156, 220, 254);">formJson</span><span style="color: rgb(212, 212, 212);"></span>" ....... &gt;   );</div><div style="color: rgb(212, 212, 212);"><span style="color: #d4d4d4;">        }</span></div></div></pre></span></div><div id="k_22111634532NPHRWR6S7" class="_section_div_" style="line-height: 32.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 248, 252); padding-left: 0px; border-top: 1px dotted rgb(238, 0, 255); border-right: none; border-bottom: none; border-left: 1px dotted rgb(238, 0, 255); border-image: initial; margin-top: 25px; margin-bottom: 12px; text-indent: 36px;"><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(238, 0, 255); background-color: rgb(247, 248, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(238, 0, 255); vertical-align: baseline;" id="k_2211163454DZC1HHXENQ">API/绑定表达式说明​</span></div><div id="k_2211163570I1LRVOJY9H" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 98px; width: 684px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190322133833DZ69B7XIK6TH"><tbody><tr id="row_0"><td id="k_2213383336M7AN2Y3GVY" tabindex="0" row="0" col="0" w="580" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 582px; background-color: rgb(139, 161, 240); text-align: left; color: rgb(255, 255, 255);" colspan="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(139, 161, 240); color: rgb(255, 255, 255);" id="k_22133833379MQMYLO5BG"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(139, 161, 240); text-align: left;" id="k_2213383339JQREB3OSOJ">​绑定表达式&nbsp; [&nbsp; this.data ] 是必须的前缀，指向json实体对象</span></p></td></tr><tr id="row_1"><td id="k_2213383346ILGI14R9P9" tabindex="0" row="1" col="0" w="183" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 185px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213383348K2YBKZIHF3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22133833499WQI8TVB2E">​​普通字段绑定</span></p></td><td id="k_2213383352FZEUQBRHYL" tabindex="0" row="1" col="1" w="395" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 397px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213383353YOOHGCV29X"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213522604INP3LDECM1">value="{{</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2213522606ZFPOJH1MQK">this.data.</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22135226079JZNKKT3QO">form.userName}}"</span></p></td></tr><tr id="row_2"><td id="k_22133833551G15T8QU62" tabindex="0" row="2" col="0" w="183" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 185px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213383356X4J8GBFT1Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213383356FR1I7AEDUQ">​​radio单选框绑定</span></p></td><td id="k_2213383358WF3XEF9B8T" tabindex="0" row="2" col="1" w="395" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 397px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_22133833591FP7OMS1ZH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213523435ZM8BE68GSX">checked="{{</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_22135234361AAYVC2821">this.data.</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213523437FYDQRPS8MT">form.sex === 1 ? true : false}}"</span></p></td></tr><tr id="row_3"><td id="k_22133833616ON26MIL9W" tabindex="0" row="3" col="0" w="183" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 185px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213383361ATHOAEFFAC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213383362FN2MGQ6LDZ">​​下拉选择框绑定</span></p></td><td id="k_2213383363717CMZKFCJ" tabindex="0" row="3" col="1" w="395" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 397px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_22133833646UCAVIUTC9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213524266Z5ZXV662DJ">selected="{{</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2213524268PFLG5HTTSZ">this.data.</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213524269KQVNF368CS">form.userid === 1 ? true:false}}"</span></p></td></tr><tr id="row_4"><td id="k_2213383366OBQ3GESJFN" tabindex="0" row="4" col="0" w="183" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 185px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213383366SQG5G3BWFM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213383367WBTN1AN2WA">​​逻辑运算绑定</span></p></td><td id="k_22133833692SPHUPY9HL" tabindex="0" row="4" col="1" w="395" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 397px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_22133833704FBI5R6LFO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213525106ARRN7TZF2T">{{</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2213525107POHIXZPBNX">this.data.</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213525840MSF44JJLUL">p1 + </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2213525841GPLY85VZFR">this.data</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213525841QDCW5OKBD4">.p2}}</span></p></td></tr></tbody></table></div><div id="k_2211162639NCXN2I66SA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2211162640V5H3Q65OT1">​</span></div><div id="k_22111627508VM8C75ZD8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 97px; width: 592px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190322135420BPXDLIRHGBFF"><tbody><tr id="row_0"><td id="k_2213542074RJN2C6EAKP" tabindex="0" row="0" col="0" w="582" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 584px; background-color: rgb(139, 161, 240); color: rgb(255, 255, 255);" colspan="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(139, 161, 240); color: rgb(255, 255, 255);" id="k_22135420757PAY1M55CW"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(139, 161, 240);" id="k_2213542075XIMZKK3SDY">​实例API说明</span></p></td></tr><tr id="row_1"><td id="k_2213542081JFV7NC7COC" tabindex="0" row="1" col="0" w="182" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 184px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213542082MUVJR8MAO1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213542083CGGM8UY94F">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_221356240441X8DAVEHD">getJson()</span></p></td><td id="k_2213542084ENIZUHAHUX" tabindex="0" row="1" col="1" w="398" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 400px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_22135420851GT8YYOUBD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22135420853W17OCU23R">​获取表单json，常用于提交数据</span></p></td></tr><tr id="row_2"><td id="k_2213542087OGMW4V3UTX" tabindex="0" row="2" col="0" w="182" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 184px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213542088HJMCYHW6VU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213542088HTWHUM5DSQ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213573620F6OG33RBUC">registeWatcher(watcher)</span></p></td><td id="k_2213542090IYGDXCJVWW" tabindex="0" row="2" col="1" w="398" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 400px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213542090BS29NQESBC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213592617UVTDKD8HK8">注册观察者，用于</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213592618LTFA6YRUPY">自定义控件的双向绑定解析实现</span></p></td></tr><tr id="row_3"><td id="k_22135420923R1J8MTV6P" tabindex="0" row="3" col="0" w="182" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 184px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_22135420931OXSEK437X"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22135420944XSH8Z4LPF">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213582416NME3KZ5I3G">registeExpress(data, el)</span></p></td><td id="k_2213542095DTQJUOQYE2" tabindex="0" row="3" col="1" w="398" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 400px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213542096D7C4J5137J"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213542097HXNRTDXTNK">​注册表达式，用于​自定义控件的双向绑定解析实现</span></p></td></tr><tr id="row_4"><td id="k_2213584058O454P8CUNW" tabindex="0" row="4" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 184px; background-color: rgba(0, 0, 0, 0);" w="182" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213584058DKSI89VORZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213584059UJWTWK67WU">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213595374FUJEMV8H6E">compile(el)</span></p></td><td id="k_2213584062JK4OKRNATL" tabindex="0" row="4" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(155, 212, 245); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 400px; background-color: rgba(0, 0, 0, 0);" w="398" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2213584062U117YYMC25"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22135840637CS6MG7LSD">​编译某个标签，用于自定义控件的双向绑定解析实现</span></p></td></tr></tbody></table></div><div id="k_22133638425U76IRLG6U" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2213363843QT8JLI24J3">​</span></div><div id="k_2214005410EMVVOCB5HE" class="_section_div_" style="line-height: 32.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 248, 252); padding-left: 0px; border-top: 1px dotted rgb(238, 0, 255); border-right: none; border-bottom: none; border-left: 1px dotted rgb(238, 0, 255); border-image: initial; margin-top: 25px; margin-bottom: 12px; text-indent: 36px;"><span style="font-size: 18px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(238, 0, 255); background-color: rgb(247, 248, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(238, 0, 255); vertical-align: baseline;" id="k_2214005411E22TPFY9RD">自定义控件的双向绑定实现​</span></div><div id="k_2214005488KAGPFA8LZ6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214075242FZGOQZS4M6">mvvm</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214075243TTS3N3T8LW">已经内置了日常开发用到的表单控件支持，但你仍然可以利用mvvm的扩展接口，实现对自己自定义控件的支持实现。</span></div><div id="k_2214040672KTINM64G2D" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214040673JCGIJC8NBN">​下面以 绑定一个 combox自定义控件为例说明：</span></div><div id="k_2214043693VXEN1NLQVS" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214052788R3HBUDBIFV">​1、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214043693J1G9LKK2IF">​combox的html结构</span></div><div id="k_22140527023BDYZKWE6A" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214052910BJIFO762NC">​&nbsp; &nbsp; &nbsp; &nbsp; 声明 wathcher 函数名称，声明express 表达式处理。</span></div><div id="k_2214092991CQ6I7YXKUP" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214092992YO9C9OV758">&nbsp; &nbsp; &nbsp; &nbsp; comboxWathcer、express会通过​registeWatcher(watcher) 、​registeExpress(data, el)注册。</span></div><div id="k_2214074445N9HDSSM2FD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: 962px; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214074446CGOJOBGY59"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #d4d4d4;">&lt;div id="wrap"&gt;   </span></div><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #808080;">    &lt;</span><span style="color: #569cd6;">input </span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">id</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"combox1" </span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">type</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"text" </span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">value</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"" </span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">watcher</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"comboxWatcher" </span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">express</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"{{this.comboxExpress(this.data.userSeletions,el)}}"</span><span style="color: #d4d4d4;"> </span><span style="color: #808080;">/&gt;</span></div><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #808080;">&lt;/div&gt;</span></div></pre></span></div><div id="k_2214052921S11ESLE8RI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;"><br></span></div><div id="k_2214082484Q1HUVC4Q96" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214115374L4VB1VDT8S">​2、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214082485XXZAQ4HYML"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22140824862Y375VL8QI">​注册上述属性声明的处理函数</span></div><div id="k_2214115384SUF33FCZUI" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214121545AID52WW6RA">&nbsp; &nbsp; &nbsp;&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214154315RKL4PXHDJS">注册[watcher]属性配置的观察者处理函数，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214154317FY9B9W8VOK">将UI事件修改数据更新至绑定的实体Json上</span></div><div id="k_2214120239BGKA3UHCPO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 37px; width: 959px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214120240GBXXL1VPM8"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #6a9955;"> //从watcher参数中得到对应的html标签，得到标签对应的json对象及绑定的属性</span></div><div><span style="color: #9cdcfe;"> mvvm</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">registeWatcher</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"mycheckBoxWatcher"</span><span style="color: #d4d4d4;">, </span><span style="color: #569cd6;">function</span><span style="color: #d4d4d4;"> (</span><span style="color: #9cdcfe;">watcher</span><span style="color: #d4d4d4;">) {</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #4ec9b0;">console</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">log</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"mycheckBoxWatcher &gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;"</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">$el</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">watcher</span><span style="color: #d4d4d4;">.</span><span style="color: #9cdcfe;">$el</span><span style="color: #d4d4d4;">;</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">propObject</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">watcher</span><span style="color: #d4d4d4;">.</span><span style="color: #9cdcfe;">propObjectArray</span><span style="color: #d4d4d4;">[</span><span style="color: #b5cea8;">0</span><span style="color: #d4d4d4;">];</span><span style="color: #6a9955;">//绑定的json对象</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">propName</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">watcher</span><span style="color: #d4d4d4;">.</span><span style="color: #9cdcfe;">propPathArray</span><span style="color: #d4d4d4;">[</span><span style="color: #b5cea8;">0</span><span style="color: #d4d4d4;">];</span><span style="color: #6a9955;">//绑定的属性</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #9cdcfe;">$el</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">children</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"input"</span><span style="color: #d4d4d4;">).</span><span style="color: #dcdcaa;">on</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"change"</span><span style="color: #d4d4d4;">, </span><span style="color: #569cd6;">function</span><span style="color: #d4d4d4;"> () {</span><span style="color: #6a9955;">//绑定标签事件，标签事件输入后更新propObject</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">$input</span><span style="color: #d4d4d4;"> = </span><span style="color: #dcdcaa;">$</span><span style="color: #d4d4d4;">(</span><span style="color: #569cd6;">this</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">propVal</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">propObject</span><span style="color: #d4d4d4;">[</span><span style="color: #9cdcfe;">propName</span><span style="color: #d4d4d4;">];</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">valArray</span><span style="color: #d4d4d4;"> = [];</span></div><div><span style="color: #d4d4d4;">      </span><span style="color: #c586c0;">if</span><span style="color: #d4d4d4;"> (</span><span style="color: #9cdcfe;">propVal</span><span style="color: #d4d4d4;"> !== </span><span style="color: #ce9178;">""</span><span style="color: #d4d4d4;">) {</span></div><div><span style="color: #d4d4d4;">           </span><span style="color: #9cdcfe;">valArray</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">propVal</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">split</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">";"</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">      }</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">v</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">$input</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">val</span><span style="color: #d4d4d4;">();</span></div><div><span style="color: #d4d4d4;">     </span><span style="color: #c586c0;">if</span><span style="color: #d4d4d4;"> (</span><span style="color: #9cdcfe;">$input</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">prop</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"checked"</span><span style="color: #d4d4d4;">)) {</span></div><div><span style="color: #d4d4d4;">        </span><span style="color: #9cdcfe;">valArray</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">push</span><span style="color: #d4d4d4;">(</span><span style="color: #9cdcfe;">v</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">     } </span><span style="color: #c586c0;">else</span><span style="color: #d4d4d4;"> {</span></div><div><span style="color: #d4d4d4;">            </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">newArray</span><span style="color: #d4d4d4;"> = [];</span></div><div><span style="color: #d4d4d4;">            </span><span style="color: #c586c0;">for</span><span style="color: #d4d4d4;"> (</span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">i</span><span style="color: #d4d4d4;"> = </span><span style="color: #b5cea8;">0</span><span style="color: #d4d4d4;">, </span><span style="color: #9cdcfe;">len</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">valArray</span><span style="color: #d4d4d4;">.</span><span style="color: #9cdcfe;">length</span><span style="color: #d4d4d4;">; </span><span style="color: #9cdcfe;">i</span><span style="color: #d4d4d4;"> &lt; </span><span style="color: #9cdcfe;">len</span><span style="color: #d4d4d4;">; ++</span><span style="color: #9cdcfe;">i</span><span style="color: #d4d4d4;">) {</span></div><div><span style="color: #d4d4d4;">                </span><span style="color: #c586c0;">if</span><span style="color: #d4d4d4;"> (</span><span style="color: #9cdcfe;">v</span><span style="color: #d4d4d4;"> !== </span><span style="color: #9cdcfe;">valArray</span><span style="color: #d4d4d4;">[</span><span style="color: #9cdcfe;">i</span><span style="color: #d4d4d4;">]) {</span></div><div><span style="color: #d4d4d4;">                    </span><span style="color: #9cdcfe;">newArray</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">push</span><span style="color: #d4d4d4;">(</span><span style="color: #9cdcfe;">valArray</span><span style="color: #d4d4d4;">[</span><span style="color: #9cdcfe;">i</span><span style="color: #d4d4d4;">]);</span></div><div><span style="color: #d4d4d4;">                }</span></div><div><span style="color: #d4d4d4;">            }</span></div><div><span style="color: #d4d4d4;">            </span><span style="color: #9cdcfe;">valArray</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">newArray</span><span style="color: #d4d4d4;">;</span></div><div><span style="color: #d4d4d4;">      }</span></div><div><span style="color: #d4d4d4;">      </span><span style="color: #9cdcfe;">propObject</span><span style="color: #d4d4d4;">[</span><span style="color: #9cdcfe;">propName</span><span style="color: #d4d4d4;">] = </span><span style="color: #9cdcfe;">valArray</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">join</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">";"</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">    });</span></div><div><span style="color: #d4d4d4;"> });</span></div></div><div><br></div></pre></span></div><div id="k_2214120231L77L29TOIM" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214120232QHYE5USXYR">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214153891XTC2NESQC4">​&nbsp; &nbsp;</span><span id="k_2214153889FIEPCTRENA" style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); text-decoration: none solid rgb(102, 102, 102); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">注册[express]属性配置的表达式解析函数</span></div><div id="k_22141559486QKHKK2I7P" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); text-decoration: none solid rgb(102, 102, 102); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;" id="k_2214155949CGCLJ6GL1M">​&nbsp; &nbsp;&nbsp;</span></div><div id="k_221416530917OL227ZD8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 45px; width: 950px; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214165309TWK7Y5LIXK"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;">    </span><span style="color: #6a9955;">/**</span></div><div><span style="color: #6a9955;">    * 注册表达式解析处理，将解析的结果修饰到控件UI上</span></div><div><span style="color: #6a9955;">    * **/</span></div><div><span style="color: #d4d4d4;">    </span><span style="color: #9cdcfe;">mvvm</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">registeExpress</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"mycheckBoxExpress"</span><span style="color: #d4d4d4;">, </span><span style="color: #569cd6;">function</span><span style="color: #d4d4d4;"> (</span><span style="color: #9cdcfe;">data</span><span style="color: #d4d4d4;">, </span><span style="color: #9cdcfe;">el</span><span style="color: #d4d4d4;">) {</span></div><div><span style="color: #d4d4d4;">        </span><span style="color: #4ec9b0;">console</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">log</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"mycheckBoxExpress 根据data 解析el表达式 &gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;"</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">        </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">$el</span><span style="color: #d4d4d4;"> = </span><span style="color: #dcdcaa;">$</span><span style="color: #d4d4d4;">(</span><span style="color: #9cdcfe;">el</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">        </span><span style="color: #9cdcfe;">$el</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">find</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"input[type=checkbox]"</span><span style="color: #d4d4d4;">).</span><span style="color: #dcdcaa;">each</span><span style="color: #d4d4d4;">(</span><span style="color: #569cd6;">function</span><span style="color: #d4d4d4;"> () {</span></div><div><span style="color: #d4d4d4;">            </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">box</span><span style="color: #d4d4d4;"> = </span><span style="color: #dcdcaa;">$</span><span style="color: #d4d4d4;">(</span><span style="color: #569cd6;">this</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">            </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">v</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">box</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">val</span><span style="color: #d4d4d4;">();</span></div><div><span style="color: #d4d4d4;">            </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">isChecked</span><span style="color: #d4d4d4;"> = </span><span style="color: #569cd6;">false</span><span style="color: #d4d4d4;">;</span></div><div><span style="color: #d4d4d4;">            </span><span style="color: #c586c0;">if</span><span style="color: #d4d4d4;"> (</span><span style="color: #9cdcfe;">data</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">indexOf</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">";"</span><span style="color: #d4d4d4;">) &gt;= </span><span style="color: #b5cea8;">0</span><span style="color: #d4d4d4;">) {</span></div><div><span style="color: #d4d4d4;">                </span><span style="color: #569cd6;">var</span><span style="color: #d4d4d4;"> </span><span style="color: #9cdcfe;">patt1</span><span style="color: #d4d4d4;"> = </span><span style="color: #569cd6;">new</span><span style="color: #d4d4d4;"> </span><span style="color: #4ec9b0;">RegExp</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">";"</span><span style="color: #d4d4d4;"> + </span><span style="color: #9cdcfe;">v</span><span style="color: #d4d4d4;"> + </span><span style="color: #ce9178;">";|;"</span><span style="color: #d4d4d4;"> + </span><span style="color: #9cdcfe;">v</span><span style="color: #d4d4d4;"> + </span><span style="color: #ce9178;">"|"</span><span style="color: #d4d4d4;"> + </span><span style="color: #9cdcfe;">v</span><span style="color: #d4d4d4;"> + </span><span style="color: #ce9178;">";"</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">                </span><span style="color: #9cdcfe;">isChecked</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">patt1</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">test</span><span style="color: #d4d4d4;">(</span><span style="color: #9cdcfe;">data</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">            } </span><span style="color: #c586c0;">else</span><span style="color: #d4d4d4;"> {</span></div><div><span style="color: #d4d4d4;">                </span><span style="color: #9cdcfe;">isChecked</span><span style="color: #d4d4d4;"> = </span><span style="color: #9cdcfe;">data</span><span style="color: #d4d4d4;"> === </span><span style="color: #9cdcfe;">v</span><span style="color: #d4d4d4;">;</span></div><div><span style="color: #d4d4d4;">            }</span></div><div><span style="color: #d4d4d4;">            </span><span style="color: #c586c0;">if</span><span style="color: #d4d4d4;"> (</span><span style="color: #9cdcfe;">isChecked</span><span style="color: #d4d4d4;">) {</span></div><div><span style="color: #d4d4d4;">                </span><span style="color: #9cdcfe;">box</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">attr</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"checked"</span><span style="color: #d4d4d4;">, </span><span style="color: #ce9178;">"checked"</span><span style="color: #d4d4d4;">).</span><span style="color: #dcdcaa;">prop</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"checked"</span><span style="color: #d4d4d4;">, </span><span style="color: #569cd6;">true</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">            } </span><span style="color: #c586c0;">else</span><span style="color: #d4d4d4;"> {</span></div><div><span style="color: #d4d4d4;">                </span><span style="color: #9cdcfe;">box</span><span style="color: #d4d4d4;">.</span><span style="color: #dcdcaa;">removeAttr</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"checked"</span><span style="color: #d4d4d4;">).</span><span style="color: #dcdcaa;">prop</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"checked"</span><span style="color: #d4d4d4;">, </span><span style="color: #569cd6;">false</span><span style="color: #d4d4d4;">);</span></div><div><span style="color: #d4d4d4;">            }</span></div><div><span style="color: #d4d4d4;">        });</span></div><div><span style="color: #d4d4d4;">    });</span></div></div></pre></span></div><div id="k_2214120272GWZYAIG6KH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214120273KNUL1CTTPX">​</span></div><div id="k_2214115455UPUDO4MBOO" class="_section_div_" style="line-height: 36.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214175589OKMWGK9N5N">​3、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214115456F6Y8HR5MAN"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214115458SRZSKPD2ZV">​执行编译</span></div><div id="k_2214175519XN53Y7217S" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 47px; width: 948px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214175520LDZ88D6LGN"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background-color: rgb(30, 30, 30); line-height: 19px;"><span style="font-family: Consolas, &quot;Courier New&quot;, monospace; color: rgb(212, 212, 212);">        </span><span style="font-family: Consolas, &quot;Courier New&quot;, monospace; color: rgb(156, 220, 254);">mvvm</span><span style="font-family: Consolas, &quot;Courier New&quot;, monospace; color: rgb(212, 212, 212);">.</span><span style="font-family: Consolas, &quot;Courier New&quot;, monospace; color: rgb(220, 220, 170);">compile</span><span style="font-family: Consolas, &quot;Courier New&quot;, monospace; color: rgb(212, 212, 212);">( <span style="font-family: Consolas, &quot;Courier New&quot;, monospace; color: rgb(255, 0, 5); font-weight: bold;"> </span></span><font><span style="color: rgb(255, 0, 5); font-weight: bold;">$("#wrap")</span>  </font><span style="font-family: Consolas, &quot;Courier New&quot;, monospace; color: rgb(212, 212, 212);">);</span></div></pre></span></div><div id="k_22140054218BZP2ZJWDI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214005422M46OWW9SHH">​</span></div><div id="k_2214005468KNZRFOXXJ6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2214005468ADS3M14YXO">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_22121935786A4MGW87Z6" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 430px; height: 34px; position: absolute; top: 1204px; left: 527px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_2212193581H6H36873LU" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;" spellcheck="false"><div id="k_2212193582455S4QVFRM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 5, 5); background: none;" id="k_2212202808MIW2VQWNBS">注意：不用写绑定表达式，只需要定义 id/name 与 json字段名一致</span></div></div></div>

</div>