<div style="position:relative">


<div id="k_23113609603MDNOZY3WU" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(242, 240, 250); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(3, 222, 36); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(20, 77, 3); background-color: rgb(242, 240, 250); letter-spacing: 1px;" id="k_23113804476LU1JZVJOT">介绍</span></div><div id="k_2311361779QBLCZ4NLY6" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 761px; text-align: left; background-color: rgb(255, 250, 255); padding-left: 12px; margin-top: 20px; border-top: 1px solid rgb(201, 210, 247); border-right: 1px solid rgb(201, 210, 247); border-bottom: none; border-left: 1px solid rgb(201, 210, 247); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311463557TM3JMVLTGP">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2311463558C9UDOJVSZI">​<img id="k_23114635581FVEQ6VIHG" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAx0lEQVQ4T2NkIBMwkqmPgXyNyZYP5v5jYFzP81pu9zfRh9NA7PnH5bcQcgljsuUDQwbG/ysYGBiL/jIwvWJi+Lfi/3/GfEKawU5Ntnpkx8DwbxrjX8bC/8yMPxkY/s4GGTT3mMJWXDbD/Zhi/tD1P/O/KYx/mXL+M/9nY/jPMJnxH2P6nJPyu7FphmtMsHpkyszwbz7I1j/MjB9gbLwa0f3JzPB3CUGngjT9Z2RYAwoQJob/T2FsgoFDdnQQii+CoUqqAWQnOQBABmAPldUjAQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_23114635592RH3EDA1M3">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_231146355776ZKQIC14E">高性能tree，采用doc</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311450776ZR3WF4DZEA">umentfragment、setTimeout机制避免递归、避免频繁触发浏览器绘制。</span></div><div id="k_23114120035VER9CSL4H" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 761px; text-align: left; background-color: rgb(255, 250, 255); padding-left: 12px; border-top: none; border-right: 1px solid rgb(201, 210, 247); border-bottom: none; border-left: 1px solid rgb(201, 210, 247); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311465109M5QBQMDV1C">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2311465110F199FXYQFD">​<img id="k_2311465110VW21PPQQAV" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAx0lEQVQ4T2NkIBMwkqmPgXyNyZYP5v5jYFzP81pu9zfRh9NA7PnH5bcQcgljsuUDQwbG/ysYGBiL/jIwvWJi+Lfi/3/GfEKawU5Ntnpkx8DwbxrjX8bC/8yMPxkY/s4GGTT3mMJWXDbD/Zhi/tD1P/O/KYx/mXL+M/9nY/jPMJnxH2P6nJPyu7FphmtMsHpkyszwbz7I1j/MjB9gbLwa0f3JzPB3CUGngjT9Z2RYAwoQJob/T2FsgoFDdnQQii+CoUqqAWQnOQBABmAPldUjAQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311465110ISWMBQHQ5S">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311465109DRZ15B22QB">支持自定义节点图标，采用font-awesome矢量图标，可自定义图标颜色，支持多种风格配置。</span></div><div id="k_2311384238XFBVR26UPL" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 761px; text-align: left; background-color: rgb(255, 250, 255); padding-left: 12px; border-top: none; border-right: 1px solid rgb(201, 210, 247); border-bottom: none; border-left: 1px solid rgb(201, 210, 247); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311384240FU2M9EBWME">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311473056C786C5UWBQ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2311473057OQNH67WLJH">​<img id="k_23114730572V4V4OWWQC" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAx0lEQVQ4T2NkIBMwkqmPgXyNyZYP5v5jYFzP81pu9zfRh9NA7PnH5bcQcgljsuUDQwbG/ysYGBiL/jIwvWJi+Lfi/3/GfEKawU5Ntnpkx8DwbxrjX8bC/8yMPxkY/s4GGTT3mMJWXDbD/Zhi/tD1P/O/KYx/mXL+M/9nY/jPMJnxH2P6nJPyu7FphmtMsHpkyszwbz7I1j/MjB9gbLwa0f3JzPB3CUGngjT9Z2RYAwoQJob/T2FsgoFDdnQQii+CoUqqAWQnOQBABmAPldUjAQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311473057DAL4LL92NO">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311473056NWMC9YFOT9">具有丰富的选项配置，如自定义id、显示文本字段；复选框配置控制；额外的远程加载参数等。</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; background-color: rgb(255, 250, 255);">​</span></div><div id="k_23114321277O2HYNJOB3" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 761px; text-align: left; background-color: rgb(255, 250, 255); padding-left: 12px; border-top: none; border-right: 1px solid rgb(201, 210, 247); border-bottom: none; border-left: 1px solid rgb(201, 210, 247); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311432127L6NESLQGLW">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2311473807TA182O2CHT">​<img id="k_2311473807ZY99N8DLDR" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAx0lEQVQ4T2NkIBMwkqmPgXyNyZYP5v5jYFzP81pu9zfRh9NA7PnH5bcQcgljsuUDQwbG/ysYGBiL/jIwvWJi+Lfi/3/GfEKawU5Ntnpkx8DwbxrjX8bC/8yMPxkY/s4GGTT3mMJWXDbD/Zhi/tD1P/O/KYx/mXL+M/9nY/jPMJnxH2P6nJPyu7FphmtMsHpkyszwbz7I1j/MjB9gbLwa0f3JzPB3CUGngjT9Z2RYAwoQJob/T2FsgoFDdnQQii+CoUqqAWQnOQBABmAPldUjAQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_23114738086XKSG4OZ5K">​</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; background-color: rgb(255, 250, 255);">精细的事件监听控制，如菜单项创建完成回调、点击回调、复选回调、ajax异步加载完成回调等。</span></div><div id="k_23114403547BYO3PZ7ID" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: 761px; text-align: left; background-color: rgb(255, 250, 255); padding-left: 12px; border-top: none; border-right: 1px solid rgb(201, 210, 247); border-bottom: 1px solid rgb(201, 210, 247); border-left: 1px solid rgb(201, 210, 247); border-image: initial;"><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; background-color: rgb(255, 250, 255);" id="k_2311440354AUI9L8P7JQ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311440312AVPQZKCREV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2311474785FC1F1F7TP1">​<img id="k_2311474786SLOJ4GLGIU" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAx0lEQVQ4T2NkIBMwkqmPgXyNyZYP5v5jYFzP81pu9zfRh9NA7PnH5bcQcgljsuUDQwbG/ysYGBiL/jIwvWJi+Lfi/3/GfEKawU5Ntnpkx8DwbxrjX8bC/8yMPxkY/s4GGTT3mMJWXDbD/Zhi/tD1P/O/KYx/mXL+M/9nY/jPMJnxH2P6nJPyu7FphmtMsHpkyszwbz7I1j/MjB9gbLwa0f3JzPB3CUGngjT9Z2RYAwoQJob/T2FsgoFDdnQQii+CoUqqAWQnOQBABmAPldUjAQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255);" id="k_2311474786NPPEDFABHZ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2311450778MVZ6HTEXNM">配合toolbar组件，树节点</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 250, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2311450778QR5B1MFOWY">支持工具栏按钮定义。</span></div><div id="k_231136184976TDAQKXZG" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23113618494HJLV2VOGD">​</span></div><div id="k_2311361926BK9XC8XDWR" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(242, 240, 250); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(3, 222, 36); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(20, 77, 3); background-color: rgb(242, 240, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(20, 77, 3); vertical-align: baseline; letter-spacing: 1px;">API及构造参数说明​</span></div><div id="k_2311362193UX7XLM3ZRP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311362193YGVXC688K8">​</span></div><div id="k_2315303195JNZDP273YY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 45px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190323153120UVKOXU31QI38"><tbody><tr id="row_0"><td id="k_23153120941QHQVLWZWV" tabindex="0" row="0" col="0" w="1290" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1292px; background-color: rgb(190, 206, 250); color: rgb(255, 255, 255);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(190, 206, 250); color: rgb(255, 255, 255);" id="k_2315312095U4WYMA98O6"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(190, 206, 250);" id="k_2315312095IBXGXWHM2W">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(108, 117, 125); background-color: rgb(190, 206, 250);" id="k_23153308771GAZ46E5DJ">var tree = new $B.Tree($("#tree1"), opts);&nbsp; &nbsp;opts参数说明</span></p></td></tr><tr id="row_1"><td id="k_2315312003K4UZSJ7W6B" tabindex="0" row="1" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgb(247, 244, 248); text-align: center;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 244, 248);" id="k_2315312004O2QP1NWXO4"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 244, 248); text-align: center;" id="k_2315312004PLBH17TCL5">参数名​</span></p></td><td id="k_2315312006MFXP3DI9B2" tabindex="0" row="1" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgb(247, 244, 248); text-align: center;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 244, 248);" id="k_2315312007XILTBSQL42"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 244, 248); text-align: center;" id="k_231531200763CWJY7P8F">参数值​</span></p></td><td id="k_23153120091N6MG2KI6B" tabindex="0" row="1" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgb(247, 244, 248); text-align: center;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 244, 248);" id="k_2315312010CTHRRM4Q9F"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 244, 248); text-align: center;" id="k_2315312010IHY9JMIRQF">说明​</span></p></td></tr><tr id="row_2"><td id="k_2315312012XTYNYDO9VR" tabindex="0" row="2" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_231531201325T9R6IY1H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312013D6OZQWFVJV">​​data</span></p></td><td id="k_2315312015W96KSAUY2Y" tabindex="0" row="2" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23153120155GR75S2RNY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315363148SGM2SUJOUJ">树形数组数据集合</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315363150WS1TCDNJIR">，如[ { id:1,text:'字段', data:{} ,.... }]</span></p></td><td id="k_2315312017QS4D2IGG7S" tabindex="0" row="2" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312018U6Z7GR4TAR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315391379KLONHLVW6I">每一个节点数据为 { 'id' : 1 , text : '节点' ,data : {} ,</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(204, 204, 204); background-color: rgb(255, 255, 255);" id="k_23153913811P2SEEW6VU"> children: [&nbsp; ]</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315391383GLKUNPY34W">&nbsp; }</span></p></td></tr><tr id="row_3"><td id="k_2315312020H9673D2I5F" tabindex="0" row="3" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312021S6Y3LWFL74"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312021SSYOI22GSC">​​url</span></p></td><td id="k_2315312024KZT53X9G8V" tabindex="0" row="3" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_231531202468BIRQB8QA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312025H5YZ24ER48">​远程请求地址</span></p></td><td id="k_2315312026ZJ93RAYVQH" tabindex="0" row="3" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23153120284NHYXVTQZX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315410481L3POD2BGLD">返回数据格式要符合 </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(255, 255, 255);" id="k_2315410482KGFSBDGQ3T">[ data ]</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315410483A4SFLNWCB4"> 属性要求的格式</span></p></td></tr><tr id="row_4"><td id="k_2315312030HHLKN6D9NC" tabindex="0" row="4" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312031Q5P6H38ZBC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312031WT32T8278E">​​params</span></p></td><td id="k_2315312032K9FNH924E5" tabindex="0" row="4" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312033WZOKFE5VWZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312034LPU5TA5KON">​{p1 : 1 ,p2 : 2}</span></p></td><td id="k_2315312035NRIN745NCX" tabindex="0" row="4" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312036ZE2P8E6DBQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312036H3176987US">​远程请求额外的参数</span></p></td></tr><tr id="row_5"><td id="k_2315312038TDG4W5HMFE" tabindex="0" row="5" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312039TI9LNJ2QB1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312040VXVHYTK9E4">​​idField</span></p></td><td id="k_2315312041QPYBJN1VKH" tabindex="0" row="5" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312042WJ6ST2RGGA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312042W9NTBTY52C">​默认值：id</span></p></td><td id="k_2315312044G4SBG48QJX" tabindex="0" row="5" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23153120446531JUVSXL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312045LBWFAOGFWG">​id字段名</span></p></td></tr><tr id="row_6"><td id="k_2315312049TZYNTZZ14R" tabindex="0" row="6" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312050V3QQGKNFAN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312051EB6TZ63G49">​​textField</span></p></td><td id="k_2315312053MFQF6KIITD" tabindex="0" row="6" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312053G61OTTIQ4G"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312054C16T5J7DAU">​默认值：text</span></p></td><td id="k_2315312056J7FL39MKEM" tabindex="0" row="6" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312057CWRR6TKGT7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312058PGAPU5AOMM">​节点显示的字段名</span></p></td></tr><tr id="row_7"><td id="k_2315312060RKWPKVYGZE" tabindex="0" row="7" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23153120613I8BZ5FKZ6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312061NLXQL2WP5X">​​showLine</span></p></td><td id="k_2315312063NFM7HB77LZ" tabindex="0" row="7" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312063XTEHZG4W35"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23153120649H7B88MHQS">​默认值：true</span></p></td><td id="k_2315312066PEJCBYS497" tabindex="0" row="7" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312066GOFCX7VI5K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312067QY7QQMN4YK">​是否显示节点线</span></p></td></tr><tr id="row_8"><td id="k_2315312068UI7IFW9HFN" tabindex="0" row="8" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312069OQDHETIARF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312070U52ARIEQY4">​extParamFiled</span></p></td><td id="k_2315312071X1HKISXDUH" tabindex="0" row="8" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312072RUPPO3VM3K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312072HGKNKFWLMG">​默认值：[]</span></p></td><td id="k_2315312073MKQ9FJQ5WA" tabindex="0" row="8" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312074WPV1NG4UOE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312075QWCSSSY5P3">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_231552107215QKDY4HN6">​远程加载时候，需要传递的其他字段参数，如 [ 'field1' , 'field2' ]</span></p></td></tr><tr id="row_9"><td id="k_2315312076K4UBPOVVKW" tabindex="0" row="9" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312076IA9UCY517O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312077489SOWL3CC">​​canClickParent</span></p></td><td id="k_23153120782BMOHMCLJL" tabindex="0" row="9" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312079RN5VMRBXVC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23153120804XNIAEPH43">​默认值：true</span></p></td><td id="k_23153120814FQPP5NXPV" tabindex="0" row="9" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312081MK8THQ7KMW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312082KOZO5ZOKMD">​​父节点是否可以进行点击/复选操作</span></p></td></tr><tr id="row_10"><td id="k_2315312083NM633IBK2L" tabindex="0" row="10" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312084UD6N83YZ49"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312084UZX76JI8PL">​​nodeParentIcon</span></p></td><td id="k_23153120861HW6E4HRQB" tabindex="0" row="10" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23153120863TA96K9DRX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312087BTCVLJ519O">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315554088W6Y1KBA59X">​默认值：k_tree_fold_closed(22 * 22)</span></p></td><td id="k_2315312088U2XL1WN5S5" tabindex="0" row="10" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23153120895DKBN9GPZB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23153120898ADYZTZUS7">​​父节点图标关闭状态的图标样式</span></p></td></tr><tr id="row_11"><td id="k_23153120916GA8O4SU9T" tabindex="0" row="11" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312091AZT4U6IE7N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312092AXQ45KZXDH">​​nodeParentOpenIcon</span></p></td><td id="k_23153120931F81J1PCO5" tabindex="0" row="11" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312094XSUXOPELK3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312094QGBX5YTC47">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23155634449E4TXZ3B73">​默认值：k_tree_fold_open</span></p></td><td id="k_2315312096DPHXWXSSUQ" tabindex="0" row="11" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312096STXAH8W593"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312097DUI79J5DVE">​​父节点打开状态图标样式</span></p></td></tr><tr id="row_12"><td id="k_2315312098RL4764LG9L" tabindex="0" row="12" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23153120993OR5EM7OLF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23153120005M3JUSPKLV">​​leafNodeIcon</span></p></td><td id="k_23153120013ITH3RCXXW" tabindex="0" row="12" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312001THFA49DV6I"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23153120024NXJP7Q5S3">​​默认值：k_tree_file_icon</span></p></td><td id="k_2315312003FDWDTQXUBG" tabindex="0" row="12" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312004QJNB5J6I2Z"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23153120057W157DSR7S">​​子节点图标</span></p></td></tr><tr id="row_13"><td id="k_2315312007QFWUVX8LYI" tabindex="0" row="13" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312008SII5MEMZVG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312009RGPCCLEY7M">​​chkEmptyIcon</span></p></td><td id="k_2315312010I32L74Q5GR" tabindex="0" row="13" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312011PY5A5XMQZO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_231531201284YTY1V5IW">​​默认值：k_tree_check_empty</span></p></td><td id="k_2315312013TZH7IYW9MV" tabindex="0" row="13" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312014MPRT4RDHXI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312014WX9W9X5S18">​​复选框空选状态图标样式</span></p></td></tr><tr id="row_14"><td id="k_2315312016LK7UQAAUWT" tabindex="0" row="14" col="0" w="170" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23153120175D1I2HNK5L"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23153120172CIMGM9OHL">​​chkAllIcon</span></p></td><td id="k_23153120202PDMLU7BMU" tabindex="0" row="14" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312020VROZUF6RBI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312021AJBSNN3KYO">​​默认值：k_tree_check_all</span></p></td><td id="k_2315312024LZ6N6ACYJT" tabindex="0" row="14" col="2" w="706" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315312025CK8E2ZQAHG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315312025J5ECPT8IUT">​​复选框选择状态图标样式</span></p></td></tr><tr id="row_15"><td id="k_23155923567B1MAEEGPO" tabindex="0" row="15" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592358T3IJ79DSQP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315592358SSLTKI6K6D">​​fontIconColor</span></p></td><td id="k_23155923617YWYVVKNW3" tabindex="0" row="15" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592361RWTJ4O7D71"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315592362YFFYPNSDGX">​​默认值：undefined</span></p></td><td id="k_2315592366HU3E7KNTK9" tabindex="0" row="15" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592367HCGRK3ZZ6N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23155923677BF9TJYTA2">​​fontawesome图标的颜色</span></p></td></tr><tr id="row_16"><td id="k_23155924864BA5M7ROKS" tabindex="0" row="16" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592487ZGTSYLL4VB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23155924883KWLM8LWBM">​​chkSomeIcon</span></p></td><td id="k_2315592493IY98LOL1LA" tabindex="0" row="16" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23155924941KTQZ5YD6I"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315592495A957HC8RZG">​​默认值：k_tree_check_some&nbsp;</span></p></td><td id="k_2315592498SH8XAHZAN7" tabindex="0" row="16" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592499O582HRW33C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315592400EQI1XB4AOL">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316004742BWMWM1XN3S">复选框部分选择状态样式（父节点）</span></p></td></tr><tr id="row_17"><td id="k_23155926071EHDMX11D6" tabindex="0" row="17" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592608FWPNHPW48U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315592608TFRNYRCFUG">​​clickCheck</span></p></td><td id="k_2315592610P2NDX9HIZT" tabindex="0" row="17" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592611GOWGKJWZ63"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316051411UXDDHL4TLH">默认值：false</span></p></td><td id="k_2315592615ANM6QMBI9V" tabindex="0" row="17" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592616RDWW19GGWS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23155926177K2M7G4DZN">​​是否点击复选（不用点击复选框图标）</span></p></td></tr><tr id="row_18"><td id="k_2315592171H32WPOIFB7" tabindex="0" row="18" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592172PZGMDBKLF7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315592173RHMVZY5MVG">​​plainStyle</span></p></td><td id="k_2315592176RK8V49ZSJM" tabindex="0" row="18" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23155921777RDGXJCS7S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23155921784VU7I4T4ZB">​​默认值：false</span></p></td><td id="k_2315592181WRKXIWPCOK" tabindex="0" row="18" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2315592182QXB2L3NQYH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315592183OVCONRQQSV">​​是否为简单无图标样式</span></p></td></tr><tr id="row_19"><td id="k_2316051191NL12RH1SVJ" tabindex="0" row="19" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316051192WQJ8S848JG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316051192WSTF9SN78H">​​onlyNodeData</span></p></td><td id="k_2316051195GGHXK3237K" tabindex="0" row="19" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316051196AM2X8VQTUJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23160511973L7JEUESSS">​​默认值：false</span></p></td><td id="k_23160511986YJ4D74M4Q" tabindex="0" row="19" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316051199NNGJS1FVDM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316051100PY6MDI6VRT">​​回调api中的参数是否只需要当前节点的数据（不带children）</span></p></td></tr><tr id="row_20"><td id="k_23160749332Z4POLSYT6" tabindex="0" row="20" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316074934RL7ABV2UE2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316074934YEYNZGZCXB">​​tree2list</span></p></td><td id="k_2316074937FFBYCNG995" tabindex="0" row="20" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23160749387APM7354F7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316074939X3JQKXQ28R">​​默认值：true</span></p></td><td id="k_2316074942T6LXQJDQ8F" tabindex="0" row="20" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316074942PYZGBX3R3F"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316074943ZVSXI4SFXH">​​回调api中的参数是否转为列表类型</span></p></td></tr><tr id="row_21"><td id="k_2316075081UTH7USDUJX" tabindex="0" row="21" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316075082OMMCOWJ6ZB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316075083NTAWJA7SSU">​​checkbox</span></p></td><td id="k_2316075085W28DWUKO1Y" tabindex="0" row="21" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23160750866DXP7VQKAI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23160750863GLD7FNBXG">​​默认值：false</span></p></td><td id="k_23160750886ROBBT5OI9" tabindex="0" row="21" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316075089L8L1RIUTUC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316085329HNWAXVTJKE">是否需要复选框</span></p></td></tr><tr id="row_22"><td id="k_2316074735X55W15JGJE" tabindex="0" row="22" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_231607473671H86FAUUO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316074737UZ4LH4X63V">​​disChecked</span></p></td><td id="k_23160747408RNPDXU28F" tabindex="0" row="22" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316074741Z1QDEDC45Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316074742EZ6OQOQGFJ">​​默认值：false</span></p></td><td id="k_2316074744LHL5DS4CTF" tabindex="0" row="22" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_231607474525TDWI3LTB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_231607474661VNUXQG6U">​​是否禁用复选框</span></p></td></tr><tr id="row_23"><td id="k_2316051061ZYVBLKMDN4" tabindex="0" row="23" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23160510621NWLFWRRUH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316051062QIA45S2UVT">​​clickItemCls</span></p></td><td id="k_2316051064NWQ5PA6OAK" tabindex="0" row="23" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23160510655EJACB497O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316051065VAT4LGHJL8">​​默认值：undefined</span></p></td><td id="k_231605106721FZG67LXS" tabindex="0" row="23" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316051068EKQBYF6K3Q"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23160510686JJMAMT1BA">​​点击行的样式</span></p></td></tr><tr id="row_24"><td id="k_2316094930YYSIBRZPQ5" tabindex="0" row="24" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316094931D4J4I96VK1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316094932FTVLQ3YJXS">​​toolbar</span></p></td><td id="k_23160949359LXRLPM4WS" tabindex="0" row="24" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23160949364DFESXWIHV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316094937P2FJZNF3RG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316105557OHF3AEXRIY">默认值：</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316105558EXT188TYML">false</span></p></td><td id="k_23160949399GERA5P4TM" tabindex="0" row="24" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23160949396L4OKMJJL7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316094940RXOVVIAPAS">​​是否需要工具栏，如需要则数据节点中要提供toolbar的JSON(请参考工具栏JSON)</span></p></td></tr><tr id="row_25"><td id="k_2316094853T9QSOY7EFL" tabindex="0" row="25" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316094854C4TEOTRW2S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316094855P8YUNI6E24">​​methodsObject</span></p></td><td id="k_2316094857LJUBJCZJO3" tabindex="0" row="25" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316094857HXF9QYTA5G"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161251521PSY784Z6M">默认值："</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161251543NYDZ64P54">methodsObject</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316125154WT54UJUTTT">"</span></p></td><td id="k_2316094860OBOS4FE48X" tabindex="0" row="25" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23160948617SILLFT612"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161255534BGCLWJZ3R">工具栏的按钮事件集合名&nbsp; : window["methodsObject</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161527721CAC1WUYWS">"] = {addFn:function(){}&nbsp;&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316152774W5VGF4SRL5">}&nbsp;</span></p></td></tr><tr id="row_26"><td id="k_2316162881J5VU4BX2K2" tabindex="0" row="26" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="103" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316162882UKJESLOVAF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161628821PX3AJUU5J">​​onCheck</span></p></td><td id="k_2316162884OU93LMZUM1" tabindex="0" row="26" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="103" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316162885XZOVDJ27TN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316162885CHZ3KNDXQO">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316163825IKKFQQ8163">​onCheck = function&nbsp;​(data, params, checked){}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316173593ZMSYUQMX9C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316173594UEGV8VAXSX">​​​​data：被称作的数据&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316174008R89OLBWSH1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316174008G9X2U2L98B">​params：节点参数&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316174269AEWAMQIA7P"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316174270FY8BJAPTHN">checked：true/false</span></p></td><td id="k_2316162889KQHR56KCFO" tabindex="0" row="26" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="103" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316162890XF8MRCDQ4B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316173270HWJZXNMTNY">​​复选操作回调</span></p></td></tr><tr id="row_27"><td id="k_2316163079IHQOTCAGQU" tabindex="0" row="27" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316163080F8SY8217DK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316163081PSM899IDS5">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316180499NHQYKRGUY5">​onTreeCreated</span></p></td><td id="k_2316163083WUCP5EIGYR" tabindex="0" row="27" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316163084NPO1Y8M8DT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161630856CX1FZI8XI">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161808149YHOMI4DP3">​onTreeCreated = function(){}</span></p></td><td id="k_2316163087PRAX2UUFU9" tabindex="0" row="27" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316163087SG1VPIZPR9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161630887ELVJL1Y7X">​​非递归，timeout机制，需要通过这个回调确认树渲染完成</span></p></td></tr><tr id="row_28"><td id="k_2316163196QUKF9CICWH" tabindex="0" row="28" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="103" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316163197J539CXLXXF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316163197NN6QBSPQAD">​​onItemCreated</span></p></td><td id="k_2316163199PYIZQYGP39" tabindex="0" row="28" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="103" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316163100JZLEI35Y56"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161842369AJDBH9EL3">onItemCreated = function&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316184237FYOLD2JVUS">(data, deep, params){}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316185278V4QMK9U4R8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316185279U2WRJ1HHMC">​​​data：数据&nbsp;​</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316190219KXYSFKPOVY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316190220OYCUOMNQ11">deep：深度&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316190482AFNUWRTYYL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161904826956FFEAHT">params:参数</span></p></td><td id="k_2316163103QL2ONGLHRJ" tabindex="0" row="28" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="103" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316163104VJWQ2FYAEL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316163104A7L2IMBIGY">​​树每一项创建完成回调，onItemCreated上下文为li标签的jquery对象</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_23161922746NWP4NGCGK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316192275BCYRLXNHLP">​可用于根据数据 渲染 节点 ui</span></p></td></tr><tr id="row_29"><td id="k_2316163225N92Y5LDSRO" tabindex="0" row="29" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="78" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316163226S4LNCKXK6D"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316163227Q37GZJYMJ8">​​onClick</span></p></td><td id="k_2316163228PG5A4ZECEX" tabindex="0" row="29" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="78" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316163229XOVUNF2ABM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316163230IYEQEBR6QI">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23162024337NEHCJF22C">onClick</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316202434RL1KN1CVAF"> = function (data, params)</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_23162001631CT7YY9TZJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316200164CYBWVHW5NW">​​​data：数据&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316200452QRTZ999Q79"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23162004533T7JPD96WZ">params：参数</span></p></td><td id="k_2316163232KK7546WJFJ" tabindex="0" row="29" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="78" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316163233XGWDZV2RG7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161632332RQYWJK396">​​点击事件回调，onClick上下文为节点的[div.k_tree_item_wrap]标签的jquery对象</span></p></td></tr><tr id="row_30"><td id="k_2316162637FO3P2WPO9N" tabindex="0" row="30" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="53" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316162638QPBHI2BG7U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23161626394MGGUOXQHD">​onload</span></p></td><td id="k_2316162640LZD7WKKQS5" tabindex="0" row="30" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="53" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316162641AO2M3KBRM6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316162642X8F96W8OYV">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23162233071OKAPYDRES">​onloaded = function (data)</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316224386DIHNY2BK1J"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316224386W8MPW51K3X">​data：远程返回数据</span></p></td><td id="k_2316162643ZQ6FTODYPA" tabindex="0" row="30" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="53" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316162644C852YPZKEL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316162645ZHF5VG6GY9">​​请求成功回调</span></p></td></tr><tr id="row_31"><td id="k_2316264576MIJQCGBN9G" tabindex="0" row="31" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="53" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316264576Z2IWKDK7D8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316264577DQP1HSMR8K">​​onOperated</span></p></td><td id="k_2316264579NNKJ7UQ6XH" tabindex="0" row="31" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="53" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23162645804DE68JMYNF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316264580RENPNROOVF">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316272977GA8UJQIY4K">onOperated</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316272978VF58L1B976"> = function(data)</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316271586BGF3R53OMX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316271587C5I56CL1NK">​data：节点数据</span></p></td><td id="k_2316264582TBHJUFPYEI" tabindex="0" row="31" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="53" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316264583UQQVG8XWAO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316264584HRQN4FQWVN">​​点击任何工具栏按钮都会触发的事件</span></p></td></tr><tr id="row_32"><td id="k_2316310503VL344KE45S" tabindex="0" row="32" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 172px; background-color: rgba(0, 0, 0, 0);" w="170" h="53" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23163105043P3FL43NND"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316310505NIW78PC4E1">​​onToggle</span></p></td><td id="k_2316310508S41QXV8N5W" tabindex="0" row="32" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 412px; background-color: rgba(0, 0, 0, 0);" w="410" h="53" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316310509T4QYJSPV6G"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316315936EE4X2H9LPT">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316361132Y9SIO8SYYS">onToggle</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316361133ZJR3ZK4Y7G"> = function(flag)</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316364976FVF4LTBIMA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316364977G3FIVQOVPS">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316365903EDDCWPZITI">​flag： show/hide</span></p></td><td id="k_2316310512W585TMV95J" tabindex="0" row="32" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 708px; background-color: rgba(0, 0, 0, 0);" w="706" h="53" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316310512OTN7NAZZK9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23163105138QLEWJU91E">​​展开、收起回调</span></p></td></tr></tbody></table></div><div id="k_2315303144YYF6GKRNE9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23153031455H9SBWES2M">​</span></div><div id="k_2316403605DXOBZ4FQ1P" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 43px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190323164042BOK5E4U7WY2U"><tbody><tr id="row_0"><td id="k_2316404226C9J6QRDFMQ" tabindex="0" row="0" col="0" w="1292" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1294px; background-color: rgb(190, 206, 250);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(190, 206, 250);" id="k_231640422751WG6KSZMM"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(190, 206, 250);" id="k_2316404228CF9T5SB2RD">​实例API</span></p></td></tr><tr id="row_1"><td id="k_2316404235N1EKYW54BX" tabindex="0" row="1" col="0" w="171" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 173px; background-color: rgb(255, 255, 255);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255);" id="k_2316404236A22A74ZLR7"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316404237UJILATFQPO">​​getClickItem()</span></p></td><td id="k_2316404238IR4E1TUFWF" tabindex="0" row="1" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgb(255, 255, 255);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255);" id="k_2316404239F21JU8KHBM"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316404240GLEG9KBONC">​</span></p></td><td id="k_231640424236TH7WZGEE" tabindex="0" row="1" col="2" w="707" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 709px; background-color: rgb(255, 255, 255);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255);" id="k_2316404243E11R3KA1BJ"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316404243MW1XHUICAA">​​获取当前点击的行，返回值：当前项数据</span></p></td></tr><tr id="row_2"><td id="k_2316404246V5WEN3ONXG" tabindex="0" row="2" col="0" w="171" h="78" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 173px; background-color: rgb(255, 255, 255);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255);" id="k_2316404246KCZ6U1FFM2"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23164042472V63ZSEODG">​​getCheckedData(args)</span></p></td><td id="k_2316404249NMGTNKL4PU" tabindex="0" row="2" col="1" w="410" h="78" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 412px; background-color: rgb(255, 255, 255);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255);" id="k_2316404249TQ342UEHWC"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316404250WAMLPWG131">​​​args={onlyId: false, onlyChild: false}&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255); padding-left: 0px;" id="k_2316424900M2AJCEQ3HL"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316424901PC4PHUK8J8">​onlyId:是否只获取ID字段&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255); padding-left: 0px;" id="k_2316425386LDEVJSJEDW"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23164253877IGIRT1CFR">onlyChild：是否只获取子节点数据</span></p></td><td id="k_23164042517E9IWJGRPR" tabindex="0" row="2" col="2" w="707" h="78" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 709px; background-color: rgb(255, 255, 255);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255);" id="k_2316404251H6SGLVHKIP"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316404252XFPO1F4131">​​获取当前所有复选的内容：返回值：数组/树形json(取决于tree2list参数)</span></p></td></tr><tr id="row_3"><td id="k_2316404253WNHWURMU83" tabindex="0" row="3" col="0" w="171" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 173px; background-color: rgb(255, 255, 255);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255);" id="k_23164042547DM3N8CBSK"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316404254TWEJ19CD9Y">​​reset()</span></p></td><td id="k_2316404255CJJ7RTS8R7" tabindex="0" row="3" col="1" w="410" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 412px; background-color: rgb(255, 255, 255);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255);" id="k_23164042568VADFIOYSB"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316404256PX17I2ZCG6">​</span></p></td><td id="k_2316404258WHPQ4LNE3K" tabindex="0" row="3" col="2" w="707" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 709px; background-color: rgb(255, 255, 255);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(255, 255, 255);" id="k_2316404258Y99AXNPRFL"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316404259R4F8HX3YQD">​​重置会取消当前点击项、勾选项</span></p></td></tr><tr id="row_4"><td id="k_2316432357BUNECOHIK3" tabindex="0" row="4" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 173px; background-color: rgb(255, 255, 255);" w="171" h="53" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316432358EHXPPFPENI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316432359AEDY27ZJAL">​​reload(arg1,arg2)</span></p></td><td id="k_2316432361EGQBBVMWR1" tabindex="0" row="4" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 412px; background-color: rgb(255, 255, 255);" w="410" h="53" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23164323629H4K6C7D6C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316432363FRFY69TKMV">​​arg1:根ul/其他子节点ul标签，不传则默认根ul</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2316434878OH5HIDPM9B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316434879296ODV4KS5">arg2:加载参数如{p:123}</span></p></td><td id="k_2316432366I7U75815M8" tabindex="0" row="4" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(190, 206, 250); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 709px; background-color: rgb(255, 255, 255);" w="707" h="53" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2316432366PTX4CEGDHI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2316432367S6JXS9P3M8">​​重新加载</span></p></td></tr></tbody></table></div><div id="k_2311362151WC6OZO26LI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311362151FIKRVPKXEV">​</span></div><div id="k_24100631492XUMUXB819" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(242, 240, 250); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(3, 222, 36); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(20, 77, 3); background-color: rgb(242, 240, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(20, 77, 3); vertical-align: baseline;" id="k_2410063151ZGTQA66IS7">Tree数据格式说明​</span></div><div id="k_241006312447KUYSWK8V" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 42px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410063126HT6JXR6EBB"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position:relative;width:100%;height:100%;min-width:100%;min-height:100%;box-sizing: border-box;-moz-box-sizing: border-box;-webkit-box-sizing: border-box;"><div>​tree数据格式如下：</div><div>[{</div><div>    id : '00001'</div><div>    text : '根节点',</div><div>    data: {</div><div>                'id': '00001',</div><div>                'field1' : '字段1'</div><div>             },</div><div>    children: [{</div><div>           id : '000002',</div><div>           text: '子节点',</div><div>           data: {      }  /***其他字段***/   </div><div>     }]</div><div>}]</div></div></pre></span></div><div id="k_2410063183Z83XWX5EE7" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410063184M3H9R7P1Y6">​</span></div><div id="k_2316420765YTVY8VSGOX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23164207669W1R5QQUDR">​</span></div><div id="k_23113621694ENYTNEFE8" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(242, 240, 250); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(3, 222, 36); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(20, 77, 3); background-color: rgb(242, 240, 250); font-weight: 400; font-style: normal; text-decoration: none solid rgb(20, 77, 3); vertical-align: baseline; letter-spacing: 1px;" id="k_231136216969XP5U7YO5">​Demo</span></div><div id="k_2311361971L6J3FVT4I2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span></div><div id="k_2311362584LLMUQEACDE" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316452268F1V2HQIFLK">​1 . </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23164514202D7ORLJTEZ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23113625853VY49C6DJS">​定义ul标签</span></div><div id="k_2316452279H5PLT3839Z" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 62px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 20px; margin-top: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316452279I6ZGRWGERP"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(83, 83, 83); background: none;">&lt;</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(41, 111, 169); background: none;">ul</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">=</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(161, 100, 75); background: none;">"tree1"</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(41, 111, 169); background: none;">ul</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(83, 83, 83); background: none;">&gt;</span></div></div></pre></span></div><div id="k_2316451440AIRR8U8OVN" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316462013M724MILG8J">​2 . </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316451442OS7KGB8BFT"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316452274GDEVAQLFNT">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316451443DOQXGMRKJK">​创建tree实例</span></div><div id="k_2316462023MVYPYIL58O" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 62px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2316462024C31G5CFSQ6"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(86, 156, 214);">new</span>&nbsp;<span style="color: rgb(78, 201, 176);">$B</span>.<span style="color: rgb(78, 201, 176);">Tree</span>(<span style="color: rgb(220, 220, 170);">$</span>(<span style="color: rgb(206, 145, 120);">"#tree1"</span>), {</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;data:</span>&nbsp;<span style="color: rgb(220, 220, 170);">getTreeData</span>(<span style="color: rgb(181, 206, 168);">2</span>,&nbsp;<span style="color: rgb(181, 206, 168);">2</span>),&nbsp;<span style="color: rgb(106, 153, 85);">//'数据'</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;clickCheck:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//是否点击复选</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;nodeParentIcon:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'fa-folder-empty'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//父节点图标关闭状态</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;nodeParentOpenIcon:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'fa-folder-open-empty'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//打开状态图标</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;leafNodeIcon:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'fa-doc'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//子节点图标</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;chkEmptyIcon:</span>&nbsp;<span style="color: rgb(206, 145, 120);">' fa-check-empty'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//不选</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;chkAllIcon:</span>&nbsp;<span style="color: rgb(206, 145, 120);">' fa-check'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//全选</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;chkSomeIcon:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'fa-ok-squared'</span>,&nbsp;<span style="color: rgb(106, 153, 85);">//部分选 ***/</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;checkbox:</span>&nbsp;<span style="color: rgb(86, 156, 214);">true</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(156, 220, 254);">&nbsp;&nbsp;&nbsp;&nbsp;fontIconColor:</span>&nbsp;<span style="color: rgb(206, 145, 120);">'#5F52E3'</span>,</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(220, 220, 170);">&nbsp;&nbsp;&nbsp;&nbsp;onClick</span><span style="color: rgb(156, 220, 254);">:</span>&nbsp;<span style="color: rgb(86, 156, 214);">function</span>&nbsp;(<span style="color: rgb(156, 220, 254);">data</span>,&nbsp;<span style="color: rgb(156, 220, 254);">params</span>) {&nbsp;<span style="color: rgb(106, 153, 85);">//function (data) { },//点击事件</span></div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;"><span style="color: rgb(78, 201, 176);">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;console</span>.<span style="color: rgb(220, 220, 170);">log</span>(<span style="color: rgb(206, 145, 120);">"onClick ="</span>&nbsp;+&nbsp;<span style="color: rgb(78, 201, 176);">JSON</span>.<span style="color: rgb(220, 220, 170);">stringify</span>(<span style="color: rgb(156, 220, 254);">data</span>) +&nbsp;<span style="color: rgb(206, 145, 120);">" params="</span>&nbsp;+&nbsp;<span style="color: rgb(78, 201, 176);">JSON</span>.<span style="color: rgb(220, 220, 170);">stringify</span>(<span style="color: rgb(156, 220, 254);">params</span>));</div><div style="color: rgb(212, 212, 212); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal;">&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">});</div></pre></span></div><div id="k_2311362575M13LZF52D4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311362576O8PGC5LCKU">​</span></div><div id="k_2410062620OY62427RFY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410062621XANQJPKWSX">​</span></div><div id="k_2410112351GLULIG3JMV" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 590px; height: 199px; position: absolute; top: 1944px; left: 299px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_2410112352LQ69G819XO" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;"><div id="k_2410112353QULSGL65CT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410112354PQQQJ7J7AM">​说明：</span></div><div id="k_2410113402CSOB67LS6R" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410135968BQTUJ22O5P">树为一个数组，可以有多个根节点</span></div><div id="k_2410120008XECKKCZ6DK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_24101200099ULXV8X1E2">​每一个节点 由 [ id 、text、data、children&nbsp; ]构成</span></div><div id="k_24101231389G4GDN6IZM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410123139H4R8ZMQT2Z">​id、text：是必须的，用于构造节点id、显示文本</span></div><div id="k_24101304873K7LLT1PSU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410130488X3JBX8A27I">​data：用于存放节点的其他字段内容，是一个对象</span></div><div id="k_2410132372HCFN4NXIP5" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410132373CBI2KKV7OE">​children：当存在子节点时候，children是一个子节点的数组</span></div><div id="k_2410171940XRWW3K17TX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none;" id="k_24101747008QHV4KXQU3">特别提示：treegrid树形表格也是采用这个数据格式</span></div></div></div>


    
</div>