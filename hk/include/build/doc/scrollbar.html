<div style="position:relative">
<div id="k_0416591490XFJSNIGIYZ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(239, 252, 237); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(24, 3, 255); border-image: initial; padding-left: 14px; margin-bottom: 10px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(239, 252, 237);" id="k_0417144311XTK4CMPK4U">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_041714431352CX7FGQ2Y">​<img id="k_0417144313R6DCQ35TLJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABYUlEQVQoU53Sv6vPcRTH8cf5fO7gDgy416ckd5RNLMo/gLqL/C6LBcPNVbgDg5FVusV2791YFcpgMEmJ0E3Jr7i/hAy4fT/vo++3rkQp98zn+arXj7CMiyWmkQNZORrsE7ZI07ipuDwjXv2u3YPWyq117Yq0WbgrPQg2YTfmFcdmuEdk9z8aOZS1KXzO1siceLmkOiCbunIJ20pxYE487kGDVZ4KjnSKgx/F8yVglVzdz47Cs6p2VXoyW5whfkRT5/VMb+viXFsbLq2HyUxf5aJwvKQT6I+wp2od+iBed6FbJd1Xeoo3kjvBF+zNdO1bMbaitjPS6bbYvyCmo6lyInk3W1xoKiPJ+QgrpfFOcXZBfF1X5WiE4U7r8IJ4/4cnL5rKKIY6xVgXWCPX99UmpUezxRixuLz0umkNyu1RGQ829ArlKTZiF763xcl5bv/q6V+LSKYWi4lP4s1fi/jf+f0EmwmiDtkpZMsAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(239, 252, 237);" id="k_0417144313N4MOMXCJ12">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(239, 252, 237);" id="k_0417144312QOLZJWQJCZ">介绍</span></div><div id="k_0417080068RLYD3XWPF1" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_04174405191P26KQNCTF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0417440520N8DI1HVYW7">​<img id="k_0417440520EX49RUC473" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAr0lEQVQ4T52SMQrCQBBF32SSEyjmDIKdlUezUCSx0EpQ8DQ22uQqEsHGTtldWUNSiLq4W/3m/fnzZ4XIJ5Ec8WCeuFIshzNUecKs0XIMJZEebpgqW2soE7iKssOwCMGvqH3cOFUKa1hauKuy8kYX5PRtcrfjADcRpXCGuYNMG6NpjVSf4A7McSNR1n6qwK3VP8H3PVE2wageypS9L+QBdauD5USfI3SvYKv/GkR/uSeZhE0PEp3wEQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0417440520YWUZ2PSQX3">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0417440519RRRZBN7O36">Bui 提供的滚动条</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0417464161VRVUKKFKYX">插件采用jquery API风格，通过</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0417464161Z9XEJBHN6T">$("#div").myscrollbar(opts)</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0417464161JOYU1F3YEG">方式调用。</span></div><div id="k_04174216751ZZT28F1UF" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0417421675HDWRTXKAP7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_041744223738VQY5KD93">​<img id="k_0417442237SCL53QTQ24" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAr0lEQVQ4T52SMQrCQBBF32SSEyjmDIKdlUezUCSx0EpQ8DQ22uQqEsHGTtldWUNSiLq4W/3m/fnzZ4XIJ5Ec8WCeuFIshzNUecKs0XIMJZEebpgqW2soE7iKssOwCMGvqH3cOFUKa1hauKuy8kYX5PRtcrfjADcRpXCGuYNMG6NpjVSf4A7McSNR1n6qwK3VP8H3PVE2wageypS9L+QBdauD5USfI3SvYKv/GkR/uSeZhE0PEp3wEQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0417442238RIFFWPZ97V">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_041743347673E6TER2XV">myscrollbar插件对dom结构有一定要求</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_04174334764NSS9FWEHK">，请参考Demo里面的开发说明。</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;">​</span></div><div id="k_0417080059MLSDDD3CKR" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04170800595P7M5UKGSL">​</span></div><div id="k_04170800579NKVF58Y81" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(239, 252, 237); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(24, 3, 255); border-image: initial; margin-top: 0px; margin-bottom: 10px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(239, 252, 237); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_0417153933XGZJ4ZIGXX">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0417153935YEUEDXFBDB">​<img id="k_0417153935RPE8G3GAOW" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABYUlEQVQoU53Sv6vPcRTH8cf5fO7gDgy416ckd5RNLMo/gLqL/C6LBcPNVbgDg5FVusV2791YFcpgMEmJ0E3Jr7i/hAy4fT/vo++3rkQp98zn+arXj7CMiyWmkQNZORrsE7ZI07ipuDwjXv2u3YPWyq117Yq0WbgrPQg2YTfmFcdmuEdk9z8aOZS1KXzO1siceLmkOiCbunIJ20pxYE487kGDVZ4KjnSKgx/F8yVglVzdz47Cs6p2VXoyW5whfkRT5/VMb+viXFsbLq2HyUxf5aJwvKQT6I+wp2od+iBed6FbJd1Xeoo3kjvBF+zNdO1bMbaitjPS6bbYvyCmo6lyInk3W1xoKiPJ+QgrpfFOcXZBfF1X5WiE4U7r8IJ4/4cnL5rKKIY6xVgXWCPX99UmpUezxRixuLz0umkNyu1RGQ829ArlKTZiF763xcl5bv/q6V+LSKYWi4lP4s1fi/jf+f0EmwmiDtkpZMsAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(239, 252, 237); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_0417153935ADWSVZ861Q">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(239, 252, 237); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_0417153934HKEJPL7CE7">API及构造参数</span></div><div id="k_0417081014GCH4ZR3W7P" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" contenteditable="false"><table style="border-collapse: collapse; width: auto; height: auto; " id="201904041746229YINM7A9X3B4"><tbody><tr id="row_0"><td id="k_0417462240FI8E4VXZ3I" tabindex="0" row="0" col="0" w="1304" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1306px; background-color: rgba(0, 0, 0, 0);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417462240KLT16YXR51"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417462240EZU1RL8NNK">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417464587UEWKTO1RSC">​$("#div").myscrollbar(opts)&nbsp; opts说明</span></p></td></tr><tr id="row_1"><td id="k_041746224383J9B8GGTC" tabindex="0" row="1" col="0" w="186" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 188px; background-color: rgba(0, 0, 0, 0); text-align: center;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgba(0, 0, 0, 0);" id="k_0417462243S5PZ3BYLWH"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); text-align: center;" id="k_0417462243VYTL8GRDFC">​参数名</span></p></td><td id="k_0417462244Y7KHQLQXED" tabindex="0" row="1" col="1" w="376" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 378px; background-color: rgba(0, 0, 0, 0); text-align: center;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgba(0, 0, 0, 0);" id="k_0417462244G4J6OVK64K"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); text-align: center;" id="k_0417462244M5HT9DMVZN">​参数值</span></p></td><td id="k_0417462244OBKOO4JU3E" tabindex="0" row="1" col="2" w="738" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 740px; background-color: rgba(0, 0, 0, 0); text-align: center;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgba(0, 0, 0, 0);" id="k_0417462244GOXGJ9HNTR"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); text-align: center;" id="k_0417462244QJZFLT3QTP">​说明</span></p></td></tr><tr id="row_2"><td id="k_04174622458YWW6JW3FL" tabindex="0" row="2" col="0" w="186" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 188px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04174622459OL6DQQCWU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417462245UB6LOUR9DJ">​size</span></p></td><td id="k_0417462246SRNGY1PS58" tabindex="0" row="2" col="1" w="376" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 378px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417462246UWQJZXOGXM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04174622462T9APZBGGR">​默认值：6px</span></p></td><td id="k_04174622463BYG2DMAKH" tabindex="0" row="2" col="2" w="738" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 740px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417462247J4T43BKML6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04174622474GDV8QJRPF">​滚动条大小</span></p></td></tr><tr id="row_3"><td id="k_04174622491LAYT3RIM1" tabindex="0" row="3" col="0" w="186" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 188px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417462249UYGF5MYDT9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417462249ZEBM2ES34W">​hightColor</span></p></td><td id="k_0417462250JU3YZMCUV7" tabindex="0" row="3" col="1" w="376" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 378px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417462250VUJN7E59C9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417462250LBXQFDCV45">​默认值：#05213B</span></p></td><td id="k_04174622506Z1ZNYR7NA" tabindex="0" row="3" col="2" w="738" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 740px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04174622503NXCYT6DJ5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417462250T785VWWDCU">​高亮颜色</span></p></td></tr><tr id="row_4"><td id="k_0417462251HXNDZFS18Z" tabindex="0" row="4" col="0" w="186" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 188px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_041746225156C4ROOQ68"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417462251IHA4VFXROY">​setPadding</span></p></td><td id="k_0417462252PNYEYBELQ2" tabindex="0" row="4" col="1" w="376" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 378px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04174622523KSN4K1P4S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_041746225217WYWWJT1I">​默认值：true</span></p></td><td id="k_0417462252B5USUN9JC8" tabindex="0" row="4" col="2" w="738" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 740px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417462252D9ZIVHSRMF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417462252ZQ6HY3PTN4">​内容区域是否设置padding让位给滚动条</span></p></td></tr><tr id="row_5"><td id="k_0417462253HI5SDN13GA" tabindex="0" row="5" col="0" w="186" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 188px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417462253TTJS3LFMYA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417462253BUAWCE3YVJ">​display</span></p></td><td id="k_04174622545PCE486JYE" tabindex="0" row="5" col="1" w="376" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 378px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04174622546IT41X7LUL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417462254H9LSRKLK41">​默认值：true</span></p></td><td id="k_04174622549D2T7VJ7Q3" tabindex="0" row="5" col="2" w="738" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 740px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04174622547QQIT31KKG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04174622544QM8CC39UD">​显示方式，auto 自动显示，隐藏，show:固定显示</span></p></td></tr><tr id="row_6"><td id="k_04175757392JN9ARZ2AU" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 188px;" w="186" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04175757394HIB4IMH4B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417575739N2U4FGHZ4Y">​axis</span></p></td><td id="k_0417575740LGXB8DTPVF" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 378px;" w="376" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417575740ITFW7WDXDI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04180010322I1QV9PF9S">默认值：true</span></p></td><td id="k_041757574224RGYUCO2Y" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 740px;" w="738" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417575742PT6JBFU8UR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417575742HOSMBK7FND">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417584536UZXY291QPO">x：x轴滚动条 ；y：y轴滚动条；xy：xy轴滚动条</span></p></td></tr><tr id="row_7"><td id="k_041757567096L1BDZUDM" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 188px;" w="186" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417575670N8RHTLIEJ2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417575670XXZZQAIG68">​checkItv</span></p></td><td id="k_0417575671OFQ6L4KGCT" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 378px;" w="376" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0417575671E9GJW32VCP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418001623P9OA9IMUN2">默认值：</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418001624R2FQAVCTEK">0</span></p></td><td id="k_04175756729A1GEKL3F7" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 740px;" w="738" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04175756725XOQAH4TRQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417575672Z5XQZRWJ53">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418004099BTBL4AQW3X">是否开启自动检查，0表示不开启，其他值 为自动检查的间隔毫秒数</span></p></td></tr><tr id="row_8"><td id="k_0418011408BDR5VZY4YE" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 188px;" w="186" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04180114085XYU7IEDXC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418011408X33CCBJNJK">​minHeight</span></p></td><td id="k_04180114092GFJLEF8AI" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 378px;" w="376" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0418011409WT26PYQAAB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04180122671HS6R3O3XK">默认值：50</span></p></td><td id="k_0418011410JGF4XEDI5X" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 740px;" w="738" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0418011410GFJNO7G7FL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04180154884F9VCWERXT">滚动块的最小高度</span></p></td></tr><tr id="row_9"><td id="k_04180113594MRXFBTBTA" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 188px;" w="186" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0418011359MB7K6S7KDI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418011359GMLN1FGA4Q">​minWidth</span></p></td><td id="k_0418011360F4YRDT7JAL" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 378px;" w="376" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04180113609SMNLOWFCU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04180113604PSH6O4D1U">​​默认值：50</span></p></td><td id="k_0418011361WOBSULFVKW" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 740px;" w="738" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0418011361XKJ3OESC58"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04180113618Q5QSZO9MO">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04180157668PL7NSIL9A">​滚动块的最大高度</span></p></td></tr><tr id="row_10"><td id="k_0418021451FAE99WUAG5" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 188px;" w="186" h="78" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_041802145135O4S95BSX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418021451JZRYZWFCIC">​slider</span></p></td><td id="k_0418021452E8LC6BYPDF" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 378px;" w="376" h="78" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0418021452HHZTBZON7N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04180214528WP8UZAL36">​默认值：{&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_04180233365M6PY5G5QM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04180233366ESTJVX1LE">&nbsp;"background-color": '#6F6F6F',&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_0418024401QBL74C2WWN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418024737NULO96KZ9D">&nbsp;"border-</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418024738HUGQ86HW3G">radius": "8px"        }</span></p></td><td id="k_0418021453ISGEO6ZQMN" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 740px;" w="738" h="78" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0418021453DV5HAMW3AB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418021453CBQ75GZSUU">​滚动块样式</span></p></td></tr><tr id="row_11"><td id="k_0418021317MA4RSEMS1F" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 188px;" w="186" h="103" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0418021317KS8FANSDWH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418021317P83E8G6WBO">​bar</span></p></td><td id="k_0418021318W6IOH6M52H" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 378px;" w="376" h="103" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_04180213187HKEX91EUQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418021318MPNG1YLST8">​默认值：{&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_0418033331995XAKGSBF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418033331MJAMZIAISJ">&nbsp;"background-color": "#e4e4e4",&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_0418033582XAS8HSVTNZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418033582G591RFDQBT">&nbsp;"border-radius": "8px",</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_0418033040F4NR3IGWKP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0418033040W2LM4UEFC8">&nbsp;"opacity": 0.5        }</span></p></td><td id="k_0418021320B8WN9LITM6" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 740px;" w="738" h="103" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0418021320CNZXPHK5XR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04180213209JRA4QV8FL">​拖动条样式</span></p></td></tr></tbody></table></div><div id="k_04170810534B4V7FBUHS" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417081053M3Z2E9IHQG">​</span></div><div id="k_0417081079I7D3EY118U" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_04170810803R7U2SB4EC">​</span></div><div id="k_0417081045YHWDJ1AB2T" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(239, 252, 237); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(24, 3, 255); border-image: initial; margin-top: 0px; margin-bottom: 10px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(239, 252, 237); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_0417155950SIFQWJPOSO">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0417155951EWGG563IYP">​<img id="k_04171559519A9EFR6CIJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABYUlEQVQoU53Sv6vPcRTH8cf5fO7gDgy416ckd5RNLMo/gLqL/C6LBcPNVbgDg5FVusV2791YFcpgMEmJ0E3Jr7i/hAy4fT/vo++3rkQp98zn+arXj7CMiyWmkQNZORrsE7ZI07ipuDwjXv2u3YPWyq117Yq0WbgrPQg2YTfmFcdmuEdk9z8aOZS1KXzO1siceLmkOiCbunIJ20pxYE487kGDVZ4KjnSKgx/F8yVglVzdz47Cs6p2VXoyW5whfkRT5/VMb+viXFsbLq2HyUxf5aJwvKQT6I+wp2od+iBed6FbJd1Xeoo3kjvBF+zNdO1bMbaitjPS6bbYvyCmo6lyInk3W1xoKiPJ+QgrpfFOcXZBfF1X5WiE4U7r8IJ4/4cnL5rKKIY6xVgXWCPX99UmpUezxRixuLz0umkNyu1RGQ829ArlKTZiF763xcl5bv/q6V+LSKYWi4lP4s1fi/jf+f0EmwmiDtkpZMsAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(239, 252, 237); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_0417155952CRC53JPDZM">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(239, 252, 237); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_0417155950YF1HKMU88P">Demo</span></div><div id="k_0417081129MB88FT5XHC" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 41px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_051338546213O4AK9BC1">1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0513470654Q82WHFAP5B">定义Dom</span></div><div id="k_05133833881HENAV8XBT" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 98px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513383482VD1KJ2E1AM">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513383388ZBI5AKLMYQ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513383388AWJEVUKLYA">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513442923XH1L756KEK">​</span><img tabindex="0" src="/bui/tmp\e11d0680576511e9a9ba3c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513442924ASIUKYL4N8">​</span></div><div id="k_0513385612W2M1U7OTTC" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 41px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513385612IZF5T33155">​</span><span id="k_0513385643EAX4SPGEE8" style="font-family: &quot;Microsoft Yahei&quot;;">​</span><span style="background-color: rgba(0, 0, 0, 0); font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_05133834867YFDG6I7TR" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 41px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05134632511ZIK4ZXD7R">2、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0513383486WAEWQE1D6S">创建实例</span></div><div id="k_0513384639UY1CS6RAKU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 97px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05133846393MKY8WQLEF"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">      var  </span><span style="background: none; color: rgb(111, 175, 209);">myScrollbar</span><span style="background: none; color: rgb(167, 167, 167);"> =  </span><span style="background: none; color: rgb(175, 175, 125);">$</span><span style="background: none; color: rgb(167, 167, 167);">(</span><span style="background: none; color: rgb(161, 100, 75);">"#<span style="background: none; color: rgb(0, 137, 255);">scroll_content</span>"</span><span style="background: none; color: rgb(167, 167, 167);">).</span><span style="background: none; color: rgb(255, 0, 209);">myscrollbar</span><span style="background: none; color: rgb(167, 167, 167);">({</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">slider:</span><span style="color: rgb(167, 167, 167); background: none;"> {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(161, 100, 75); background: none;">"background-color"</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'#64FF2E'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(161, 100, 75); background: none;">"border-radius"</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"0"</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },               </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">size:</span><span style="color: rgb(161, 100, 75); background: none;">"6px"</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">display:</span><span style="color: rgb(161, 100, 75); background: none;">'auto'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">hightColor:</span><span style="color: rgb(161, 100, 75); background: none;">"#39E300"</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">bar:</span><span style="color: rgb(167, 167, 167); background: none;">{</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(161, 100, 75); background: none;">"background-color"</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"#585858"</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(161, 100, 75); background: none;">"border-radius"</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"0"</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(161, 100, 75); background: none;">"opacity"</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">0.4</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }   </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            }).</span><span style="color: rgb(255, 0, 209); background: none;">getMyScrollIns</span><span style="color: rgb(167, 167, 167); background: none;">();</span></div></div></pre></span></div><div id="k_05133848808M76CETFFZ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 41px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513384880EI51AMS3LE">​</span></div><div id="k_0513465189BSALRCTU5Y" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 41px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513465189FISPDQFJHD">​</span></div><div id="k_0417075539MZWOFBDD9U" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0417075540I2XNXB4GPZ">​</span></div><div id="k_0513381760OZIF73EKQP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513381760S23BH7LSVI">​</span></div><div id="k_0513485907KDP7LM25UC" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 462px; height: 151px; position: absolute; top: 826px; left: 602px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_0513485909N9XGEBMHTC" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;"><div id="k_0513485909K3GONMPE5Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05134931956EQO7YNSJQ">​1 . </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513485909L3DTIY9GEA">​定义一个明确高宽的div容器。</span></div><div id="k_05134931992CM1A3978O" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513501927JXDFBH94GN">​2 . </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513493199HF2VJ8CKJ9">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513493199FSMZSNLOML">​div容器内声明一个高宽100%，overflow:hidden的内容器div。</span></div><div id="k_05135019306KUZMGJBHT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513513769K84JZS887X">​3 . </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513501930NLVP13SWLY">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513501930HH4RUOJSYP">​内容器div里面声明需要滚动的内容的div容器 "scroll_content"。</span></div><div id="k_05135137722WOZK1I2J8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513520255KUWBX9D5TD">​4 .</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513513772JSM99DYKJ4">​ 当scroll_content里的内容发生变化时候，你可以调用。</span></div><div id="k_0513520259D2L9PPW137" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0513520259IF2RL4AKNS">​&nbsp; &nbsp; &nbsp;myScrollbar.resetSliderPosByTimer()获得滚动条适配</span></div><div id="k_0513522594RMHB698ELE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0513522594AIPQ4P8KUX">​</span></div></div></div>
</div>