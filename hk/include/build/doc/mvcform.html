<div style="position:relative">
<div id="k_2615311573EWNV38LMRG" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(138, 103, 245); border-image: initial; padding-left: 14px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_26153246216PQS7YRVEN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2615324628K9B288JMNY">​<img id="k_26153246296AWUHYV79O" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABW0lEQVQoU5WQsUoDQRCGZ/Y8AhYHau0bWNmIiDnRQkRFsBKEgFZiEPcuQeMLKEKSW5GIhSgEUQsRJAjaqLkoYmPlG1hY2VyhJuF2ZA4CCQbBaXb49//+3RmEfxZWMpkuXaspJJoixE4EuIR6fSXKMc1dAphBok8CODdMM42+6x4DwHtoWRvsEUGwDkRLEYC4ry1rGwA6RBBkEaAbfce5I8SEnc+/NX7nSznJfVypq4ZWdt1eJCryC9exanV+oFD4+Guc52Sy5zsWO2WgRFof2Upd/AWUpZxFIRYYGAOiHcMw5oay2dd20GM63ReG4RkgriIbKo4zTQBbQJSIK/XSDPlS9gNikQA2bc87iQCuByknQiHypPXiiFJPrN1LOYhCHAqitWHPK0WLa0lLpcaJSIHWSQL4EkIcAKIbz+VuGr4WgMWylKOIuMc9ES3bSt02h/4C+JKH5LPdEn4ACFyIGqf/SDIAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2615324632B7ZM9SUM4E">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2615324811GA21U4A8EC">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_2615324812QW1RQ9PDYH">介绍</span></div><div id="k_26153130924NHLFF3N45" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615383136EJHZZ2NTN9">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2615383138YSH3V4P9HV">​<img id="k_2615383139VS9L5EHMRP" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA2klEQVQoU5WRMUoDYRCFv2dYsbeQXMDaNliK2MrCzo/gEbxAuhWDWNpZeIIdyAUsU6ZLtdhYaqPYB3Fkhci6RnGnG2a+x3szomdpte/uo4g4AraAhaSpmS27enL3zYi4BU4lbbQWHoATM5u3IVVVdSFpDAy6ahGxyLLsIM/zl9WsAe4l7f4SZRkRZymlxsFnNZaege0/so/N7KoNPALDPsAdcLgOiIhX4DilNGtn2AemknY60JukSVEU59+u1DTuvgdcAyMgi4gn4LKu65uyLN9/AH2e/fXp/0IftgZKDSEsRrEAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615383140KGP96VT1US">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615383137I78XGZR2QR">为简化表单开发，Bui对mvvm实现了一简化的API封装：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(139, 109, 56); background-color: rgb(255, 255, 255);" id="k_26153934418VS13UKG7O">$B.bindForm</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(139, 109, 56); background-color: rgb(255, 255, 255);" id="k_2615393442ANU46V63PA">($("#myform"), jsonData ,changeCallFn)</span></div><div id="k_261531301793YM8OD9L8" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615383983QTS9DMBKDL">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2615383986NDW8E1ENZA">​<img id="k_2615383987QLKT155VY8" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA2klEQVQoU5WRMUoDYRCFv2dYsbeQXMDaNliK2MrCzo/gEbxAuhWDWNpZeIIdyAUsU6ZLtdhYaqPYB3Fkhci6RnGnG2a+x3szomdpte/uo4g4AraAhaSpmS27enL3zYi4BU4lbbQWHoATM5u3IVVVdSFpDAy6ahGxyLLsIM/zl9WsAe4l7f4SZRkRZymlxsFnNZaege0/so/N7KoNPALDPsAdcLgOiIhX4DilNGtn2AemknY60JukSVEU59+u1DTuvgdcAyMgi4gn4LKu65uyLN9/AH2e/fXp/0IftgZKDSEsRrEAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26153839887K1JPZHXCY">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615383985R9CW8RY6Y4">$B.bindFrom(...) 省去编写绑定表达式的工作，只需要将标签ID/name与json属性名对应上即可</span></div><div id="k_2615363084E46NQE6UIG" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615384776XTW76SJYT5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2615384779SO4Y1M9QV4">​​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26153847805WTOGXCAO7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2615390567QR6CWSNECQ">​​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615390569JZKFIQLJVV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2615391982L4VVW7RPG6">​<img id="k_2615391983P3BYKPKAFU" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA2klEQVQoU5WRMUoDYRCFv2dYsbeQXMDaNliK2MrCzo/gEbxAuhWDWNpZeIIdyAUsU6ZLtdhYaqPYB3Fkhci6RnGnG2a+x3szomdpte/uo4g4AraAhaSpmS27enL3zYi4BU4lbbQWHoATM5u3IVVVdSFpDAy6ahGxyLLsIM/zl9WsAe4l7f4SZRkRZymlxsFnNZaege0/so/N7KoNPALDPsAdcLgOiIhX4DilNGtn2AemknY60JukSVEU59+u1DTuvgdcAyMgi4gn4LKu65uyLN9/AH2e/fXp/0IftgZKDSEsRrEAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615391985WE5PJNSGHN">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615384777KV9VEA5M9Y">$B.bindFrom(...)返回一个mvvm实例，同样可以通过返回的实例实现自定义UI控件的处理，可参考《双向绑定(mini-mvvm)》</span></div><div id="k_26153541372EYIT294Y6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615354138DFFTYS18ST">​</span></div><div id="k_2615354193PABVROXQXJ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615354194LXTUXOCCEZ">​</span></div><div id="k_2615314022KQYRVNNLDE" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(138, 103, 245); border-image: initial; margin-top: 0px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615334285EC84BV1HVP">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2615334288YUVLGEOSLO">​<img id="k_2615334288S8EGNPG724" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABW0lEQVQoU5WQsUoDQRCGZ/Y8AhYHau0bWNmIiDnRQkRFsBKEgFZiEPcuQeMLKEKSW5GIhSgEUQsRJAjaqLkoYmPlG1hY2VyhJuF2ZA4CCQbBaXb49//+3RmEfxZWMpkuXaspJJoixE4EuIR6fSXKMc1dAphBok8CODdMM42+6x4DwHtoWRvsEUGwDkRLEYC4ry1rGwA6RBBkEaAbfce5I8SEnc+/NX7nSznJfVypq4ZWdt1eJCryC9exanV+oFD4+Guc52Sy5zsWO2WgRFof2Upd/AWUpZxFIRYYGAOiHcMw5oay2dd20GM63ReG4RkgriIbKo4zTQBbQJSIK/XSDPlS9gNikQA2bc87iQCuByknQiHypPXiiFJPrN1LOYhCHAqitWHPK0WLa0lLpcaJSIHWSQL4EkIcAKIbz+VuGr4WgMWylKOIuMc9ES3bSt02h/4C+JKH5LPdEn4ACFyIGqf/SDIAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615334291TWN2SGSY1L">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615404606ZB2HWLZTVH">&nbsp;$B.bindForm(</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(109, 94, 247); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615404608D2SB95H9UJ">f</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(109, 94, 247); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615404376MRW7G8WUPT">ormWrap ,&nbsp; json</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(109, 94, 247); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615404611KEFL9D5P6O">Data , changeFn</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615404613XI2GXSG5XI">)&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615393858BZ22RATOIB">参数说明</span></div><div id="k_2615313895XL7UPCGBDI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 17px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190326154125AK462MY4CJ3H"><tbody><tr id="row_0"><td id="k_26154125074MU1TTUNP5" tabindex="0" row="0" col="0" w="211" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 213px; background-color: rgba(0, 0, 0, 0); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgba(0, 0, 0, 0);" id="k_2615412508AOYFFEX3E2"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); text-align: center;" id="k_2615412509Z25RG5L9TJ">​参数名</span></p></td><td id="k_2615412512QXB6GYWGJY" tabindex="0" row="0" col="1" w="337" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 339px; background-color: rgba(0, 0, 0, 0); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgba(0, 0, 0, 0);" id="k_2615412513GA7EPC19KX"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); text-align: center;" id="k_2615412514RDX7CCIQNF">​参数值</span></p></td><td id="k_26154125174IW75FCG2F" tabindex="0" row="0" col="2" w="774" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 776px; background-color: rgba(0, 0, 0, 0); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgba(0, 0, 0, 0);" id="k_26154125188MHM4F2R81"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); text-align: center;" id="k_2615412519TSBIECXS2I">​说明</span></p></td></tr><tr id="row_1"><td id="k_2615412523EHMY3XCGNI" tabindex="0" row="1" col="0" w="211" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 213px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615412524CQQ6YAE9LO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154125251LH5HM2MRQ">​formWrap</span></p></td><td id="k_2615412528RKFJJVBYJE" tabindex="0" row="1" col="1" w="337" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 339px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615412529B35ZLMQXSO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154125299YTSRBT4FM">​标签的容器标签对象</span></p></td><td id="k_2615412531EJI4I5CPVB" tabindex="0" row="1" col="2" w="774" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 776px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615412532KDAH4S6CDD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615412532G6UPVGQQQT">​</span></p></td></tr><tr id="row_2"><td id="k_2615412535EWBK2P26MV" tabindex="0" row="2" col="0" w="211" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 213px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615412536V991PO5H1Z"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615412538AHF26YABKV">​jsonData</span></p></td><td id="k_2615412541I3QR977383" tabindex="0" row="2" col="1" w="337" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 339px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26154125423NKYTIODNI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615412544PBE23U2DIT">​表单对应的json数据</span></p></td><td id="k_2615412548SF9OL4G3V6" tabindex="0" row="2" col="2" w="774" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 776px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615412549MNJN6MNAA3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154125509W6PX1CSPH">​</span></p></td></tr><tr id="row_3"><td id="k_26154125513BW9FIXCLY" tabindex="0" row="3" col="0" w="211" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 213px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615412552NAY7CJL22U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615412553VFCXTAIDS7">​changeFn</span></p></td><td id="k_2615412554XNQDYNAF18" tabindex="0" row="3" col="1" w="337" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 339px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615412555O1C9AH1MWF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154125553U15FNGR8Z">​请参考《changeFn说明》</span></p></td><td id="k_2615412557H38NDVATST" tabindex="0" row="3" col="2" w="774" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 776px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26154125579CRQFF4KLN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615583130F68F8O8RP1">数据变更通知回调，通常情况下</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_261558313164OFTBHLAM">是</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615583132JI63MRJ4IM">不需要这个通知，则可以不传这个参数</span></p></td></tr></tbody></table></div><div id="k_2615313878NEH32NO5EM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615313879F3J5JTIRWK">​</span></div><div id="k_2615432613BGGKUU941G" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190326154353IPBTRW7QWN2V"><tbody><tr id="row_0"><td id="k_2615463232JKQJCYP7YO" tabindex="0" row="0" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1321px; background-color: rgba(0, 0, 0, 0);" w="1319" h="28" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615463233ALBA9WIXM1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615470958MW2DVUOM9I">changeFn</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615470959MC9F7RUTBB">回调函数</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615470961MSGCOQSGVW">说明</span></p></td></tr><tr id="row_1"><td id="k_2615435302GK3QMVDR95" tabindex="0" row="1" col="0" w="1319" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1321px; background-color: rgba(0, 0, 0, 0);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435303MU522K8P7I"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(135, 143, 151); background-color: rgb(255, 255, 255);" id="k_2615451780OCEDFMRGSO">changeFn(&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(135, 143, 151); background-color: rgb(255, 255, 255);" id="k_261545178225TMKCIB5S">propObj</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(135, 143, 151); background-color: rgb(255, 255, 255);" id="k_2615454252LCBKV3QERE">,</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(135, 143, 151); background-color: rgb(255, 255, 255);" id="k_2615454253MBB3R7PV4L">&nbsp;propName</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(135, 143, 151); background-color: rgb(255, 255, 255);" id="k_2615461029WKP3EF7OY3">,&nbsp;newValue,&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(135, 143, 151); background-color: rgb(255, 255, 255);" id="k_2615461030TNUM8XY3UX">oldValue&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(135, 143, 151); background-color: rgb(255, 255, 255);" id="k_26154610317TBPW772BC">)&nbsp;</span></p></td></tr><tr id="row_2"><td id="k_2615435316B1OCH9UTC6" tabindex="0" row="2" col="0" w="214" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 216px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435317PKID5QK62B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615435318QO4GXNXRQP">​​ propObj</span></p></td><td id="k_2615435320D5RJ8BP3ZA" tabindex="0" row="2" col="1" w="332" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435321YZHUJOSNVP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615435322M91Q886HKC">​数据对象</span></p></td><td id="k_26154353247DUU542NM8" tabindex="0" row="2" col="2" w="769" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 771px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435325KXB8JG9SLF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615435327X9AT6Q6ZMC">​</span></p></td></tr><tr id="row_3"><td id="k_2615435330PRQ3EVKNRE" tabindex="0" row="3" col="0" w="214" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 216px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435331YSYPJ493Z2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154353322Y66VV3KWY">​​&nbsp;propName</span></p></td><td id="k_26154353359U7RPERYKQ" tabindex="0" row="3" col="1" w="332" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435336QW8KH6VW6B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615435337HWNPT52P17">​属性</span></p></td><td id="k_2615435341CFN8IYL1HL" tabindex="0" row="3" col="2" w="769" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 771px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26154353429P4TB2WL6O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154353435JJZYK966A">​</span></p></td></tr><tr id="row_4"><td id="k_2615435345N2SSV9TJP6" tabindex="0" row="4" col="0" w="214" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 216px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_261543534512CNOF3TAE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154353462Q3N7JSNJ6">​ newValue</span></p></td><td id="k_2615435348HAGXESSJR6" tabindex="0" row="4" col="1" w="332" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435349L38XJBB5HG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154353492HJYNEAPLF">​新值</span></p></td><td id="k_261543535193SE9SR2TP" tabindex="0" row="4" col="2" w="769" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 771px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435352H7WNOGG58F"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154353529U2S3LAWRM">​</span></p></td></tr><tr id="row_5"><td id="k_2615435354G3JMTDKE5J" tabindex="0" row="5" col="0" w="214" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 216px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_261543535557BCXNIWJ5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615435356XDOK5LKNSA">​​ oldValue</span></p></td><td id="k_2615435357Y1P9GGQ1CO" tabindex="0" row="5" col="1" w="332" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435358NZCIGYJPO4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615435359K62HAFJ336">​旧值</span></p></td><td id="k_2615435360I5S3T3E1HU" tabindex="0" row="5" col="2" w="769" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 771px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2615435361KYY2WJIOZ9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615435361SAZ37XEHUQ">​</span></p></td></tr></tbody></table></div><div id="k_26154326462Z8FPA8K4N" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615432647PPM482IPQ7">​</span></div><div id="k_26153130646ZHFMYQUNF" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(138, 103, 245); border-image: initial; margin-top: 0px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_26153400254PZ7353G4W">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_26153400273XOT5XLOLI">​<img id="k_2615340028AI6ASZZ6GG" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABW0lEQVQoU5WQsUoDQRCGZ/Y8AhYHau0bWNmIiDnRQkRFsBKEgFZiEPcuQeMLKEKSW5GIhSgEUQsRJAjaqLkoYmPlG1hY2VyhJuF2ZA4CCQbBaXb49//+3RmEfxZWMpkuXaspJJoixE4EuIR6fSXKMc1dAphBok8CODdMM42+6x4DwHtoWRvsEUGwDkRLEYC4ry1rGwA6RBBkEaAbfce5I8SEnc+/NX7nSznJfVypq4ZWdt1eJCryC9exanV+oFD4+Guc52Sy5zsWO2WgRFof2Upd/AWUpZxFIRYYGAOiHcMw5oay2dd20GM63ReG4RkgriIbKo4zTQBbQJSIK/XSDPlS9gNikQA2bc87iQCuByknQiHypPXiiFJPrN1LOYhCHAqitWHPK0WLa0lLpcaJSIHWSQL4EkIcAKIbz+VuGr4WgMWylKOIuMc9ES3bSt02h/4C+JKH5LPdEn4ACFyIGqf/SDIAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615340030LVDAZDA3MI">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2615340026CY26PSNL2U">&nbsp;Demo</span></div><div id="k_2615313123AUHB83LGA4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615492449PMU2TBLPB2">​1、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615313124CEPH9YGXVC">​定义实体json，通常这个json来源于服务端</span></div><div id="k_2615492464558OP9WEKB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615492454H3D5X2VG5F">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615492465N835OSPMAJ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154924676ZOFV87I7Y">​</span></div><div id="k_2615500623Y4W8BJCETI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 39px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26155006289T3YV4SUJB"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(41, 111, 169); background: none;"> var   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(0, 179, 0); font-weight: bold; background: none;">userData</span><span style="color: rgb(167, 167, 167); background: none;"> = {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">         userName:</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(161, 100, 75); background: none;">'kevin.huang'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">         userPwd:</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(161, 100, 75); background: none;">'000000'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">         sex:</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(136, 161, 123); background: none;">1</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">         hobby:</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(161, 100, 75); background: none;">'1,3'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">         userTech:</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(136, 161, 123); background: none;">2</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">         userid:</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(136, 161, 123); background: none;">1</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">         userAddr:</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(161, 100, 75); background: none;">''</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">         userDesp:</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(161, 100, 75); background: none;">'介绍：这家伙是一个热爱编程工作的人！'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">     };</span></div></pre></span></div><div id="k_2615500671MKQTADI5E9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615500672YNVJAW24QQ">​</span></div><div id="k_26154924601V66BGZ3XM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615493017JZXAI416J5">​2、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615492458X3MTD1LSUH"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615492461454FV55ZBG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26154924624L922Y3W7N">​编写表单from，注意对应表单id/name与json数据字段一致</span></div><div id="k_2615493025V9N28X4W8O" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615493005R54O5DQ5J7">​</span></div><div id="k_2615494527F1B5L8JLLV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 37px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615494528QTFRVC5Q64"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(83, 83, 83); background: none;">  &lt;</span><span style="color: rgb(41, 111, 169); background: none;">table </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">style</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"</span><span style="color: rgb(161, 100, 75); background: none;">margin-top:20px;"  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"<span style="color: rgb(0, 179, 0); font-weight: bold; background: none;">myform</span>"  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"form_table"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">      &lt;</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">td  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"label"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">姓名：</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">input</span><span style="color: rgb(255, 0, 5); background: none;"> id="userName"  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"text"</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">/&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">      &lt;</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">            &lt;</span><span style="color: rgb(41, 111, 169); background: none;">td  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"label"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">性别：</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">            &lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">label   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"k_radio_label k_radio_anim"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">                            &lt;</span><span style="color: rgb(41, 111, 169); background: none;">input  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"radio"</span><span style="color: rgb(255, 0, 5); background: none;"> name="sex"   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">value</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"1"</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">/&gt;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">i  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"k_radio_i"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">i</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">男 </span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">label</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">                  &lt;</span><span style="color: rgb(41, 111, 169); background: none;">label   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"k_radio_label k_radio_anim"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">                           &lt;</span><span style="color: rgb(41, 111, 169); background: none;">input   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"radio" </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(255, 0, 5); background: none;">name="sex"  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">value</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"0"</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">/&gt;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">i   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"k_radio_i"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">i</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">女 </span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">label</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">          &lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">     &lt;/</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">     &lt;</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">          &lt;</span><span style="color: rgb(41, 111, 169); background: none;">td   </span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"label"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">爱好：</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">          &lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">label  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"k_checkbox_label k_checkbox_anim"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">input  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"checkbox"</span><span style="color: rgb(255, 0, 5); background: none;"> name="hobby"  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">value</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"1"</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">/&gt;</span></div><div style="background: none;"><span style="color: rgb(83, 83, 83); background: none;">                               &lt;</span><span style="color: rgb(41, 111, 169); background: none;">i  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"k_checkbox_i"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">i</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">跑步</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">label</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;"></span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">             &lt;</span><span style="color: rgb(41, 111, 169); background: none;">label  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"k_checkbox_label k_checkbox_anim"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">input</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"checkbox"  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(255, 0, 5); background: none;">name="hobby"</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">value</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"3"</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">/&gt;</span></div><div style="background: none;"><span style="color: rgb(83, 83, 83); background: none;">                              &lt;</span><span style="color: rgb(41, 111, 169); background: none;">i  </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"k_checkbox_i"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">i</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">编程</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">label</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">         &lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">     &lt;/</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">     &lt;</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">            &lt;</span><span style="color: rgb(41, 111, 169); background: none;">td   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"label"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">户籍：</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">                 &lt;</span><span style="color: rgb(41, 111, 169); background: none;">select</span><span style="color: rgb(255, 0, 5); background: none;"> id="userid"</span><span style="color: rgb(83, 83, 83); background: none;">&gt; </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">option </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">value</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"0"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">广西钦州</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">option</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">option </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">value</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"1"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">广西南宁</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">option</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">select</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">            &lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">     &lt;/</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;"></span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(83, 83, 83); background: none;">  &lt;/</span><span style="color: rgb(41, 111, 169); background: none;">table</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></pre></span></div><div id="k_2615520256TSJFHV91MX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615520256YEUE72Y927">​</span></div><div id="k_2615493015AKP44WNMAM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615493605VA5DAYQH2L">​3、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615493016V4A5PBBKQT"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615493017R174SXMPCB">​调用​$B.bindForm(...)实现绑定双向绑定</span></div><div id="k_2615493615A8KSCLKGLI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615493654VK15EFHBBI">​</span></div><div id="k_2615494316QRS3XGY6G3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615494316ZBQ3EISHO9"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​      <span style="color: rgb(167, 167, 167); background: none;">var vm ; // 定义vm对象</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(41, 111, 169); background: none;">      function   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(175, 175, 125); background: none;">pageLoaded</span><span style="color: rgb(167, 167, 167); background: none;">() {</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">               vm</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">bindForm</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(255, 0, 5); background: none;">$("#myform"</span><span style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(255, 0, 5); background: none;">)</span>, </span><span style="color: rgb(0, 179, 0); font-weight: bold; background: none;">userData</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(0, 137, 255); background: none;">propObj, propName, newValue, oldValue</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(41, 111, 169); background: none;">               var   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">ms</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(161, 100, 75); background: none;">"onChanged propName="</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">propName</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">" newValue = "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">newValue</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">"     ;   oldValue = "</span><span style="color: rgb(167, 167, 167); background: none;"> +   </span><span style="color: rgb(111, 175, 209); background: none;">oldValue</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(0, 137, 255); background: none;">               console.log("数据发送变化" + ms);</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">            });</span><span style="color: rgb(167, 167, 167); background: none;"></span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">        }</span></div><br style="background: none;"><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(41, 111, 169); background: none;">       function   </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(175, 175, 125); background: none;">mySubmit</span><span style="color: rgb(167, 167, 167); background: none;">() { // 提交表单 </span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(41, 111, 169); background: none;">              var     </span><span style="color: rgb(167, 167, 167); background: none;"></span><span style="color: rgb(111, 175, 209); background: none;">formJson</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">vm</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">getJson</span><span style="color: rgb(167, 167, 167); background: none;">();</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;"></span>              console.log("执行提交 <span style="color: rgb(111, 175, 209); background: none;">formJson</span><span style="color: rgb(167, 167, 167); background: none;"></span>" ....... &gt;   );</div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">        }</span></div></pre></span></div><div id="k_2615530103U2O4QZIUH2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2615530104NYQR43TZHB">​</span></div><div id="k_26153131835FPUHWXO5H" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26153131844YGUA6RM14">​</span></div><div id="k_26153132432G5P5ZPYX5" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26153132442C2XBP49VR">​</span></div>
</div>