<div style="position: relative;">
<div id="k_0514213654GDDSU2TQOV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 241, 255); margin-bottom: 10px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(99, 58, 224); border-image: initial; padding-left: 14px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 241, 255);" id="k_05142136543ORORCTP8V">介绍</span></div><div id="k_0514232759ZRMDC8R9OR" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514330135QLDNIZ8G8M">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0514330138ZMKUE7VMY3"><img id="k_0514330139WZ7BLRJGF1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAaElEQVQ4T2NkIBMwkqmPgVGC4b/ZfyYGa0YGBjZ0Q/7/Y7j+i4Hh4HsGxo/ocowSzP93MDAwuOOy+d9fBptXDIxHRzViCSEaBA7T/0UMjAyxWKPjP8PDv/8YAl4zMF7AiA6yUw7dNQIAWxM7D0xSQzAAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514330141ARGC7H3GHT">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0514330136HVKIZVZX4L">Bui封装了常用的导航菜单，支持自定义scrollbar滚动条样式，可应用于左侧菜单栏。</span></div><div id="k_0514232840DRJDZMHRGX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514232841L8SY8BDID5">​</span></div><div id="k_0514232825PAU2JIRNW7" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 241, 255); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(99, 58, 224); border-image: initial; margin-top: 0px; margin-bottom: 10px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 241, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_051429426883KF4FWZ6M">API及构造参数说明</span></div><div id="k_0514234640N6G1NUWAOT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190405143543PLZ7EY2WQO1D"><tbody><tr id="row_0"><td id="k_0514354262FJFBD1BFR3" tabindex="0" row="0" col="0" w="1295" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1297px; background-color: rgba(0, 0, 0, 0);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354263A3QRQUUJO4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354263A8V8L7VBS6">​var menu =&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514360989SWZG7TJ9EM">new $B.Menu(ul,opts) opts说明</span></p></td></tr><tr id="row_1"><td id="k_0514354272A45NEJJW5W" tabindex="0" row="1" col="0" w="245" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 247px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_05143542733ZLIGSK7S6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354273VA2AM1O9PL">​参数名</span></p></td><td id="k_0514354275M9M8NE1PQR" tabindex="0" row="1" col="1" w="303" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 305px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354275Z6ZDQ9C991"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05143542764T37GSJN4H">​参数值</span></p></td><td id="k_0514354277I7BJX6LTAM" tabindex="0" row="1" col="2" w="743" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 745px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_05143542784A5JXEQM92"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05143542791YV5IRZNPD">​说明</span></p></td></tr><tr id="row_2"><td id="k_05143542807VSKAASCVD" tabindex="0" row="2" col="0" w="245" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 247px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_05143542816H6Q9GOODB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354281X2MEZ8ZGMH">​idField</span></p></td><td id="k_0514354284AU8S3NXDTK" tabindex="0" row="2" col="1" w="303" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 305px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354285JJRRJFEN3F"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354285YKBUJG5122">​默认值：id</span></p></td><td id="k_0514354287MMVDF7LYXA" tabindex="0" row="2" col="2" w="743" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 745px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354288C58P1V8T7W"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354288WAIHLQLLM4">​菜单项的id&nbsp; 字段名&nbsp;&nbsp;</span></p></td></tr><tr id="row_3"><td id="k_0514354290DDH46EBT29" tabindex="0" row="3" col="0" w="245" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 247px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354291YKJPZ7YIB4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05143542927FKXBZTVKP">​textField</span></p></td><td id="k_0514354293DX3P4LPHTI" tabindex="0" row="3" col="1" w="303" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 305px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354294LQSRDGA9XL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05143542943NT8LX9P5U">​默认值：text&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_0514354296TLT5D2HEX9" tabindex="0" row="3" col="2" w="743" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 745px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354296J8QQO6AGHV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354298RO5PD6SX28">​菜单显示的字段</span></p></td></tr><tr id="row_4"><td id="k_05143542997MYSQIC1YH" tabindex="0" row="4" col="0" w="245" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 247px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_05143543005893ZR8G7H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05143543013TKI738XQO">​ellipsis</span></p></td><td id="k_0514354302W6FMKVOPNU" tabindex="0" row="4" col="1" w="303" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 305px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354303VWBJF19RNA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354304LLK62K2CGK">​默认值：false&nbsp; &nbsp;&nbsp;</span></p></td><td id="k_05143543059SZGMKGLNZ" tabindex="0" row="4" col="2" w="743" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 745px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354306Z1MMR2F9GW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354307XTLKINZI9Y">​是否省略号模式&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_5"><td id="k_0514354308GQH5GF6M8X" tabindex="0" row="5" col="0" w="245" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 247px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_05143543094ZCT1NKZ59"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354309DCUXWZ91IB">​scrollStyle</span></p></td><td id="k_0514354310EW9VDOX118" tabindex="0" row="5" col="1" w="303" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 305px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354311YMHRZD6MKA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05144409847W5N8XK1CA">​</span></p></td><td id="k_0514354313LDFBCY6RPO" tabindex="0" row="5" col="2" w="743" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 745px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514354313C21BO9VRY3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514354314ALA2C1BRG4">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514441456HZIBH4O2FP">​滚动条样式，请参考滚动条组件说明</span></p></td></tr><tr id="row_6"><td id="k_0514475413LET3IDTL4S" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 247px;" w="245" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514475413QJB8QK78T5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05144754145UH85N5P5I">​onClick</span></p></td><td id="k_0514475416AX9FYYYEN6" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 305px;" w="303" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514475417QDA8PH8G9U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_051447541895SS8K5VIW">​默认值：undefined</span></p></td><td id="k_0514475420EO42BJPVWH" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 745px;" w="743" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0514475420GUXTPR56MF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514475421LOUPK2PLP7">​菜单点击事件= function(data,isParent){}</span></p></td></tr></tbody></table></div><div id="k_05142346121NGELT3XXV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_051423461449VAN2EUAQ">​</span></div><div id="k_0514234602UKCA1J9FFO" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(240, 241, 255); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(99, 58, 224); border-image: initial; margin-top: 0px; margin-bottom: 10px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 241, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0514294851H4C6POXMBW">Demo</span></div><div id="k_051423208688DDCZECQ7" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0514495025FCK5LL45LI">一、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0514232087ZEVZ27FOBM">​定义ul标签</span></div><div id="k_05144536293MEMF77MGF" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 66px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">      </span><span style="background: none; color: rgb(83, 83, 83);">&lt;</span><span style="background: none; color: rgb(41, 111, 169);">div</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">id</span><span style="background: none; color: rgb(167, 167, 167);">=</span><span style="background: none; color: rgb(161, 100, 75);">"m2"</span><span style="background: none; color: rgb(167, 167, 167);">  </span><span style="background: none; color: rgb(111, 175, 209);">style</span><span style="background: none; color: rgb(167, 167, 167);">=</span><span style="background: none; color: rgb(161, 100, 75);">"</span><span style="background: none; color: rgb(161, 100, 75);">width:100%;height:100%"</span><span style="background: none; color: rgb(83, 83, 83);">&gt;</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">            &lt;ul id="menu"&gt;&lt;/ul&gt;<br style="background: none;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">       </span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></div></pre></span></div><div id="k_0514455183RLNC1ZM39J" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514455184G3U37RPCA6">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514455185FBCX46N3XX">​</span></div><div id="k_0514453823G6KE4XMN6A" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0514473271BHXMM8R5HD">二、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0514453824T7S44QWC7P">创建实例对象</span></div><div id="k_0514454620I3P4LQP7JL" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 69px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514454621XDKOVI72XI"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">            </span><span style="background: none; color: rgb(41, 111, 169);">new</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(33, 156, 131);">$B</span><span style="background: none; color: rgb(167, 167, 167);">.</span><span style="background: none; color: rgb(33, 156, 131);">Menu</span><span style="background: none; color: rgb(167, 167, 167);">(</span><span style="background: none;"><font>  <span style="color: rgb(255, 0, 5);">$("#menu")</span>  </font></span><span style="background: none; color: rgb(167, 167, 167);">,{</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">data:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(255, 0, 5); background: none;">MenuData</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">ellipsis:</span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: rgb(175, 175, 125); background: none;">onClick</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">,</span><span style="color: rgb(111, 175, 209); background: none;">isParent</span><span style="color: rgb(167, 167, 167); background: none;">){</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"#msg2"</span><span style="color: rgb(167, 167, 167); background: none;">).</span><span style="color: rgb(175, 175, 125); background: none;">html</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"导航菜单2 你点击了："</span><span style="color: rgb(167, 167, 167); background: none;"> +</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">menuText</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            }});</span></div></div></pre></span></div><div id="k_0514454864GIYVXK1E37" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514454865WCAFQYJ4UH">​</span></div><div id="k_05142320747WZP4JLNZS" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0514232075FKQIW7NBK6">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_0514515654K5C2SCU71T" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 300px; height: 34px; position: absolute; top: 632px; left: 332px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_0514515655DYZ6BOD6HW" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;"><div id="k_051451565674HGHRSJKF" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background: none;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background: none;" id="k_0514522147I9NJH1BDH8">MenuData : 请参考树组件数据格式</span></div></div></div>
</div>