<div style="position:relative">
<div id="k_24130601553DL5QCLZCG" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(3, 118, 240); border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(5, 252, 5); border-image: initial; padding-left: 12px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240);" id="k_2413143711BCXKN797X8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2413143713ONJH2AFXEN"><img id="k_2413143714WZCUL9QLFQ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA2UlEQVQoU63SoUqEURDF8d8gRkGwWVywadSywWb3CTSIwSBi1RewCqtNUDD4ErtZ3GC3GYxuscvIhfvB58e6bvCGe2GY/5kzhxvqycwN3GAHC7X8heuIOGv6yhvlqsAdtvAaEZu1PsRbRBz9gFrAOsZYmwd6QQ8X2Ea/AxW7i40rTCIzP7HUrIbniOhn5h4GWMV9sZiZ+7gs0DvOI+KhFcohTvGIAzzNhDLzuKhhuZvgr5My87bsGBG77cRqmtPtzQt9YKWjOpo5aYqFv+39F3SFk9b/6+pOvgF9WIfo8UJjjgAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240);" id="k_2413143715BQLENGNX13">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240);" id="k_2413144002RJLJVZ8HJS">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240);" id="k_2413144003DLU5AT1CGX"> Demo说明</span></div><div id="k_2413130434ACSMUMEACM" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24131304357G83YEZ2JB">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_2413151206PKM5Z9UAZJ" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 62px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;;" id="k_24131512074WVMNN9RWA"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">            </span><span style="background: none; color: rgb(41, 111, 169);">new</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(33, 156, 131);">$B</span><span style="background: none; color: rgb(167, 167, 167);">.</span><span style="background: none; color: rgb(33, 156, 131);">Ctxmenu</span><span style="background: none; color: rgb(167, 167, 167);">(</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                [{</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">text:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'右键菜单项目1'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-chart-bar'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(175, 175, 125); background: none;">click</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> () {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(175, 175, 125); background: none;">alert</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">text</span><span style="color: rgb(167, 167, 167); background: none;">());</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">items:</span><span style="color: rgb(167, 167, 167); background: none;"> [{</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">text:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'子项目1'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-laptop'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(175, 175, 125); background: none;">click</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> () {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                            </span><span style="color: rgb(175, 175, 125); background: none;">alert</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">text</span><span style="color: rgb(167, 167, 167); background: none;">());</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span><span style="color: rgb(167, 167, 167);">]</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }, </span><span style="color: rgb(167, 167, 167);"> {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">text:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'右键菜单项目4'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-doc-text'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(175, 175, 125); background: none;">click</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> () {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(175, 175, 125); background: none;">alert</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">text</span><span style="color: rgb(167, 167, 167); background: none;">());</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }]);</span></div></div></div></pre></span></div><div id="k_24131447618T7T9YNBSS" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;;" id="k_2413144762KAAON311HY">​</span></div><div id="k_24131316751TN2RDTEV9" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24131316772LLE655N66">​</span></div><div id="k_2413130422AUO6EGSX8A" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(3, 118, 240); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(5, 252, 5); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2413150861UQHMXYCSAE">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_24131508645YLLEHMFNT">​<img id="k_2413150865H9GXBXOIKK" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA2UlEQVQoU63SoUqEURDF8d8gRkGwWVywadSywWb3CTSIwSBi1RewCqtNUDD4ErtZ3GC3GYxuscvIhfvB58e6bvCGe2GY/5kzhxvqycwN3GAHC7X8heuIOGv6yhvlqsAdtvAaEZu1PsRbRBz9gFrAOsZYmwd6QQ8X2Ea/AxW7i40rTCIzP7HUrIbniOhn5h4GWMV9sZiZ+7gs0DvOI+KhFcohTvGIAzzNhDLzuKhhuZvgr5My87bsGBG77cRqmtPtzQt9YKWjOpo5aYqFv+39F3SFk9b/6+pOvgF9WIfo8UJjjgAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_24131508668Q96AQO5CD">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2413150862FFKMQWUA4I">&nbsp; 参数说明</span></div><div id="k_2413092327Z2BCVDJXUW" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><table style="border-collapse: collapse; width: auto; height: auto; " id="2019032413173293SPLPZEMKSP"><tbody><tr id="row_0"><td id="k_2413173248Z2HUCGCTML" tabindex="0" row="0" col="0" w="420" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 422px; background-color: rgb(3, 118, 240); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(3, 118, 240); color: rgb(255, 255, 255);" id="k_24131732499W5MKRNWC1"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240); text-align: center;" id="k_2413173250QZEB1O3FZQ">参数​</span></p></td><td id="k_2413173257NHXUYISLHK" tabindex="0" row="0" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 425px; background-color: rgb(3, 118, 240); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(3, 118, 240); color: rgb(255, 255, 255);" id="k_2413173258J91H71R2GN"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240); text-align: center;" id="k_2413173260PGFZL8H5VY">参数值​</span></p></td><td id="k_2413173263B4PR2NX5BU" tabindex="0" row="0" col="2" w="425" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 427px; background-color: rgb(3, 118, 240); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(3, 118, 240); color: rgb(255, 255, 255);" id="k_2413173265R34R4Y9BAZ"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(3, 118, 240); text-align: center;" id="k_2413173266EGVVA4OHI5">说明​</span></p></td></tr><tr id="row_1"><td id="k_2413173270PRBM4SMAMB" tabindex="0" row="1" col="0" w="420" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 422px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241317327218DPO2LFO8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413173272A3MS7IZGPT">​​text</span></p></td><td id="k_2413173275MHSSICR2AQ" tabindex="0" row="1" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 425px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2413173276T4TNAEH3ZT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413173278772NTVOMUO">​​菜单项文本</span></p></td><td id="k_2413173280OAVT9A3OAD" tabindex="0" row="1" col="2" w="425" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 427px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24131732818P88672ROP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24131732823V48RFC2CJ">​</span></p></td></tr><tr id="row_2"><td id="k_2413173286A8ELUPE2A1" tabindex="0" row="2" col="0" w="420" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 422px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2413173287S9QWWMRT5A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413173288FM3RPTB4IZ">​​iconCls</span></p></td><td id="k_24131732909RIH68LEPA" tabindex="0" row="2" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 425px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241317329139JFIIMMBA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241317329158RZLUX3BF">​​菜单项图标</span></p></td><td id="k_2413173293K6PGIH4VU5" tabindex="0" row="2" col="2" w="425" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 427px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241317329457DXGAZ18V"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413173295MJC6LP8ZSU">​​fontawesome矢量图标样式</span></p></td></tr><tr id="row_3"><td id="k_2413173296M63Z5W83UZ" tabindex="0" row="3" col="0" w="420" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 422px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2413173297LXC85WORVQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413173299BBJBEOO9MO">​​click</span></p></td><td id="k_2413173201BMRO71LQKD" tabindex="0" row="3" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 425px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241317320128R4X9K2JM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413173202W54GZJLNFD">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413192926MNXHQ7ISKC">​click = function(){}</span></p></td><td id="k_241317320423TRZU5U9I" tabindex="0" row="3" col="2" w="425" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 427px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2413173204G43E1SADUV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413173205A6ODXU6S4D">​​菜单点击事件</span></p></td></tr><tr id="row_4"><td id="k_2413194518G1BW3ZV199" tabindex="0" row="4" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 422px;" w="420" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2413194519GF479897QC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413194519HUMELGBB65">​items</span></p></td><td id="k_2413194521JJP846JXCF" tabindex="0" row="4" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 425px;" w="423" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2413194522XPFX9SZW6T"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24131945237VAXW18QQK">​子菜单项目</span></p></td><td id="k_2413194524EA7X6X182Z" tabindex="0" row="4" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 427px;" w="425" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2413194525EOA7YJK7XB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413194526YJWNEBV6BQ">​</span></p></td></tr></tbody></table></div><div id="k_2413131983YXLPTDMLJD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2413131984Z68C2JKL24">​</span></div><div id="k_2413132120MTHL7SMPBE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24131321218WAQTMFMSA">​</span></div>

</div>