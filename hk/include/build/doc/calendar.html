<div style="position:relative;">
      
<div id="k_01202406125T29MZSIGK" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(242, 243, 255); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(24, 3, 255); border-image: initial; padding-left: 14px; margin-bottom: 10px; margin-top: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(242, 243, 255);" id="k_0120252976R8OSPQJDF5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background: none rgb(242, 243, 255);" id="k_0120252977I4JZI1VXI8">​<img id="k_0120252978PTGBE4PGI5" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABJklEQVQ4T5WSsS9DURjFf+e+DhIj2m5mRouY7BaLRCLpQFmaSLEYpHsjsVjEhD+BxGKyWGwm8R/0tRgwEL33k1d5zVMa7V3P90vOOfeIId84NhM5FjQMl8fm5DgCrgcGM9Atge2BwC4k3tue1Sd0/y84hk3lIk4wpi2w1ERXSTyB5SZgpIXeevOmkGDWjFocqIPaHbAQ2Qqw2fasJRZSOI8VFHEsWDS4DJ71FmqkuorOdhF1gxvvKT+ihwI2Ksc+omLGa9ZiF/xx9A1XI0dJoto5Mg4agT3QRzZKp5ysraxoxp0PLCcuevN3W80WkR6ZsRUHDkHWF0yEIjaP4wwxaXCBZyNGzb/W1fOPFhUdOwY1AqUYnfeb5K8BJGXhKH8GTp/RSz/wC1uVcGxhyq8+AAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(242, 243, 255);" id="k_0120252978M2P9YS78PH">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(242, 243, 255);" id="k_01202529765GS9BHE6BX">介绍</span></div><div id="k_0120243439R8MNVNPPC4" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_012036413464TYK3NHMR">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0120364135G6N8Y7476T">​<img id="k_01203641353W3J231CNO" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABeUlEQVQoU22SMUhbURSGv/89H1roIO1SnIoI7SCUDg7FwRYk0SgdCg8iidSsHTrUxcGt3boJgpNiyBviWvCpg4ZCJx26lRa6ONRSqFMLii/3lBteglXPdu/5v3PP/3NV3al+Bh4Af4FfwKe2tddPBk4OW89aGVdK1bS6hSgBG8AUMOI1hv1AvEkKie9bl1MlrbyStApcmGxRTjXE41xwAbxvFBvLXUhz6dyTUOE2MGhmXwl5jWNDaKgHiVqj2Ej8WfFOfKef/n3gEYYz2btOw+SnBvl6H0/daSEtpefyF7mPOJ94jPHSsDVJPgxv6GcQBRP1yfq3DlDZrczi2JJ0K5/YBFpCK0DkE7TAXiSFZK8DLBwsDGTnWQo87e1tLBk2Ien5NcCL5nfnR525D0L3LyW0CYwZdi9z2Xiz1PzeeaFb5b3yUF+7bw2YuWT4D8aXMAqn65P13/8BXTA+iG9HZ9G0pLtBEByZs2HDHibF5O2NwNXv0AvGGPsHLGeR/NEsfw4AAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120364136F4YJFKZ3NG">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0120364134VZVONFG1RO">支持</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0120313262V49PPBRXG2">常用的4种时间格式格式：yyyy-MM</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0120283540OL4AYWD13R">、yyyy-MM-dd、yyyy-MM-dd hh:mm、yyyy-MM-dd hh:mm:ss。</span></div><div id="k_0120292238R56KFPK79A" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0120365206ZVHQVIMCQ4">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_012036520778JO39Y2AF">​<img id="k_0120365207779YNMQAER" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABhUlEQVQoU23SvUtcQRQF8HPevKcSV3c7EZVdounsEmyUqGilpAuCwvqBNqaIpa0hiP4FgiJZWbYS7UwasRC1DYSAYCGuYqHRkN1EttB9c8J7+gQ/prt37m/unWGYSRa+gmqH+BtAHsQmylw/Pq09nAEtHi1mUoV5CuOiZim8B9ke1Aj66VjzYfikZo+gIseVZLEf1IakEq0ZlPEnCL4LUZADP44cx79EiJnG4iu4dotgk6Btl96Ub29yIFsjZOj2DedrtoOY2TpV+1XFVRJ9EHxRE+GGuAzC3I6ibDKfGO8GywzC8B7A9O2J+C6ZNJ3yAsHOuy77oNczlo+dhSDbVHhjDTYBJMIDLeZI7ljYNZIvIFwax+1NH8V+hGAR8ipTf5cAjUZzA0iDeEtw6gkIinINpcayd70OoO0ekZ8pdYBsrqDbNXQUOw87RCuT+pMg8EngJAHvLv9Lwm51KT4ycMGrByCC31pUeXbz77WB4r504MCpF/Ry9KQ29yx4/B2ih/GNWv8D1hylGlx6FwMAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_01203652084ULTSBABHG">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0120365206QWA6Z51LPX">支持固定显示定义，支持初始化默认值。</span></div><div id="k_0120295106TDR2NC1H37" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 46px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0120370748GLHCSO88XB">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0120370749P9X371C3FK">​<img id="k_012037074923EH7RSJGU" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABWklEQVQoU3WSP0ibURTFf+c9KThkkcZPoRAKLq1jnRyka3E1lFJiOxQKurSFDBVURLBdUgcXZwUXhywZOnQROxaDo1MbCoaUOIV0yntX8uUL/kHv9h7n9865910lznYFzxFNjD8mfrrAjyb8BUVulRJnH4E3JioyXki86msMaiGw3oY6yIac8tic9xxhNIi8xVFELKUCoxEjn/5BdQhpEitEz4FgFmOvF9nynopgfgj1Igtt9Kt/Ftho4tiWeG9Gh0gpFTr2JXKpkfGlFVkFBfUv+n1IfMuyVy1Qdo41xGKW/Xs3UOqgdgpMYNM4DhFPshc/xMiJd+wjCmachsjLNjpLAbCRxLEhsXKt2XfO8Qzx9Q4AxrHEeXaA4hAysSljBvG4G3jdQReZw8DnIZbzjmVBGTGWgXWDYx/5fI7+3wCuPtUe5OEpkAvw20PewaMW1O4Bbi/EYDAGU5fxN4NyDLg7CAAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0120370749ZJRL6N8D9N">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0120370748IN31GMVPOV">支持时间范围定义，支持单列模式，当前页面只需要一个Calendar实例即可。</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;">​</span></div><div id="k_01202507468MVQEJQZQW" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120250746CG96RXSI9Y">​</span></div><div id="k_0120243582ZHH3MCHPYO" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(242, 243, 255); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(24, 3, 255); border-image: initial; margin-top: 0px; margin-bottom: 10px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120253860VS2DAXKRFQ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0120253861XYZGLROQWU">​<img id="k_0120253861HECRJZZINI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABJklEQVQ4T5WSsS9DURjFf+e+DhIj2m5mRouY7BaLRCLpQFmaSLEYpHsjsVjEhD+BxGKyWGwm8R/0tRgwEL33k1d5zVMa7V3P90vOOfeIId84NhM5FjQMl8fm5DgCrgcGM9Atge2BwC4k3tue1Sd0/y84hk3lIk4wpi2w1ERXSTyB5SZgpIXeevOmkGDWjFocqIPaHbAQ2Qqw2fasJRZSOI8VFHEsWDS4DJ71FmqkuorOdhF1gxvvKT+ihwI2Ksc+omLGa9ZiF/xx9A1XI0dJoto5Mg4agT3QRzZKp5ysraxoxp0PLCcuevN3W80WkR6ZsRUHDkHWF0yEIjaP4wwxaXCBZyNGzb/W1fOPFhUdOwY1AqUYnfeb5K8BJGXhKH8GTp/RSz/wC1uVcGxhyq8+AAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120253861BNT4RDZSLO">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(242, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_01202538605G9XGG3IVQ">API及构造参数说明</span></div><div id="k_0120244992FT94H12GQ3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" contenteditable="false"><table style="border-collapse: collapse; width: auto; height: auto; " id="201904012038315NGNG87OKI8Y"><tbody><tr id="row_0"><td id="k_0120383150FJ68GVTH82" tabindex="0" row="0" col="0" w="1291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1293px; color: rgb(255, 255, 255); background-color: rgb(165, 214, 156);" colspan="3" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; color: rgb(255, 255, 255); background-color: rgb(165, 214, 156);" id="k_01203831512WEWHMQZSG"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(165, 214, 156);" id="k_012038315233SQFYWEXM">​var ins =&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(165, 214, 156);" id="k_0120395115X5YHSQLN9K">new $B.Calendar(&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(165, 214, 156);" id="k_0120395115Q6AP2I8HMQ">jqObj</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(165, 214, 156);" id="k_0120400863SL83UK6J5D">,</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(165, 214, 156);" id="k_0120400863JSG1RPNW75">opt</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(165, 214, 156);" id="k_012040086344CCQXFGVZ">s )&nbsp; opts参数说明</span></p></td></tr><tr id="row_1"><td id="k_0120383156FCRD7BDS1J" tabindex="0" row="1" col="0" w="155" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383156IYVXIBGEVQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383156T58ZIR2JMZ">​参数名称</span></p></td><td id="k_0120383158TRD8B1MVCO" tabindex="0" row="1" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383158YYOTGIAZTT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383158TVH5MW9M54">​参数值</span></p></td><td id="k_0120383159AA19RROV8Q" tabindex="0" row="1" col="2" w="831" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383159SPWQGZIUMQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383159DB36F5SE6J">​说明</span></p></td></tr><tr id="row_2"><td id="k_01203831975EDT1HY6EK" tabindex="0" row="2" col="0" w="155" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01203831975E8NFILGVN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01203831971I6654ZAC1">​show</span></p></td><td id="k_0120383199CNX4KUZ799" tabindex="0" row="2" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383199HBUEIB61JP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120472728D3QHMXZ35W">默认值：&nbsp; &nbsp; false&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120472728ABDOQ8ZOY6">&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120425875OYL2WHO7PM">&nbsp;&nbsp;</span></p></td><td id="k_0120383101G3HCHV9LDV" tabindex="0" row="2" col="2" w="831" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383101YG34DU5INA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383101XDP5LASP29">​是否马上显示</span></p></td></tr><tr id="row_3"><td id="k_0123293411BQ6IO8ZYMN" tabindex="0" row="3" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" w="155" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0123293411KGIQXWLCKT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01232934125N4A24AOBL">​clickHide</span></p></td><td id="k_01232934141Z5IIMUB2H" tabindex="0" row="3" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" w="301" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0123293415OQUN8I5R5A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0123293416A2LLUZ5D9R">​​默认值：&nbsp; &nbsp; false&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_01232934185UYXMV76I3" tabindex="0" row="3" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" w="831" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0123293419WD65LME35D"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0123293420HC3OKSIHA3">​点击选择事件后，是否自动关闭</span></p></td></tr><tr id="row_4"><td id="k_0212031546ECNUT14YW5" tabindex="0" row="4" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" w="155" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0212031547HAC5JKGXH7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0212031548M4H73J6I2F">​shadow</span></p></td><td id="k_0212031550NNONLO9DLB" tabindex="0" row="4" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" w="301" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_02120315515Q94AA4KHX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0212031552RABUVACSKX">​默认值：&nbsp; &nbsp; true</span></p></td><td id="k_021203155462MTJX5UBW" tabindex="0" row="4" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" w="831" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0212031554NK6X6LGBAI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_02120315559TVU2NPHN3">​是否需要阴影</span></p></td></tr><tr id="row_5"><td id="k_0121424109U9F959IE9F" tabindex="0" row="5" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" w="155" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121424110ZUCUXNOS7T"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121424111RZ6NSLVPH6">​fitWidth</span></p></td><td id="k_0121424113S62K7JWY4T" tabindex="0" row="5" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" w="301" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01214241145N6FEGRS5A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121424114RT3EXAC2CY">​​默认值：&nbsp; &nbsp; false&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_0121424116EQVKSA88BM" tabindex="0" row="5" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" w="831" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01214241173641ZXMNYQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121424117JD654KUL92">​是否将input宽度和 控件宽度调整一致</span></p></td></tr><tr id="row_6"><td id="k_01203831023Q3VD6X9X8" tabindex="0" row="6" col="0" w="155" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01203831026MMB4W5D11"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383102CW9FGLD3VB">​fixed</span></p></td><td id="k_0120383104RBI27S7B9N" tabindex="0" row="6" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383104RUNZI717CF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383104THNOPZK2TV">​​默认值：&nbsp; &nbsp; false&nbsp;&nbsp;</span></p></td><td id="k_0120383105VB11W97DEF" tabindex="0" row="6" col="2" w="831" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383105ITMM1ABO16"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01203831054QSEC9GDUY">​是否固定显示，&nbsp; &nbsp; 当设置了fixed，show会无效</span></p></td></tr><tr id="row_7"><td id="k_01203831066FX6MHMCJ3" tabindex="0" row="7" col="0" w="155" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383106UC7TPPLY2M"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383106EOCXQK3NZS">​initValue</span></p></td><td id="k_0120383108MMS3T6W7NV" tabindex="0" row="7" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383108DQPCZKP59R"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383108DZEDPE5A5A">​默认值：&nbsp; &nbsp; undefined</span></p></td><td id="k_01203831098NJBK3FDGQ" tabindex="0" row="7" col="2" w="831" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_012038310919XMXLP75I"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383109KMJWWBAWKO">​初始化时间值：&nbsp;&nbsp;&nbsp;&nbsp;可以是date对象，可以是时间格式的字符串，设置该值会覆盖input标签上的value值</span></p></td></tr><tr id="row_8"><td id="k_01203831101JFR2DY3TJ" tabindex="0" row="8" col="0" w="155" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383110L3ISMRMR9Z"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383110UF77372QNK">​fmt</span></p></td><td id="k_0120383112Z1L5LPLNNH" tabindex="0" row="8" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383112GYJJ1VCRCL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383112SXSN4SGTKB">​默认值：&nbsp;&nbsp;&nbsp;&nbsp;yyyy-MM-dd hh:mm:ss&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_01203831136332P4W4LN" tabindex="0" row="8" col="2" w="831" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01203831138QQ79ET8KE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383113B5WEMQTFKF">​格式，支持: yyyy-MM、yyyy-MM-dd、yyyy-MM-dd hh:mm、yyyy-MM-dd hh:mm:ss&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_9"><td id="k_0120383115S5LXLR1YOM" tabindex="0" row="9" col="0" w="155" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01203831151793CRBKB7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383115TIJEBSJ1BG">​readonly</span></p></td><td id="k_0120383121CAW5ENWM69" tabindex="0" row="9" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120383121U64GCGOMPN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383121J2UN5KJJQC">​默认值：&nbsp; &nbsp; true&nbsp; &nbsp;&nbsp;</span></p></td><td id="k_0120383122TFT1BK6RMD" tabindex="0" row="9" col="2" w="831" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01203831229NEB57J6LI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120383122TBPUXDKM7V">​是否只读模式</span></p></td></tr><tr id="row_10"><td id="k_0120470989EV7UUMF3LX" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" w="155" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120470989ZPI3C9MYEF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120470989X1XD2OC8IZ">​isSingel</span></p></td><td id="k_0120470991CGLZIK7LSF" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" w="301" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120470991RUGQIRGFFT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120470991NHHF9WZF7T">​​默认值：&nbsp; &nbsp; false&nbsp;</span></p></td><td id="k_01204709922PHVPSS6WA" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" w="831" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120470992T3O76OLR9E"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120470992NDWNBVBUKE">​是否单列模式，单列模式下仅实例化一个calendar，各个空间之间互斥显示</span></p></td></tr><tr id="row_11"><td id="k_0120482988P5LR2W89ZH" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 157px;" w="155" h="103" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01204829899ZIRMPNWJ5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120482989ZNSZF91YHB">​range</span></p></td><td id="k_01204829907JHZ3LRUUN" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 303px;" w="301" h="103" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120482990A1HAKGEIYR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120482990CIB5WTX6AG">​默认值：&nbsp;&nbsp;&nbsp;&nbsp;{&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_01204905041174C18HBL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120490504AY9NLT7VE3">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; min: undefined,&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_0120490898GF494UOANG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01204908992R7Z6K5OYC">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; max: undefined&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_0120491054Z9Z8FB2PP1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120491054ADN8ZDGPWX">}</span></p></td><td id="k_0120482992YHSMMVGA5W" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 105px; width: 833px;" w="831" h="103" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_012048299272YXQL1XGP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120482992GQ8WV88HRO">​&nbsp; &nbsp; 时间范围限制定义，min/max可以取值：</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_01205028927151BBFW8M"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120503859OHH6LVB62A">​&nbsp; &nbsp; 1）</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01205028923BH6KR6PMP">"now"表示当前时间</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_0120503863RZ9N3NUJ64"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120511695OWH57QL1I7">​&nbsp; &nbsp; 2）</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_012050386334BI8ZO342">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120503863MAHML74U7P">​"#inputId"表示另一个时间input标签控件的ID</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_0120511699TMEILFXVNO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120511600ML3SYLUDE9">​&nbsp; &nbsp; 3）"2019-01-01 11:11:11"时间格式的字符串值</span></p></td></tr><tr id="row_12"><td id="k_0120521546JEAL6O8TX5" tabindex="0" row="12" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 157px;" w="155" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120521547ZS3IIUEP6A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_012052154751FRM3KHA5">​onChange</span></p></td><td id="k_01205215485WFTXLLF26" tabindex="0" row="12" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" w="301" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120521548MDGCGLC7MH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120521548OLQLS7EYBX">​默认值：&nbsp; &nbsp; undefined&nbsp; &nbsp;&nbsp;</span></p></td><td id="k_012052154952UO2LAM8X" tabindex="0" row="12" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 833px;" w="831" h="28" class="k_box_size"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0120521549KC6WTHLQJ5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120521549KJJ9MUNHZR">​onChange=function( date,dateString&nbsp; &nbsp; ){}&nbsp; date: Date实例对象，dateString：字符串时间</span></p></td></tr></tbody></table></div><div id="k_0120244965UV3EZTOFIR" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120244966DD6A6DE1KN">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_0121072699TO3LEPDWW6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 42px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" contenteditable="false"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190401210832U3K2SDKSBD8J"><tbody><tr id="row_0"><td id="k_0121083248QBPGCOXEGN" tabindex="0" row="0" col="0" w="1293" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1295px; background-color: rgb(165, 214, 156); color: rgb(255, 255, 255);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(165, 214, 156); color: rgb(255, 255, 255);" id="k_0121083249LAZTVOCQYD"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(165, 214, 156);" id="k_0121083250DA15DC9PHF">​实例API</span></p></td></tr><tr id="row_1"><td id="k_0121085946ZNUZ352VDY" tabindex="0" row="1" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 160px; background-color: rgba(0, 0, 0, 0);" w="158" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121085947FNVHDY8UYR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121085948DLDWGREITG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121093975BV9HTWOMZN">setPosition()</span></p></td><td id="k_0121085951M14JI4W5ZF" tabindex="0" row="1" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px; background-color: rgba(0, 0, 0, 0);" w="301" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121085951EM8IJ7YWD7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121085952AVYHNCXDWA">​</span></p></td><td id="k_0121085954Y3FB45NB6T" tabindex="0" row="1" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 832px; background-color: rgba(0, 0, 0, 0);" w="830" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121085955JCA8HGQPWF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121085955XIZZZYYPXY">​根据input标签位置重新设置位置</span></p></td></tr><tr id="row_2"><td id="k_0121083259I4SEF1O3YX" tabindex="0" row="2" col="0" w="158" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 160px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121083260N4Y4HEW9WN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083260VW8KYBJ87B">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121101384MRMHJZ5RFK">slideToggle()</span></p></td><td id="k_0121083262CXRGW5JTS3" tabindex="0" row="2" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121083263744MZAO34Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083263MT1LAC637U">​</span></p></td><td id="k_0121083265ENHHZK897E" tabindex="0" row="2" col="2" w="830" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 832px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121083266S5PYQBM9PW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083266DN4T8NMCIU">​显示/隐藏</span></p></td></tr><tr id="row_3"><td id="k_0121083268JTGFXHQNBF" tabindex="0" row="3" col="0" w="158" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 160px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_012108326928EIC6RRF1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083269KJ1MT97IYD">​show()</span></p></td><td id="k_0121083271S4PMVEY2DO" tabindex="0" row="3" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121083272WRFY34UZYE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01210832721CTZFYF9PH">​</span></p></td><td id="k_0121083274TAZ712E5TJ" tabindex="0" row="3" col="2" w="830" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 832px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01210832754ZC4XI37N9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083276M69L7FSBRK">​显示</span></p></td></tr><tr id="row_4"><td id="k_0121083277QRW42J7UIQ" tabindex="0" row="4" col="0" w="158" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 160px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01210832785HRAVTY96U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083279Q8Q2UTHM7G">​hide()</span></p></td><td id="k_0121083280AR12PW5K9B" tabindex="0" row="4" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_01210832818LROI1NAVZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083281H2E8MTJG54">​</span></p></td><td id="k_0121083283XGKWR9YM14" tabindex="0" row="4" col="2" w="830" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 832px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121083284TGD9LBVBUG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083284HPKZMBWSGG">​隐藏&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_5"><td id="k_0121083286YJWZW5QYHD" tabindex="0" row="5" col="0" w="158" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 160px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121083287BUMD2RHLVE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083287JHDR3IQ4MU">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121110202MQV49NETJR">setValue(date)</span></p></td><td id="k_0121083289UN1VQQKKOZ" tabindex="0" row="5" col="1" w="301" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 303px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121083290NW2P6GCX6P"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01210832902EF36POHTU">​date ： Date对象实例 / 时间格式的字符串</span></p></td><td id="k_0121083292XZVYK5A1WE" tabindex="0" row="5" col="2" w="830" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 832px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_0121083293MVV7TSK3OA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121083293LP7R7MN6BT">​设置时间值</span></p></td></tr></tbody></table></div><div id="k_0120244972H8I51R3MIB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01202449727ZYHJXLTVE">​</span></div><div id="k_01202450309PQ7DF76UV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(242, 243, 255); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(24, 3, 255); border-image: initial; margin-top: 0px; margin-bottom: 10px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120254614MYR1CZDFHP">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0120254615XB78Y8G7OQ">​<img id="k_0120254615EC3ZTEBIZM" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABJklEQVQ4T5WSsS9DURjFf+e+DhIj2m5mRouY7BaLRCLpQFmaSLEYpHsjsVjEhD+BxGKyWGwm8R/0tRgwEL33k1d5zVMa7V3P90vOOfeIId84NhM5FjQMl8fm5DgCrgcGM9Atge2BwC4k3tue1Sd0/y84hk3lIk4wpi2w1ERXSTyB5SZgpIXeevOmkGDWjFocqIPaHbAQ2Qqw2fasJRZSOI8VFHEsWDS4DJ71FmqkuorOdhF1gxvvKT+ihwI2Ksc+omLGa9ZiF/xx9A1XI0dJoto5Mg4agT3QRzZKp5ysraxoxp0PLCcuevN3W80WkR6ZsRUHDkHWF0yEIjaP4wwxaXCBZyNGzb/W1fOPFhUdOwY1AqUYnfeb5K8BJGXhKH8GTp/RSz/wC1uVcGxhyq8+AAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0120254615LGLDO9X612">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(242, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_01202546145EPQDKIRLP">Demo</span></div><div id="k_0120242997GYLKYFNLUU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 39px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121135517GTVZYYHR73">​1、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121161011G7N8E95HK6">定义input标签</span></div><div id="k_01211355298V4FIM9MMD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121135512QXEE7P2QLS"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">input</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"text"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"<span style="color: rgb(255, 0, 5); background: none;">_time_sc</span>"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">value</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">""</span><span style="color: rgb(83, 83, 83); background: none;">/&gt;</span></div></pre></span></div><div id="k_0121135851VP6FHUDD6Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 39px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121135853XPMP73HVH1">​</span></div><div id="k_01211355202QVBTD48Z4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 39px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01211401614PXNYLLTXF">​2、创建Calendar实例</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121135521XBPPYV1C8J"></span></div><div id="k_0121140170RO9T9N8S8T" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121140171Q8A2B3GKFU"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">            </span><span style="background: none; color: rgb(41, 111, 169);">var</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">now</span><span style="background: none; color: rgb(167, 167, 167);"> = </span><span style="background: none; color: rgb(41, 111, 169);">new</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(33, 156, 131);">Date</span><span style="background: none; color: rgb(167, 167, 167);">();</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">c3</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(41, 111, 169); background: none;">new</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(33, 156, 131); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(33, 156, 131); background: none;">Calendar</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"<span style="color: rgb(255, 0, 5); background: none;">#_time_sc</span>"</span><span style="color: rgb(167, 167, 167); background: none;">),{</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">fmt:</span><span style="color: rgb(161, 100, 75); background: none;">'yyyy-MM-dd hh:mm:ss'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">readonly:</span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">show:</span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">initValue:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">now</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            });</span></div></div></pre></span></div><div id="k_0121140395MZPQIF6Q3A" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 39px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0121140396TX7PHSKKEW">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_0123292902YR6S2FYHZ2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_01232929036IKMH9A48S">​</span></div><div id="k_01234220176QEH3H1T26" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0123422018WLZC1AGCKV">​</span></div><div id="k_021203082566FV9V64O5" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0212030826YFVLIDHJGF">​</span></div>
</div>