<div style="position:relative">
<div id="k_2621072197YZS7X6MR5L" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(235, 243, 255); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(0, 235, 227); border-image: initial; margin-bottom: 16px; padding-left: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255);" id="k_2621141648GOTOUGXLA9">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621141650YD8OGNBXVF">​<img id="k_262114165146RLUDM1EY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA6UlEQVQ4T6WSPU4CQRiGn3eIFzDy4xXsuIAHMLGSlooCEyXEA2CCR7E1MaH1PDRLAQEKiGbnM0vYjSwzKwlT7uyT553v/cQZR2ewnARfYdc1xzNgiWcM+smklXAOCR4NLszTmaGvPG0Q3kM9wQvicvez8f7tGczRKgi3sLo5+gdQxhnrsrWIHYOKYQasO7jpbCh4LeKVxp9Z8XQTNCk3oyZ2L8fNwYW4Be723z42Kf0lWhzBoZ4bzkZOvFVZo1XlMBC1/gv7lIcZ+oxtYbDnzCzR3qb0Qm+tXJKWsyfzTEMT/pvipN2Oxf4F1eVYEFF+XvgAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255);" id="k_2621141652JVZKM6MDZ7">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255);" id="k_2621142362XANLGCPMP6">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255);" id="k_2621142363FPT3RQSU9U">介绍</span></div><div id="k_2621073181ODKNISW1X9" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2621182761QV7TUGJMWN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_26211827631QV21B2ODZ">​<img id="k_2621182764J1HUAY6CKW" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABGUlEQVQ4T6WSsUoDYRCEv7lLIdiql1JsLAQfwUL0AQSLILFUO1FsxEZsxE4QBDVtimAtCCI+hIVWBstcUoiFkML7R4IoEmMumC2X/XaHmRVDlIZgyYGtBGYCtFvwBPLPY33hCTwbRdQs6u2M1Vf0MhCc4FEijiXWbLbSwMnAl5PYJcwF4lYZGw3U6vanp+winnRMFTNFoJyiu17GagIvCkZSuAa9gwtJxIHEns1hGtj/7P8uJbGvMHMOLDfRTYLniagi6sooN9DzX3F24BXMGeJBGbuO2cQsINbTTLV+f6COzGLEDuLoa9CmQmA7RW85MBTxuGPOBUuYxxAoNdF93vd9uz2GpwsxFcxlI3DanWlPw/K258r+74IP9FhgEObdW3YAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2621182766SSWEGUDN3W">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_26211827622CPXKL7BGH">采用JSON-API书写方式&nbsp;</span></div><div id="k_26211709744NLAQLWR5I" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2621183480XFNLHGUAVI">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_26211834826O4KPHBNTW">​<img id="k_26211834829O3SNQYN49" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABGUlEQVQ4T6WSsUoDYRCEv7lLIdiql1JsLAQfwUL0AQSLILFUO1FsxEZsxE4QBDVtimAtCCI+hIVWBstcUoiFkML7R4IoEmMumC2X/XaHmRVDlIZgyYGtBGYCtFvwBPLPY33hCTwbRdQs6u2M1Vf0MhCc4FEijiXWbLbSwMnAl5PYJcwF4lYZGw3U6vanp+winnRMFTNFoJyiu17GagIvCkZSuAa9gwtJxIHEns1hGtj/7P8uJbGvMHMOLDfRTYLniagi6sooN9DzX3F24BXMGeJBGbuO2cQsINbTTLV+f6COzGLEDuLoa9CmQmA7RW85MBTxuGPOBUuYxxAoNdF93vd9uz2GpwsxFcxlI3DanWlPw/K258r+74IP9FhgEObdW3YAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2621183484TNDU1ZK3WP">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2621183480IUU5OR9HU4">内置14种常用验证&nbsp;</span></div><div id="k_2621171376VJVDWHHOEN" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2621184315LJ7WEH49JH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621184317XPBTN9M5YU">​<img id="k_2621184318WN85SP56VF" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABGUlEQVQ4T6WSsUoDYRCEv7lLIdiql1JsLAQfwUL0AQSLILFUO1FsxEZsxE4QBDVtimAtCCI+hIVWBstcUoiFkML7R4IoEmMumC2X/XaHmRVDlIZgyYGtBGYCtFvwBPLPY33hCTwbRdQs6u2M1Vf0MhCc4FEijiXWbLbSwMnAl5PYJcwF4lYZGw3U6vanp+winnRMFTNFoJyiu17GagIvCkZSuAa9gwtJxIHEns1hGtj/7P8uJbGvMHMOLDfRTYLniagi6sooN9DzX3F24BXMGeJBGbuO2cQsINbTTLV+f6COzGLEDuLoa9CmQmA7RW85MBTxuGPOBUuYxxAoNdF93vd9uz2GpwsxFcxlI3DanWlPw/K258r+74IP9FhgEObdW3YAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2621184319SIK8JULZHG">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2621184316P1J719CZ4X">支持远程验证，支持自定义正则表达式验证</span></div><div id="k_26210732719YCI8JNVIZ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621073272B589OJJQSA">​</span></div><div id="k_26210732519EE8Z74LBO" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(235, 243, 255); padding-left: 18px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(0, 235, 227); border-image: initial; margin-top: 0px; margin-bottom: 16px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_2621144050I5CJCT8RK5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_26211440537HU4TF2WEE">​<img id="k_2621144053176FPHLPW7" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA6UlEQVQ4T6WSPU4CQRiGn3eIFzDy4xXsuIAHMLGSlooCEyXEA2CCR7E1MaH1PDRLAQEKiGbnM0vYjSwzKwlT7uyT553v/cQZR2ewnARfYdc1xzNgiWcM+smklXAOCR4NLszTmaGvPG0Q3kM9wQvicvez8f7tGczRKgi3sLo5+gdQxhnrsrWIHYOKYQasO7jpbCh4LeKVxp9Z8XQTNCk3oyZ2L8fNwYW4Be723z42Kf0lWhzBoZ4bzkZOvFVZo1XlMBC1/gv7lIcZ+oxtYbDnzCzR3qb0Qm+tXJKWsyfzTEMT/pvipN2Oxf4F1eVYEFF+XvgAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_2621144054AHUJ5BS4K3">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_26211447195JS8JNDZ2R">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_2621144721RHMR8S6F6Y">$B.validate( args , formWrap ) 参数说明</span></div><div id="k_2621080828ZDIPCW6XYP" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 27px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26212601001NU4TDO7DF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_26212601067WNQRVVQYY">​<img id="k_26212601072IBLC9HXPN" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABWklEQVQoU3WSP0ibURTFf+c9KThkkcZPoRAKLq1jnRyka3E1lFJiOxQKurSFDBVURLBdUgcXZwUXhywZOnQROxaDo1MbCoaUOIV0yntX8uUL/kHv9h7n9865910lznYFzxFNjD8mfrrAjyb8BUVulRJnH4E3JioyXki86msMaiGw3oY6yIac8tic9xxhNIi8xVFELKUCoxEjn/5BdQhpEitEz4FgFmOvF9nynopgfgj1Igtt9Kt/Ftho4tiWeG9Gh0gpFTr2JXKpkfGlFVkFBfUv+n1IfMuyVy1Qdo41xGKW/Xs3UOqgdgpMYNM4DhFPshc/xMiJd+wjCmachsjLNjpLAbCRxLEhsXKt2XfO8Qzx9Q4AxrHEeXaA4hAysSljBvG4G3jdQReZw8DnIZbzjmVBGTGWgXWDYx/5fI7+3wCuPtUe5OEpkAvw20PewaMW1O4Bbi/EYDAGU5fxN4NyDLg7CAAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262126010829DS7XACOF">​&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621541498ADE5CP5BO2">f</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26215414999C1FJNCGZ5">ormWrap：</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621231425HP4F67V9DC">表单容器jq对象</span></div><div id="k_26212039862XGJO2W7AG" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 27px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621260906NTR234PWJN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621260908XGXUQCHWEN">​<img id="k_2621260910DG6FXJP9Y2" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABWklEQVQoU3WSP0ibURTFf+c9KThkkcZPoRAKLq1jnRyka3E1lFJiOxQKurSFDBVURLBdUgcXZwUXhywZOnQROxaDo1MbCoaUOIV0yntX8uUL/kHv9h7n9865910lznYFzxFNjD8mfrrAjyb8BUVulRJnH4E3JioyXki86msMaiGw3oY6yIac8tic9xxhNIi8xVFELKUCoxEjn/5BdQhpEitEz4FgFmOvF9nynopgfgj1Igtt9Kt/Ftho4tiWeG9Gh0gpFTr2JXKpkfGlFVkFBfUv+n1IfMuyVy1Qdo41xGKW/Xs3UOqgdgpMYNM4DhFPshc/xMiJd+wjCmachsjLNjpLAbCRxLEhsXKt2XfO8Qzx9Q4AxrHEeXaA4hAysSljBvG4G3jdQReZw8DnIZbzjmVBGTGWgXWDYx/5fI7+3wCuPtUe5OEpkAvw20PewaMW1O4Bbi/EYDAGU5fxN4NyDLg7CAAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26212609115QJKJ4JTP5">​&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621260907VPNWL34LV5">args：字段验证配置</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621211938E9SRI7TFNQ">，如：</span></div><div id="k_262120560875M7U4518J" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 27px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262120560949YT3T8OOW"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">{</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;userName:</span>&nbsp;[{<span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'require'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'wchar'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule :</span>{&nbsp;<span style="color: rgb(111, 175, 209); background: none;">minlength:</span><span style="color: rgb(136, 161, 123); background: none;">2</span>}},{<span style="color: rgb(111, 175, 209); background: none;">rule:</span>{<span style="color: rgb(111, 175, 209); background: none;">remote:</span><span style="color: rgb(161, 100, 75); background: none;">'../Handler.ashx?flag=valid'</span>}}],</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;userPwd:</span><span style="font-size: medium; font-family: Consolas, font-size; color: rgb(167, 167, 167); background: none; white-space: normal;" id="k_2621231428Z8NNFUME3N">&nbsp;[</span><span style="font-size: medium; font-family: Consolas, font-size; color: rgb(167, 167, 167); background: none; white-space: normal;" id="k_26212314296CXU9Q7LQ4">{</span><span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'require'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule :</span>{&nbsp;<span style="color: rgb(111, 175, 209); background: none;">minlength:</span><span style="color: rgb(136, 161, 123); background: none;">6</span>}}],</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;zhName:</span>&nbsp;[{<span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'require'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'chchar'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule :</span>{&nbsp;<span style="color: rgb(111, 175, 209); background: none;">minlength:</span><span style="color: rgb(136, 161, 123); background: none;">2</span>}}]</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">}</div></pre></span></div><div id="k_2621080893LX1HMEXS29" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621080894IW813AZN5M">​</span></div><div id="k_2621084180JVIOKZ1LBJ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621084180NCT4USWNP1">​</span></div><div id="k_2621540266B3O5EMAWD1" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 27px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2621542578ST9JD9YIO3">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_26215425805G5ZY6HVRU">​<img id="k_2621542581T6AKDCKL6Z" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABWklEQVQoU3WSP0ibURTFf+c9KThkkcZPoRAKLq1jnRyka3E1lFJiOxQKurSFDBVURLBdUgcXZwUXhywZOnQROxaDo1MbCoaUOIV0yntX8uUL/kHv9h7n9865910lznYFzxFNjD8mfrrAjyb8BUVulRJnH4E3JioyXki86msMaiGw3oY6yIac8tic9xxhNIi8xVFELKUCoxEjn/5BdQhpEitEz4FgFmOvF9nynopgfgj1Igtt9Kt/Ftho4tiWeG9Gh0gpFTr2JXKpkfGlFVkFBfUv+n1IfMuyVy1Qdo41xGKW/Xs3UOqgdgpMYNM4DhFPshc/xMiJd+wjCmachsjLNjpLAbCRxLEhsXKt2XfO8Qzx9Q4AxrHEeXaA4hAysSljBvG4G3jdQReZw8DnIZbzjmVBGTGWgXWDYx/5fI7+3wCuPtUe5OEpkAvw20PewaMW1O4Bbi/EYDAGU5fxN4NyDLg7CAAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2621542582ZDA3DXKSQ1">​&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2621542579BVCHCOR7M9">验证调用</span></div><div id="k_26215402103VB6AOHCBJ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621540211QH2J26RCD7">​&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></div><div id="k_2621565088I9UQRY7M8H" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621565089VS9IL21ET8"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none; color: rgb(66, 130, 164);">var validObj</span><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;=&nbsp;</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none; color: rgb(41, 111, 169);">new</span><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none; color: rgb(33, 156, 131);">$B</span><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">.</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none; color: rgb(33, 156, 131);">Validate</span><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">({}, formWrap)&nbsp; ;&nbsp; <span style="color: rgb(120, 120, 210); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">//创建验证</span></span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(120, 120, 210); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none; color: rgb(152, 89, 147);"><br></span></span></span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(120, 120, 210); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none; color: rgb(165, 165, 255);">//调用验证</span></span></span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(120, 120, 210); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none; color: rgb(152, 89, 147);">if</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none;">(</span><span style="color: rgb(255, 0, 209); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">validObj.valid</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none;"><span style="color: rgb(255, 0, 209); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none;">()</span>){</span></span></span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(120, 120, 210); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none;">&nbsp; &nbsp; &nbsp;console.log("通过验证！");</span></span></span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(120, 120, 210); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none;">}else{</span></span></span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(120, 120, 210); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none;">&nbsp; &nbsp; &nbsp;console.log("没有通过验证");</span></span></span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(120, 120, 210); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; background: none;">}</span></span></span></div></pre></span></div><div id="k_2621540208YKA11CQJOL" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26215402094TF4USDWO2">​</span></div><div id="k_2621084154959XEXOVXO" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(235, 243, 255); padding-left: 18px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(0, 235, 227); border-image: initial; margin-top: 0px; margin-bottom: 20px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_2621150125QVM7Q21TM7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_262115012895FL6SABA8">​<img id="k_262115012844R5V4YHM6" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA6UlEQVQ4T6WSPU4CQRiGn3eIFzDy4xXsuIAHMLGSlooCEyXEA2CCR7E1MaH1PDRLAQEKiGbnM0vYjSwzKwlT7uyT553v/cQZR2ewnARfYdc1xzNgiWcM+smklXAOCR4NLszTmaGvPG0Q3kM9wQvicvez8f7tGczRKgi3sLo5+gdQxhnrsrWIHYOKYQasO7jpbCh4LeKVxp9Z8XQTNCk3oyZ2L8fNwYW4Be723z42Kf0lWhzBoZ4bzkZOvFVZo1XlMBC1/gv7lIcZ+oxtYbDnzCzR3qb0Qm+tXJKWsyfzTEMT/pvipN2Oxf4F1eVYEFF+XvgAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_26211501297R4O3V1B3H">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_2621151501JNDLNWCO4N">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_2621151502WGRL2DF3MR">内置验证说明</span></div><div id="k_26210842203BFNVC5VT7" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 27px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190326212907UP6Q4HA6N83Q"><tbody><tr id="row_0"><td id="k_2621290651IWVS1QICZ2" tabindex="0" row="0" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290651H22ZWE1IHL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621292651Q5C37VIIYF">​​​必填验证</span></p></td><td id="k_262129065563VRYATVYM" tabindex="0" row="0" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290656VWE21XS8G4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290657WY2OWEYQHP">​{rule : 'require', msg:’自定义信息，有默认，可不要’}</span></p></td></tr><tr id="row_1"><td id="k_2621290658E6WS5B8AEY" tabindex="0" row="1" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26212906596MDJ2P1NQX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290660JZJIK19VEF">​数字验证</span></p></td><td id="k_2621290661YTOA144944" tabindex="0" row="1" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290662UIX7H61BHL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290662FECT5LFA48">​{rule : ‘number’, msg:’自定义信息，有默认，可不要’}</span></p></td></tr><tr id="row_2"><td id="k_2621290664CJ2MGBXR3W" tabindex="0" row="2" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290664J6N46OJ3RM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26212906658IRPHW9EJ8">​整数验证</span></p></td><td id="k_2621290666GC3PSMFYQI" tabindex="0" row="2" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290667BXKPZ1EC2B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26212906683TYIAOW55H">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621304231643RNXHSU1">{rule : 'digits', msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_3"><td id="k_2621290669WXO5C698LN" tabindex="0" row="3" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290670FE3M4J1I46"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290672UA4FI4Q9LS">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621305093SKQR1YFCAM">手机号验证&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290674AZ8J3A5M5E" tabindex="0" row="3" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26212906757NGA8MOOEW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26212906755RV24QH6KS">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26213059642PD7MRXMIH">{rule : 'phone', msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_4"><td id="k_2621290677SG9VHJ3W5E" tabindex="0" row="4" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290678RA7U42SO68"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290678NH4IC3DWAH">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621310939LARSX9RKXG">座机号验证&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290679NUMYM388PU" tabindex="0" row="4" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290680NPY2QV6MFR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262129068116RDQ76D7A">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621311872EC8442WDSM">{rule : 'telphone', msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_5"><td id="k_2621290682E3BOG6LZKG" tabindex="0" row="5" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290683Y5RFWN4CYB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290684DQ8S9VLAWW">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621313397WCBJNATUWS">电子邮件验证&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290686J8IQSTWNZH" tabindex="0" row="5" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26212906863YP5H4GYP3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290687DK3LPTAMJA">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621314747V5SLPG6NBK">{rule : 'email', msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_6"><td id="k_2621290688W8ZLA9QTNQ" tabindex="0" row="6" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26212906893O7J8OU66J"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290690EG1YFN717M">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26213154098RA9G4MWH4">中文验证&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290691L4GHPJB8JD" tabindex="0" row="6" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290692ON54QPL7SB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262129069361477CEP4R">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26213201255O3TZ1C7O7">{rule : 'chchar', msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_7"><td id="k_2621290694T8YUUKUZ14" tabindex="0" row="7" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290695IAPCFQRADX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290695T19NH2DKGE">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621321273VIZUWY3Q8D">英文字符验证&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290696W6IJSEN3H5" tabindex="0" row="7" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290697NBLPV658ZK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290698UHIP25U9WI">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26213222398H6TZUPUSU">{rule : 'enchar', msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_8"><td id="k_2621290699F45Z8MYFLR" tabindex="0" row="8" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290700E15ZR7PAFU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26212907001DXHN6LLQ9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621323488828X8V9VOQ">URL验证&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290702DM7ZDIVVDA" tabindex="0" row="8" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290703Z4RQGOBTIU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290703Q9RO5IJXKF">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621324296HV18XGMUOT">{rule : 'url', msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_9"><td id="k_2621290705R1I6CJABN7" tabindex="0" row="9" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290706S38RBTE88U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290707DT5SRQSQPX">​输入范围验证</span></p></td><td id="k_2621290708PKQA36PLEH" tabindex="0" row="9" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290709RDTDL8E2WU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290709RP8MTV4LTR">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621331450B6NNKXPN54">{rule :{ range:[10 ,100] } , msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_10"><td id="k_262129071168IJCJ9QW7" tabindex="0" row="10" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26212907117QGUZYBEEE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290712V13WGSDWPB">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621332625JWVB6K8KWU">输入最小长度&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290713FGRURXHZ5J" tabindex="0" row="10" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290714W4D2S535L8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290715SC74FJWAC6">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621333501PR4K9FPKO8">{rule :{ minlength:10 } , msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_11"><td id="k_2621290716PP8E11NMYA" tabindex="0" row="11" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290717DL4O9D479O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290718P8XT9M99F4">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621340076YS6KOHENHA">输入最大长度&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290719Q7A23EAOBL" tabindex="0" row="11" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290720ILVI8MHSXX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290720L6X3E359I1">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621340999GM16S79JFY">{rule :{ maxlength:10 } , msg:’自定义信息，有默认，可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_12"><td id="k_26212907223AM8VWBYS8" tabindex="0" row="12" col="0" w="215" h="29" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290723NNUBCUQP2I"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290723BPZ3IGMWPC">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621342158MP43OQTIP7">正则表达式验证&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290725PB3T1JT8EO" tabindex="0" row="12" col="1" w="1118" h="29" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290725OSE6M1R2PU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290726DRO935L1NQ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621343365E7M1PYX5LG">{rule :{ regex : /正则表达式/ } , msg:’自定义信息，有默认,可不要’}&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td></tr><tr id="row_13"><td id="k_2621290727GALJRRLL77" tabindex="0" row="13" col="0" w="215" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 217px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26212907289SI5YMQ97Q"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290728XMYI8KSRZZ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621345193OT9PA9TP8U">远程调用验证&nbsp;&nbsp;&nbsp;&nbsp;</span></p></td><td id="k_2621290729GZHOV9W6LH" tabindex="0" row="13" col="1" w="1118" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1120px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2621290730XCKO2AAUT7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621290731IVWW1IU4ST">​{rule :{remote: 远程url地址}, msg:’自定义信息，有默认可不要’}</span></p></td></tr></tbody></table></div><div id="k_2621084328S6Q6YZ2PP8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621084329M7SJ2GP2WK">​</span></div><div id="k_2621080843962LPFAMUZ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(235, 243, 255); padding-left: 18px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(0, 235, 227); border-image: initial; margin-top: 0px; margin-bottom: 20px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_2621154836IKI8D1FPNV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621154839SBOCXJYZUN">​<img id="k_2621154840S1LXSN52YI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA6UlEQVQ4T6WSPU4CQRiGn3eIFzDy4xXsuIAHMLGSlooCEyXEA2CCR7E1MaH1PDRLAQEKiGbnM0vYjSwzKwlT7uyT553v/cQZR2ewnARfYdc1xzNgiWcM+smklXAOCR4NLszTmaGvPG0Q3kM9wQvicvez8f7tGczRKgi3sLo5+gdQxhnrsrWIHYOKYQasO7jpbCh4LeKVxp9Z8XQTNCk3oyZ2L8fNwYW4Be723z42Kf0lWhzBoZ4bzkZOvFVZo1XlMBC1/gv7lIcZ+oxtYbDnzCzR3qb0Qm+tXJKWsyfzTEMT/pvipN2Oxf4F1eVYEFF+XvgAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_26211548419NUIZ3K9EN">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(39, 156, 3); background-color: rgb(235, 243, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(39, 156, 3); vertical-align: baseline;" id="k_2621154836C31ILX2XCP">&nbsp;Demo</span></div><div id="k_2621073264OJ9TBCRLIM" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 24px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 18px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26213909168GMR2OLRMW">​1、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621073265DALGDO4QNA">​表单定义（注意标签 id = 字段名）</span></div><div id="k_26213909329ZYCU7QJQ9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 24px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621391153W8B3COJRFL"></span></div><div id="k_2621424879VI6U1PDCDF" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 24px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;;"></span></div><div id="k_2621425730S223MG28P5" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 48px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621425731JX8JECWZRM"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="white-space: normal; color: rgb(83, 83, 83); background: none;">&lt;</span><span style="white-space: normal; color: rgb(41, 111, 169); background: none;">table</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="white-space: normal; color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">=</span><span style="white-space: normal; color: rgb(161, 100, 75); background: none;">"myform"</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="white-space: normal; color: rgb(111, 175, 209); background: none;">class</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">=</span><span style="white-space: normal; color: rgb(161, 100, 75); background: none;">"form_table"</span><span style="white-space: normal; color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">class</span>=<span style="color: rgb(161, 100, 75); background: none;">"label"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span>姓名：<span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">input</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">id</span>=<span style="color: rgb(161, 100, 75); background: none;">"userName"</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">type</span>=<span style="color: rgb(161, 100, 75); background: none;">"text"</span>&nbsp;<span style="color: rgb(83, 83, 83); background: none;">/&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">class</span>=<span style="color: rgb(161, 100, 75); background: none;">"label"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span>中文名：<span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">input</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">id</span>=<span style="color: rgb(161, 100, 75); background: none;">"zhName"</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">type</span>=<span style="color: rgb(161, 100, 75); background: none;">"text"</span>&nbsp;<span style="color: rgb(83, 83, 83); background: none;">/&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">class</span>=<span style="color: rgb(161, 100, 75); background: none;">"label"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span>密码：<span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">input</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">id</span>=<span style="color: rgb(161, 100, 75); background: none;">"userPwd"</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">type</span>=<span style="color: rgb(161, 100, 75); background: none;">"password"</span>&nbsp;<span style="color: rgb(83, 83, 83); background: none;">/&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">td</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">tr</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">table</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></pre></span></div><div id="k_2621425743433246BJ3Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 24px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621425744WVDQD8BI9B">​</span></div><div id="k_2621391164CLMWE31F32" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 24px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 16px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621514992GFF7BBUF4A">2、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621391165HGW4OH7547">创建验证</span></div><div id="k_2621401568OGGJY7ZW1D" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 50px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262140157113FJQUYTBZ"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(61, 108, 40); white-space: normal; background: none;">//绑定客户端验证</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">var validObj</span>&nbsp;=&nbsp;<span style="color: rgb(41, 111, 169); background: none;">new</span>&nbsp;<span style="color: rgb(33, 156, 131); background: none;">$B</span>.<span style="color: rgb(33, 156, 131); background: none;">Validate</span>({</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;userName:</span>&nbsp;[{<span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'require'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'wchar'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule :</span>{&nbsp;<span style="color: rgb(111, 175, 209); background: none;">minlength:</span><span style="color: rgb(136, 161, 123); background: none;">2</span>}},{<span style="color: rgb(111, 175, 209); background: none;">rule:</span>{<span style="color: rgb(111, 175, 209); background: none;">remote:</span><span style="color: rgb(161, 100, 75); background: none;">'http://localhost/myui/demo/Handler.ashx?flag=valid'</span>}}],</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;userPwd:</span>&nbsp;[{<span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'require'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule :</span>{&nbsp;<span style="color: rgb(111, 175, 209); background: none;">minlength:</span><span style="color: rgb(136, 161, 123); background: none;">6</span>}}],</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;zhName:</span>&nbsp;[{<span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'require'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule:</span><span style="color: rgb(161, 100, 75); background: none;">'chchar'</span>},{<span style="color: rgb(111, 175, 209); background: none;">rule :</span>{&nbsp;<span style="color: rgb(111, 175, 209); background: none;">minlength:</span><span style="color: rgb(136, 161, 123); background: none;">2</span>}}]</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">},&nbsp;<span style="color: rgb(175, 175, 125); background: none;">$</span>(<span style="color: rgb(161, 100, 75); background: none;">"#myform"</span>));</div></pre></span></div><div id="k_2621083783JXOUKLEZU6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621083784GUMZI2GME9">​</span></div><div id="k_26215202092YFLGV3FV7" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 24px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 16px; text-indent: 0px;"><span id="k_2621520210WKPZ38ANTZ" style="font-family: &quot;Microsoft Yahei&quot;; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline; font-size: 14px; font-weight: 400; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal;">3、</span><span id="k_26215202111D47E7B5O9" style="font-family: &quot;Microsoft Yahei&quot;; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline; font-size: 14px; font-weight: 400; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal;">​调用验证</span></div><div id="k_26215320176BYLFLNUQ8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 51px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26215320185H39UMOYSL"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="white-space: normal; color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="white-space: normal; color: rgb(175, 175, 125); background: none;">mySubmit</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">() {</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(152, 89, 147); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;if</span>(<span style="color: rgb(255, 0, 5); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">validObj.valid()</span>){</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(41, 111, 169); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;alert("验证通过！");</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp; }<span style="color: rgb(152, 89, 147); background: none;">else</span>{</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; alert</span>(<span style="color: rgb(161, 100, 75); background: none;">"验证没有通过！"</span>);</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp; }</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">}</div></pre></span></div><div id="k_2621514578CJ2Q1VXN8I" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621514579FSH1BME11E">​&nbsp; &nbsp;</span></div><div id="k_2621083875S9LYV72C8C" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2621083876353U8ILKH8">​</span></div><div id="k_26212638561IDPJOD7P9" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 458px; height: 34px; position: absolute; top: 272px; left: 257px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_2621263858WNJ5IFNK7J" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;"><div id="k_2621263859SM4X8D8LCA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 17px; width: auto; text-align: left; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621274442BF2LRUSODR">验证配置 ： { </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621274727XGRC8ZFKDU">字段</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621274728556RKVCKI2">1</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621274910XPBZH9VEGB"> : [&nbsp; {验证</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621274910QN66YCEJBW">配置}&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2621275270TWGMYRO9V5"> ]&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_26212752716EIAQHIIXS"> }</span></div></div></div>

</div>