<div>
  

  <div id="k_2114513874R8NS4H6LSI" class="_section_div_" style="line-height: 32.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(113, 101, 252); border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(54, 247, 64); border-image: initial; padding-left: 12px;"><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: bold;" id="k_2119260187BX2NNOVN8Y">样式表引入</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: bold;" id="k_2119260188FZE7T5QVWB">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(113, 101, 252);" id="k_21150710721IBCUIDUYV">​​</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: bold;" id="k_2115071072WNKRYPRI2E">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(113, 101, 252);" id="k_21150731323FV5ANUWX7">​​</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: bold;" id="k_2115073133CEAR34KCKD">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2119253581WVMRIXYWMT">​<img id="k_2119253581B863X6799G" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA6ElEQVQoU43RPyuGYRgF8N8ZlOUtHwAfgLIpozJZjBaJ0cyExWYzUJKSDNhYbMpot/oIFIOsLj16Xr3+u8frPudc55wr/vGqahK7DTR/4atqAvsYxsavhKoawSlGcZxk4UdCCz7EOK4xneQp7cpOksuuvarq4AxTuMdSkvO3DFV10/pbTbLXDKvqCHN4wU6Sla5YQ2iUG6VHrGMIy+hHIzab5LaX0A02hgf0obH0jLUk271NvoWuqum2usH2s3CRZOZz7e8tVdUSNjGAu96gXzb0tHOAeZwkWfzuqB/u0Na51RwrydV3hFeUbU1Nse9GYgAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: bold;" id="k_2119253583LOFYDLVRUC">​</span></div><div id="k_2114545870DNVFUUYJLN" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 48px; width: 789px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2114545870XJ8NN2HUE3"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;">    &lt;link type="text/css" rel="stylesheet" <span style="color: rgb(255, 0, 5);">class="k_bui_css"</span> th:href="@{/static/theme/font/bui-fonts.min.css}"&gt;    <div>    &lt;link type="text/css" rel="stylesheet" <span style="color: rgb(255, 0, 5);">class="k_bui_css"</span> th:href="@{/static/theme/blue/main.min.css}"&gt;<div>   </div></div></pre></span></div><div id="k_2114545881VNBN1TA4JY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span></div><div id="k_2119240633FMN8OGEIUI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2119240634YHK5O1TFJ2">​</span></div><div id="k_2115044733CD93BFZ7UA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21150447351AMOVQFX4G">​</span></div><div id="k_2114550113YBXA6VTCB2" class="_section_div_" style="line-height: 32.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(113, 101, 252); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(2, 181, 11); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2119261569URZOKGH8XA">脚本引入</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_21192615708UQJXMPJSA">&nbsp;</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background: none rgb(113, 101, 252); font-style: normal; vertical-align: baseline; font-weight: 700; text-decoration: none solid rgb(255, 255, 255);" id="k_21150745766YK5D2F99W">​​</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_21150745771L23Y9WDJ4">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2119272258KTB21UGGG6">​<img id="k_2119272259HUH7AIJDQ9" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA6ElEQVQoU43RPyuGYRgF8N8ZlOUtHwAfgLIpozJZjBaJ0cyExWYzUJKSDNhYbMpot/oIFIOsLj16Xr3+u8frPudc55wr/vGqahK7DTR/4atqAvsYxsavhKoawSlGcZxk4UdCCz7EOK4xneQp7cpOksuuvarq4AxTuMdSkvO3DFV10/pbTbLXDKvqCHN4wU6Sla5YQ2iUG6VHrGMIy+hHIzab5LaX0A02hgf0obH0jLUk271NvoWuqum2usH2s3CRZOZz7e8tVdUSNjGAu96gXzb0tHOAeZwkWfzuqB/u0Na51RwrydV3hFeUbU1Nse9GYgAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_21192722604L4HWCELXP">​</span></div><div id="k_2114545937T2NRNSLV1F" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 65px; width: 772px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2114545938BXJRCJ3LCZ"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;">&lt;script type="application/javascript" th:src="@{/static/lib/jquery.min.js}"&gt;&lt;/script&gt;<div style="background: none;">&lt;script type="application/javascript" th:src="@{/static/lib/config.js}"&gt;&lt;/script&gt;  </div><div style="background: none;">&lt;script type="application/javascript" th:src="@{/static/lib/bui-0.0.1.js}"&gt;&lt;/script&gt;</div><div style="background: none;"><br></div></pre></span></div><div id="k_2114542832RZAL44VZ74" class="_section_div_" style="line-height: 32.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(113, 101, 252); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(2, 181, 11); border-image: initial; margin-top: 12px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_21192633329BFAPHG3D5">使用</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2119263333FOX8H55XQY">De</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2119263334UD99DJ86TN">mo</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(113, 101, 252); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2119262763C35TMK5VF8">&nbsp;</span><span style="font-size: 18px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background: none rgb(113, 101, 252); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2115075514UDAD1RTFRG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2119272424ZUWFM8O16S">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2119273170AW29PXCJBC">​<img id="k_2119273171KRVEC9TOOQ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA6ElEQVQoU43RPyuGYRgF8N8ZlOUtHwAfgLIpozJZjBaJ0cyExWYzUJKSDNhYbMpot/oIFIOsLj16Xr3+u8frPudc55wr/vGqahK7DTR/4atqAvsYxsavhKoawSlGcZxk4UdCCz7EOK4xneQp7cpOksuuvarq4AxTuMdSkvO3DFV10/pbTbLXDKvqCHN4wU6Sla5YQ2iUG6VHrGMIy+hHIzab5LaX0A02hgf0obH0jLUk271NvoWuqum2usH2s3CRZOZz7e8tVdUSNjGAu96gXzb0tHOAeZwkWfzuqB/u0Na51RwrydV3hFeUbU1Nse9GYgAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21192731736JXEU8C3V3">​</span></div><div id="k_211454288331BW2MFFDG" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21151234704FV5F9J55B">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21151234718KMFK9TZ8B">​<img id="k_2115123471VMTO4QROSJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABTElEQVQ4T6XQvy8DcRjH8ffTC3dXg6sYOomEhH+A2CRiKLEhiMVksEhsjbmxm8TUzWBhQCIkRhaRsEpMbK5t0t6lufaRa1rpac+P+K6f5/V5vnmEfzyJs4omAFMQL24mFr/jbwm6KFhLDuJ2K+iKXbxh4AoYAdlOYe3/CisqRfycQrYJnoHZFPbL14KOzUUqU3W4UCgL4gNDoAcO9o4gQXtBBCtqufh5gZUEkjEwH6r4N4KmEzDXT/I2FheoLCtyBBhRzDjoqYO9Lki5VfC5uYQO1vDOQCbDsBNTE3TNIXkcweGRXPysQK4VdMGA3vdiL/Qhb+FcY3MJfyxAL6VxnO+fwm4Ka08QFUV7CviHwEb0GLpqYDwF1E+A0Vam6CsYmQHMR3HxphXOBZK/wc2ZvIO1GeJrYOan77bnChWBeSlSnahRT/8FGxCAefcBm153Ov9VsTUAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115123472B852YME51Z">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2119295196D3J72INU1X">须</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2119295197HJGD9SHQF6">知：</span></div><div id="k_2115093043NYBKKJU3JB" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 109px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115133141II1NS6GL98">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21151331428WXMCA3XFE">​<img id="k_21151331429JVL3C6N7T" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAzklEQVQ4T82TPQ4BcRDFf2OzWImPLTRO4ABqnc5HIg6hU2lcQeUSEpUbSFxASHQSlUSFDYV/smL0a21WtmDqmffmvZcnJBxJeM8fAnioq5g56KqEMxLERMl8k6Co7XEfgwxAJyWcoSD+J5BQD74BEQ/TV57VIIMiadCOIJWoT+TCfQnUo+PUdQZaOXKH4N7HGM+YJugM2FtIt0h2F0YSCuBhGspzqsjJJtXLk9nGNvGGln3MAigIVtslvfk2Rrni12wexzDNsT2I25Hfd+EFAjtDEYorLO0AAAAASUVORK5CYII=" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115133142VTWRRMGPUE">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115133141ZLDFX7WE97">Bui所有</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115124517TRUTESDS6R">控件均封装在[ </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_2115124517FPVWXI9IZQ">$B</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115162784W5OCWDZNFQ"> ]对象下，您需要通过[ </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_2115162784VTW8X8A9VA">$B.xxx</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115162784FMBOPLZQT6"> ]访问对应的API、组件。如：</span></div><div id="k_2119280643EFJQLUBB71" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 153px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2119285431SSMJTFJOVW">[&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2119285432IYAUQQHC3C"> &nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_21192912805LZ355GCZQ">var tree = new $B.tree($("ul") , opts )&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2119291281VWBJ8AAS3K"> </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2119285087OG3L9TTICN">&nbsp;]</span></div><div id="k_21145428094DULE1QEFW" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 109px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 6px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115134070ZY9AEUBDCH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21151340715YWSLWATOD">​<img id="k_2115134071D1YVGHIUS1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAzklEQVQ4T82TPQ4BcRDFf2OzWImPLTRO4ABqnc5HIg6hU2lcQeUSEpUbSFxASHQSlUSFDYV/smL0a21WtmDqmffmvZcnJBxJeM8fAnioq5g56KqEMxLERMl8k6Co7XEfgwxAJyWcoSD+J5BQD74BEQ/TV57VIIMiadCOIJWoT+TCfQnUo+PUdQZaOXKH4N7HGM+YJugM2FtIt0h2F0YSCuBhGspzqsjJJtXLk9nGNvGGln3MAigIVtslvfk2Rrni12wexzDNsT2I25Hfd+EFAjtDEYorLO0AAAAASUVORK5CYII=" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115134071BZO3EOM2AC">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21151340703M3AVYJ4PL">特例：Bui提供的拖动实现、滚动条、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115250968BQUYUXVA2K">按钮submit则采用jquery标准封装格式。如：</span></div><div id="k_211528366373HVB1R2L6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 154px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115283664G98FUXBLS9">[&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102);" id="k_2115283665KJEOUYRCEQ"></span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102);" id="k_2115283666KXMVZ6IE4A">$("#id").draggable({...})&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 13, 250); background-color: rgb(255, 255, 255);" id="k_21152836676B791Y9G6X"> |</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(255, 255, 255);" id="k_21152836685U2E25YGCV">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102);" id="k_2119291753PS4TDKHAIJ"> $(</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102);" id="k_2119291754BT58AUUQJ1">"#id).myscrol</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102);" id="k_2119291755DCM1U5P22C">lbar({})</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 13, 250); background-color: rgb(84, 156, 102);" id="k_2115283671PC22BF787S">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 13, 250); background-color: rgb(255, 255, 255);" id="k_2115283673C1SXB2U4B8">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 13, 250); background-color: rgb(255, 255, 255);" id="k_2115283674LF3ZH91WMO">|</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 13, 250); background-color: rgb(255, 255, 255);" id="k_2115283675TM1P9LPAKS">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 13, 250); background-color: rgb(84, 156, 102);" id="k_2115283676C11QMNFOMZ">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102);" id="k_2115283677K9JG3JD39W">$("btn").submit(function(){})</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21152836785L21NCQL3H"> ]&nbsp;&nbsp;</span></div><div id="k_21151153928CAD1ATNH5" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span></div><div id="k_2115142563VU637XQ5JJ" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 69px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115150893A1GP92R8WV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2115150894BOVCHS2HWW">​<img id="k_21151508948M3XO6LY5H" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABZklEQVQ4T52TvUsDQRDF3+wep7YJKmLESy6VYKUiWIidaYTYCwoiNpbiHyF2SWkvdhaiNkIsbGwsgk3I3QUStVKxSTjN7UgugXwo7ul2szPzgzdvhtD7PMuSbJwyeAGMByWCNSQrlb6agYB6Y+HaOQLttf4Y3CDi7SDpnvwTgCfFKgPbLUYGoDYZF/7IPoBxJZGDVb7/rbmV65OgK/4p/x1QTcTQNMZgVUogKB20C/CsRcnGITOWQPwZZYBtCQxTeHYeTJtEMP/iQAiQjn3NwBwIdQJNdABvCiqDlHenlSDd9EZgNi6lP3wMomwIYC4q01/BVO1VCwgLqomY+BgqENFsGDOfBbazrmvu2uik5gWJKwLiDGYAByrlHEUGSNfeYaZ8a4jM/K4EZZEsFyIDBm6gpChYFcrYVQafY9q51a6ydNM3AJbb8tEEwQezF+0an9Ojoo4CEWY6FioCXQRmYwuJxxedjC9Vt5cRXt4zJAAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115150895ER7ULJZLWZ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_21151508945X6A2GDQ47">以window类型组件使用为例：</span></div><div id="k_21151154083DR84DESG3" class="_section_div_" style="line-height: 36.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 109px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115204952IIKQX5COTV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21152049535JX6S61LZT">​<img id="k_21152049532RUVLEASGV" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABYElEQVQ4T6WTvUtCYRTGn3NvZaTUkEVEXbVacxVddGmJhsCkIUT7WJuabPJvCIQQr1yIghqdgpYWb0q5tTTlB1QGUdSiyfWElmJ2uRK+68vvOec8zzmEPh71wcIQ3l1g09tkeexInXrWK2IIB51P5kFLJSFolExkbRfdAj3hIUvlDAwfiLdl1X4KELdEdGCmkKc0J7IWIKZZCLwCkATmGrOwJ2WkWBRUbwj8gkOe4rzImkxEXr0ZGdCIcaCZeF+5dFTa8I6rsMQinwCwGiXAwEMdwqqiStdNOOwuLQqknRMwbRwdF5lEfzIt3TTbDvvuh8VPKMzkBdEhMacAsSBfzbwGneWRpmHAMoPvCOKarEq3bcM23SUvQQtAMEeS6YmPzsqNqL7dZqvGWFcyjnznP2158v6Bqi0Vz1Gtu+UfOCJWEYvn7I9/ct5wvYweZ8ff9WdligLUiuZfS9Jr7/s6jC/6E38QehxLdAAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115204953SKN8BYLXGV">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_211520495248MTGH44QQ">弹出一个警告窗口：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(84, 156, 102);" id="k_2115194027MRJZWVCAW6">&nbsp;&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102);" id="k_21151940289YLHZLJINW">$B</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102);" id="k_2115195731HNM24IDIYQ">.alert("这是</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102);" id="k_2115195731N3H9GWRR8K">一个警告信息")&nbsp; &nbsp;</span></div><div id="k_2115153020KOJYP5X96M" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 109px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115205947W7LKC1OARM">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2115205947XKDQNADPIH">​<img id="k_2115205947ZMAXAER1TT" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABYElEQVQ4T6WTvUtCYRTGn3NvZaTUkEVEXbVacxVddGmJhsCkIUT7WJuabPJvCIQQr1yIghqdgpYWb0q5tTTlB1QGUdSiyfWElmJ2uRK+68vvOec8zzmEPh71wcIQ3l1g09tkeexInXrWK2IIB51P5kFLJSFolExkbRfdAj3hIUvlDAwfiLdl1X4KELdEdGCmkKc0J7IWIKZZCLwCkATmGrOwJ2WkWBRUbwj8gkOe4rzImkxEXr0ZGdCIcaCZeF+5dFTa8I6rsMQinwCwGiXAwEMdwqqiStdNOOwuLQqknRMwbRwdF5lEfzIt3TTbDvvuh8VPKMzkBdEhMacAsSBfzbwGneWRpmHAMoPvCOKarEq3bcM23SUvQQtAMEeS6YmPzsqNqL7dZqvGWFcyjnznP2158v6Bqi0Vz1Gtu+UfOCJWEYvn7I9/ct5wvYweZ8ff9WdligLUiuZfS9Jr7/s6jC/6E38QehxLdAAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115205948V721IE4O1U">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2115205947TP4QVRBSD8">弹出一个成功窗口：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(84, 156, 102); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2115200404A65UMRUM5S">&nbsp; $B.success("处理成功，5秒后自动关闭窗口", 5 )&nbsp;&nbsp;</span></div><div id="k_2115115441KJNODDD7B8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21151154416JDAX6ACYH">​</span></div><div id="k_21192400998CYSMM614H" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2119240000DOSBUZ4DLS">​</span></div><div id="k_2115045216FGUYULLIEL" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 384px; height: 36px; position: absolute; top: 139px; left: 213px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_21150452179LBJFECDRL" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;" spellcheck="false"><div id="k_2115045217MFC6RXKV3E" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(221, 5, 250); background: none;" id="k_2115052942TIS2216LSA">请注意声明</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2115053972BL7B3TZNAJ"> [&nbsp; </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 189, 49); background: none;" id="k_2115053973KB32H3PBKC">class="k_bui_css"</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2115053973D62LM87XPV">&nbsp; ]</span></div></div></div>

</div>