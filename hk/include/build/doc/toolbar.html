<div style="position:relative">

    <div id="k_23194617888R7R26DC6N" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(237, 252, 239); border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(5, 126, 255); border-image: initial; padding-left: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(237, 252, 239);" id="k_2320021164CFICAXCVK8">介绍</span></div><div id="k_2319462409VZCFYZQQ1T" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 42px; width: 830px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 20px; margin-top: 20px; border: 1px solid rgb(142, 219, 152);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320125591RWXOCWH6OB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2320125593AU2PJTQOJ1">​<img id="k_2320125594HIK2L8JZ7K" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAApUlEQVQoU5WRTQrCMBCF34sResq6cuPChfVnJYgLEVyqiBtpl+I1vIcHielIQislFBNnO/PlfZMh/iwaXT4VeVcmPxGUGE+ry0LAHYl5CkTBdfjW2ZqQBclCmfzyK4lOoYH2hExikAc60IGQMQTTgR3d+pJCoFVbKvM6E5s6/ISvUq2zmQDb2PJ+6dRhl0arq1XzstM49ml0tdrDPVKGfULssmH/A2GXTg0sXqvNAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320125595MZAZRKVH4D">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320135926971EGBYJPY">toobar工具栏组件</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320135927ROZ7QFXU6P">是</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320114023SCNAOB3LAZ">d</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320121643RRFGWZMUL6">atagrid、tree的依赖组件，其</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320121644LCT2ATN2YP">实现的列表、树节点按钮采用toolbar组件实现</span></div><div id="k_2320054632ME4PU44AAJ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 42px; width: 830px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 20px; border-top: none; border-right: 1px solid rgb(142, 219, 152); border-bottom: 1px solid rgb(142, 219, 152); border-left: 1px solid rgb(142, 219, 152); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320131358GM474M32E4">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2320131360VE4HYJUGGN">​<img id="k_2320131361LS3HNLT4KU" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAApUlEQVQoU5WRTQrCMBCF34sResq6cuPChfVnJYgLEVyqiBtpl+I1vIcHielIQislFBNnO/PlfZMh/iwaXT4VeVcmPxGUGE+ry0LAHYl5CkTBdfjW2ZqQBclCmfzyK4lOoYH2hExikAc60IGQMQTTgR3d+pJCoFVbKvM6E5s6/ISvUq2zmQDb2PJ+6dRhl0arq1XzstM49ml0tdrDPVKGfULssmH/A2GXTg0sXqvNAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320131362TX16MQF5FU">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320131358ANNQCH4TG4">采用font-awesome字体图标，可以通过参数指定图标大小、颜色等，具有多样式风格配置实现</span></div><div id="k_2320065596Y286UGAHFL" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 42px; width: 830px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 20px; border-top: none; border-right: 1px solid rgb(142, 219, 152); border-bottom: 1px solid rgb(142, 219, 152); border-left: 1px solid rgb(142, 219, 152); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320132207AWQZSI9FQA">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2320132209C6VYHYLP4O">​<img id="k_2320132210LOFWOK84QO" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAApUlEQVQoU5WRTQrCMBCF34sResq6cuPChfVnJYgLEVyqiBtpl+I1vIcHielIQislFBNnO/PlfZMh/iwaXT4VeVcmPxGUGE+ry0LAHYl5CkTBdfjW2ZqQBclCmfzyK4lOoYH2hExikAc60IGQMQTTgR3d+pJCoFVbKvM6E5s6/ISvUq2zmQDb2PJ+6dRhl0arq1XzstM49ml0tdrDPVKGfULssmH/A2GXTg0sXqvNAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320132211BY9WSD3985">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_232015208942RGBIEOE8">采用事件集合设计原则，按钮事件必须定义在window的某个对象上，如&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(135, 143, 151);" id="k_232015209073SZZAIK25"> window["toolbarEvents"] = {}&nbsp;</span></div><div id="k_2320084939BBRPNH6OFZ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 42px; width: 830px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 20px; border-top: none; border-right: 1px solid rgb(142, 219, 152); border-bottom: 1px solid rgb(142, 219, 152); border-left: 1px solid rgb(142, 219, 152); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320132970ZTARVVWIVE">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_232013297363664E9NVM">​<img id="k_2320132973B9RNUFH19H" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAApUlEQVQoU5WRTQrCMBCF34sResq6cuPChfVnJYgLEVyqiBtpl+I1vIcHielIQislFBNnO/PlfZMh/iwaXT4VeVcmPxGUGE+ry0LAHYl5CkTBdfjW2ZqQBclCmfzyK4lOoYH2hExikAc60IGQMQTTgR3d+pJCoFVbKvM6E5s6/ISvUq2zmQDb2PJ+6dRhl0arq1XzstM49ml0tdrDPVKGfULssmH/A2GXTg0sXqvNAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23201329746UJW48AHMA">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23201329717LP4B12VNX">工具栏支持下拉列表、分组按钮、普通按钮三种风格</span></div><div id="k_232009408536JIMCOIF9" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 42px; width: 830px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 20px; border-top: none; border-right: 1px solid rgb(142, 219, 152); border-bottom: 1px solid rgb(142, 219, 152); border-left: 1px solid rgb(142, 219, 152); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320133945A48CKILU91">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2320133948FBA7LGE2Y6">​<img id="k_23201339483KXS91G2GJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAApUlEQVQoU5WRTQrCMBCF34sResq6cuPChfVnJYgLEVyqiBtpl+I1vIcHielIQislFBNnO/PlfZMh/iwaXT4VeVcmPxGUGE+ry0LAHYl5CkTBdfjW2ZqQBclCmfzyK4lOoYH2hExikAc60IGQMQTTgR3d+pJCoFVbKvM6E5s6/ISvUq2zmQDb2PJ+6dRhl0arq1XzstM49ml0tdrDPVKGfULssmH/A2GXTg0sXqvNAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_232013394999LB2IS66N">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320133946CFWWNXZHO6">支持动态增加、删除、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320114027U4P3TTIUY6">启用、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23201359309XIH53T9AG">禁用</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_232013593191QFSEPWUH">按钮</span></div><div id="k_2319462424I4G1Z9A84N" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2319462425RPZA2ZDJJJ">​</span></div><div id="k_23194624885P2IX7IG9H" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(237, 252, 239); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(5, 126, 255); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(237, 252, 239); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 118, 240); vertical-align: baseline;" id="k_23194624887G8SYL2D36">API及构造参数</span></div><div id="k_2319463439T2NRKIPGCR" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="2019032320162599QX94794VNX"><tbody><tr id="row_0"><td id="k_2320162567GBDD6JQZGO" tabindex="0" row="0" col="0" w="1335" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1337px; background-color: rgb(153, 225, 247); text-align: left; font-size: 16px;" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(153, 225, 247);" id="k_2320162568CGW1FX8EW7"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(139, 109, 56); background-color: rgb(153, 225, 247); text-align: left;" id="k_2320183289X5BSBWDN4T">var tool = new $B.Toolbar($("#toolId</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(139, 109, 56); background-color: rgb(153, 225, 247); text-align: left;" id="k_23201859419ONDAOFDLF">"), opts);&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(153, 225, 247); text-align: left;" id="k_2320185942IG893XKSKT"> &nbsp;opts说明</span></p></td></tr><tr id="row_1"><td id="k_2320162578GW6ACH26G5" tabindex="0" row="1" col="0" w="343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px; background-color: rgb(119, 170, 186); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(119, 170, 186); color: rgb(255, 255, 255);" id="k_2320162579RK3S2DSJ3R"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(119, 170, 186); text-align: center;" id="k_2320162580NFLE2NI2CS">​参数名</span></p></td><td id="k_2320162582MJEYNQXLNB" tabindex="0" row="1" col="1" w="359" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px; background-color: rgb(119, 170, 186); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(119, 170, 186); color: rgb(255, 255, 255);" id="k_23201625829VWC3EQM7M"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(119, 170, 186); text-align: center;" id="k_2320162583BXB86ZE91Q">参数值​</span></p></td><td id="k_23201625856OYMTW1QZB" tabindex="0" row="1" col="2" w="629" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px; background-color: rgb(119, 170, 186); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(119, 170, 186); color: rgb(255, 255, 255);" id="k_2320162585XJ368FZNYD"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(119, 170, 186); text-align: center;" id="k_2320162586MT9KJVLW7N">说明​</span></p></td></tr><tr id="row_2"><td id="k_23201625875CM23SVH93" tabindex="0" row="2" col="0" w="343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23201625881BGS14ZQZR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162589KSJ8LW7LA1">​​params</span></p></td><td id="k_2320162590445H3OWML2" tabindex="0" row="2" col="1" w="359" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162591A8NW3PMGN5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320250603F93CI8AYRU">默认值：undefined</span></p></td><td id="k_2320162593DIK86N6T27" tabindex="0" row="2" col="2" w="629" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162594GT6UE8QZ91"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232016259442P3A6JAEZ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232021245639MOYPCVMI">​设置按钮参数，按钮事件中可以拿到这些参数</span></p></td></tr><tr id="row_3"><td id="k_2320162596HRT7IEHV6Q" tabindex="0" row="3" col="0" w="343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23201625964O66UWVJ6W"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162597KO74EXKM15">​​align</span></p></td><td id="k_2320162599TBLDRION3N" tabindex="0" row="3" col="1" w="359" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_232016250159PFMQMBOD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162502F33Q5TZVBW">​​默认值：left</span></p></td><td id="k_2320162503JTE1VCQNVQ" tabindex="0" row="3" col="2" w="629" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23201625041N2H21VMSY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162505KOZW4XBA2K">​​对齐方式:left 、center、right</span></p></td></tr><tr id="row_4"><td id="k_2320162506GKX8QHYE4Q" tabindex="0" row="4" col="0" w="343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162507V9MSG8J5I4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162507OLJ3OJRPX2">​​style</span></p></td><td id="k_2320162509RYS6GHKB2I" tabindex="0" row="4" col="1" w="359" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23201625099UATPXE4AB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162510WZ8PD7GREF">默认值：normal</span></p></td><td id="k_2320162511IJSJPKZ96W" tabindex="0" row="4" col="2" w="629" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162512X3Y2XWR7JF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162513711UUWE5GG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320235640ASYK3SL8SE">​风格：plain(简单),&nbsp; min(小),&nbsp; normal(默认), big(大)</span></p></td></tr><tr id="row_5"><td id="k_2320162514H1VEGDYDCF" tabindex="0" row="5" col="0" w="343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162516JF7REMEQCV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162517LGO8IKQ95W">​​showText</span></p></td><td id="k_2320162518FF99F75PGN" tabindex="0" row="5" col="1" w="359" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162518L1BL57X3PJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162519H2KOBRA875">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320243728B4DJC56LVK">​默认值：true</span></p></td><td id="k_2320162520WEXDMPHL1U" tabindex="0" row="5" col="2" w="629" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162521ET66W7FT36"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162521S6FPWQ4VEQ">​​是否显示按钮文本</span></p></td></tr><tr id="row_6"><td id="k_2320162523QCHVITZ6EB" tabindex="0" row="6" col="0" w="343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162524UTH1PQUHO2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162524WEKFRMGFX2">​​buttons</span></p></td><td id="k_2320162525BDFAC8W3XS" tabindex="0" row="6" col="1" w="359" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162526LNXRO2R362"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162527LZK4MYD1XY">​​默认值：undefined</span></p></td><td id="k_2320162528C3HND359RF" tabindex="0" row="6" col="2" w="629" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162528O24YHWWW6A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23201625291C98ORA6OU">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320311152TEYBF9199G">请查看</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(255, 255, 255);" id="k_2320311153J12DNQHY1G">《buttons按钮定义说明》</span></p></td></tr><tr id="row_7"><td id="k_2320162530O4VA2X816O" tabindex="0" row="7" col="0" w="343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162531KB95B3RUFQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23201625319ZN6Z8G3Z7">​​color</span></p></td><td id="k_2320162532WQCHZE57H3" tabindex="0" row="7" col="1" w="359" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162533ZBLNFI7873"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162534O2GHOPXMAU">​​默认值：undefined</span></p></td><td id="k_2320162535HJ69PZO4KR" tabindex="0" row="7" col="2" w="629" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23201625353WZFYUZP9V"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162536SDWKSUI8AY">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320263600NXPPMOC7AK">​统一设置按钮颜色,但会优先取button里面的设置</span></p></td></tr><tr id="row_8"><td id="k_2320162537AS62SR6V6N" tabindex="0" row="8" col="0" w="343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162538EQR6FDQ5B4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23201625386FBLBGYHD8">​​iconColor</span></p></td><td id="k_2320162540WUZ4X8S8NZ" tabindex="0" row="8" col="1" w="359" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162540NTPRUZNJIN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162541OHDAQZZ5B4">​​默认值：undefined</span></p></td><td id="k_2320162542AKFGTDH13E" tabindex="0" row="8" col="2" w="629" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320162543SGEZPYEY2S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320162543BX98A4ZNKY">​​按钮图标颜色，优先取button里面的设置</span></p></td></tr><tr id="row_9"><td id="k_2320272262H1CHBUCILI" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px;" w="343" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320272263D7H8DNLYNE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320272264944IVNAESW">​​fontColor</span></p></td><td id="k_23202722666E5TT8J6K9" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px;" w="359" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320272267RFZ337DFZX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320272268TCW334J5O3">​​默认值：undefined</span></p></td><td id="k_2320272271HG3NZLD4NQ" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px;" w="629" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23202722727C89CC91Y9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320272272PE81Z15XOV">​​字体颜色，优先取button里面的设置</span></p></td></tr><tr id="row_10"><td id="k_2320272301M5HKJZ25PJ" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 345px;" w="343" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23202723019CGWQOWSCK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320272302OJKRZTN7VK">​​onOperated</span></p></td><td id="k_2320272304WCGXCT54ZO" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 361px;" w="359" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320272304UUXUVY6U12"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23202723055HOP4OMO5C">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320281915EBPOBCEVJG">​​​​默认值：undefined</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2320283649L6HB7DNWMC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320283650798Z99NLV5">onOperated = function(params){}</span></p></td><td id="k_2320272307AJU5NYFXZV" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 631px;" w="629" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320272308LNTC1IGCBP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320272308TSOU4E1S41">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320285787PT51RGAT5A">​工具栏中任何按钮的点击都会触发的事件</span></p></td></tr><tr id="row_11"><td id="k_2320272144F2EROKTUNU" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 345px;" w="343" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320272145UOL4MSL4KN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23202721468HEIHKB231">​​methodsObject</span></p></td><td id="k_2320272148UMY1XVAS7U" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 361px;" w="359" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320272149ASCM3QXFUE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23202721494CTL3OBI6I">​​默认值：'methodsObject'</span></p></td><td id="k_2320272152RFTJGAJV4X" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 631px;" w="629" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320272153TA323TMPM1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320272154S6TUHX9JGK">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320455140Q9G4LLXNAD">按钮事件的集合名，如window["</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2320455141M3TSY3WIFI">methods</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320455795O8STECKWR2">"] = {m1:function(){}} 中的"</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2320455701OU1DGUNDSB">methods</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23204557023E7IPYM7ML">"</span></p></td></tr></tbody></table></div><div id="k_2320154391SRJP5282MI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23201543923GX3H51ZLD">​</span></div><div id="k_23203100389QAA3WRFOQ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190323203229Y2Y3135MZ6F2"><tbody><tr id="row_0"><td id="k_2320322951Y8R7RW7RAR" tabindex="0" row="0" col="0" w="654" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 656px; background-color: rgb(157, 224, 245); text-align: left; font-size: 16px;" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(157, 224, 245);" id="k_23203229522P31G3CNLM"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 5, 5); background-color: rgb(157, 224, 245); text-align: left; font-weight: normal;" id="k_2320343443NZ1XDYWXIJ">buttons按钮定义说明</span></p></td><td id="k_2320412594RPVR4DG7N4" tabindex="0" row="0" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 681px; background-color: rgb(157, 224, 245); text-align: left; font-size: 16px;" w="679" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320412595EQ4MHZ39E2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23204125956VT8H2HC9W">​</span></p></td></tr><tr id="row_1"><td id="k_2320322959J53PKCHTOV" tabindex="0" row="1" col="0" w="255" h="357" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 359px; width: 257px; background-color: rgba(0, 0, 0, 0);" rowspan="11"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23203229604U2C3UXMSI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23203229606D47MOPQPR">​buttons = [{</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2320360983ROCNBU75EA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320370747QWL3DQHMNT">​&nbsp; &nbsp; &nbsp; id :&nbsp; 'xxx',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2320371301YZRG4BBLJZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320371302TKC1RKCKCV">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320372094CA4J5ENYCR">​&nbsp; &nbsp; &nbsp; text: 'xxx',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_23203728404S5QK4H3UK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320372841JSLESCKRWM">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320373836DQ8XRL1V82">​&nbsp; &nbsp; &nbsp; params: { },</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_23203743947SH1AG1UVD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320374394U2D8Y4PFMG">​&nbsp; &nbsp; &nbsp;&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23203818192Z3Q8Y3PRT">​methodsObject: '事件集合名',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2320382831SHX51JMH5C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320382832132Q19U3AO">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320384198VCX7GAK947">​&nbsp; &nbsp; &nbsp; color: '#fff',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_23203848809YQPHGPP6H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320384881CUW292LESM">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232038580523X3Z7XVXO">​&nbsp; &nbsp; &nbsp; iconCls: '',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2320390106OB7LHBCWTS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23203901074B4BALK5K7">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320391453P82E86XJ2R">​&nbsp; &nbsp; &nbsp; disabled:false,</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_23203918906V8RC8SP2Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320391891SKGISTFFON">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320393490XOFSDQMJGV">​&nbsp; &nbsp; &nbsp; click: xxx,</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_23203939261CQ6C9Y8J5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320393926MKNBRXWLN9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232039506585N736IIL8">​&nbsp; &nbsp; &nbsp; iconColor:''</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_23203610652NN43AJ9T5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320400280FMFLH6TG8Y">​&nbsp; &nbsp; &nbsp; fontColor:''</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2320361241M1FL411TUZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320361242GYT4OW9M9L">},......]</span></p></td><td id="k_23203229622Q28K5Y9YM" tabindex="0" row="1" col="1" w="172" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 174px; background-color: rgb(240, 244, 255); text-align: center; font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(240, 244, 255);" id="k_2320322962YGTI4ZO373"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 244, 255); text-align: center;" id="k_2320322963BA4AECN2P4">​参数名</span></p></td><td id="k_2320322964OGUW4J9H7Q" tabindex="0" row="1" col="2" w="223" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 225px; background-color: rgb(240, 244, 255); text-align: center; font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(240, 244, 255);" id="k_2320322965HJ1MTPKO6Y"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 244, 255); text-align: center;" id="k_2320322965F9MDX5ASRU">参数值​</span></p></td><td id="k_2320412598DDPHCQSEVC" tabindex="0" row="1" col="3" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 681px; background-color: rgb(240, 244, 255); text-align: center; font-size: 16px;" w="679" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(240, 244, 255);" id="k_2320412598UGXV7UMQHO"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 244, 255); text-align: center;" id="k_2320412599HHTFDNQ2RK">说明​</span></p></td></tr><tr id="row_2"><td id="k_23203229701HKTKSGNBH" tabindex="0" row="2" col="0" w="172" h="29" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 174px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320322971IXMN1W7KVT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320322972B6WDAY8IFN">​id</span></p></td><td id="k_232032297345WYLLCTQN" tabindex="0" row="2" col="1" w="223" h="29" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 225px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320322974DM376XQ4C8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320322975VICDE6Y1OV">​默认值：undefined</span></p></td><td id="k_2320412502APSGXVDFWA" tabindex="0" row="2" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 681px;" w="679" h="29"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320412502DQWSTCTLXI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320412503WD93X3J2GC">​按钮id</span></p></td></tr><tr id="row_3"><td id="k_2320322979YSNVLWJPH2" tabindex="0" row="3" col="0" w="172" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 174px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23203229804ORGTXZR4F"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320322981C474EVCGRH">​text</span></p></td><td id="k_2320322982U6DUVLUK54" tabindex="0" row="3" col="1" w="223" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 225px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320322983NA2O8S8U5C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320322984G7ENF1FKQI">​</span></p></td><td id="k_23204125078WIHRSQ9I5" tabindex="0" row="3" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 681px;" w="679" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23204125084GTA96WTV3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320412508OCT4NF6ZEE">​按钮文本</span></p></td></tr><tr id="row_4"><td id="k_2320322988LB14Y1RB34" tabindex="0" row="4" col="0" w="172" h="29" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 174px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320322989XKEDO6Q7BL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320322990854H22BRRN">​params</span></p></td><td id="k_2320322992986FLMM4DG" tabindex="0" row="4" col="1" w="223" h="29" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 225px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320322993IRD1M57SW9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23203229937JSV8AWS42">​默认值：undefined</span></p></td><td id="k_2320412513G9FRA91ASN" tabindex="0" row="4" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 681px;" w="679" h="29"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320412513S25KPAKVFC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232044001592KUXKH4X4">按钮参数 如 ：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(170, 170, 170); background-color: rgb(255, 255, 255);" id="k_23204400179KRQZN53XO"> { p1:1 ,p2:2&nbsp; &nbsp;}</span></p></td></tr><tr id="row_5"><td id="k_2320365200UBSCB19CGT" tabindex="0" row="5" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 174px; background-color: rgba(0, 0, 0, 0);" w="172" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320365201OCSEN78US2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320365202U4GV7B7KQP">​methodsObject</span></p></td><td id="k_2320365203EC5T4UUQQA" tabindex="0" row="5" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 225px; background-color: rgba(0, 0, 0, 0);" w="223" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320365204PNJ89EYL7P"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320462269MSIYJC6IJP">默认值：undefined</span></p></td><td id="k_23204125169XJ7MKJDN8" tabindex="0" row="5" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 681px; background-color: rgba(0, 0, 0, 0);" w="679" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23204125178W62V142RZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320412518D3UPX8XWIM">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23204824814Q74XN8Y38">按钮事件的集合名，如 </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23204824813PTCU98F6Y">window["</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2320453754N9OMO7YG5V">methods</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320482485I2UVTYGJH8">"]</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23204824861JLPI31U77"> = {m1:function(){}} 中的"</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2320454458HR4WEQLI1C">methods</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320454460F629UCRN42">"</span></p></td></tr><tr id="row_6"><td id="k_2320365046M2PBKTDIOW" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 174px; background-color: rgba(0, 0, 0, 0);" w="172" h="29"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320365047OG1LQKJYPF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320365048WJ6X8OWN3S">​color</span></p></td><td id="k_2320365050KSYW7BYW8Y" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 225px; background-color: rgba(0, 0, 0, 0);" w="223" h="29"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320365050EBDELRF17Q"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320463833YPG728IAT2">​默认值：#1A7BC9</span></p></td><td id="k_2320412520KF7VFRY49X" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 681px; background-color: rgba(0, 0, 0, 0);" w="679" h="29"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320412520H7ASH3CUYN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23204125216YEXIN6AZG">​按钮颜色</span></p></td></tr><tr id="row_7"><td id="k_2320364854AMWO5MYXVO" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 174px; background-color: rgba(0, 0, 0, 0);" w="172" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320364854NBF5537KUY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23203648559UGE8NJCH9">​iconCls</span></p></td><td id="k_2320364857KEA2UCF5QX" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 225px; background-color: rgba(0, 0, 0, 0);" w="223" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320364858J2PXXLK9JY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320364858FRFN7IZV1E">​​默认值：undefined</span></p></td><td id="k_23204125239D5RKX1OA6" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 681px; background-color: rgba(0, 0, 0, 0);" w="679" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320412524134U4VVMR5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23204731289JMIZT54QY">按钮图标</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320473131FS1UPEPO2X">字体图标</span></p></td></tr><tr id="row_8"><td id="k_2320364779MYLB1YOUKF" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 174px; background-color: rgba(0, 0, 0, 0);" w="172" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320364780KAGNKWTX4W"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320364780UAA6THHP8X">​click</span></p></td><td id="k_2320364782OLPUEAMX1X" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 225px; background-color: rgba(0, 0, 0, 0);" w="223" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23203647831NX6X269NU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320364784UMU6DWGLC4">​​​​默认值：undefined</span></p></td><td id="k_23204125272EZ9BZG5F9" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 681px; background-color: rgba(0, 0, 0, 0);" w="679" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320412528VXZF5UOXMJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320500436J614UKRMET">clic</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320500437CI8C5HTC6J">k可以是字符串函数名称</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320500438G1IAT6ND49">，对应window["methods"] = {</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2320492891F3XT3HE84T"> </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold;" id="k_232049289268RKL2M3JM">myClick</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232049206033RZBDNCD6">:function(prs){}&nbsp; }中的 </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold;" id="k_23204920619OXDMVQ69D">myClick</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2320493719K8I8GYP6U6"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_23205010158JWPTWV883">click可以是事件，如 click = function(prs){}</span></p></td></tr><tr id="row_9"><td id="k_2320364432ENXM5DMBTL" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 174px; background-color: rgba(0, 0, 0, 0);" w="172" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320364433ZCTJOMSY8N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320364434QTBONXJ27P">​disabled</span></p></td><td id="k_2320364436UD1XPEXAMC" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 225px; background-color: rgba(0, 0, 0, 0);" w="223" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320364436MCAD845V9B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320364437UFM44HBGPN">​默认值：false</span></p></td><td id="k_2320412530EF3QFEAN9E" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 681px; background-color: rgba(0, 0, 0, 0);" w="679" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320412531F2OSE38HZS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320412533LNP6MV6V7K">​是否禁用按钮</span></p></td></tr><tr id="row_10"><td id="k_23203642724H3G1MQN9G" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 174px; background-color: rgba(0, 0, 0, 0);" w="172" h="29"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23203642733VJZ5F6ALI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320364274STMMTH6C8S">​iconColor</span></p></td><td id="k_2320364276YK934T34R8" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 225px; background-color: rgba(0, 0, 0, 0);" w="223" h="29"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320364277V5ARJJSAN2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_232051469251KPYY4QD6">默认值:#ffffff</span></p></td><td id="k_23204125362439IOILYD" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 31px; width: 681px; background-color: rgba(0, 0, 0, 0);" w="679" h="29"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23204125363PWIPNX3E3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23204125376MDW5U885J">​​字体图标颜色</span></p></td></tr><tr id="row_11"><td id="k_2320513022CZ14G8P8BH" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 174px; background-color: rgba(0, 0, 0, 0);" w="172" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320513023KVTAKTYYH5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23205130232PC4YBFSGK">​​fontColor</span></p></td><td id="k_2320513025IAJULM1LN6" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 225px; background-color: rgba(0, 0, 0, 0);" w="223" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23205130265XK6ZINP2D"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320513026EVYZTM5XJX">​​默认值:#ffffff</span></p></td><td id="k_2320513029F5B7JM74NH" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 681px; background-color: rgba(0, 0, 0, 0);" w="679" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2320513030EKEIEXFCM3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320513030UBCQV6KLD8">​​文字颜色</span></p></td></tr></tbody></table></div><div id="k_2320154357817M4UGKWY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320154358XWDY827OME">​</span></div><div id="k_2319463429WWJ4YID8L7" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2319463430MFMESIWFQ9">​</span></div><div id="k_23194634295E69YMNQ9F" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(237, 252, 239); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(5, 126, 255); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(237, 252, 239); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 118, 240); vertical-align: baseline;" id="k_2320024962DT76JEBZVG">Demo</span></div><div id="k_2319462437Y86Y82INBM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2319462438VJKNJJ2SAB">​</span></div><div id="k_2319462475GACVS7GMAX" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 59px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23205612058HGGKF8DOR">1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2319462476J384PDJ9XG">​定义工具栏div容器</span></div><div id="k_2320554579A8G5JPAEJD" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 81px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2320554734ZC7CIQ4JFM"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="color: rgb(167, 167, 167); background: none;">           </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">style</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"</span><span style="color: rgb(161, 100, 75); background: none;">margin-top:12px;margin-bottom: 12px;"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"ctrtools"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></div></pre></span></div><div id="k_2320554743UQ5D7NZMOC" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 59px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23205547442VNOTZ13PT">2、创建toolbar实例</span></div><div id="k_2319462557J7IP3UK5AU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 80px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23194625588CSEKBVKUG"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;">        </span><span style="color: rgb(61, 108, 40); background: none;">/**事件集合**/</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">        </span><span style="background: none;"><font style="background: none;">window<span style="font-weight: bold; color: rgb(255, 0, 5); background: none;">.</span></font></span><span style="color: rgb(255, 0, 5); background: none; font-weight: bold;">toolMethods</span><span style="color: rgb(122, 122, 122); background: none;"> = {</span></div><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;">            </span><span style="color: rgb(15, 224, 0); background: none;">test</span><span style="color: rgb(66, 130, 164); background: none;">:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(122, 122, 122); background: none;"> (</span><span style="color: rgb(66, 130, 164); background: none;">prs</span><span style="color: rgb(122, 122, 122); background: none;">) {</span></div><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(130, 130, 80); background: none;">alert</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(122, 122, 122); background: none;">.</span><span style="color: rgb(130, 130, 80); background: none;">stringify</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(66, 130, 164); background: none;">prs</span><span style="color: rgb(122, 122, 122); background: none;">));</span></div><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;">            },</span></div><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;">            </span><span style="color: rgb(15, 224, 0); background: none;">test1</span><span style="color: rgb(66, 130, 164); background: none;">:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(122, 122, 122); background: none;"> (</span><span style="color: rgb(66, 130, 164); background: none;">prs</span><span style="color: rgb(122, 122, 122); background: none;">) {</span></div><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(130, 130, 80); background: none;">alert</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(122, 122, 122); background: none;">.</span><span style="color: rgb(130, 130, 80); background: none;">stringify</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(66, 130, 164); background: none;">prs</span><span style="color: rgb(122, 122, 122); background: none;">));</span></div><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;">            }</span></div><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;">        };</span></div><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;">        </span></div><div style="color: rgb(122, 122, 122); background: none;"><span style="color: rgb(122, 122, 122); background: none;"><div style="color: rgb(122, 122, 122); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">        </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(66, 130, 164); background: none;">ctrTools</span><span style="color: rgb(122, 122, 122); background: none;"> = </span><span style="color: rgb(41, 111, 169); background: none;">new</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(33, 156, 131); background: none;">$B</span><span style="color: rgb(122, 122, 122); background: none;">.</span><span style="color: rgb(33, 156, 131); background: none;">Toolbar</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(130, 130, 80); background: none;">$</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"#ctrtools"</span><span style="color: rgb(122, 122, 122); background: none;">), {</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">align:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'center'</span><span style="color: rgb(122, 122, 122); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//对齐方式，默认是left 、center、right</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">style:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'normal'</span><span style="color: rgb(122, 122, 122); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// plain / min  / normal /  big                </span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">showText:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(122, 122, 122); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// min 类型可以设置是否显示文字</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">methodsObject:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'<span style="color: rgb(255, 0, 5); background: none;">toolMethods</span>'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                </span><span style="color: rgb(66, 130, 164); background: none;">buttons:</span><span style="color: rgb(122, 122, 122); background: none;"> [{</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(66, 130, 164); background: none;">id:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'btn1'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(66, 130, 164); background: none;">text:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'按钮1'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(66, 130, 164); background: none;">click:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'<span style="color: rgb(15, 224, 0); background: none;">test</span>'</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                }, {</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(66, 130, 164); background: none;">id:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'btn2'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(66, 130, 164); background: none;">text:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'按钮2'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(66, 130, 164); background: none;">methodsObject:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'<span style="color: rgb(255, 0, 5); background: none;">toolMethods</span>'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(66, 130, 164); background: none;">click:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'<span style="color: rgb(15, 224, 0); background: none;">test1</span>'</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                }, {</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(66, 130, 164); background: none;">id:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'btn3'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(66, 130, 164); background: none;">text:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'按钮3'</span><span style="color: rgb(122, 122, 122); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    </span><span style="color: rgb(130, 130, 80); background: none;">click</span><span style="color: rgb(66, 130, 164); background: none;">:</span><span style="color: rgb(122, 122, 122); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(66, 130, 164); background: none;">prs</span><span style="color: rgb(122, 122, 122); background: none;">){</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                        </span><span style="color: rgb(130, 130, 80); background: none;">alert</span><span style="color: rgb(122, 122, 122); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"按钮3"</span><span style="color: rgb(122, 122, 122); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">                }]</span></div><div style="background: none;"><span style="color: rgb(122, 122, 122); background: none;">            });</span></div></div></span></div></div></pre></span></div><div id="k_2319462583L2AMKUONZK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2319462584AP3BW4UHQA">​</span></div><div id="k_2320011232S9JSQG4EHZ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2320011234NSUJA117QT">​</span></div>
    
</div>