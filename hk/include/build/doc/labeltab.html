<div style="position:relative">

<div id="k_2420014038SM2S2RGXZF" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 248, 255); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(122, 142, 255); border-image: initial; padding-left: 20px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 248, 255);" id="k_24200329416ND7BW22QR">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2420032944I2GKER2D5S">​<img id="k_24200329455YT2LTR5QA" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABHUlEQVQoU42SO0sDQRRGzzeTVhBfSW2nhU0KS3+C2JkinYgIEkFIJ1haWYhCtBEbsbGwSGtrbWXhH8hm0cIggmTnysbsGp/JlDP3XO537oiRj2kGVuQoazTGVPRUMPZCoDYClAMNYDcKHA6BPkZyjgOgaYF6hF7+hUrYEo5zE62QUI3RQxpHk9hcwbMQJVyBulnGKazsPccY84j1KNFl9qais1Ng1cRmO+ECFPqNzgSLZhxFgTroNYeygn7HjZBw4zwNwbIZd91A5RHdD1ruZcpnhwkglphN783YTm2B7AcEn1olxnoANEPCWoxa33c5YM8KJccOYt+MDoFqhK5/W/4X5SVs2jwnMjpvga0n9DwUSgtSMR7G2+j2ry/2Dj6Rawi4l1CsAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 248, 255);" id="k_2420032947ZOH8MPWMXJ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 248, 255);" id="k_2420033848YL6QMKKU2Z">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(247, 248, 255); letter-spacing: 1px;" id="k_2420033849S4F44YPX4D">API与构造参数</span></div><div id="k_2420021906FJJPHNY3AM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420021907CRG1G51XDA">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_24200441786PW51B4R1Z" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 22px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324200512F4HFD89DMBZ7"><tbody><tr id="row_0"><td id="k_2420051225CG44MHSWCE" tabindex="0" row="0" col="0" w="1313" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1315px; background-color: rgb(247, 247, 252);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 247, 252);" id="k_2420051226CRS5E3RQE9"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252);" id="k_2420051226MX8WWT9ZOW">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252);" id="k_2420220723OVMEHAUK43">var tab = new $B.Labeltab($("#wrap"),opts); opts 参数说明</span></p></td></tr><tr id="row_1"><td id="k_24200512354XOSJ6W4N3" tabindex="0" row="1" col="0" w="350" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 352px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420051236FPU28T1HX4"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420051237MTXFFSP9LU">​参数名</span></p></td><td id="k_2420051239VBM6X2SBLV" tabindex="0" row="1" col="1" w="323" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 325px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420051240DN517OO3WH"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420051240NR6JGD33NT">​参数值</span></p></td><td id="k_2420051243H5TTKFDWFB" tabindex="0" row="1" col="2" w="636" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 638px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420051244JP4V2GJMF5"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420051245Q4U9JCDOS6">说明​</span></p></td></tr><tr id="row_2"><td id="k_2420083887ECAZZ2HOX1" tabindex="0" row="2" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 352px;" w="350" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420083888GTXV2POKNG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420213736UG8H67FKSR">​​​tabs</span></p></td><td id="k_2420083891TXTOJ6V2EN" tabindex="0" row="2" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 325px;" w="323" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420083892LCOFWPBFUL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420232072DBBJIJ5HMV">请参考《</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2420232073CO3HEGSDOI">tabs参数说明</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420232074IDEMHO4MFK">》</span></p></td><td id="k_2420083894S5WPD6K9DW" tabindex="0" row="2" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 638px;" w="636" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_242008389519L1FHMJAB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420215286WAREOWZY1M">​</span></p></td></tr><tr id="row_3"><td id="k_2420104978VCKDFLUUDC" tabindex="0" row="3" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 352px;" w="350" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420104979NBSQYG2KM4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420193702Y47646SERU">​​onclick(title)</span></p></td><td id="k_24201049827K8JL85MQU" tabindex="0" row="3" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 325px;" w="323" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420104983OKIL4KL863"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24201108458K5CKBOPDL">onclick</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420110846Z4M48XE3IX">=</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420110847QCRTJFAUXD"> function (title){}</span></p></td><td id="k_2420104985ULJMNU26XI" tabindex="0" row="3" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 638px;" w="636" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420104986YQE1YY6N7L"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420104987GY7I47XKKX">​点击事件回调</span></p></td></tr><tr id="row_4"><td id="k_2420104730TOVMDHRD7O" tabindex="0" row="4" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 352px;" w="350" h="78"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420104731D4OA7RMAIO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420113210Z5UXSZL9XO">onLoaded</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420113213U6W7OD35X4">(title, data)</span></p></td><td id="k_2420104734QUA3WPCHP7" tabindex="0" row="4" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 325px;" w="323" h="78"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420104735Y18UD3UZIZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420113898RY5ROMXG95">onLoaded</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420113899QHQK2FZUZF">=</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420113800483E8ZGMKX"> function (title, data){}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2420114508PCFGYX4A2K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_242011450917LIXDAV1U">​​title：点击的tab标题</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2420121857PHCE8DK23B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420121858H8SZQ94VZV">data：请求返回的数据</span></p></td><td id="k_2420104737DR9RYMSE4H" tabindex="0" row="4" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 638px;" w="636" h="78"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420104739S7VUQOWNSX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420104740U3WHY3WQP4">​​加载完成回调</span></p></td></tr></tbody></table></div><div id="k_24200219108UDEHSI1FE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span></div><div id="k_24201034157CO5PWW7AH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420103415LZKMGWR2NN">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_24201739164M9NZZP6VE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 22px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 20px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324201753YT1XGYC23M8H"><tbody><tr id="row_0"><td id="k_24201753249EGI4CO6QT" tabindex="0" row="0" col="0" w="1313" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1315px; background-color: rgb(247, 247, 252);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 247, 252);" id="k_2420175325UN6TVT3YAC"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252);" id="k_24201753264M5FE4HPTS">​​tabs参数说明&nbsp;</span></p></td></tr><tr id="row_1"><td id="k_2420175336SVF8C39KEV" tabindex="0" row="1" col="0" w="435" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 437px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420175337NK7EPWLSM1"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420175337Q81QNUMOVF">​参数名</span></p></td><td id="k_2420175340PUCCAN4HFB" tabindex="0" row="1" col="1" w="437" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420175341XFULQITXHK"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420175342RWW5BZNR5P">参数值​</span></p></td><td id="k_2420175350ICFAOCXVBN" tabindex="0" row="1" col="2" w="437" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px; background-color: rgb(247, 247, 252); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(247, 247, 252);" id="k_2420175351XEXTIUDAW8"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 247, 252); text-align: center;" id="k_2420175351DKZTWG2GDH">说明​</span></p></td></tr><tr id="row_2"><td id="k_2420175355PTQISUUNRW" tabindex="0" row="2" col="0" w="435" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 437px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420175356UMP2YT6SIE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420222061MY3ERKCEC5">​</span></p></td><td id="k_2420175360VWXQGID1EP" tabindex="0" row="2" col="1" w="437" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420175361FBSIGXUI5E"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420222446ESFD7NGNEC">​</span></p></td><td id="k_2420175364N7MRAVJAS2" tabindex="0" row="2" col="2" w="437" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420175364UMY759XUDU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24201753659CG7ALQNNA">​</span></p></td></tr><tr id="row_3"><td id="k_2420202325BHQM19IM3I" tabindex="0" row="3" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 437px;" w="435" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202326XG7W748R46"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202327ZY53M2E7QY">​​title</span></p></td><td id="k_2420202330ASIQ4KJTC4" tabindex="0" row="3" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202331NXHDVR7R52"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202332ZQVDYZQ733">​</span></p></td><td id="k_2420202335UV2AWU57MK" tabindex="0" row="3" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202336X57CTULHE8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420204914J7HDIR8JSV">​标题</span></p></td></tr><tr id="row_4"><td id="k_24202022874D42AWA6R1" tabindex="0" row="4" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 437px;" w="435" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202288I1E7AGN136"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202289LDVF3BC4XO">​​actived</span></p></td><td id="k_242020229114G3COHB3V" tabindex="0" row="4" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202291ZJNMTCOGE8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202292KUHNNMM7AX">​​​​默认值：undefined</span></p></td><td id="k_2420202294PJSM4XNDRV" tabindex="0" row="4" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202295LU8BPTC3FP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202296SRHQLBC1SY">​​​​是否为激活状态</span></p></td></tr><tr id="row_5"><td id="k_2420202104C8WGCELAI1" tabindex="0" row="5" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 437px;" w="435" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202105M59FUZODW2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202106213ZXFSPD9">​​​​iconCls</span></p></td><td id="k_24202021089UG2NF39FO" tabindex="0" row="5" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202109HNEG4ZULHL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202110VMQERFJYAM">​​​​默认值：undefined</span></p></td><td id="k_2420202112TXLJ84YUVX" tabindex="0" row="5" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202112FRR3GZ5DMK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202113AQXDGJ4BDO">​​​​图标（font-awesome字体图标）</span></p></td></tr><tr id="row_6"><td id="k_2420202037W1L1C4VXZX" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 437px;" w="435" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202037OJK44LKTYD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24202020383HQ7A3PNLM">​​​​content</span></p></td><td id="k_24202020403QG9DPJW9T" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202041QKA8CLU3AP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202042ZNJJWA6HNA">​​​​默认值：undefined</span></p></td><td id="k_2420202044MB9NCBK2UL" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420202045VLBTALZFBJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420202046E57D9AL6UX">​​​​静态内容</span></p></td></tr><tr id="row_7"><td id="k_2420201917RHMWUDDSEY" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 437px;" w="435" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_242020191826PC53ZOOF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_242020191838K1F7XTJZ">​​​​url</span></p></td><td id="k_2420201922P8ALEBESTQ" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420201923MU1AN21BLW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24202019241PMFFQGHMU">​​​​默认值：undefined</span></p></td><td id="k_2420201926PIDR2KRMJ8" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;" w="437" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420201927N34JQKZVPO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420201927EKEKBZ1Y8V">​​​​url地址</span></p></td></tr><tr id="row_8"><td id="k_2420175367NSGIKE7B6P" tabindex="0" row="8" col="0" w="435" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 437px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420175367KULDBXOHXA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420175369TYM196IJRT">​​​​dataType</span></p></td><td id="k_2420175371G77BQO5HB8" tabindex="0" row="8" col="1" w="437" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420175372XGQJYNKYBF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420175373RNGX5TMHMH">​​​​默认值：html</span></p></td><td id="k_24201753754V9FSL9MQN" tabindex="0" row="8" col="2" w="437" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 439px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2420175375J6A4IZT1IB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24201753768Q24BAXA1T">​​​​请求内容类型：html / iframe / json</span></p></td></tr></tbody></table></div><div id="k_2420224318VCGKTF1EB1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;;" id="k_2420224319LOOCH9ZLYP">​</span></div><div id="k_2420022255EBG25YRHA1" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(247, 248, 255); padding-left: 20px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(122, 142, 255); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 248, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2420043567AH6YSCKJAO">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2420043570FRALB7C4GM">​<img id="k_2420043571GGXF1HC5R9" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABHUlEQVQoU42SO0sDQRRGzzeTVhBfSW2nhU0KS3+C2JkinYgIEkFIJ1haWYhCtBEbsbGwSGtrbWXhH8hm0cIggmTnysbsGp/JlDP3XO537oiRj2kGVuQoazTGVPRUMPZCoDYClAMNYDcKHA6BPkZyjgOgaYF6hF7+hUrYEo5zE62QUI3RQxpHk9hcwbMQJVyBulnGKazsPccY84j1KNFl9qais1Ng1cRmO+ECFPqNzgSLZhxFgTroNYeygn7HjZBw4zwNwbIZd91A5RHdD1ruZcpnhwkglphN783YTm2B7AcEn1olxnoANEPCWoxa33c5YM8KJccOYt+MDoFqhK5/W/4X5SVs2jwnMjpvga0n9DwUSgtSMR7G2+j2ry/2Dj6Rawi4l1CsAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 248, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2420043572HP3IO4BY6G">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(247, 248, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2420043568YHRKBEGGXD">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(247, 248, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_24200424904EO9ZE5Y8Q">Demo</span></div><div id="k_2420021967MPBMUNX2C7" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2420021968FCW8FKBBRI">​</span></div><div id="k_2420022035COS6UB2AS2" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 21px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420152940S86FEMAD6C">1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420022036YHURT4936S">​定义容器div</span></div><div id="k_242014388911JC38QOE7" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 21px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_242014395961J5M8Q8LZ"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"wrap"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></pre></span></div><div id="k_2420143967GJMN166MSW" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 21px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420144695RBOIULV9A5">​2、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24201441784DHBW82SCP"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420143968N8NFR3SWWA">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420143969JAYCQYWLOS">​定义opts项目</span></div><div id="k_2420144606OMOBAJTMA6" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 21px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24201446075H4AFES7CR"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​var  <span style="background: none; color: rgb(111, 175, 209);">tabs =</span><span style="background: none; color: rgb(167, 167, 167);"> [{</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-codeopen'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'静态内容[表格]'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">actived:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">content:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">$tableWrap</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'iframe加载'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-file-word'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">actived:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'test.html'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">dataType:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'iframe'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'HTML片段'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">actived:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fragment.html'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">dataType:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'html'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-desktop'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">title:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'JSON标签请求'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">actived:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'/bui/api/json?flag=datagridTree'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">dataType:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'json'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">   ]；</span></div></div></pre></span></div><div id="k_2420144184IST9K93GGO" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 21px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420151952ZVFNGZYUPW">​3、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420144185KCR3CW2IBI"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420144186GLVKDFQBH6">​创建对象</span></div><div id="k_2420151958K49EWVME7S" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 21px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420151959GC9NRASRQ6"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">           </span><span style="background: none; color: rgb(41, 111, 169);">var</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">tab</span><span style="background: none; color: rgb(167, 167, 167);"> = </span><span style="background: none; color: rgb(41, 111, 169);">new</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(33, 156, 131);">$B</span><span style="background: none; color: rgb(167, 167, 167);">.</span><span style="background: none; color: rgb(33, 156, 131);">Labeltab</span><span style="background: none; color: rgb(167, 167, 167);">(</span><span style="background: none; color: rgb(175, 175, 125);">$</span><span style="background: none; color: rgb(167, 167, 167);">(</span><span style="background: none; color: rgb(161, 100, 75);">"#wrap"</span><span style="background: none; color: rgb(167, 167, 167);">), {</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onclick</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">title</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(152, 89, 147); background: none;">if</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">title</span><span style="color: rgb(167, 167, 167); background: none;"> === </span><span style="color: rgb(161, 100, 75); background: none;">"静态内容[表格]"</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                     </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onLoaded</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">title</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                   </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">tabs:</span><span style="color: rgb(167, 167, 167); background: none;"> <span style="color: rgb(255, 0, 5); background: none;">tabs</span></span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            });</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">        }</span></div></div></pre></span></div><div id="k_24201521573WKF67YVJ8" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 21px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2420152158HVS24IHAHR">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div>

</div>