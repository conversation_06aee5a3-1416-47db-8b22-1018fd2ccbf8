<div style="position:relative">
<div id="k_2309074132VTN5UD7GPO" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(159, 154, 252); border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(24, 3, 255); border-image: initial; padding-left: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(159, 154, 252); letter-spacing: 2px;" id="k_2309094811NWDEZXWMI5">介绍</span></div><div id="k_2309075102S899TZOBTL" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: 977px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(168, 168, 250); border-bottom: none; border-left: 1px solid rgb(168, 168, 250); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309075103LLK4UVOI8M">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_23092747903D5KMWYU3J">​<img id="k_2309274791RDE7LMQ6Y1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABS0lEQVQ4T6WSvUrDYBSGnxNMutRREFwUhPYCxMVFEPxBurRgk8Ei3oCLIG4BR130AnSQ0iSbiLiIDuIFiEtxKugmLoJCE/sdiXWS2lT6red7nwPveYQhnwyZJxPg+2oVi0xZFk9raxL/XpgJCIL4GNgAvXTd3Oq/AGH4WVI1dWBUlVPPc2oDA6JI851OciPCDGgLnCXXlceBAY1Ge09EdgEFtl3XOexVeM8OoiieNUbPQMZVuc7n7VKpJB89AUEQP6gyZllSqVbtu7T1QiG+EJFl4EVVKp5n3/51bgmC9jPIhKo2wVkRSRaBI8AB3Xfd3E4/VyQI4i3gABgBrkAnQaaBe8exF8plee0L6IqSnADr0BVLlXfL0s1qNRdlmfodSE9mTHIOzP8E6s2mXfN9MQMB0k9hmMwZY9KNb2kXnietrHA6z1Q5CzI04AtYxnIRbXNKAAAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23092747925U7TRDGDS6">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23092421075325N81DML">dragga</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23092451797ENDIKS1J7">ble组件采用jquer</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_230943540329L8ZJJ7J7">y</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309435404WJCOD74KO3">标准风</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309283454WZ96I91WJ6">格构建。</span></div><div id="k_2309150219VZSR3FCLFG" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: 977px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(168, 168, 250); border-bottom: none; border-left: 1px solid rgb(168, 168, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309275625GRY4KVT18J">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2309275628JY36IEHR35">​<img id="k_2309275629ZCPQ9OONPJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABS0lEQVQ4T6WSvUrDYBSGnxNMutRREFwUhPYCxMVFEPxBurRgk8Ei3oCLIG4BR130AnSQ0iSbiLiIDuIFiEtxKugmLoJCE/sdiXWS2lT6red7nwPveYQhnwyZJxPg+2oVi0xZFk9raxL/XpgJCIL4GNgAvXTd3Oq/AGH4WVI1dWBUlVPPc2oDA6JI851OciPCDGgLnCXXlceBAY1Ge09EdgEFtl3XOexVeM8OoiieNUbPQMZVuc7n7VKpJB89AUEQP6gyZllSqVbtu7T1QiG+EJFl4EVVKp5n3/51bgmC9jPIhKo2wVkRSRaBI8AB3Xfd3E4/VyQI4i3gABgBrkAnQaaBe8exF8plee0L6IqSnADr0BVLlXfL0s1qNRdlmfodSE9mTHIOzP8E6s2mXfN9MQMB0k9hmMwZY9KNb2kXnietrHA6z1Q5CzI04AtYxnIRbXNKAAAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23092756306N8UOQRWRK">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309275626Q9JRL9U5MJ">丰富的拖动选项，可控制定时触发拖动、代理拖动、自定义响应拖动的元素，定义拖动时候层级</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309194147QUIESKIKX5">，定义拖动方向</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309194148GSNHYYHBT2">等。</span></div><div id="k_2309172570LSXJWU5O83" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: 977px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(168, 168, 250); border-bottom: none; border-left: 1px solid rgb(168, 168, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309280969KUW57QESF2">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2309280971MO1VOTY7QF">​<img id="k_2309280972YEBLNU18IX" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABS0lEQVQ4T6WSvUrDYBSGnxNMutRREFwUhPYCxMVFEPxBurRgk8Ei3oCLIG4BR130AnSQ0iSbiLiIDuIFiEtxKugmLoJCE/sdiXWS2lT6red7nwPveYQhnwyZJxPg+2oVi0xZFk9raxL/XpgJCIL4GNgAvXTd3Oq/AGH4WVI1dWBUlVPPc2oDA6JI851OciPCDGgLnCXXlceBAY1Ge09EdgEFtl3XOexVeM8OoiieNUbPQMZVuc7n7VKpJB89AUEQP6gyZllSqVbtu7T1QiG+EJFl4EVVKp5n3/51bgmC9jPIhKo2wVkRSRaBI8AB3Xfd3E4/VyQI4i3gABgBrkAnQaaBe8exF8plee0L6IqSnADr0BVLlXfL0s1qNRdlmfodSE9mTHIOzP8E6s2mXfN9MQMB0k9hmMwZY9KNb2kXnietrHA6z1Q5CzI04AtYxnIRbXNKAAAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309280973OXW6PHL5EN">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309280970X49OWQXY6L">解决浏览器</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309183485C8S42E39US">未发生实际位移而会触发​mousemove的bug。</span></div><div id="k_2309184521ZIAY2E57W7" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: 977px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(168, 168, 250); border-bottom: none; border-left: 1px solid rgb(168, 168, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309281720MQWMQCG6X8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2309281723TZ4D5YKJ7W">​<img id="k_2309281723K3V5LDJ9BS" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABS0lEQVQ4T6WSvUrDYBSGnxNMutRREFwUhPYCxMVFEPxBurRgk8Ei3oCLIG4BR130AnSQ0iSbiLiIDuIFiEtxKugmLoJCE/sdiXWS2lT6red7nwPveYQhnwyZJxPg+2oVi0xZFk9raxL/XpgJCIL4GNgAvXTd3Oq/AGH4WVI1dWBUlVPPc2oDA6JI851OciPCDGgLnCXXlceBAY1Ge09EdgEFtl3XOexVeM8OoiieNUbPQMZVuc7n7VKpJB89AUEQP6gyZllSqVbtu7T1QiG+EJFl4EVVKp5n3/51bgmC9jPIhKo2wVkRSRaBI8AB3Xfd3E4/VyQI4i3gABgBrkAnQaaBe8exF8plee0L6IqSnADr0BVLlXfL0s1qNRdlmfodSE9mTHIOzP8E6s2mXfN9MQMB0k9hmMwZY9KNb2kXnietrHA6z1Q5CzI04AtYxnIRbXNKAAAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309281725VGWV62WQPI">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309281721CXLPVAOJZQ">完善的</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309221627ER938O57PD">拖动</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309221628ZFJPSXRUB2">过程</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309221238XPYW3NR4TY">监听机制，可监听onDragReady、onStartDrag、onDrag、onStopDrag、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23092048381UGWOKGHDU">onMouseUp拖动过程中的每一个步骤。</span></div><div id="k_2309213606GXC9Q7G4TP" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: 977px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px; border-top: none; border-right: 1px solid rgb(168, 168, 250); border-bottom: 1px solid rgb(168, 168, 250); border-left: 1px solid rgb(168, 168, 250); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23092831666Q5CNI1IJB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2309283170BXRNWMZY8I">​<img id="k_2309283170M9W3SSB6MV" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABS0lEQVQ4T6WSvUrDYBSGnxNMutRREFwUhPYCxMVFEPxBurRgk8Ei3oCLIG4BR130AnSQ0iSbiLiIDuIFiEtxKugmLoJCE/sdiXWS2lT6red7nwPveYQhnwyZJxPg+2oVi0xZFk9raxL/XpgJCIL4GNgAvXTd3Oq/AGH4WVI1dWBUlVPPc2oDA6JI851OciPCDGgLnCXXlceBAY1Ge09EdgEFtl3XOexVeM8OoiieNUbPQMZVuc7n7VKpJB89AUEQP6gyZllSqVbtu7T1QiG+EJFl4EVVKp5n3/51bgmC9jPIhKo2wVkRSRaBI8AB3Xfd3E4/VyQI4i3gABgBrkAnQaaBe8exF8plee0L6IqSnADr0BVLlXfL0s1qNRdlmfodSE9mTHIOzP8E6s2mXfN9MQMB0k9hmMwZY9KNb2kXnietrHA6z1Q5CzI04AtYxnIRbXNKAAAAAABJRU5ErkJggg==" style="width:16px;height:16px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23092831729S2E8G9V7D">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309283168WUM7NTLBG3">利用jquery事件的</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309245183WACU1G58DD">namespace，</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309283457TQGPFZA1I1">支持一个元</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2309283458XEWYWMZ5LG">素绑定多种拖动，可动态开启，禁用某一个拖动。</span></div><div id="k_2309075413Q84V9428HU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span></div><div id="k_2309075690VOH2LUY59L" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(159, 154, 252); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(24, 3, 255); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(159, 154, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline; letter-spacing: 2px;" id="k_2309300233X858RF4UTX">API</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(159, 154, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline; letter-spacing: 4px;" id="k_23093010491C4JFNJXCJ">及</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(159, 154, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline; letter-spacing: 2px;" id="k_2309301050IMU97YF21O">构造参数</span></div><div id="k_23090814598EF37O2GIA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="201903230931034I6ZBKUDADWP"><tbody><tr id="row_0"><td id="k_2309310359SH9Q5MFMP8" tabindex="0" row="0" col="0" w="1306" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1337px; background-color: rgb(190, 206, 250); color: rgb(24, 3, 255);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(190, 206, 250); color: rgb(24, 3, 255);" id="k_2309310361RRRB7TWNCT"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(190, 206, 250);" id="k_2309310362IHISVEKVBP">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(190, 206, 250);" id="k_2309330345FI7VYIQUFP">$("#divId").draggable(opts);&nbsp; &nbsp;opts 参数说明</span></p></td></tr><tr id="row_1"><td id="k_2309310377O8KIBTKBQN" tabindex="0" row="1" col="0" w="172" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px; background-color: rgb(221, 221, 221); text-align: center;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(221, 221, 221);" id="k_2309310378K6SASPE16M"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(221, 221, 221); text-align: center;" id="k_230931037938RV46AYLG">​参数名称</span></p></td><td id="k_23093103813FS5G1EVRD" tabindex="0" row="1" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px; background-color: rgb(221, 221, 221); text-align: center;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(221, 221, 221);" id="k_2309310383649K2A7IB5"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(221, 221, 221); text-align: center;" id="k_2309310384YZT5QL3WVX">参数值​</span></p></td><td id="k_2309310387QALQ3E8TUV" tabindex="0" row="1" col="2" w="707" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px; background-color: rgb(221, 221, 221); text-align: center;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(221, 221, 221);" id="k_2309310388MV8SY7L167"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(221, 221, 221); text-align: center;" id="k_2309310389F2BFHW19OB">说明​</span></p></td></tr><tr id="row_2"><td id="k_2309310391HGNSFFQW8M" tabindex="0" row="2" col="0" w="172" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310392UNXX2OLSOO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23093103947TFM6AB1XZ">​​nameSpace</span></p></td><td id="k_2309310396QXM923OELJ" tabindex="0" row="2" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310397OBZN799GY1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23093103998IYCIOMWX7">​默认值：​draggable</span></p></td><td id="k_23093103021OFV9PEF7X" tabindex="0" row="2" col="2" w="707" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310303ODVFJK9GXW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309310304RT653RRHSR">​jquery事件命名空间，用于一个元素绑定多个拖动</span></p></td></tr><tr id="row_3"><td id="k_2309310307WCJ1D4HWU5" tabindex="0" row="3" col="0" w="172" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23093103086YKUZ2KNKX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309310309TUSDWSO9IJ">​​which</span></p></td><td id="k_2309310311E2S6B4AXS9" tabindex="0" row="3" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310312683AQ9LDAW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309310313RC47ZFN8GT">​默认值：undefined ，which = 3(右键响应) / 1(左键响应)&nbsp;</span></p></td><td id="k_2309310317I7F2P33CUB" tabindex="0" row="3" col="2" w="707" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310318WY1QPLXW7H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23093103205YJ944BU6H">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309372788CGAH41KO5A">​响应拖动的鼠标键码</span></p></td></tr><tr id="row_4"><td id="k_2309310322AVUGBW2ODR" tabindex="0" row="4" col="0" w="172" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23093103239531NSSQSL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309310324WUJCV8QROV">​​holdTime</span></p></td><td id="k_2309310327863IP8KER4" tabindex="0" row="4" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310329NFO6BITJ3K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309310330CGDCHDSJ8R">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309375920T3SY6UBAAO">​默认值：undefined ，毫秒数</span></p></td><td id="k_2309310333DNUUAT479C" tabindex="0" row="4" col="2" w="707" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310334FARE1EIGCP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23093103352MUK52H6W7">​按下鼠标 holdTime 毫秒后才可以拖动</span></p></td></tr><tr id="row_5"><td id="k_23093103365ZAC33NFVC" tabindex="0" row="5" col="0" w="172" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310337WASDF767UJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309310338OPYF4Q53JI">​defaultZindex</span></p></td><td id="k_2309310340UZAH2ZCB5P" tabindex="0" row="5" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310341PBBDMGIQZG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_230931034258V6L3UEN5">​默认值：​999999</span></p></td><td id="k_23093103436OZEAGD5EB" tabindex="0" row="5" col="2" w="707" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310344HPMM6EOXZ8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23093103453HMBHGOI7X">​被拖动元素，拖动时候设置的层级</span></p></td></tr><tr id="row_6"><td id="k_2309310347I4BEY6NAV7" tabindex="0" row="6" col="0" w="172" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310348TAD4BCFZ33"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309310349MOJ8QYU46Q">​​isProxy</span></p></td><td id="k_2309310351XT3QKQCSC1" tabindex="0" row="6" col="1" w="423" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310352HKND7F869N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309310353VPUO5VTFTF">​默认值：false</span></p></td><td id="k_2309310355XUFB4FHS37" tabindex="0" row="6" col="2" w="707" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309310356U8WCNZH75M"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23093103578HJL627PI9">​是否是代理拖动</span></p></td></tr><tr id="row_7"><td id="k_2309400251WKVTRHMA5T" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px;" w="172" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309400252MR9LGD5S9U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309400253I8WUPSNK8W">​​handler</span></p></td><td id="k_2309400255Q4JW8NLZEW" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px;" w="423" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309400257IX2ELAR2EW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309400257IYXVCN9Z6E">​默认值：undefined</span></p></td><td id="k_230940026071GOCZYFSB" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px;" w="707" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23094002613PMRL6JXBV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309400263LRE1GTRFLZ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309403395M1U8J7XBH5">​触发拖动的元素，jquery对象或者[#id\.class] ，如按下window的头部标题才可以拖动</span></p></td></tr><tr id="row_8"><td id="k_23094001747OO5ELABH8" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px;" w="172" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309400175RN5UC2W2WE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309400175XYMKSR2F7P">​​cursor</span></p></td><td id="k_2309400178H2KG5IJK3J" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px;" w="423" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309400179FM5EEPDFFL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309400180MN1RS73JCQ">​默认值：move</span></p></td><td id="k_2309400182KR26S3A56T" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px;" w="707" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309400182F1J12XDYCI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309400183DVAXTZOAZ2">​移动时候的鼠标样式</span></p></td></tr><tr id="row_9"><td id="k_2309395920I15MSTMK3I" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px;" w="172" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23093959213DV8SQ4QJP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309395922UR4DQREBVA">​​axis</span></p></td><td id="k_2309395925MK33OVI53Q" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px;" w="423" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309395926XA58XHX5FZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311095961UDZEDP5HAC">默认值：undefined</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311095963BT5DUFX7LH">，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311095964SP4XUM7UFJ">axis = h水平移动 / v垂直移动</span></p></td><td id="k_2309395928DRXJ4I281T" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px;" w="707" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23093959298X7ZRA5S61"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309395930ABZYOYA3WJ">​​移动方向限制</span></p></td></tr><tr id="row_10"><td id="k_23094241962FM6WE2XKS" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 177px;" w="172" h="53" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309424196CBQY3X6KS1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23094241989AWLO4JSNJ">​​onDragReady</span></p></td><td id="k_23094241008CLOHQYF2N" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 432px;" w="423" h="53" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_230942410098XMY63WNJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23094241012EYRPZ56JF">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311131662AHD1SOWUKO">默认值：undefined，</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_231110397412FSQI3WJQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311123946X4JOI24GV8">onDragReady=function( </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 222, 36); background-color: rgb(255, 255, 255);" id="k_23111239472MRUAWCIYE">args</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311123948RBNHQWE7UH"> ){&nbsp; &nbsp; return true / false }</span></p></td><td id="k_2309424103EA8SBWPPU6" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 728px;" w="707" h="53" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23094241047IEXUXRB15"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_231112470289PHJBEUJ4">鼠标按下准备拖动前监听，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 222, 36); background-color: rgb(255, 255, 255);" id="k_2311124703XY6JNUH5GK">args</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311124704LXHDHSIZW6">：当前位置，偏移量等拖动参数</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2311113340A91W4DGGWO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_231111334199I3AOPI8L">​返回true则执行拖动，false不执行拖动</span></p></td></tr><tr id="row_11"><td id="k_2309424019RLOA3L3X3W" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 177px;" w="172" h="53" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309424020HURMQHT14S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_230942402115NWJ7L3WR">​​onStartDrag</span></p></td><td id="k_2309424023GL36XYDL8L" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 432px;" w="423" h="53" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23094240249WYUKWLSA4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23094240252L3P5W7WQG">​默认值：undefined，</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2311132193W5NM8YRLZK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311132743DQULNU7H4K">onStartDrag</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311134262J5YM2OJ3H2">=function(&nbsp;args&nbsp;){&nbsp; &nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23111342645XDQMOVQVC">&nbsp;}</span></p></td><td id="k_2309424027F6D5N32ZST" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 728px;" w="707" h="53" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309424028NG47M8OYX1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309424028LY2374KMEK">​开始拖动监听事件，args ：同上</span></p></td></tr><tr id="row_12"><td id="k_2309423811SLOG3DSJZD" tabindex="0" row="12" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 177px;" w="172" h="53" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309423812MRHTTPB3FC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309423813RNHTM56BMR">​​onDrag</span></p></td><td id="k_23094238155OSE4ZITOE" tabindex="0" row="12" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 432px;" w="423" h="53" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309423816D1ODC2EA2T"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_230942381753KXO9UK2I">​​默认值：undefined，</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2311143796ROP6ISJVBI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311143797EZ3ESOZLI7">onDrag=function(&nbsp;args&nbsp;){&nbsp; &nbsp;&nbsp;}</span></p></td><td id="k_2309423818IK99L5KTK6" tabindex="0" row="12" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 728px;" w="707" h="53" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309423819WO9LR33MPZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23094238204P2RE2OG2V">​拖动中监听事件，args：同上</span></p></td></tr><tr id="row_13"><td id="k_2309423751BABUJ5CNGF" tabindex="0" row="13" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 177px;" w="172" h="53" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309423752H9F57FTFX3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309423753VECSZNUOMK">​​dragEnd</span></p></td><td id="k_23094237556M3532CBVW" tabindex="0" row="13" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 432px;" w="423" h="53" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309423756D8H7RXQDX3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309423757WQ1HM3431P">​​​默认值：undefined，</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2311151321LVF67MFI8I"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311151322UWRKQWIUPV">dragEnd=function(&nbsp;args&nbsp;){&nbsp; &nbsp;&nbsp;}</span></p></td><td id="k_2309423759G2HSMUYTFT" tabindex="0" row="13" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 728px;" w="707" h="53" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2309423760Q3AYND9AUD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309423761LBSS7GVX2K">​拖动结束事件监听，args：同上</span></p></td></tr><tr id="row_14"><td id="k_2311154033OPQDRG3ZFQ" tabindex="0" row="14" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 177px;" w="172" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2311154034TU28L3XCC3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23111540357JNTRTJZDD">​​onMouseUp</span></p></td><td id="k_2311154038PC2WD7KXGS" tabindex="0" row="14" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 432px;" w="423" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_23111540395VQ68WE25L"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311154039SXDMEZUZBB">​​​​默认值：undefined，</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2311155579FHGBVNLFZV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311155580SUSOJD8KZ2">onMouseUp=function(&nbsp;args&nbsp;){&nbsp; &nbsp;&nbsp;}</span></p></td><td id="k_2311154041Z5G2NO7A3I" tabindex="0" row="14" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 728px;" w="707" h="53"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2311154042G7GUSOH488"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311164208OTFK6VDY51">放开鼠标事件监听，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 88, 232); background-color: rgb(255, 255, 255);" id="k_2311164209TAQ1HQZ3KY">当发生了拖动后，该事件不会被触发</span></p></td></tr></tbody></table></div><div id="k_2309081418KQZ5NBHNIA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23090814197W9YSQIG9W">​</span></div><div id="k_2311184855PT5IABJBA4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 27px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190323111858J86BV7QHWKPD"><tbody><tr id="row_0"><td id="k_2311185815B26Z5NH1V4" tabindex="0" row="0" col="0" w="1308" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1310px; background-color: rgb(168, 168, 250);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(168, 168, 250);" id="k_2311185816PC82KGYK1K"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(168, 168, 250);" id="k_2311192765H6L3GVRQVQ">实例 API</span></p></td></tr><tr id="row_1"><td id="k_2311185825EIHGY9OKJ4" tabindex="0" row="1" col="0" w="180" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 182px; background-color: rgb(221, 221, 221); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(221, 221, 221);" id="k_2311185826MI2H9QGF7K"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(221, 221, 221); text-align: center;" id="k_23111858277VCNECOWRA">​名称</span></p></td><td id="k_2311185830IPGPJWQYDJ" tabindex="0" row="1" col="1" w="417" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 419px; background-color: rgb(221, 221, 221); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(221, 221, 221);" id="k_2311185831KEAMGF3P7M"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(221, 221, 221); text-align: center;" id="k_2311185832CXBRCGTSA1">​参数</span></p></td><td id="k_2311185834ZACEVTKOZP" tabindex="0" row="1" col="2" w="707" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 709px; background-color: rgb(221, 221, 221); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(221, 221, 221);" id="k_2311185835FWGSK9WQP7"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(221, 221, 221); text-align: center;" id="k_23111858367LZU1P7X1Z">​说明</span></p></td></tr><tr id="row_2"><td id="k_23111858379O478QHKPM" tabindex="0" row="2" col="0" w="180" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 182px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2311185838IQJXROYHC8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311185839K871QUBBA6">​​unbind(nameSpace)</span></p></td><td id="k_2311185840Q7VBAF1F3W" tabindex="0" row="2" col="1" w="417" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 419px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2311185841A8HUC3VNL9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311185842QRZVC7KREH">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311221102SQ9HACYP89">nameSpace</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23112211037JYBV4YDVS">: 事件命名空间&nbsp;</span></p></td><td id="k_2311185843JA1J69C792" tabindex="0" row="2" col="2" w="707" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 709px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2311185844OD9HO598R2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311222572EHNM8WDIAS">解除命名空间为 [&nbsp;nameSpace ] 的拖动</span></p></td></tr><tr id="row_3"><td id="k_2311193622W133OWTD55" tabindex="0" row="3" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 182px;" w="180" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_231119362247QDHOT825"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311193623PTLC9UXBQQ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23112137661HYYEF885E">​enable(nameSpace)</span></p></td><td id="k_2311193625BINAQF9C29" tabindex="0" row="3" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 419px;" w="417" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2311193626FJC9JBNJUN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311193627XJBA7476UU">​​nameSpace: 事件命名空间&nbsp;</span></p></td><td id="k_2311193628UORJRWRNHB" tabindex="0" row="3" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 709px;" w="707" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2311193629R9UPPTERTX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311193630UCHRPT1AP4">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311222880BL472QU9HD">​启用命名空间为 [&nbsp;nameSpace ] 的拖动</span></p></td></tr><tr id="row_4"><td id="k_2311185846TMLEIJ8BMV" tabindex="0" row="4" col="0" w="180" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 182px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2311185847MO1V1SAC23"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311185848CRMO9VEQPE">​​disable(nameSpace)</span></p></td><td id="k_2311185849L556N8UVSA" tabindex="0" row="4" col="1" w="417" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 419px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2311185850FBMYRNU1K4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_231118585157K12DRZP4">​​nameSpace: 事件命名空间&nbsp;</span></p></td><td id="k_2311185852QE5LWH2J5N" tabindex="0" row="4" col="2" w="707" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 709px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_231118585346ZWZ64L9T"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311185854IHUS5I5MX2">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_23112249362FA1RNKFCC">​​禁用命名空间为 [&nbsp;nameSpace ] 的拖动</span></p></td></tr></tbody></table></div><div id="k_2311184899WR4XLCJK5V" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311184802637W4O1UAM">​</span></div><div id="k_2311195032C66YRVRD5H" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311195034VAT3AKFXJA">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_23090814332H5UEDFOQK" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(159, 154, 252); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(24, 3, 255); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(159, 154, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2309081434YT7119ZUV8">​Demo</span></div><div id="k_2309075633L3LWFESK6R" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2311270334IGGUMYUMVE">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_23112703379JUEATN72C">​<img id="k_2311270338EBYJVPZ9N4" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABxklEQVQ4T42SMWgTURzGv+/dXYWGJhe61dQ7lE7iIg46OIiz4iCVYqkXUq0KIoi4FCfpIlIQQa02JJZKsTjpLA6CKIhOTmK5S6Ob5JISUe/u/eWKKdIc4hs//r/3e9+fR2SclxCzUers/9HMf5gBo6wZZoVLo+GBxJCnRsKTU+v2u/8CU1vgtO8DchDgGyconD8CxtvhPuMf2y3R5kWq+K6R8GqWlQJhtdQpWoZ2INwjCmchWPUCu1p3wgqIcWo8BOVzlKig0sy3CArrTuuVEPsoXAPxVjRfQ+nnZb8YLozI4IDVOUHKYYEcgmCMwHshjrFWao/RkBVS3Zjyh56lt23vk75qyd04LqKvS8KJcrPwabNjzW25BFcEvO35+Sd/wylUdzunCLkskImyX/RTZms5m2ZTL5rRjtOTXwabPevyzu+l2Pr5WGI1nZp6+RZY37WxV1Q8l1P2mfE1tnsDq7ul0NXhI2pz1msMfcwAw6NQnLS0XPlFTINyCcI7A4LFSHEeWpa9hv2iH3TDCwDOCWQ47as1FpTCTNqL4DcADzzfvtcH1pzwJoCc1tZcZT33tTdQHe2OKBXNAuiWA/taH5j1H/+V/QYB28LNcfJyiQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2311270339K5PND1QVS2">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2311270335V4CGFOU5C7">定义需要移动的Div</span></div><div id="k_2311251047NIT8IIEN2I" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: 829px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;" id="k_2311251048KAPMA7T6R6"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">   </span><span style="background: none; color: rgb(83, 83, 83);">&lt;</span><span style="background: none; color: rgb(41, 111, 169);">div</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">id</span><span style="background: none; color: rgb(167, 167, 167);">=</span><span style="background: none; color: rgb(161, 100, 75);">"dragdiv"</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">style</span><span style="background: none; color: rgb(167, 167, 167);">=</span><span style="background: none; color: rgb(161, 100, 75);">"</span><span style="background: none; color: rgb(161, 100, 75);">width:350px;height:120px;background: #ccc;position: absolute; top:200px;left:50px;"</span><span style="background: none; color: rgb(83, 83, 83);">&gt;</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">        </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">h1</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"title"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">style</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"</span><span style="color: rgb(161, 100, 75); background: none;">line-height:22px;background-color: cadetblue;color:#fff;"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">我是可拖动的元素！！！！！！</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">h1</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">        </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">p</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span><span style="color: rgb(167, 167, 167); background: none;">任意区域可响应拖动</span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">p</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">        </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">style</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"</span><span style="color: rgb(161, 100, 75); background: none;">margin:0px auto;width: 80%;color:red;font-size: 20px"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"_angel"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">    </span><span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></div></div></pre></span></div><div id="k_2311234437V7OS5MSJFY" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_23112344371NNHX5LYL8">​</span></div><div id="k_2311263400Y9QN1QVMPV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2311271268DAFA7DNDK1">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2311271270NLVB5YDA5N">​<img id="k_23112712715WU3JJI6SW" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABxklEQVQ4T42SMWgTURzGv+/dXYWGJhe61dQ7lE7iIg46OIiz4iCVYqkXUq0KIoi4FCfpIlIQQa02JJZKsTjpLA6CKIhOTmK5S6Ob5JISUe/u/eWKKdIc4hs//r/3e9+fR2SclxCzUers/9HMf5gBo6wZZoVLo+GBxJCnRsKTU+v2u/8CU1vgtO8DchDgGyconD8CxtvhPuMf2y3R5kWq+K6R8GqWlQJhtdQpWoZ2INwjCmchWPUCu1p3wgqIcWo8BOVzlKig0sy3CArrTuuVEPsoXAPxVjRfQ+nnZb8YLozI4IDVOUHKYYEcgmCMwHshjrFWao/RkBVS3Zjyh56lt23vk75qyd04LqKvS8KJcrPwabNjzW25BFcEvO35+Sd/wylUdzunCLkskImyX/RTZms5m2ZTL5rRjtOTXwabPevyzu+l2Pr5WGI1nZp6+RZY37WxV1Q8l1P2mfE1tnsDq7ul0NXhI2pz1msMfcwAw6NQnLS0XPlFTINyCcI7A4LFSHEeWpa9hv2iH3TDCwDOCWQ47as1FpTCTNqL4DcADzzfvtcH1pzwJoCc1tZcZT33tTdQHe2OKBXNAuiWA/taH5j1H/+V/QYB28LNcfJyiQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2311271272G6R7LHU5VV">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2311271269FKGKBBKEGP">调用draggable( args )绑定拖动</span></div><div id="k_2311250071ES3PECSKP3" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 25px; width: 827px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2311250072QB26SO8G6C"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">opt1</span><span style="color: rgb(167, 167, 167); background: none;"> = {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">nameSpace:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'draggable'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//命名空间，一个对象可以绑定多种拖动方式</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">which:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">undefined</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//鼠标键码，是否左键1,右键3 才能触发拖动，默认左右键均可</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">isProxy:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否空代理</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">holdTime:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">undefined</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//按下鼠标500毫秒后才可以拖动</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">handler:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">undefined</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//触发拖动的对象</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;"> background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'move'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">disabled:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否禁用拖动</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">axis:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">undefined</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// v or h  水平还是垂直方向拖动 ，默认全向</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onDragReady</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> () { </span><span style="color: rgb(61, 108, 40); background: none;">//鼠标按下时候的事件，返回true则往下执行，返回false则停止</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onDragReady &gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt; return true"</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">$msg1</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">html</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onDragReady return true"</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(152, 89, 147); background: none;">return</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onMouseUp</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">$msg1</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">html</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"没有发生拖动，触发onMouseUp事件！！！"</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onMouseUp &gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt; "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onStartDrag</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">) { </span><span style="color: rgb(61, 108, 40); background: none;">//开始拖动</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">txt</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(161, 100, 75); background: none;">"&gt;&gt;&gt;&gt;&gt;&gt;onStartDrag nameSpace = "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">options</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">nameSpace</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">":&lt;br/&gt; data="</span><span style="color: rgb(167, 167, 167); background: none;"> +</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">txt</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">$msg1</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">html</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">txt</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">bodyWidth</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"body"</span><span style="color: rgb(167, 167, 167); background: none;">).</span><span style="color: rgb(175, 175, 125); background: none;">width</span><span style="color: rgb(167, 167, 167); background: none;">();</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">bodyHeight</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"body"</span><span style="color: rgb(167, 167, 167); background: none;">).</span><span style="color: rgb(175, 175, 125); background: none;">height</span><span style="color: rgb(167, 167, 167); background: none;">();</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">maxTop</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">bodyHeight</span><span style="color: rgb(167, 167, 167); background: none;"> - </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">height</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">maxLeft</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">bodyWidth</span><span style="color: rgb(167, 167, 167); background: none;"> - </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">width</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onDrag</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">) { </span><span style="color: rgb(61, 108, 40); background: none;">//拖动中</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(61, 108, 40); background: none;">//拖动限制</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(152, 89, 147); background: none;">if</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">top</span><span style="color: rgb(167, 167, 167); background: none;"> &lt; </span><span style="color: rgb(136, 161, 123); background: none;">0</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">top</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(136, 161, 123); background: none;">0</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(152, 89, 147); background: none;">if</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">left</span><span style="color: rgb(167, 167, 167); background: none;"> &lt; </span><span style="color: rgb(136, 161, 123); background: none;">0</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">left</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(136, 161, 123); background: none;">0</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(152, 89, 147); background: none;">if</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">top</span><span style="color: rgb(167, 167, 167); background: none;"> &gt; </span><span style="color: rgb(111, 175, 209); background: none;">maxTop</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">top</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">maxTop</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(152, 89, 147); background: none;">if</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">left</span><span style="color: rgb(167, 167, 167); background: none;"> &gt; </span><span style="color: rgb(111, 175, 209); background: none;">maxLeft</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">left</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">maxLeft</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">txt</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(161, 100, 75); background: none;">"onDrag nameSpace = "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">options</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">nameSpace</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">":&lt;br/&gt; "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                   </span><span style="color: rgb(61, 108, 40); background: none;">console.log(txt);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onStopDrag</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">) { </span><span style="color: rgb(61, 108, 40); background: none;">//拖动结束</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">txt</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(161, 100, 75); background: none;">"onStopDrag nameSpace = "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">options</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">nameSpace</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">":&lt;br/&gt; "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">state</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">_data</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">txt</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            };</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(61, 108, 40); background: none;">//绑定拖动1</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(111, 175, 209); background: none;">drag</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"#dragdiv"</span><span style="color: rgb(167, 167, 167); background: none;">).</span><span style="color: rgb(175, 175, 125); background: none;">draggable</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">opt1</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div></div></pre></span></div><div id="k_2309075471ST9M4SJNO8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309075472HVZYNAYRAS">​</span></div><div id="k_2309075411SDFO357N2K" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2309075412Y6QMWOQRY6">​</span></div><div id="k_2311093361N8PGC4GG7C" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311093362G2LA3V73TP">​</span></div>

</div>