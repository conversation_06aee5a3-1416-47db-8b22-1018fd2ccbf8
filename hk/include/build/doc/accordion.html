<div style="position:relative">
     
    <div id="k_2410042343M9V5PRR5DV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 243, 235); border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(210, 145, 250); border-image: initial; padding-left: 12px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235);" id="k_2410311170B1MZXXM5IP">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410311175XU86PRPXNK">​<img id="k_2410311176NOJ5TAE8CD" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABJElEQVQoU62SsUvDQBjF37uaCiImm0jFxEnBRTdxlepYXR20dHNQEV0VpJujW5cmVHEWpIO4a/8DcTK6OZmKFtTrnZwk0Aasgt5w3/H4ft97fBwRnwq0lXWjDQBlkgNG1lq3NDL50v3QVdJnKs0VA3sAdkjWiqGzbvRgrLkpBG5XQ/uiC0oAknko+BCY+RHy3egQwLwlswVlfUwp6KVOCNT7ABwQmditwcCLbgBMfgkabZLltdA+8L0nxySkxoKgWDYRT8dfht+UrNJ3o3q/6Cut3A0+JrmP3dcRCbkrJCvaUtsCPOsJneRao9J6Pwcwnd7gt041r7moFCaKD/ZR58bM+29Q4EXXAGa7pipu9XRKR/hVvH+Bqu7zHNG+TP5feiiAxif47rYUrtRrCwAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235);" id="k_2410311178HSSCC1OWLC">​&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235);" id="k_2410311171CCBR48LUBE">介绍</span></div><div id="k_2410043095CZYH6CVCII" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410363539SLBRN3AVS2">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410363542CA1QLQBUK5">​<img id="k_2410363542ZJMYARSCWF" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA/0lEQVQoU5WRv0vDcBDF37uY+qPabHVoMRkdRfBPKIiLo5OJzo7i5NJZ8G/QioPO2qHQtUMFJwdBEWMGu4YWgpTwPXFobKuF9rbj3ucdd4+YsTjQ18pJyUi6KTDzYs29vr3nn6qgGfdjFSruWvcIoucE7EygaFqw/f2PfGcYYs3r7irMDYCFcTcFbgHn4DDk12DGCzeuk9j57xSF9ixIxQ+ddgZcevEzgPVJtwu47YdO43eDFz8Q2JoecOMzEicTgBf2c5XgcynKNlyXknJq9+8AbAxDqpqIWntBVLgf+dJPc7XaK5rF9FSBgMplEI80cuxHKy2C+geYJews6WmhbzfbTQ0J38TYAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410363543T93Y2PWEF9">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410363540L5O83EKZUE">支持通过样式参数定义手风琴的ui样式。</span></div><div id="k_24103509279KGL5STNKJ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410364420UYCFNNSE2X">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_24103644224JIVM2LIUB">​<img id="k_24103644236PXECVU9ED" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA/0lEQVQoU5WRv0vDcBDF37uY+qPabHVoMRkdRfBPKIiLo5OJzo7i5NJZ8G/QioPO2qHQtUMFJwdBEWMGu4YWgpTwPXFobKuF9rbj3ucdd4+YsTjQ18pJyUi6KTDzYs29vr3nn6qgGfdjFSruWvcIoucE7EygaFqw/f2PfGcYYs3r7irMDYCFcTcFbgHn4DDk12DGCzeuk9j57xSF9ixIxQ+ddgZcevEzgPVJtwu47YdO43eDFz8Q2JoecOMzEicTgBf2c5XgcynKNlyXknJq9+8AbAxDqpqIWntBVLgf+dJPc7XaK5rF9FSBgMplEI80cuxHKy2C+geYJews6WmhbzfbTQ0J38TYAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410364424OY8844J3ZN">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_241036442151OP2NKXNU">支持fontawesome图标定义。</span></div><div id="k_24103531775DL5S6YWCK" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24103656168JR9DBWWQ7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410365618JE2SYW3ORQ">​<img id="k_2410365619HJX128AUXM" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA/0lEQVQoU5WRv0vDcBDF37uY+qPabHVoMRkdRfBPKIiLo5OJzo7i5NJZ8G/QioPO2qHQtUMFJwdBEWMGu4YWgpTwPXFobKuF9rbj3ucdd4+YsTjQ18pJyUi6KTDzYs29vr3nn6qgGfdjFSruWvcIoucE7EygaFqw/f2PfGcYYs3r7irMDYCFcTcFbgHn4DDk12DGCzeuk9j57xSF9ixIxQ+ddgZcevEzgPVJtwu47YdO43eDFz8Q2JoecOMzEicTgBf2c5XgcynKNlyXknJq9+8AbAxDqpqIWntBVLgf+dJPc7XaK5rF9FSBgMplEI80cuxHKy2C+geYJews6WmhbzfbTQ0J38TYAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410365620HA6AOP2LQH">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410365617LRBPG4ZU3K">支持远程数据加载（html片段/iframe嵌入页面/json数据）。</span></div><div id="k_2410043182PJOZM3CJIE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410043183N9W45S412J">​</span></div><div id="k_2410043143XD7ZQSG39X" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 243, 235); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(210, 145, 250); border-image: initial; margin-top: 0px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_24103153787R5NYRMR9D">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410315381UWYMR4RP7Q">​<img id="k_2410315381AG83AJB1IJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABJElEQVQoU62SsUvDQBjF37uaCiImm0jFxEnBRTdxlepYXR20dHNQEV0VpJujW5cmVHEWpIO4a/8DcTK6OZmKFtTrnZwk0Aasgt5w3/H4ft97fBwRnwq0lXWjDQBlkgNG1lq3NDL50v3QVdJnKs0VA3sAdkjWiqGzbvRgrLkpBG5XQ/uiC0oAknko+BCY+RHy3egQwLwlswVlfUwp6KVOCNT7ABwQmditwcCLbgBMfgkabZLltdA+8L0nxySkxoKgWDYRT8dfht+UrNJ3o3q/6Cut3A0+JrmP3dcRCbkrJCvaUtsCPOsJneRao9J6Pwcwnd7gt041r7moFCaKD/ZR58bM+29Q4EXXAGa7pipu9XRKR/hVvH+Bqu7zHNG+TP5feiiAxif47rYUrtRrCwAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2410315383REET1TGSBS">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2410321530IQ822DF33Z">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline; letter-spacing: 1px;" id="k_2410321531UANG8HH69M">API及构造参数</span></div><div id="k_2410043948XMYBRV1Z7M" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 37px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324103736XQEXEXKJOZ7S"><tbody><tr id="row_0"><td id="k_2410373617MK2KS4F9XI" tabindex="0" row="0" col="0" w="1298" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1300px; background-color: rgb(183, 214, 247); color: rgb(255, 255, 255);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(183, 214, 247); color: rgb(255, 255, 255);" id="k_2410373617QDUDBSYVBT"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(183, 214, 247);" id="k_2410373618P1IUX2J4ZF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(183, 214, 247);" id="k_2410385965HJCGV4PZIS">​var accordion = new $B.Accordion($("#accordion"),opts);&nbsp; opts参数说明</span></p></td></tr><tr id="row_1"><td id="k_2410373626WQZ8V7I9XL" tabindex="0" row="1" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px; background-color: rgb(183, 214, 247); text-align: center; font-size: 16px; color: rgb(3, 118, 240);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(183, 214, 247); color: rgb(3, 118, 240);" id="k_2410373626EDCVK99DUT"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(183, 214, 247); text-align: center;" id="k_24103736274R48EE138T">​参数名</span></p></td><td id="k_2410373629Q7Z5P5NNJJ" tabindex="0" row="1" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px; background-color: rgb(183, 214, 247); text-align: center; font-size: 16px; color: rgb(3, 118, 240);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(183, 214, 247); color: rgb(3, 118, 240);" id="k_2410373630EHHTDOWYT6"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(183, 214, 247); text-align: center;" id="k_2410373630W33LJ6BPCD">参数值​</span></p></td><td id="k_2410373632C55HPMO4IE" tabindex="0" row="1" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px; background-color: rgb(183, 214, 247); text-align: center; font-size: 16px; color: rgb(3, 118, 240);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(183, 214, 247); color: rgb(3, 118, 240);" id="k_2410373633EQCN5K726B"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(183, 214, 247); text-align: center;" id="k_2410373633RNFZ3TIPCX">说明​</span></p></td></tr><tr id="row_2"><td id="k_2410373638W7JBICGBEG" tabindex="0" row="2" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373638BOF1Q3JTMI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373639V3ZZ7LRFIP">​​width</span></p></td><td id="k_2410373641KW4QIRJH8A" tabindex="0" row="2" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241037364177HO6QF67H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373662ZYBFGPSJEP">​​​默认值：undefined&nbsp;</span></p></td><td id="k_2410373602MEQCL9FUK5" tabindex="0" row="2" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373603OI6UXG292Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373603DKX4QAZN1N">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410410892FER6J3PX6D">​undefined则取沾满整个父元素宽度</span></p></td></tr><tr id="row_3"><td id="k_24103736057C5F8H7R2L" tabindex="0" row="3" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373605HM58FTAUKB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24103736066TC8OSQOHZ">​​height</span></p></td><td id="k_24103736072ARNSHBS5H" tabindex="0" row="3" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373608IEM3UG5KHK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24103736085IRD28H1JW">​​​默认值：undefined&nbsp;</span></p></td><td id="k_2410373610E3LXMWA8TZ" tabindex="0" row="3" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373611ROHIBA2Y27"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373611VAF4RHL4Q9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410411799ZUSB8UOFIL">​undefined则取沾满整个父元素高度</span></p></td></tr><tr id="row_4"><td id="k_2410373614FMVTCCG1HF" tabindex="0" row="4" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373614HF17NXDN1S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24103736157XIQD4TC66">​​iconCls</span></p></td><td id="k_2410373616CIYWEBHQ8Z" tabindex="0" row="4" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373617YJLK4KCFAW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373617ZDGCHTSGU5">​​默认值：fa-angle-double-right</span></p></td><td id="k_2410373621WFB3GP3JGA" tabindex="0" row="4" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373621JLLVEZJOFN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373622GXU1ETJKTC">​​收起图标</span></p></td></tr><tr id="row_5"><td id="k_2410373623O4I37JJGXJ" tabindex="0" row="5" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373624TYT6S5HF8K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373625IMH1BHK6PU">​​iconClsAct</span></p></td><td id="k_24103736265BQCP9PX94" tabindex="0" row="5" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373627D7288WA8S1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373627GM9M5MVZIS">​​默认值：fa-angle-double-down</span></p></td><td id="k_24103736281GED5H8WPS" tabindex="0" row="5" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373629SEO8JKTVA1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373630CCI8CX8KBW">展开图标</span></p></td></tr><tr id="row_6"><td id="k_2410373632UVINA6SHHN" tabindex="0" row="6" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373633AOSBPSOCFX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373634SSOWF2EKBN">​​iconPositon</span></p></td><td id="k_24103736356VRT3IA24S" tabindex="0" row="6" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373636EZXFO3FHQL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373636CXBE1HZ5ME">​​默认值：right，可取值：right 、left</span></p></td><td id="k_24103736389537YPBNGQ" tabindex="0" row="6" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373639TM2RON5MCJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373639JXHIDZ6QK3">​​图标位置</span></p></td></tr><tr id="row_7"><td id="k_2410373641V3PNS9A28Y" tabindex="0" row="7" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24103736414WUW7AXUGV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373642BETK92M25C">​​iconColor</span></p></td><td id="k_24103736449A5WRFJP7B" tabindex="0" row="7" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373646B6P2WZP7Z8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24103736476DUYJONYGI">​​默认值：#666666</span></p></td><td id="k_2410373649PJFHP12DET" tabindex="0" row="7" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373649ZX7WI5SJWK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373650KEPN6O517M">​​图标颜色</span></p></td></tr><tr id="row_8"><td id="k_2410373652DKIYDBL7F5" tabindex="0" row="8" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373653NATJNORU74"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373653Q76SEY7I8V">​​fontStyle</span></p></td><td id="k_2410373655FAO7BPDA95" tabindex="0" row="8" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373655SUD38AKE5I"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24103736567BASRUV7NY">​​默认值：{"font-size":"14px","font-weight":"bold","color":"#666666"}</span></p></td><td id="k_2410373657MNL3GVMD9Q" tabindex="0" row="8" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373658LJW4BGHZL6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24103736594C5FPBZ6PX">​​标题字体颜色、大小配置</span></p></td></tr><tr id="row_9"><td id="k_2410373661EHIRDW6LW5" tabindex="0" row="9" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373662CWZBJMR3WH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373662CGH1MIHHB4">​​activedStyle</span></p></td><td id="k_24103736643NC6XZU6EY" tabindex="0" row="9" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24103736658NV3B5FJI3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373666L44INXFYAL">​​默认值：{"background":"#EAEAEA","color":"#3E76C9", "iconColor":'#3E76C9'}</span></p></td><td id="k_2410373667SVDNIM96YX" tabindex="0" row="9" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373668OAIVJSEYXE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373669XNLAHEWRFP">​​激活状态样式</span></p></td></tr><tr id="row_10"><td id="k_24104449389GG6MEDZZG" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;" w="149" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410444939W6ZIKPXHR6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410444940QORMINBO94">​​accordionStyle</span></p></td><td id="k_2410444942WKFQW6YZGQ" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;" w="554" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410444943ISK4BHVSCM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410444944XL6U7EI6JM">​​默认值：{"background":"#F6F6F6","border":"1px solid #C5C5C5"}</span></p></td><td id="k_2410444945X9SWYLIULL" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;" w="591" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410444946K7P27218XC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410444946Y925WX8AYF">​​手风琴边框、颜色定义</span></p></td></tr><tr id="row_11"><td id="k_2410445009WRQ4DKSFT7" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;" w="149" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410445010T2PF16NM9N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410445011CMCEHJ2H1W">​​onCreate</span></p></td><td id="k_2410445012N3YFAGVJQ6" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;" w="554" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410445013SOMZ6ARTKF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410445014OXZCAKNMX7">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410465213695UA6HFKX">onCreate =&nbsp;function(</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410465214YLUELRYS1A">args</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410465215EOGRL2H9AD">){}</span></p></td><td id="k_2410445015I2GBSVV8A7" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;" w="591" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410445016OZIIARS48P"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24104450167AQ99VYZEM">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410461578K4P4NZUFUL">​手风琴创建监听回调; args：当前创建的项名称</span></p></td></tr><tr id="row_12"><td id="k_24103736702IN6CSFQCT" tabindex="0" row="12" col="0" w="149" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410373671ILAGGUPHPL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373672SGKBA6T2RC">​​onOpened</span></p></td><td id="k_2410373673HTEN5C2M9R" tabindex="0" row="12" col="1" w="554" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24103736744QUX3EY2ZB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373675FLSJ57NFZC">​onOpened =&nbsp;​function(name){}</span></p></td><td id="k_24103736767O6TC4RXVL" tabindex="0" row="12" col="2" w="591" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24103736777QWD682FBH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410373677VU23ED2EQ1">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410470660EOWA78BYPG">​当前展开某一项回调 ；name：当前项目名称</span></p></td></tr><tr id="row_13"><td id="k_24104716909W9HKW6QVB" tabindex="0" row="13" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;" w="149" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410471691LNVIF5HG8N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241047169253RSUY3VST">​​onLoaded</span></p></td><td id="k_24104716943GFVF6RGP9" tabindex="0" row="13" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;" w="554" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410471696UAOMNGZMQY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24104716977MY2HI1D46">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410474267CDRX839CG7">​onLoaded =&nbsp;​function(type,data){}</span></p></td><td id="k_2410471601P5XGD6U5CM" tabindex="0" row="13" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;" w="591" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410471602HZPZ4V7LJE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410471602B1BEBJBYVN">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410480373FVSQVDSWGQ">​远程加载完成回调，onLoaded中的上下文 this为 当前展开的div</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2410480707KOOH1QW6VQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410480708MSNEPRHN73">type：html / iframe / json</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_24104811843MYDQITU2A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241048118574Y2ENCBRC">data:当type=json时候的json数据</span></p></td></tr><tr id="row_14"><td id="k_2410471555JEIGOHRUBS" tabindex="0" row="14" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 151px;" w="149" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410471555XBBIAOCG6U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24104715568Q13QMY9DL">​​items</span></p></td><td id="k_2410471558JMRLJG51L2" tabindex="0" row="14" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 556px;" w="554" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410471559VNMPBX532B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410565983O8V35LKRUX">请参考《</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_2410565984PD9O3WKEQY">items配置说明</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24105659856LDYHAB66C">》</span></p></td><td id="k_2410471561B63UZDQHRD" tabindex="0" row="14" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 593px;" w="591" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410471562P6XTI183CQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410471563ODDYOU6R6O">​手风琴项目配置</span></p></td></tr></tbody></table></div><div id="k_2410043971UN5268R3HP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410043971BAGF6YVO7X">​</span></div><div id="k_2410493408IGYNFF3DLN" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410493409WQA7R6KXSC">​</span></div><div id="k_24104934666CZGGTRC3G" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 37px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324104952FU7RKTOECLFE"><tbody><tr id="row_0"><td id="k_2410495201BG3NY6DLNG" tabindex="0" row="0" col="0" w="1298" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1300px; background-color: rgb(183, 214, 247);" colspan="4"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(183, 214, 247);" id="k_2410495202ZHXX1FLN5I"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(183, 214, 247);" id="k_2410504627FYTC1A7WSN">items配置说明</span></p></td></tr><tr id="row_1"><td id="k_2410495216J6116L25U8" tabindex="0" row="1" col="0" w="192" h="203" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 180px; width: 194px; background-color: rgba(0, 0, 0, 0);" rowspan="6"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24104952178Y4GMB39KL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410495218T5FAGAVGFV">​items:[{</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2410520057AZSWCV78AU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410542149MNPNBJGJ4R">&nbsp; &nbsp;&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410542150Z7UVB1K6EL">nam</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241054215188P9POXYTN">e: '名称',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2410523511LVK1OUXDZ8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410523512KS322883M1">​&nbsp; &nbsp;&nbsp;​icon: 'font_icon_cls',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2410530303L717BC56AK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410530304L5PZEXY2J7">​&nbsp; &nbsp;&nbsp;​actived:true,</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2410531855IM2OY6MQ6T"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410531856BXBD4N7ZO3">​&nbsp; &nbsp;&nbsp;​content: '内容',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2410533871LHY1TGQWBT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410533871L52KUF385B">​&nbsp; &nbsp;&nbsp;​url:'',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2410535114SD87Y5RZ4P"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24105351158SV1ZMSZKD">​&nbsp; &nbsp;&nbsp;​type: 'json'</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2410520451QEFLTK9JBN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410520452GZ443ZM68K">}]</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></p></td><td id="k_2410514257VOWSTSDKOH" tabindex="0" row="1" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 338px; background-color: rgba(0, 0, 0, 0);" w="336" h="32"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410514258G75POT28X5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410514258V7ZGH7JQKE">​​name</span></p></td><td id="k_2410495221KSLKIO6UIW" tabindex="0" row="1" col="2" w="422" h="32" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 424px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241049522436GRMXWIWG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410545723RQFZFRDHFI">​</span></p></td><td id="k_2410495230G2HNGFS53B" tabindex="0" row="1" col="3" w="342" h="32" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 344px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410495231JWCZEX4NA5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241049523386IFHAYC7Z">​​标题名称</span></p></td></tr><tr id="row_2"><td id="k_2410514260BGS42IUGQR" tabindex="0" row="2" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 338px;" w="336" h="32"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410514261EOS84M4J1U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24105142618P4CTRJ8EE">​​icon</span></p></td><td id="k_2410495239CXZM7QN4GE" tabindex="0" row="2" col="1" w="422" h="32" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 424px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410495240ZY43KWJ5FN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24104952413KFS8LAZL5">​​默认值：undefined</span></p></td><td id="k_24104952423EUIQ2IGJC" tabindex="0" row="2" col="2" w="342" h="32" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 344px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410495243AGUNP4EM5T"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410495245KGDNULXGL5">​​标题左侧图标样式</span></p></td></tr><tr id="row_3"><td id="k_2410514263VGGHSIRSXG" tabindex="0" row="3" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 338px;" w="336" h="32"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410514263ZVE7DB65GS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410514264B6LZTQBWS7">​​actived</span></p></td><td id="k_2410495252E9MJHCEYO4" tabindex="0" row="3" col="1" w="422" h="32" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 424px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410495253QFWF7I471O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410495254UZJLSZ7UAB">​​默认值：undefined</span></p></td><td id="k_2410495257GMN3UAVCLB" tabindex="0" row="3" col="2" w="342" h="32" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 344px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410495258XUVUQWIPXQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410495259VZ36EHZ6YQ">​​是否激活项</span></p></td></tr><tr id="row_4"><td id="k_24105142652OISC8ZDB4" tabindex="0" row="4" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 338px;" w="336" h="32"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410514266TSU6E8VDRW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410514267SRF5RQM9W1">​​content</span></p></td><td id="k_2410495266EUMB9CSHVW" tabindex="0" row="4" col="1" w="422" h="32" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 424px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410495266NNWOIPNKJB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410495267O1MDJ8RZX7">​</span></p></td><td id="k_2410495269ABKA1ZBVV5" tabindex="0" row="4" col="2" w="342" h="32" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 344px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410495270LE7415NO1L"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410495271DCH3E8U7VE">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410555333U4B64XQEL2">​项里的内容，硬编码的html静态内容</span></p></td></tr><tr id="row_5"><td id="k_2410514269QEIAXQMRAI" tabindex="0" row="5" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 338px; background-color: rgba(0, 0, 0, 0);" w="336" h="32"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410514270HRLWYHHTHM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24105142717OLGS915CQ">​​url</span></p></td><td id="k_2410512668B4YERTN451" tabindex="0" row="5" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 424px; background-color: rgba(0, 0, 0, 0);" w="422" h="32"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24105126698LR4S4SEPG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410512669NNF85YJGRC">​</span></p></td><td id="k_24105126716W2FRNF22X" tabindex="0" row="5" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 344px; background-color: rgba(0, 0, 0, 0);" w="342" h="32"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241051267244UI58VNB7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410512673R4FK836PMN">​​请求url</span></p></td></tr><tr id="row_6"><td id="k_24105142737JQ9BBN8PD" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 338px; background-color: rgba(0, 0, 0, 0);" w="336" h="33"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410514274NE3FP3O48P"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410514274I1QVNURL6U">​​type</span></p></td><td id="k_24105125432IYRXZE4QD" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 424px; background-color: rgba(0, 0, 0, 0);" w="422" h="33"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2410512544RF3XFAI58I"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410512544FKDMPMJLF6">​​默认值：html ,可取值 html / iframe / json</span></p></td><td id="k_2410512547EPW17R6K8N" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 344px; background-color: rgba(0, 0, 0, 0);" w="342" h="33"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24105125483GLOYZHOC4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410512549YWCXYYKNUM">​​请求数据类型，配合url使用</span></p></td></tr></tbody></table></div><div id="k_2410493474PGE5L7958G" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410493475FBCDVDXGHQ">​</span></div><div id="k_241049342523M7YPKL81" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410493426VZHTXNKBKN">​</span></div><div id="k_2410043968ITPVVAS3D1" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 243, 235); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(210, 145, 250); border-image: initial; margin-top: 0px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2410320583F5N8SNEFF7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2410320586ZD8LCHQEKN">​<img id="k_2410320587NDEU45U3AJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABJElEQVQoU62SsUvDQBjF37uaCiImm0jFxEnBRTdxlepYXR20dHNQEV0VpJujW5cmVHEWpIO4a/8DcTK6OZmKFtTrnZwk0Aasgt5w3/H4ft97fBwRnwq0lXWjDQBlkgNG1lq3NDL50v3QVdJnKs0VA3sAdkjWiqGzbvRgrLkpBG5XQ/uiC0oAknko+BCY+RHy3egQwLwlswVlfUwp6KVOCNT7ABwQmditwcCLbgBMfgkabZLltdA+8L0nxySkxoKgWDYRT8dfht+UrNJ3o3q/6Cut3A0+JrmP3dcRCbkrJCvaUtsCPOsJneRao9J6Pwcwnd7gt041r7moFCaKD/ZR58bM+29Q4EXXAGa7pipu9XRKR/hVvH+Bqu7zHNG+TP5feiiAxif47rYUrtRrCwAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2410320588PSSZHXLK6I">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 243, 235); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2410320584YUMTI674VR">&nbsp;Demo</span></div><div id="k_2410043103Y2ZRXT38X2" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 37px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410591748I2FOTBALGO">1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410043104K9GZYKZUXS">​定义div容器标签</span></div><div id="k_2410584961XLEOZ5UE25" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410584903YIBYH5R4DJ"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"accordion"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></div></pre></span></div><div id="k_24105849145V5PR833JP" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 37px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410590217J3JP8VRAXS">​2、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410584915AH3J6KX6U3"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410584916JMDE4P6IN3">​定义items配置</span></div><div id="k_2410590223G4H5ZM6NVY" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 62px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 14px; margin-top: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410590296THCUUYBWHY"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">               <span style="color: rgb(255, 0, 5); background: none;">var </span></span><span style="color: rgb(111, 175, 209); background: none;"><span style="color: rgb(255, 0, 5); background: none;">items</span> =</span><span style="color: rgb(167, 167, 167); background: none;"> [{</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">name:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'项目远程加载html片段'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">icon:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-menu'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">type:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'html'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"div.html"</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">actived:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">name:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'远程加载页面【firame】'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">type:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'iframe'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">icon:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-database'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"test.html"</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">name:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'远程加载json'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">type:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'json'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">icon:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-cubes'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"/bui/api/json?flag=datagridTree"</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">name:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'项目A'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">content:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'&lt;div&gt;项目静态内容&lt;/div&gt;'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">name:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'项目B'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">content:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'&lt;div&gt;项目静态内容&lt;/div&gt;'</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                ]；</span></div></div></pre></span></div><div id="k_2410590303KHVMIAZELL" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 37px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410590303TINYJRADC3">3、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2410590304TXV81W4JHP">​创建对象</span></div><div id="k_2410043169P57JHLQDXH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 65px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410043170QDJD6YT6V5"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">opts</span><span style="color: rgb(167, 167, 167); background: none;"> = {</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(61, 108, 40); background: none;">// width:400,</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(61, 108, 40); background: none;">// height:700,</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">iconCls:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-angle-double-right'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//收起图标</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">iconClsAct:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'fa-angle-double-down'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//展开图标</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">iconPositon:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'right'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//图标位置</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">iconColor:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'#666666'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//图标颜色</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(61, 108, 40); background: none;">// fontStyle:{"font-size":"14px","font-weight":"bold","color":"#666666"},//标题字体颜色、大小配置</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(61, 108, 40); background: none;">// activedStyle:{"background":"#1C2428","color":"#FFFFFF","iconColor":'#FFFFFF'},//激活状态样式            </span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(61, 108, 40); background: none;">// accordionStyle:{"background":"#263238","border":"1px solid #364248"},//手风琴边框、颜色定义</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onCreate</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">name</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">name</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">"  onCreate "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">[</span><span style="color: rgb(136, 161, 123); background: none;">0</span><span style="color: rgb(167, 167, 167); background: none;">].</span><span style="color: rgb(111, 175, 209); background: none;">outerHTML</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onOpened</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">name</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">name</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">"  onOpened "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">[</span><span style="color: rgb(136, 161, 123); background: none;">0</span><span style="color: rgb(167, 167, 167); background: none;">].</span><span style="color: rgb(111, 175, 209); background: none;">outerHTML</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onLoaded</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">) { </span><span style="color: rgb(61, 108, 40); background: none;">//加载完成</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onLoaded  "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(41, 111, 169); background: none;">this</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">attr</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"_title"</span><span style="color: rgb(167, 167, 167); background: none;">));</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(152, 89, 147); background: none;">if</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;"> === </span><span style="color: rgb(161, 100, 75); background: none;">"json"</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">));</span><span style="color: rgb(167, 167, 167);">            </span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    }</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">items:</span><span style="background: none;"><font> <span style="color: rgb(255, 0, 5);">items</span></font></span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">            };</span></div><div style="color: rgb(167, 167, 167); background: none;"><span style="color: rgb(167, 167, 167); background: none;">            var </span><span style="color: rgb(111, 175, 209); background: none;">accordion</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(41, 111, 169); background: none;">new</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(33, 156, 131); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(33, 156, 131); background: none;">Accordion</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"#accordion"</span><span style="color: rgb(167, 167, 167); background: none;">), </span><span style="color: rgb(111, 175, 209); background: none;">opts</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div></div></pre></span></div><div id="k_2410291532EIVQ7GUBR4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2410291534I4DMZ9LTG7">​</span></div>

</div>