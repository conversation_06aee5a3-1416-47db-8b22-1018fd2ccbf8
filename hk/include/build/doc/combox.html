<div style="position:relative">

    <div id="k_241623279319HS5FPDHQ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(220, 210, 250); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(0, 235, 227); border-image: initial;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-weight: bold;" id="k_2416262847PVZO668V9U">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2416262851AUSW21XJTT">​​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-weight: bold;" id="k_2416262853DSOHP3MXGX">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2416271158AAIN2YABG9">​<img id="k_2416271159MFOICAB8GP" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABLElEQVQoU41RvUoDYRCc2cMUQXMHNjY24lMoWNgERARBEBvBQgQrEXJYKFxpjBHESkWClbbaCZJHEDtJY2kl5LtATKHZlYvGhBh/tttlZndnhvhPhTbkWXyWQPknPrK01N0xwElVmfmdEFlK6u4U4CLBlWbBv/iZkIBfXBHgOgwHWvBDgEZElkYDA8gz7rxnlLC2DVgEw72qZFHMPLc0SK56DnJCBVnsBo/J0MvFSwYrAfamxjnsB+X2MkoY77Q2ARUVzMJkREyvCQwbUFLnr+GEr18EfAojuWzAA8wGSY4a7EnhTWMvU+l28kP0RjXwUrgCOJW0BiiMm1rwD3tt77i05cZEcUNg3Ax33UK/X2hPQrcgsBIpq828f9kv1J4cjAjdPBrBLY5Y60d4BxQ0cqxwfP2DAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-weight: bold;" id="k_2416271161TNIXOGNWW6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-weight: bold;" id="k_2416291143V2RUBU2W5A">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-weight: bold;" id="k_24162911438L4TQ7CVR3">介绍</span></div><div id="k_2416241781W5ED6NKR8S" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416241782F7C62HASWH">​</span></div><div id="k_2416301870CLYQM48LVZ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416322977WFC2Y8JWQ7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_24163229795WFI35HL98">​<img id="k_2416322980ICIMH7VBV9" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAARUlEQVQoU2NkIBEwkqiegXHVqlVPGBgYpLFo/MjAwBARFha2A1kOpOENAwODMBYN3////58cHh6+fDhqIC2USI4HmmsAAM9XOQ1qq9J6AAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416322981G4PJ971NX6">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416322978E8BH3668O2">combox采用tree组件构建，支持树形列表，普通列表。</span></div><div id="k_2416304581YZI1JZB16K" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416323768WFG3KN3OPV">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2416323771OMA63RS9DF">​<img id="k_2416323771KBTJ5RSBJH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAARUlEQVQoU2NkIBEwkqiegXHVqlVPGBgYpLFo/MjAwBARFha2A1kOpOENAwODMBYN3////58cHh6+fDhqIC2USI4HmmsAAM9XOQ1qq9J6AAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416323772IYWKMGXGKS">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24163237696GQ8TNO31B">支持远程Ajax模糊查询/本地模糊查询。</span></div><div id="k_2416311298ZPFV7KWIIK" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416324452BGJU3T6KXX">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2416324454P6WGSOS37W">​<img id="k_241632445541UDCKPNBP" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAARUlEQVQoU2NkIBEwkqiegXHVqlVPGBgYpLFo/MjAwBARFha2A1kOpOENAwODMBYN3////58cHh6+fDhqIC2USI4HmmsAAM9XOQ1qq9J6AAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416324456VJR9UOJ8VY">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24163244536M2DVFNRXX">根据tree组件的自定义样式方式，可以定制combox UI风格。</span></div><div id="k_2416241794UY3RLO9T7M" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416241795GKNT8CUVAS">​</span></div><div id="k_2416241733CIPTQLHGBH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(220, 210, 250); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(0, 235, 227); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-style: normal; vertical-align: baseline; font-weight: 700; text-decoration: none solid rgb(63, 0, 250);" id="k_2416300159BL8IB9TAI4">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2416300161NQJRBVSH1V">​<img id="k_241630016268ACVL8BJW" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABLElEQVQoU41RvUoDYRCc2cMUQXMHNjY24lMoWNgERARBEBvBQgQrEXJYKFxpjBHESkWClbbaCZJHEDtJY2kl5LtATKHZlYvGhBh/tttlZndnhvhPhTbkWXyWQPknPrK01N0xwElVmfmdEFlK6u4U4CLBlWbBv/iZkIBfXBHgOgwHWvBDgEZElkYDA8gz7rxnlLC2DVgEw72qZFHMPLc0SK56DnJCBVnsBo/J0MvFSwYrAfamxjnsB+X2MkoY77Q2ARUVzMJkREyvCQwbUFLnr+GEr18EfAojuWzAA8wGSY4a7EnhTWMvU+l28kP0RjXwUrgCOJW0BiiMm1rwD3tt77i05cZEcUNg3Ax33UK/X2hPQrcgsBIpq828f9kv1J4cjAjdPBrBLY5Y60d4BxQ0cqxwfP2DAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-style: normal; vertical-align: baseline; font-weight: 700; text-decoration: none solid rgb(63, 0, 250);" id="k_2416300163J95AGY2EPK">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-style: normal; vertical-align: baseline; font-weight: 700; text-decoration: none solid rgb(63, 0, 250);" id="k_2416330341368LCIXRR7">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-style: normal; vertical-align: baseline; font-weight: 700; text-decoration: none solid rgb(63, 0, 250); letter-spacing: 1px;" id="k_2416330344PQPQHJCMRS">API及构造参数</span></div><div id="k_2416241930DS7VOX94TO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24162419317P8TLZ5WPC">​</span></div><div id="k_2416335431SKEY843938" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" contenteditable="false"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324163432YJI1BS751YDT"><tbody><tr id="row_0"><td id="k_2416343144FWTLW1XT1Y" tabindex="0" row="0" col="0" w="1291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1293px; background-color: rgb(213, 241, 250);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(213, 241, 250);" id="k_2416343145YE2ROKJJWY"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(213, 241, 250);" id="k_2416343146I5EZT5S1K6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(213, 241, 250);" id="k_2416361711QS8K8WWTJL">​var combox = new $B.Combox($("#combox1"),opts);&nbsp; opts 说明</span></p></td></tr><tr id="row_1"><td id="k_2416343158Q73RJI83RD" tabindex="0" row="1" col="0" w="170" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px; background-color: rgb(213, 241, 250); text-align: center; font-size: 16px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(213, 241, 250);" id="k_2416343160ZQEPRZIK4R"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(213, 241, 250); text-align: center;" id="k_2416343160LS8XIK86UP">参数名​</span></p></td><td id="k_2416343165OZ7HXXIWK3" tabindex="0" row="1" col="1" w="454" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px; background-color: rgb(213, 241, 250); text-align: center; font-size: 16px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(213, 241, 250);" id="k_24163431669ZPTZ3J6OQ"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(213, 241, 250); text-align: center;" id="k_24163431679A43LCWABO">参数值​</span></p></td><td id="k_2416343171LRK9WZUSPF" tabindex="0" row="1" col="2" w="663" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px; background-color: rgb(213, 241, 250); text-align: center; font-size: 16px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(213, 241, 250);" id="k_2416343172UIH2HS3KCE"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(213, 241, 250); text-align: center;" id="k_2416343173CSS8HP6KHK">说明​</span></p></td></tr><tr id="row_2"><td id="k_2416343176RHF16O35CL" tabindex="0" row="2" col="0" w="170" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416343177TZKUU8O5TK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416343178DT5DOTKYU9">​​data</span></p></td><td id="k_2416343180LVLEL1PPOB" tabindex="0" row="2" col="1" w="454" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416343181Q7UPASVTVV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416343182PZUEYKXPXS">​</span></p></td><td id="k_2416343184Z5MJBT4LZL" tabindex="0" row="2" col="2" w="663" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416343185KS3L5765TY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416343186EBVYS9N2AB">​​数据，数组，可以是tree组件的树形数据，可以是[{},{}]普通列表数据</span></p></td></tr><tr id="row_3"><td id="k_2416343187KQ1RTX2IEQ" tabindex="0" row="3" col="0" w="170" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241634318824CI3RN1GP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24163431896XMENGHI47">​​default</span></p></td><td id="k_241634319163V747ODTS" tabindex="0" row="3" col="1" w="454" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416343192U8GFQG4DS4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416343193KS28VVJDTO">​​默认值：{id:"",text:"请您选择"}</span></p></td><td id="k_2416343194HIYDPFTUI9" tabindex="0" row="3" col="2" w="663" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416343196TQ8MAD9I11"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416343197RD473I6HJ1">​​默认选项</span></p></td></tr><tr id="row_4"><td id="k_24163431995UQL2QV55S" tabindex="0" row="4" col="0" w="170" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24163432006RGC452ZKZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416343201UXGNBLX343">​​mutilChecked</span></p></td><td id="k_2416343202UXYGDCOPZH" tabindex="0" row="4" col="1" w="454" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24163432038OJ8SSZDWD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416343203E23XESO1NJ">​​默认值：true</span></p></td><td id="k_2416343207P1HKEAIXUM" tabindex="0" row="4" col="2" w="663" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416343208H14W7PE7VN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24163432085XQA7UXDUX">​​是否可以多选，配合checkox使用</span></p></td></tr><tr id="row_5"><td id="k_2416392404V7CIQNUPWV" tabindex="0" row="5" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24163924059D5BLP53HW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392406UUARC7QJB2">​​initShow</span></p></td><td id="k_2416392409TMUDVUIV55" tabindex="0" row="5" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392410NB29RGA2DZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392410UDPHGC5NIJ">​​默认值：false</span></p></td><td id="k_2416392414KV5LS1QR6V" tabindex="0" row="5" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392415VZO4FGYSY4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24163924163X24M1DCKF">​​是否立即显示下拉列表</span></p></td></tr><tr id="row_6"><td id="k_2416393031ZGO9K99WXP" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24163930313ZAIVHV4RO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416393032A4E24SLW3U">​​isTreeData</span></p></td><td id="k_2416393034K63RRES74B" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241639303515SDMDJ7CW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24163930359ZMSYKZBPF">​​默认值：true</span></p></td><td id="k_24163930371CU9TW2SX4" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416393038PY8DI1MM78"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416393039IOCAKC7JIV">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416405188Q53VGKPV7J">​是否是树形数据，配合数据 data是否是树形数据</span></p></td></tr><tr id="row_7"><td id="k_2416392892HJWW5BX4K4" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392892HCY8L4P4MN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392893112M8MBMCK">​​placeholder</span></p></td><td id="k_24163928956LE79IBXDR" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24163928962RGREJN4J4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392897ZQC22UGMWN">默认值：请选择</span></p></td><td id="k_2416392800532RYXOG9P" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392802UNV3JH3EQF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392803H6J1ISWTP6">​​placeholder提示</span></p></td></tr><tr id="row_8"><td id="k_24163926623G8XTWZNF9" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392663NQJ4S73O38"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392663X2MPGKH63M">​​extParamFiled</span></p></td><td id="k_2416392665M1GRBTAZOF" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392666YUT9IBIUKS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392666UBZ25JDEZD">​​默认值：[]</span></p></td><td id="k_24163926682Y12AVMDHV" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392669ZE5WDP2MUE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164218466F4EMIGDM7">异步加载时候，可以定义</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164218477YW4D5759X">其他</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416421848RDHY8EY88M">字段作为参数，默认不设置只传pid（同tree配置相同）</span></p></td></tr><tr id="row_9"><td id="k_24163927857YX774W8LT" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392786AAIUKUX49N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392787FRUX4ITWXU">​​search</span></p></td><td id="k_24163927905SOJ2T483V" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392791FV5NWJ885C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392792NNUUCQL5X4">​​默认值：undefined</span></p></td><td id="k_24163927953AZOLY7CLB" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392797E623L3RLGM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392797FBL5NE58A7">​​开启模糊搜索,undefined(关闭搜索) / remote(远程搜索) / local(本地搜索)</span></p></td></tr><tr id="row_10"><td id="k_2416425971CHJI2WX6FN" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416425972B439PUZORC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416425973GYEPM3SAUK">​​localSearchFiled</span></p></td><td id="k_2416425974HC4PAD6VQ6" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416425975QB3INW16M6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164259761AHMQS3J7J">​​默认值：['text']</span></p></td><td id="k_2416425978NQ9S89WOGI" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24164259788LXBYV66P6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416425979DNNRM164HK">​​本地搜索字段，数组，可以定义搜索多个字段，配合search=local使用</span></p></td></tr><tr id="row_11"><td id="k_2416425805DA16OKFJG9" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416425806DX96NGTY45"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416425806YQASKWBKDU">​​url</span></p></td><td id="k_2416425808AXWFM9AL12" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416425809LK2V5J966B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416425809841P3I2AB7">​</span></p></td><td id="k_24164258119CWAJWQCQZ" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416425812M1K53AHY74"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241642581351RJ1KXU84">​​远程加载地址</span></p></td></tr><tr id="row_12"><td id="k_24164257877LB8OMWF1Z" tabindex="0" row="12" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416425788SFEWC7C157"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416425788QQ9YX87KKY">​​readonly</span></p></td><td id="k_2416425792SDZ174CXIL" tabindex="0" row="12" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416425793N5YSKZV3BW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164257946GRBO9U1YY">​​默认值：true</span></p></td><td id="k_2416425796GKPMHJRNMG" tabindex="0" row="12" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416425797I6QA1EOW4U"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164257981US698X2EK">​​是否只读</span></p></td></tr><tr id="row_13"><td id="k_24164424939MEFR6PEG3" tabindex="0" row="13" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416442494SGA8D7C3LX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416442495YTVPJ1PZEG">​​idField</span></p></td><td id="k_2416442498BFB72LZU9C" tabindex="0" row="13" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416442499HF1DDQMFWM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416442400QZYX9AIUHF">​​默认值：id</span></p></td><td id="k_2416442403HU72P7EM5L" tabindex="0" row="13" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416442403CDEVCYD1J7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164424046HT6N3IL7O">​​id的字段名称</span></p></td></tr><tr id="row_14"><td id="k_2416392561BHVHVG84PF" tabindex="0" row="14" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392562N9C3N3GB6N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392563OWPXWZHNJL">​​textField</span></p></td><td id="k_2416392566TEGCCHGRWJ" tabindex="0" row="14" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392567O2DG5849UY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392568M65233X2RS">​​默认值：text</span></p></td><td id="k_2416392572Y2YOSM3PFY" tabindex="0" row="14" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416392573QO43HKYYIY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416392573VIARJTNEIQ">​​显示的字段名称</span></p></td></tr><tr id="row_15"><td id="k_2416460665FN2ICBE9ZI" tabindex="0" row="15" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416460666NAM585TFOK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164606672SYBWP832N">​​onCheck</span></p></td><td id="k_24164606709Y957PUS9U" tabindex="0" row="15" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24164606718M68P9RBOE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416462192QYEEZ367IQ">onCheck =&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416462194ZAQ29A5DT2">function (data, params, checked){}</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></p></td><td id="k_24164606744NXAYK2YCG" tabindex="0" row="15" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416460675M9DB2F27J8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164606768SPR8AGRHG">​​checkbox复选框事件</span></p></td></tr><tr id="row_16"><td id="k_2416460559BX2JJONTNH" tabindex="0" row="16" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 172px;" w="170" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241646055992Y5L9QOEG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416465359A3NIPAYAVK">onClick</span></p></td><td id="k_2416460562AL29EOPMJT" tabindex="0" row="16" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 456px;" w="454" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24164605639DDI6GC63X"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241646056472NF7JXVHV">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416465775NTYZ7WBDQM">​onClick = function (data){}</span></p></td><td id="k_2416460565TRCXB8HW47" tabindex="0" row="16" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 665px;" w="663" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241646056612FSGP1CBQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416472445CT1NTKE7O7">点击事件</span></p></td></tr></tbody></table></div><div id="k_2416241989NGVREIQEVY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416241990V84QH8PQR9">​</span></div><div id="k_2416473963QPFK5OGPP6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416473964TW9RSI4QBC">​</span></div><div id="k_2416473994V877V8TCGK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 43px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="201903241647543Y2RNGZ2KCUV"><tbody><tr id="row_0"><td id="k_2416475410ST5BHK2R9A" tabindex="0" row="0" col="0" w="1292" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1294px; background-color: rgb(213, 241, 250);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(213, 241, 250);" id="k_2416475411UPKK7WCUN4"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(213, 241, 250);" id="k_2416502393ZRHF5WOD18">实例API</span></p></td></tr><tr id="row_1"><td id="k_241647542034APTVQ7EY" tabindex="0" row="1" col="0" w="291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475420TCQD2K47PB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164754213A6VP77RMS">​​reset()</span></p></td><td id="k_2416475423EVFGEXHTKB" tabindex="0" row="1" col="1" w="401" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 403px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475426M8DHLCOVPV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416475433GIERAIF3J9">​</span></p></td><td id="k_2416475445E3NQF75T73" tabindex="0" row="1" col="2" w="596" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 598px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475446IQN3KUQKI5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416475447YPYPK6DDBG">​​重置，取消点击项、取消复选项目</span></p></td></tr><tr id="row_2"><td id="k_2416475448MZ5VWWU7A1" tabindex="0" row="2" col="0" w="291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475449LNCKX15LO3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164754493ESQ1KCG7H">​​getCheckedData()</span></p></td><td id="k_2416475451RTANNZGO5J" tabindex="0" row="2" col="1" w="401" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 403px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475451NFIMGQZF73"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416475452ZZTW7ROX2H">​​返回值：数组， 可参考tree组件相同API说明</span></p></td><td id="k_2416475453WABHB4CQT4" tabindex="0" row="2" col="2" w="596" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 598px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475454L5UIMVTQ7W"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416475455RRAGC9ZCNV">​​获取当前复选的数据</span></p></td></tr><tr id="row_3"><td id="k_2416475457RSZU5NXEKR" tabindex="0" row="3" col="0" w="291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475459456WVDWR7V"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241647546022T3NOG9FH">​​getCheckedIds()</span></p></td><td id="k_24164754614ISA7DQJ76" tabindex="0" row="3" col="1" w="401" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 403px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475462GKKZGN3K2F"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24164754637636EUHVEM">​​返回值：数组， 可参考tree组件相同API说明</span></p></td><td id="k_24164754646FZQV696LB" tabindex="0" row="3" col="2" w="596" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 598px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475465BVKH85T6BJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416475465VPTIG8IF3B">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416494342S2TFPE4E36">​获取当前复选的数据Id</span></p></td></tr><tr id="row_4"><td id="k_2416475467XYXK8CHW62" tabindex="0" row="4" col="0" w="291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475468EL49JKL2VG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416475468E6GN3R4GDR">​​show()</span></p></td><td id="k_2416475469R6991VWXTJ" tabindex="0" row="4" col="1" w="401" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 403px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475470E7FSVDERHB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416475471AQBUQXVFFX">​</span></p></td><td id="k_2416475472TYTB7ALMDR" tabindex="0" row="4" col="2" w="596" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 598px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416475473EUWJO19MLH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416475473MIB6CW15ST">​​显示下拉列表</span></p></td></tr><tr id="row_5"><td id="k_2416495213JK3AQOWVHL" tabindex="0" row="5" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" w="291" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241649521365NOU7DM15"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416495214Y7OGBEEPTY">​​hide()</span></p></td><td id="k_241649521768PPKW6YJR" tabindex="0" row="5" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 403px;" w="401" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416495218TD7R7P4C2G"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416495219M73USZW1P4">​</span></p></td><td id="k_2416495222BW18HHB257" tabindex="0" row="5" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 598px;" w="596" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2416495223Q76S65B5XS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416495223DYC7OMJODW">​​隐藏下拉列表</span></p></td></tr></tbody></table></div><div id="k_2416473928VA3KYSDN6P" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416473928RSWXW2H19Q">​</span></div><div id="k_2416473969IVBA62ZPPL" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416473970AD9LFURLBM">​</span></div><div id="k_2416241892OZHT3FT2UC" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(220, 210, 250); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(0, 235, 227); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(63, 0, 250); vertical-align: baseline;" id="k_2416301016K4IKYIPR6T">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_241630101844S5U1XZJ2">​<img id="k_24163010192VZNJUP2B6" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABLElEQVQoU41RvUoDYRCc2cMUQXMHNjY24lMoWNgERARBEBvBQgQrEXJYKFxpjBHESkWClbbaCZJHEDtJY2kl5LtATKHZlYvGhBh/tttlZndnhvhPhTbkWXyWQPknPrK01N0xwElVmfmdEFlK6u4U4CLBlWbBv/iZkIBfXBHgOgwHWvBDgEZElkYDA8gz7rxnlLC2DVgEw72qZFHMPLc0SK56DnJCBVnsBo/J0MvFSwYrAfamxjnsB+X2MkoY77Q2ARUVzMJkREyvCQwbUFLnr+GEr18EfAojuWzAA8wGSY4a7EnhTWMvU+l28kP0RjXwUrgCOJW0BiiMm1rwD3tt77i05cZEcUNg3Ax33UK/X2hPQrcgsBIpq828f9kv1J4cjAjdPBrBLY5Y60d4BxQ0cqxwfP2DAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(63, 0, 250); vertical-align: baseline;" id="k_2416301020CYBWXZI8CE">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(63, 0, 250); background-color: rgb(220, 210, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(63, 0, 250); vertical-align: baseline;" id="k_2416301017M7KFN5VPQQ">&nbsp;Demo</span></div><div id="k_2416243087M4AP35SNI1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2416243088PMUONZ9PJ4">​</span></div><div id="k_2416243028457WZYC5TZ" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 42px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 16px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416511325IPTHUXRTI9">​1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416243029RXA5DYVZRT">​定义input标签</span></div><div id="k_2416511340YSXXORQJZF" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 42px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 16px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416511377V4Q8AUKNTZ"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="color: rgb(167, 167, 167); background: none;">         </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">input</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"combox1"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"text"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(83, 83, 83); background: none;">/&gt;</span></div></div></pre></span></div><div id="k_2416511388UW52CF5VP2" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 42px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 16px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416511915ZZQQ8XXLX3">​2、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24165113896UBR1233B4"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416511390QZQKD1W9NZ">​构造数据</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></div><div id="k_2417464496X4GGI5LAQC" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24174644976AICQ7EMTY"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">var data = ​[{
        "id": "1_0",
        "text": "节点1_0",
        "data": {
            "f1": "name10",
            "f2": "字段1_0"
        }
    },
    {
        "id": "1_1",
        "text": "节点1_1",
        "data": {
            "f1": "name11",
            "f2": "字段1_1"
        },
        "children": [
            {
                "id": "deep2_0",
                "text": "节点deep2_0",
                "checked": true,
                "data": {
                    "f1": "abc2",
                    "f2": "字段deep2_0"
                }
            },
            {
                "id": "deep2_1",
                "text": "节点deep2_1",
                "checked": true,
                "data": {
                    "f1": "abc2",
                    "f2": "字段deep2_1"
                }
            }
        ]    
}];</div></pre></span></div><div id="k_2416575904J1U778ID21" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24165759058475EA19UN">​</span><span id="k_2416574815UWGUEA44AS" style="font-family: &quot;Microsoft Yahei&quot;;">​</span><span style="background-color: rgba(0, 0, 0, 0); font-family: &quot;Microsoft Yahei&quot;;">​&nbsp;</span></div><div id="k_2416512071B3EVAII567" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 42px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 16px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416512071CHVS3SXVHN">3、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2416512072UJ48UB855J">​创建实例对象</span></div><div id="k_2416243083EQQM7LYV7C" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 42px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 16px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24162430845N6BDJPF3B"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​    <span style="background: none; color: rgb(41, 111, 169);">var</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(111, 175, 209);">args</span><span style="background: none; color: rgb(167, 167, 167);"> = {</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(61, 108, 40); background: none;">data: data   ,   //数据 参考树数据</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">default:</span><span style="color: rgb(167, 167, 167); background: none;"> {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(161, 100, 75); background: none;">"id"</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">''</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(161, 100, 75); background: none;">"text"</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"请您选择"</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">placeholder:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'请您选择/输入'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//默认选择项目</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">url:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">"/bui/api/json?flag=tree"</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">mutilchecked:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//是否多选</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">checkfather:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">// 单选的时候，是否可以选择父节点</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">readonly:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//不可以编辑                </span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">plainStyle:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">false</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//\"true\" 为简单无图标样式</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">textField:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'text'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//菜单名称字段，默认为text</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">idField:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'id'</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//菜单id字段,默认为id</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onCheck</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(111, 175, 209); background: none;">params</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(111, 175, 209); background: none;">checked</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(175, 175, 125); background: none;">alert</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"onCheck"</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                },</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onClick</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(175, 175, 125); background: none;">alert</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(33, 156, 131); background: none;">JSON</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">stringify</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(111, 175, 209); background: none;">data</span><span style="color: rgb(167, 167, 167); background: none;">));</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            };</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">initShow</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">;</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(111, 175, 209); background: none;">search</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(161, 100, 75); background: none;">'remote'</span><span style="color: rgb(167, 167, 167); background: none;">; </span><span style="color: rgb(61, 108, 40); background: none;">//远程搜索</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">  var </span><span style="color: rgb(111, 175, 209); background: none;">c1</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(41, 111, 169); background: none;">new</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(33, 156, 131); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(33, 156, 131); background: none;">Combox</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"#combox1"</span><span style="color: rgb(167, 167, 167); background: none;">), </span><span style="color: rgb(111, 175, 209); background: none;">args</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div></div></pre></span></div><div id="k_2416241833BW16CTVNHK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24162418349ND4Z7PMVI">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div>

</div>