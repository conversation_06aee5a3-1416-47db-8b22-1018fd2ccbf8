<div style="position:relative">

<div id="k_262221257841M6K1SEUI" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(129, 247, 129); border-image: initial; padding-left: 14px; margin-bottom: 16px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_2622245004XAFTTJC9AF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background: none;" id="k_26222450071FHIRO7LXY">​<img id="k_2622245008EHF1CWMPID" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABM0lEQVQoU42RPy+DURTGf+feDhpCohGhou37dtDPwGCTSEz+DBLfQGwWIUIkvoGIRcRiwWAwYfIlpH3bRlOJSUJIyHuPvFpV6t/Z7snznHt+zxH+U6VUn3Wxw0gqf+oryYR9jh+pknO42d8Ntz0d9qHrRJURDBsuk1/72RCJ7zsPVBgX5Tj0CtMITqgkExCPM5CvNNZTjCn6u8AcUHaqY/jB1RuDDbIXiuacZZJU4TJqmmJ2FccyECK64LzCzvswMYG/V5skN87qjA0lo7At0I7qeRh/mqC/+tgwUAcDRhWqQJtAt6J3TnUKPzhrTrIGXUynjcZOBYaip6IKuuW8YP5r7B8plf1hE3IoSK+ipWbQ1h/qHRNkl0BXEN10mWD9u6N+vkMUZ8lbdPZln8HriKelXgGSVXUaxKHw5QAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_2622245441VUEFO8L3G3">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_2622245005FJCREUJM7G">介绍</span></div><div id="k_2622220804UKPB8TYUN4" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 17px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2622570661SQJF3DNJ8N">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_26225706633MMCAS9LB1">​<img id="k_26225706648WH4ITC3G4" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAA3klEQVQ4T62SvWoCURCFz7m3SKEgBCQoirLXB7CRkMImFj6AnansfI+8ikmT2tLSt/DualJpLaTQnRE7s9c/lkw5Mx/nnGGInMWcHP4R3JSL2BYLiJL1NTeBovVuqmBHICO4eHoJDq36qGfIjyMgqm9w8ewcTHzXq5CHp9OhUe1C+A7qr1gM0PDzLEzjWxsS5UuWFLoUs39Fc5Wc7hBJq28EL39AgzpUh1DuCBmnLv4MFAOlpNk2Yr9AVgiM02gxOZ8x07XezZR8vgYdkfCqP7VHpLaUzXTb6p0/mPvlDtSgQQ8kGOgWAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2622570665H9AZK5JQIP">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2622570662VTDHPOM2Q2">基于约定胜于</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_26225641234ACQFUNM7Z">配置的原则，模板设计模式的思想，通常情况下我们会在服务端封装一个通用的controller基类，大大解放了controller的编码工作。</span></div><div id="k_2622284555ISAWNK28EI" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 17px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_26225714169IOPMW3JA4">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2622571418FZ3GY7QFQZ">​<img id="k_2622571419HB2OIKMN1F" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAA3klEQVQ4T62SvWoCURCFz7m3SKEgBCQoirLXB7CRkMImFj6AnansfI+8ikmT2tLSt/DualJpLaTQnRE7s9c/lkw5Mx/nnGGInMWcHP4R3JSL2BYLiJL1NTeBovVuqmBHICO4eHoJDq36qGfIjyMgqm9w8ewcTHzXq5CHp9OhUe1C+A7qr1gM0PDzLEzjWxsS5UuWFLoUs39Fc5Wc7hBJq28EL39AgzpUh1DuCBmnLv4MFAOlpNk2Yr9AVgiM02gxOZ8x07XezZR8vgYdkfCqP7VHpLaUzXTb6p0/mPvlDtSgQQ8kGOgWAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_262257142058UH48DAFN">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2622571417G3HJUEPIKA">灵感来源于服务端CURD的通用封装思想，Bui利用toolbar、datagrid、window三种组件组合应用在页面前端封装通用的CURD，将前端CURD通用编码彻底解放。</span></div><div id="k_2622320166Z2185C1S9C" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 17px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2622572940FFVBZZYBAV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2622572942Y72GLB9U7K">​<img id="k_2622572943ND7295JEAN" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAA3klEQVQ4T62SvWoCURCFz7m3SKEgBCQoirLXB7CRkMImFj6AnansfI+8ikmT2tLSt/DualJpLaTQnRE7s9c/lkw5Mx/nnGGInMWcHP4R3JSL2BYLiJL1NTeBovVuqmBHICO4eHoJDq36qGfIjyMgqm9w8ewcTHzXq5CHp9OhUe1C+A7qr1gM0PDzLEzjWxsS5UuWFLoUs39Fc5Wc7hBJq28EL39AgzpUh1DuCBmnLv4MFAOlpNk2Yr9AVgiM02gxOZ8x07XezZR8vgYdkfCqP7VHpLaUzXTb6p0/mPvlDtSgQQ8kGOgWAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2622572944XXN6DVTI6U">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_26225729414CLIXUBQKB">前后端约定：新增接口</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_26225641278TQZRMLW5K">为 add、删除接口为 delete、更新接口为 update、多条件分页可排序接口为list、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2622343586C1A9AUU4JT">普通条件可排序</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_26223704135J8PM72OGD">查询接口为query、id查询接口为get、页面请求接口</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2622370414UX5DKRFNFN">为</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2622370415Q4PSMAXYZP">page。</span></div><div id="k_26223459496X2L685GDV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2622345950RQC2EPAELV">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_26222229125LF43SN7GK" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(129, 247, 129); border-image: initial; margin-top: 0px; margin-bottom: 16px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26222545046STS36TLD4">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2622254507S2WPEB4415">​<img id="k_2622254507OH5N8YEIKB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABM0lEQVQoU42RPy+DURTGf+feDhpCohGhou37dtDPwGCTSEz+DBLfQGwWIUIkvoGIRcRiwWAwYfIlpH3bRlOJSUJIyHuPvFpV6t/Z7snznHt+zxH+U6VUn3Wxw0gqf+oryYR9jh+pknO42d8Ntz0d9qHrRJURDBsuk1/72RCJ7zsPVBgX5Tj0CtMITqgkExCPM5CvNNZTjCn6u8AcUHaqY/jB1RuDDbIXiuacZZJU4TJqmmJ2FccyECK64LzCzvswMYG/V5skN87qjA0lo7At0I7qeRh/mqC/+tgwUAcDRhWqQJtAt6J3TnUKPzhrTrIGXUynjcZOBYaip6IKuuW8YP5r7B8plf1hE3IoSK+ipWbQ1h/qHRNkl0BXEN10mWD9u6N+vkMUZ8lbdPZln8HriKelXgGSVXUaxKHw5QAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2622254508WUOXIRT3BA">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2622254505AKZFXOGRYV">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2622260780E25WRC6NJY">$B.CURD( </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(93, 167, 245); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2622260781VM2GYXFAM1">tableJq, gridOpts</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2622260782YD87NM18VF"> )</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2622223783RTTC3AE3CV">构造器</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2622223785PAEKUD84BN">参数说明</span></div><div id="k_2622224901A5H2IMN7IU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 17px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190326225807R5RB6PTITYQU"><tbody><tr id="row_0"><td id="k_2622580796R7SYNSAY4Y" tabindex="0" row="0" col="0" w="1335" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1337px; background-color: rgb(250, 251, 255);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 251, 255);" id="k_2622580797L1UAGGBIRN"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255);" id="k_2622582969RKPJY6K2AT">​var&nbsp; curdIns =&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255);" id="k_2622580797Z5PVRFZART">new $B.CURD(tableJq, gridOpts);构造器配置参数</span></p></td></tr><tr id="row_1"><td id="k_2622580705WKUV43TPIV" tabindex="0" row="1" col="0" w="263" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 265px; background-color: rgb(250, 251, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(250, 251, 255);" id="k_2622580706JIOH24QSRC"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255); text-align: center;" id="k_2622580707MNP37OGI6G">​参数名</span></p></td><td id="k_26225807096FK9ZNNCPY" tabindex="0" row="1" col="1" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px; background-color: rgb(250, 251, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(250, 251, 255);" id="k_2622580710JIK686MWQ9"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255); text-align: center;" id="k_2622580711SEXFYCQCZW">参数值​</span></p></td><td id="k_2622580713J1TYG4C5YQ" tabindex="0" row="1" col="2" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px; background-color: rgb(250, 251, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(250, 251, 255);" id="k_26225807143ZLYTFXKFG"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255); text-align: center;" id="k_2622580715IZ57VWS5LW">说明​</span></p></td></tr><tr id="row_2"><td id="k_2622580717MGAB2BGTNH" tabindex="0" row="2" col="0" w="263" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 265px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2622580718EWPRNWMCWI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262258071913KTSO29I3">​tableJq</span></p></td><td id="k_26225807228LIW3DY9RZ" tabindex="0" row="2" col="1" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2622580723TXRT5H4FZF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2622580724595LL5BSZ8">​对应的table标签对象</span></p></td><td id="k_26225807265RACP9LHOW" tabindex="0" row="2" col="2" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2622580729WLU3YOILOW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26225807304UYMR5VCF8">​</span></p></td></tr><tr id="row_3"><td id="k_2622580733Y8789XKY5P" tabindex="0" row="3" col="0" w="263" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 265px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26225807352L13JTA772"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262258073697WZCLFG8H">​gridOpts</span></p></td><td id="k_2622580741IVW1PVDHTV" tabindex="0" row="3" col="1" w="624" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 626px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2622580743P328L25SNL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262258074544A7AHA6FJ">​对应datagrid组件的配置，请参考datagrid组件</span></p></td><td id="k_262258074776VXUB82OZ" tabindex="0" row="3" col="2" w="444" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 446px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2622580748JEN28TOLL3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2622580749SETRLRET4I">​</span></p></td></tr></tbody></table></div><div id="k_26222249755ANMRPB7H5" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26222249769ZJQ5RWQIL">​</span></div><div id="k_2623012417ZV1GO34JLT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623012418DMO54ZZHUI">​</span></div><div id="k_26222249003A3NTBWY65" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(129, 247, 129); border-image: initial; margin-top: 0px; margin-bottom: 16px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2622255521T468R34M71">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2622255524XM8622JCBX">​<img id="k_2622255524U2SLKZY1KP" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABM0lEQVQoU42RPy+DURTGf+feDhpCohGhou37dtDPwGCTSEz+DBLfQGwWIUIkvoGIRcRiwWAwYfIlpH3bRlOJSUJIyHuPvFpV6t/Z7snznHt+zxH+U6VUn3Wxw0gqf+oryYR9jh+pknO42d8Ntz0d9qHrRJURDBsuk1/72RCJ7zsPVBgX5Tj0CtMITqgkExCPM5CvNNZTjCn6u8AcUHaqY/jB1RuDDbIXiuacZZJU4TJqmmJ2FccyECK64LzCzvswMYG/V5skN87qjA0lo7At0I7qeRh/mqC/+tgwUAcDRhWqQJtAt6J3TnUKPzhrTrIGXUynjcZOBYaip6IKuuW8YP5r7B8plf1hE3IoSK+ipWbQ1h/qHRNkl0BXEN10mWD9u6N+vkMUZ8lbdPZln8HriKelXgGSVXUaxKHw5QAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2622255525ZOA42NEFEQ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2622255522R96K2VA1A7">&nbsp;CURD实例API</span></div><div id="k_2622225778VYLZXTNM83" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 15px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190326230214ESGJW1S65E3Q"><tbody><tr id="row_0"><td id="k_2623021436UV1CUV8SY2" tabindex="0" row="0" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px; background-color: rgb(250, 251, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(250, 251, 255);" id="k_262302143774V5GS25IW"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255); text-align: center;" id="k_26230214378ES8RNACY9">参数名​</span></p></td><td id="k_26230214407WEY2KJSUJ" tabindex="0" row="0" col="1" w="412" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 414px; background-color: rgb(250, 251, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(250, 251, 255);" id="k_2623021441ZF5263L4VI"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255); text-align: center;" id="k_2623021442G93M88NQWC">参数值​</span></p></td><td id="k_2623021446S43Y8YW4N5" tabindex="0" row="0" col="2" w="634" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 636px; background-color: rgb(250, 251, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(250, 251, 255);" id="k_26230214471BREMSWJJG"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255); text-align: center;" id="k_2623021448PQ1NXEWYI8">说明​</span></p></td></tr><tr id="row_1"><td id="k_2623021451VL4RFRJX6Z" tabindex="0" row="1" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021452VPLKZMDCUV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021453TFMIJIWQND">​​getDataGrid()</span></p></td><td id="k_26230214568VPR97OQGJ" tabindex="0" row="1" col="1" w="412" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021457F1953CHNXE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021459KJBXAYXA29">​</span></p></td><td id="k_2623021460ICJU7EV8O8" tabindex="0" row="1" col="2" w="634" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021462YLGOCCCEL6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021464OHKYD3IPGY">​​获取datagrid对象</span></p></td></tr><tr id="row_2"><td id="k_2623021466EDAU36JP45" tabindex="0" row="2" col="0" w="270" h="153" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 155px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26230214678T9ZKP6JGX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26230214687DLQX2Z323">​​openInner(args)</span></p></td><td id="k_2623021470485L5GFT14" tabindex="0" row="2" col="1" w="412" h="153" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 155px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021470QY9SE4LEMR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021471ROUG16A32Q">​​args= {</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623035921OJVSYGO6DT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623035923TKKWALXW4F">&nbsp; &nbsp; &nbsp; target:'行jQuery对象',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_26230348833N8WM8M1N7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623034884MGDT5JQP1A">&nbsp; &nbsp; &nbsp; content:'内容或者url',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623035172Q9ZHMLHMUB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623035173HH29JIWTUJ">&nbsp; &nbsp; &nbsp; type:'如果是url请求的时候，type=html/iframe',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623035496RZEGP46OER"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623035497ZPXCYP3B64">&nbsp; &nbsp; &nbsp;onLoaded:function() {如果是url加载，会触发加载完成事件}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623040274KNHUOEMGTT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623040275JP63RA59GB">}</span></p></td><td id="k_2623021472DG3EGA2F7O" tabindex="0" row="2" col="2" w="634" h="153" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 155px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021473Y567ECUPZL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26230214743VRAIFK53S">​​打开行内嵌页面</span></p></td></tr><tr id="row_3"><td id="k_2623021475488365VDQJ" tabindex="0" row="3" col="0" w="270" h="78" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021476HULIT6DW16"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021477NWB7YY4IYL">​​window(args, isUpdated)</span></p></td><td id="k_2623021479COHX42E6LO" tabindex="0" row="3" col="1" w="412" h="78" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021479CVXY1WEN86"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021480Y42X1HPB4F">​​args：window窗口参数，请参考window说明</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623050190NUVCQIPLLE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26230501917DFK45EGWW">isUpdated:是否是打开更新窗口</span></p></td><td id="k_262302148239VPQ5JYNB" tabindex="0" row="3" col="2" w="634" h="78" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021482TMBFYFAMJ5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623110153WTLF3YOM52">window内部会根据</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26231101552M7ULEP8MB">$B.config.curdOpts配置发起请求</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623110155DCQ39VRISZ">：</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623061677MMV2PFUWI6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623074030PTCXH5MT3C">http://xxx/</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623074032K9DEJ1EDAZ">xx</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623074033UTMGMBEHGV">/page/</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold;" id="k_262306389082PECN1HE9">form,约定新增/修改为form.html</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623080456GSB7XOGA2P"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold;" id="k_2623084967VD2LDSRCAH">如果要不是约定的form.html。可以设置args.pageName = </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold;" id="k_2623084969TVADKQ6EZ4">pageName进行调整</span></p></td></tr><tr id="row_4"><td id="k_2623021484653M6UBSZP" tabindex="0" row="4" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021485AE225WMJ9K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262302148675WKAAZV7C">​​close()</span></p></td><td id="k_26230214873RZZ7BISRP" tabindex="0" row="4" col="1" w="412" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26230214882D6R1FX9IW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26230214899IR492DJM5">​</span></p></td><td id="k_2623021490LW5QJ3X8UK" tabindex="0" row="4" col="2" w="634" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26230214915XPAP2TCYO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021491BJFMT4C3T5">​关闭当前窗口</span></p></td></tr><tr id="row_5"><td id="k_26230214934NVGL8291C" tabindex="0" row="5" col="0" w="270" h="178" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 180px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_262302149351IPS8SWII"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021494YV5T6OLSE9">​​add (args)</span></p></td><td id="k_2623021495VI8E841HNC" tabindex="0" row="5" col="1" w="412" h="178" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 180px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26230214966VWK4JGVCZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021497LG6WZJYW2I">​​args = {&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623100127UI2V87ZCRE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623100128VTWU9WLL2W">&nbsp; &nbsp; data:{}, //提交的参数，通常是实体bean参数</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623094587FEMCCQ34FG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623094588BR3J7TRS73">&nbsp; &nbsp; ok:function(data,message){}//成功处理后的回调,</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623095041JAGIKJ9COU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623095042XKQGTR5CTW">&nbsp; &nbsp; final:function(res){} //无论成功，失败都回调的事件</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623100376HGQW1N448X"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623100377ZWFDI325NP">}​</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623095420LPHCKHPL9F"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262309542056IF4XOSCL">或者</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623095671HB9B2HVWZO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623095672LAEH9RBFXH">&nbsp;args = {p1:1,p2:2}</span></p></td><td id="k_26230214996NFMOFNAMI" tabindex="0" row="5" col="2" w="634" h="178" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 180px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26230214994BI5XH336W"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021400TIMVFANVTL">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623105252KEGVF2P7GC">​新增一条数据，</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623105312CDKSXA4GKJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26231132928M7GUASQ39">内部会通过$B.config.curdOpts配置发起请求：</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​http://xxx/xx/add</span></p></td></tr><tr id="row_6"><td id="k_2623021402H2KH6B668W" tabindex="0" row="6" col="0" w="270" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021402GD2OECPTRU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021403MSQPL8VD5Z">​​update (args)</span></p></td><td id="k_2623021404QYUSYBCGHP" tabindex="0" row="6" col="1" w="412" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021405JAU8IZIJNQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021406Q9XQV4G4X8">​同上</span></p></td><td id="k_26230214079EV2YPHG3W" tabindex="0" row="6" col="2" w="634" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021408C35BIUGBC4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021408D7QQ9PSBL3">​修改数据</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623113008EZWTMGPC61"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623133008PE9K84U4CA">内部会通过</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623133009G46ZIEVXKU">$B.config.curdOpts配置发起请求：http://xxx/xx/</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623113936TR3XC99JT2">update</span></p></td></tr><tr id="row_7"><td id="k_2623021410JFKAN5QV3B" tabindex="0" row="7" col="0" w="270" h="178" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 180px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021411VRZ16MK5Z7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_262302141238HL3N868S">​​delChecked (args)</span></p></td><td id="k_2623021413TBXRP59ZAG" tabindex="0" row="7" col="1" w="412" h="178" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 180px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021414MTP76X95CQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021415FKQRA3BB3W">​​args={</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623124692D64SXTIKXN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26231246939IR55A12L2">&nbsp; &nbsp; message:'提示信息',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623124904FTRV9IG4LC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26231249057KK7CK62CE">&nbsp; &nbsp; params:{},</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623125153EMUVKI6Y9X"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623125154WRG75WUREF">&nbsp; &nbsp; ok:function(data,message){},</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623125427T1EEPTVDBF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623125428V1HJBFINNT">&nbsp; &nbsp; final:function(res){},</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_26231257267OP2ZX45VW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26231257278LFQXD3NZN">&nbsp; &nbsp; fail:function(){}</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2623125989C96SVNM64C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623125989TL7BQK3L65">}</span></p></td><td id="k_262302141639UL42MDRZ" tabindex="0" row="7" col="2" w="634" h="178" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 180px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26230214178M8SMIOCA1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021418AFIZCB8YEN">​删除勾选的数据，args可以不传，</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_26231326158GFSY466JW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623133475X2N16MLY6B">内部会$B.config.curdOpts配置发起请求：http://xxx/xx/</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623133477DAIVESUFSD">delete</span></p></td></tr><tr id="row_8"><td id="k_2623152062ZNPVXICM1X" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;" w="270" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623152063ODB1K81SHE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26231520648UE81AOC9E">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623152361YGOTO3EITD">deleteData(idArray)</span></p></td><td id="k_262315206673X83M6NFQ" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 414px;" w="412" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26231520669ZFHJY27JI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623152067IL55EJWCDM">​idArray : id数组</span></p></td><td id="k_2623152069KRWG2GEQBJ" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 636px;" w="634" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623152070QE5OIT3P2N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26231520708XVXKLE43S">​根据id删除数据，url请求同上</span></p></td></tr><tr id="row_9"><td id="k_2623021419DBF8GVM587" tabindex="0" row="9" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021420Z6B67CRC8N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021421Z6HGPMT5AH">​​getCheckedData ()</span></p></td><td id="k_26230214228A7LFX3FU5" tabindex="0" row="9" col="1" w="412" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26230214233CL1TZZX2B"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26230214234A8J6JZWKG">​</span></p></td><td id="k_26230214251G1IIW5RFT" tabindex="0" row="9" col="2" w="634" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021425ZJPMI5A7PL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021426PCOPMFI8X9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623135813YA96BP1LN7">​获取当前选择的数据，返回数组</span></p></td></tr><tr id="row_10"><td id="k_26230214293GA8ZDVGZQ" tabindex="0" row="10" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021430J153TT981N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021430D4GK6F5ATH">​​getCheckedId ()</span></p></td><td id="k_2623021432M5EKUNGSVX" tabindex="0" row="10" col="1" w="412" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26230214324RKA9PLBBJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021433VWBL468O75">​</span></p></td><td id="k_26230214349UT85FY9YG" tabindex="0" row="10" col="2" w="634" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021435JH5YZ16ZVT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021435IRJDXK4MLC">​获取当前选择数据的id集合，返回数组</span></p></td></tr><tr id="row_11"><td id="k_26230214379DCXS4F293" tabindex="0" row="11" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021437SD1W2121SC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021438EDA6V7K8IJ">​​getData ()</span></p></td><td id="k_262302143948IIRQGS7V" tabindex="0" row="11" col="1" w="412" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021440G38WWHCIHR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021441MSS5FOFL6R">​</span></p></td><td id="k_26230214426H7OR7RWIQ" tabindex="0" row="11" col="2" w="634" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021443I1XNKK5CVB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021443Z12WFA1KBV">​获取datagrid的数据列表</span></p></td></tr><tr id="row_12"><td id="k_2623021445S7H5K5WHFF" tabindex="0" row="12" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26230214459KHR3MIGP9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021446FJYB8K6KRN">​​reload(args)</span></p></td><td id="k_2623021447JCBLFD66UF" tabindex="0" row="12" col="1" w="412" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021448MMS5ZUF9Q4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021449WJEGGI8V7P">​args:查询参数</span></p></td><td id="k_2623021450LQ2C8KXHOT" tabindex="0" row="12" col="2" w="634" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021451PCT387AUN1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021451BXAHDC5OXM">​重新加载，会回到第一页</span></p></td></tr><tr id="row_13"><td id="k_2623021453G7VACAWAEN" tabindex="0" row="13" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021453J11VBQSLIX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021454YPQHEZH124">​​refresh()</span></p></td><td id="k_2623021455SPMV6HK1XQ" tabindex="0" row="13" col="1" w="412" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 414px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021456SDQXT3ZLKC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021456XDHGKJ1S6V">​</span></p></td><td id="k_26230214585YB7HL7Y5J" tabindex="0" row="13" col="2" w="634" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 636px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2623021458JOJ5Y53SWL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2623021459SSJ1X3ER1J">​刷新当前页，保持当前页码</span></p></td></tr></tbody></table></div><div id="k_2622225793FGCP3UQ24K" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2622225794QKQUIHJGLX">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_2622220334JSF7HM7XXA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2622220335LT93TS8VXM">​</span></div>
</div>