<div style="position: relative">
   <div id="k_21084433664OD26CAN7X" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(152, 147, 250); border-top: 1px solid rgb(88, 184, 14); border-right: none; border-bottom: none; border-left: 1px solid rgb(88, 184, 14); border-image: initial; padding-left: 12px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250);" id="k_2108465156EWDKCNQSB5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background: none rgb(152, 147, 250);" id="k_2108465158ZH4L1HLRIF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250);" id="k_2208152446PVBEG4Z2L3">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2208152448C6YGDZHYE9">​<img id="k_2208152449ZHKYFNVE4T" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAABCUlEQVQ4T62TMUpDQRCGvx9tLGzsPYHExsbGC9hKQE06YzyCVhYp1CqFlYqCpUUukBukslAIAUHwAHaCWMgvGybwfJj3Qnzb7c7Oxzczu6KCpQoYVAuxfQwcAavAIvAB9IFTScMi47GJ7XugEcn5+0/AXhFItuvAFbAS2V/AN7AE43INPEjan2aTIMtAF2gCL0Bb0sB2D9iJxKGktamQScD2FvCetG1vAjdALeKPkjZKIRnYJdCKctLxJ3AhqTMTxPZJmkYAUi9egXNJd6XTyVjcAgexfwN2U3/KHuSvxxaT2gYWgJGkszJAiuchWZNnSev/hRSONQvPm1wDh2E4n8ks6n/dqeQX/wAenlESqqOsqQAAAABJRU5ErkJggg==" style="width:17px;height:17px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250);" id="k_2208152450VC78EJXPQX">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: bold;" id="k_21084832692QWV4PI5NW">疑</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: bold;" id="k_2109442958G5FWCK92ME">问 ：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: bold; letter-spacing: 1px;" id="k_21094429581Y9JOKK7TR">现代的UI框架</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: bold; letter-spacing: 1px;" id="k_2108462708O6I69EXQQC">很多</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: bold;" id="k_21085750765RS9KHGENY">：ext</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: bold;" id="k_2108575076QNTW1YQKZY">、easyui、elementui.....</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: bold;" id="k_2109443776L14VIXD11G">.... </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: bold; letter-spacing: 1px;" id="k_21094437763IIUAUZ9N7">为何还要造个轮子</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background: none rgb(152, 147, 250);" id="k_2108471974W7HOQJ1TRE">​<img id="k_21084719745JYJQ2W8PN" src="data:image/gif;base64,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" style="width:19px;height:19px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250);" id="k_2108471977QPG1P9QLUT">​</span></div><div id="k_2108462374CMHLI8DQJV" class="_section_div_" style="line-height: 36.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 16px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109394912CYHRJ41TCQ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109394913CPG6CEC5UT">​<img id="k_2109394913H1581KGI9E" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABQUlEQVQoU52SrU/DUBTFz2kYZV0hAbH/hBAkjhEkWUKQcwgMI7TF1NBWLCGZQCFwEJghATwGAQKDmEYgMCQkLese6yVvH6QDxZ567973uznn5BITHE7AoA85jsyTyYaI1Yoivuua78tMtxtXRQoPYWi288NZr0u5UEiuSSyKyJ1Spapt4yNNkyaJmgheyalKEJjPI5CumyyR2S3AhWHxHpAXgFUAhgiUCLeiyLr8gfTF8+IKIOcA5/IyNEByLwiKTYAyBumH68ZrAC5IlIbNLxHuh2HxKA/oXj8IbTpN4xOSm1rSaKIIHpWy1hsNvo0F8RsYSIKWMq0/iuBJKWs1D9LzPldEelckZwF0sgy7gNEme2ckywAyQGpBYJ+OeXKceNsw5DDLeBBFpeOBx2QZkBaAG9O0dnyfnT9B/GczJlqjb7QChA5z09+EAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109394914PM41296EPO">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_211121109824DP6OFOG7">ui框架很多，UI风格都大</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111211099Q1NGZHRV7Y">体相似，但这</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111211000CPO2YEU3CO">只是表面的，要以</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21094206066ZZ2UAKKJA">一</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21094206063OFZIOLO4O">个从事前后端开发的[全栈]工程师的工作效率来评价ui框架的开发效率。</span></div><div id="k_21085024837HYJ7WWFX9" class="_section_div_" style="line-height: 36.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109395850KO3C5J7X31">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109395851V3X5R9599A">​<img id="k_2109395851XIXHCVHUIT" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABQUlEQVQoU52SrU/DUBTFz2kYZV0hAbH/hBAkjhEkWUKQcwgMI7TF1NBWLCGZQCFwEJghATwGAQKDmEYgMCQkLese6yVvH6QDxZ567973uznn5BITHE7AoA85jsyTyYaI1Yoivuua78tMtxtXRQoPYWi288NZr0u5UEiuSSyKyJ1Spapt4yNNkyaJmgheyalKEJjPI5CumyyR2S3AhWHxHpAXgFUAhgiUCLeiyLr8gfTF8+IKIOcA5/IyNEByLwiKTYAyBumH68ZrAC5IlIbNLxHuh2HxKA/oXj8IbTpN4xOSm1rSaKIIHpWy1hsNvo0F8RsYSIKWMq0/iuBJKWs1D9LzPldEelckZwF0sgy7gNEme2ckywAyQGpBYJ+OeXKceNsw5DDLeBBFpeOBx2QZkBaAG9O0dnyfnT9B/GczJlqjb7QChA5z09+EAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109395852N3I1L4VOY5">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21093958518X1GCE9X8Y">细节决定效率，量身定制决定适配度，一套面向PC端的高效的简洁</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109380466RSVSJC14DO">的UI框架，是我这个后端码农一直追求的理想，尝试过ext、easyui、elementui，最终发现它们都不是我想要的。</span></div><div id="k_2111155345HMUJZJ21L5" class="_section_div_" style="line-height: 36.4px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 20px; margin-top: 0px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111225259O33G6NI3VZ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2111225261ZUQKTMUMTE">​<img id="k_2111225262RHV3CLSTCR" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABY0lEQVQ4T7WSv0scURSFv/NmhiDujBbxN0j8R4KFJGkFC7tYWIhlCAgxRRpBSRMQY2FpKZYBAxap0lsICZLSVRc1uroRs2/elQnZJsxGgvEVt3j3fue8d+8Vdziqx9VNsH7zyVQXvXstrXq0P43jpXDLaXNgpcyjgF8hFjB7m/rBOSG74KAnJLYlY9j5aKxC304pfMXBo5+JbQOJmvYkY+hrPaqOIzbM9D7L+18I+VK4uKzH+3NIixjzqefdRcw68PhvrgWnIvzgeMgn1x+ADgU3HxTWBGutb7TrqQxLLjnsJmbWZG8war+LJ8x37mZk34s+lD77Mq4+DWKrjfoX14xGK/QdlcJGrdIgjIBciPPXiAmhWTXd54C7Snn4TSi/D2d70KDWXSiHJF8Cniu4SeX6FMjzlIFTodB2VK3EeVRdlWPGGc8qfvDjbZv7a1T/BTZOsga+o5PeM6Hrf3K+rfjP/A2auowQtdRF6AAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111225263K2CXTCMZU8">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111295222LQSHPVPW3W">效率第一，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21112956264R7EMY6I4B">效益是</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21112956283XTMHF7BOB">目标，我们的</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111211454WNWC6QA4IU">团队不像BATJ那样土豪，养一群专业的人</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111203793SZJVH6X1RC">折腾前端，我们需要每一个人前端后都会，但希望前端</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111203795SZHXOPE7ZZ">开</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111203796QGKET2UUNU">发的知识成本要低</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111204636F7U6TE7L65">，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111251544JZDDABJQSD">同时UI、交互</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111251545LHVE5Q2OPQ">不落后于主流</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2111204639BWCJBLYZZM">。</span></div><div id="k_2108564725WPVGXS8AKY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(152, 147, 250); padding-left: 12px; border-top: 1px solid rgb(88, 184, 14); border-right: none; border-bottom: none; border-left: 1px solid rgb(88, 184, 14); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_210858209328X74FQB19">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2108582094HD6IVW6KAE">​​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2208154269E9ZYIFULH5">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2208154271SBDDKTOD3E">​​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2208154273VEKYC2LWE5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2208155919Q4OJ4MJDQJ">​<img id="k_2208155920ZFI8K37SGE" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAABMklEQVQ4T63TvUodURAA4G/QIo3kAcRSAr6AfYJgYSFikWijaArByjplLO0ECxXTxFgEEbEQRHtfQBIs1d4bO0k4YWSvrNd7xb/tlpnz7cyc2fAKTzSNUsoE3uMtzrAWESe1+ABm0YcGjiJiM+NRSsngOgbzvVZYJi5HxJdSylfMVx+4dXGMmUR2MdICNBP/4hT96G7TecFeIufofcFoLhL5g54XIFeJXLb0+lSvkcgvvHvqyVr+70QWsYA3z4CusZRIzmMbHzrcUCc7b+YQYzd7UUE/MfRIKIEDjEfEVX1js6IfGEbXA639wz4+JXCzsfXkqqINjHaAEtjBdBO4h9RaW8HHFiiBLczVgbZI7Yf7hslq3XP9v0fEVLs277TTmlBKWa0q2oqIz53m9CDy2L35D3kDZgdufHpmAAAAAElFTkSuQmCC" style="width:17px;height:17px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_22081559223Y486GBWUF">​&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline; letter-spacing: 1px;" id="k_2108582704Y2SWIPVN6G">我理想中的ui框架是这样的</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2108582706OREO3TGLQE">​<img id="k_2108582706OIYC9G35SR" src="data:image/gif;base64,R0lGODlhGAAYAPZQAEDAHRZFDhlJEB1OFeokJPaJijWAJCtfIuskJBpKEpURETeFJjuiH4sDA/dbUMbfwKshITydJB5PFupkZL8mJt5QSDOBIrgmJjN+I6QcHIQZGfBLR9Vtbe5IQithIuElJe5CQMglJT+cJzt8LTV4J+kkJP6TitslJc8zM58gII8CAkiLOitdIjeVHzukH+9JR/6SiephYTN2JVOJRz61HyJUGnm6ae1APmOUWoK5dlWyPkGaK3fLYOxJSu5KRoIMDIwCAq4mJjqdIcntv4YKCh1OFD2rIa8uLe9KSokPD2S8TTmcIUqpMdlzcxlJEf1lWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAUAAFAALAAAAAAYABgAAAeYgFCCg4SFhoeIiYpQPYJJgwSLhUhPDj8NGheRkoMwT08VREAIMZyDJp9PRyolE6aCPqlPCh8or1AbshknIbcFL58dEBQct1AFIDcpQU3GUC4WHiwHLcZCSlA4CUM8O85QOQPfgw8S4wA0GOLOAEYGIjWEEZxMCwY6UEUjSwAMK84CbMggMeObgHGDnCAUFGAhlIYLITpUFAgAOw==" style="width:19px;height:19px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2108582706OAQEYQOB51">​</span></div><div id="k_2108462427HHBRB8VMPY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109435125C487H9M5RI">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21094351264A3OIA9MQT">​<img id="k_2109435126U11D9FXGLI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22080039611LB5YL5Z6Q">​标准的HTML标签及属性，无额外自定义的标签属性</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109435125K1QIHLPEKF">。</span></div><div id="k_2109004860PL4ZW4WXAT" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109440403MNIROQS1JQ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21094404036LVGF2X3BF">​<img id="k_2109440404CG6O3O4UDV" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_220801214683OQT32BB4">​不需要什么</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109440403J82T1DUGMN">编译、打包命令的环境，明明白白看得见、摸得着的html、css、JavaScript，普通文本编辑器也可以开发组件。</span></div><div id="k_2109051624Y98E3RI71Q" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109474957FXIU4WLYPW">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_210947495874A1595ZJM">​<img id="k_2109474958S2L5B8HI4S" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208022448C5SGLW54BX">​符合</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208022293Q45QFC267G">【表现(css) + 结构(html) + 行为(javascript)】分离的设计原则，不能用乱如意大利面的jsx。</span></div><div id="k_2109024663ON4A83D1EZ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109475792KEAZ3SD15K">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109475793KBN9VO45QJ">​<img id="k_21094757935BUAKWGS9D" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208032252UA8BK1DY3C">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109475792XFK5IRXN7E">便于在运行环境（测试、生产）定位问题，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109195033EW31Y8N6TE">可以</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22080331623VNXXFAEXM">马上应对小的调整修改。不能</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208033167M5GF7X36OJ">：加个链</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109173332BU1Q9M1A3Z">接、改个颜色，要重新编译、打包、部署。</span></div><div id="k_2109152191JVE23B27ZQ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109480911NHF28NEN5E">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109480912NA9F8QF93S">​<img id="k_2109480912YQ7BNZFO6O" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208034508IJW3RLVODA">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2109480911BSI18X3W6L">使用方式要是经典的面向对象风格的</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(61, 101, 245); background-color: rgb(255, 255, 255);" id="k_2109173170D5E7D98B8C">（new一个实例）</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21091731713LZ5USTXE6">。不喜欢jquery组件套路风格，也不需要elementUi那种style。</span></div><div id="k_2109204595EK19O9817T" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109482649NMK5MIJDWN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109482651Y9L3LWXOL5">​<img id="k_2109482651UUPGF5YF6H" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208041778KL4P47R31Z">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_220804269727GTZ8JP8O">设计上要有对接后端接口的考虑，要有统一处理的设计，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22080432775HX8BIT6EI">统一的请求出入口，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208043278NULSILTZ7P">统一的无权限，异常处理提示</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22080432793W1KIVYDNA">。</span></div><div id="k_2109245301C2ME5EVI79" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109484319FYMUD4OCG5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109484320FVNHATFZC5">​<img id="k_2109484320U1ODALLMWH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208045702YIYS37D9F7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208050650AGJJLXZXIE">能够解放</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208050651EGVU1S9FEX">前端curd、表单读写</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208052465XYQN49M7FN">重复性的工作，要有</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_220805246745699OZCXO">curd的通用封装，要有表单双向联动实现。</span></div><div id="k_2109292050V9EAT6JD3L" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109485533Y6YZ6ER5NP">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21094855341KIF25WXW9">​<img id="k_2109485534UXYF6CWSCI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold;" id="k_26095400839336Q7K2TZ">使用简单，只需要定义一个html标签，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_220806558231UV92E1H9">然后new一个组件实例即可，组件全面完整，能够覆盖常见表单UI需求。</span></div><div id="k_2109314992HPWIDCONJB" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21094906784NN1TK5743">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21094906792KVP9U5I1D">​<img id="k_21094906791ZG79SP3B1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208072497HLXM8XGFVJ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208072859JBPGB9GDPW">只需要支持json参数方式创建即可，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208072861YTSZSKBTWA">可以通过后端控制json参数进而控制ui组件，特别是按钮权限可见性、可用性的问题，我不想写额外的ui权限逻辑。</span></div><div id="k_2109361723YULTMIEXZF" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109493444JDOZ5KOS9W">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(255, 255, 255);" id="k_22082006919HIEBUZABH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2609593587IBMS6NUJGB">​<img id="k_2609593588W3ILLN83E3" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABi0lEQVQoU5WSv2sUARSEv3lrJIVaKArKwa279ydYaiEcCFZiUPAaRUxhY6oINpJKC1MFi6AWNgYsbASNtRJRsbMQxN29QyQQf4B6EDG378mtRMXOV8+8gW9G/Oep0Qdm/Xwa1yzQRpGAPiO/4Wk5h/ix+VeNuOzcQnGa0FDEK6SNiMgRLcGz2kZTpIPVsUlWZTO4zUs8rbeuH6f1/tPv1Cq7RtgFEffrrDiBcFmRv0Ts8ogj5OWbRry2e1vybcfdsHiusINBHHD8GFn1WFZ01kSsIN2LYM7xmQSdD3EU1Ie4Q+giFrO+v1iQlZ2PEA8JjVCcaRCAQKuexMlkFGlIN4HLnhXzY8NrYN0nvndtY3IBorcppl2sWJEvgnqOnyIvH8jK7Dph0xhXPH171fqdSx71Mln1giLrGrYkYlBv/3qYPR+GokpTiy3LBJ2GhnwR+GJhPaRzEENPmBqnNVgbKv323sQnbgfRFbJfXUYQFC4/S1Y9+VPc39N419rJaPKQue/zpH5EOqj+Xc5P0xi1DQAn1awAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(255, 255, 255);" id="k_260959358947HJT5XQK7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208075555QJ8QFUBTVE">统</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208080853ZX1GTS68OU">一规范，支持</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_261001091295WZ8N4PPE">代码生</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_26100132534WO7W7IDIF">成器生成列表、新增/修改</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2610022386DDC5DJGWZA">这些通用的页面，我们希望能解放curd页面的基础工作</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_26100109158VM7OUUKW7">。</span></div><div id="k_210944567669BFSLQF24" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109494288GQANPF1ITV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109494289WUDMPMC5PC">​<img id="k_2109494289KG7XPKDEA7" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208082909AW97SX664G">​满足</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21094942888PUU971KG9">常规需求，如国际化语言、自定义某些提示语、自定义字体图标、主题样式包那也是必须支持的。</span></div><div id="k_2109510826VTYSTRA7PI" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2109535346R1YT2EMPD2">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2109535348Y92D6CRTX1">​<img id="k_210953534894S3MDQKOI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABiElEQVQoU52SPWgUcRDFf2/P3WBIZ+0HoiCIIO522sRLLsGoiIVF0EasbQRBK21TimAtFkIINhLhNn6ghSJ6uVNBElC0UQ6xixhvs/t/cgEPRRDM1PObmffmiQ2UfjFJM92DNAMeFRox/AB3gqsL5UTn6e+z16FkIZ0m6AZ4ROiT5deg3di7kArZF3uN1nWE+/3alGcHI/uuxGpFNF02XjwebF9I92HNAVtROF2ML95Zh5JmNoc8KXSq13h5b3DG/WxnYs6Eyk9qEbNGH4rhlTqHllc0lKcfDd0iqk3Fobpse76UlxJq84L9hluYYeR6iMKRcqz9TEN51u0LNiwKXQJ/wXxH2mH8poh0Ig4+LzgX4GTZaOVK8mxZpturaseSWnkTOC4pMnQKqika7c9Jns6CDjtU9bXJ9qs/NcVfHybFlttI2wuqo30gfpAeiCqahvdFvDrG6NtvG3Ov71bczM5KvobZjPQOWAK2gfeCKvCVYrw1M/jTvxIh8yjgq2sTred/JeJ/4/cTTOzCDk084aAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_22080840255NPX6MPRKE">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208083879DWI3545IOB">纯前端的，适用</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208124899JZAX24FS3U">于前后端分</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208124901VN4HNQL5K3">离开发，也可以通过</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110071263SOZXL4SRM3">后端实施某些配置来控制前端ui，比如将数据列表/工具栏做成后端xml配置方式。</span></div><div id="k_2208091517Q9QKIE2PHH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 20px; margin-top: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208132406LLGRXGU5U2">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2208132409IC9GHG4OPL">​<img id="k_2208132409R3GGFIG9G3" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABi0lEQVQoU5WSv2sUARSEv3lrJIVaKArKwa279ydYaiEcCFZiUPAaRUxhY6oINpJKC1MFi6AWNgYsbASNtRJRsbMQxN29QyQQf4B6EDG378mtRMXOV8+8gW9G/Oep0Qdm/Xwa1yzQRpGAPiO/4Wk5h/ix+VeNuOzcQnGa0FDEK6SNiMgRLcGz2kZTpIPVsUlWZTO4zUs8rbeuH6f1/tPv1Cq7RtgFEffrrDiBcFmRv0Ts8ogj5OWbRry2e1vybcfdsHiusINBHHD8GFn1WFZ01kSsIN2LYM7xmQSdD3EU1Ie4Q+giFrO+v1iQlZ2PEA8JjVCcaRCAQKuexMlkFGlIN4HLnhXzY8NrYN0nvndtY3IBorcppl2sWJEvgnqOnyIvH8jK7Dph0xhXPH171fqdSx71Mln1giLrGrYkYlBv/3qYPR+GokpTiy3LBJ2GhnwR+GJhPaRzEENPmBqnNVgbKv323sQnbgfRFbJfXUYQFC4/S1Y9+VPc39N419rJaPKQue/zpH5EOqj+Xc5P0xi1DQAn1awAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2208132410FU7RIWWLO8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_22081324078WHU7IMV7Z">主题易于修改，比如可以修改图标的颜色、大小、修改按钮大小规格，表格可以定义是否需要分割线等。</span></div><div id="k_2109204961BIGRDSTUVE" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(152, 147, 250); padding-left: 12px; border-top: 1px solid rgb(88, 184, 14); border-right: none; border-bottom: none; border-left: 1px solid rgb(88, 184, 14); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2110185355OBSOATHA3T">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2208164012VCPPSDH2UO">​<img id="k_2208164013RV8PZV8UDW" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAABIElEQVQ4T+3TsUvXQRzG8dcj4iCEk/9BkyCOujkEjoLVqFMtgegY/DYFcXITXHQSxCEdWsTqHwiEwKG/QHANkqzBLi6+ymEKgrZ56+fuzd1z7yceYOWSUUoZwQ5G8Qkvk3xv5k/wDs9wjNkkX+u8hazjDfrwE8tJVhvIMt5iAL+xkWT+OmQPz5vXbSV53UA28aqZ7yd58V9ucv9M7vNJV8E+Qq4S+CeTUsoM5jCG4W5nlesUR9XqJAdthq2xVaRq5NPW5BsCL/iCXpLDv7KVUmontlBv0I8f+Iz3+FD70fVqCtMYxyDOsZlkoUI+dqW66A4uJjm57ctLKRNYwWS3Z7dCvnWlW0uydFdfSim1sDWCXxVSm3uWZPuugKaUPQz9AVGQdSaI2Y9yAAAAAElFTkSuQmCC" style="width:17px;height:17px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2208164014WLLYKGXVAA">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_21101853584RK9ZBTWHZ">​​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_22081619684NFY4L3VZM">​​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_22081619706BXU2JL1VX">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_211018536041AP9GFCK1">​&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(152, 147, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_211018535634OZPYR1E7">理想很丰满，现实很骨感，我只得自己动手写一个满足上述需求的UI框架Bui，下面以datagrid方式简要说明Bui组件的使用style</span></div><div id="k_2110094849QXKZEMZOMW" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 20px; margin-bottom: 12px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110240455XFDY2R4ZNG">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2110240457EJFO6A5RZJ">​<img id="k_2110240458DALNYMVIU8" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABDklEQVQ4T6WSPS8EURSGnzOTwcylkSgkWgkSOzq9XyBRoBLZRCRItlCoJAqJqJRCJbQajV+gZuxGohLZzkcj5lqy5ogNW5ndjXvak+c5H3kFhxIHFjf4mveRgK7HUeQ5wQ4FiD9GeN/JRnJFugk6bDArlnQ6Q5Y9/MWYntt2Aqmi4RNvW0I2qJg1DzuTQbETQePmCtpbJ90G+rsxpRp2VtA5wV+ICe/yNmg+7EdwCFLrIyq9YFcFnRLMfIw8/CVowgl2UtF9wVsPyG4+0BNFTieIDgSp58JlXgufyBHoRoaUPfRY4LyA2csDv2XyX7ABX2J3ffRinOgswS4BAzHRTquJvye4JaxdEFr1nSZ/AXLCXBAZ9kDKAAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110240460JIEMCIB2V3">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 55, 255); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110314136IRNNX898YN">定义一</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 55, 255); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110314137NXESKIHNKE">个用于</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 55, 255); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110314141I12OMPL2DI">列表的ta</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 55, 255); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110314142MY8D3VADPG">bl</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 55, 255); background-color: rgb(255, 255, 255); font-weight: bold; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110312913NQGJL9X1F8">e标签</span></div><div id="k_2110230028IEVN1BPCFH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 92px; width: 714px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21102300297CIW8QE9H8"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">      &lt;</span><span style="color: #569cd6;">div    </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"section-content"</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">              &lt;</span><span style="color: #569cd6;">h1   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"section-title"</span><span style="color: #808080;">&gt;</span><span style="color: #d4d4d4;">静态数据带工具列</span><span style="color: #808080;">&lt;/</span><span style="color: #569cd6;">h1</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">              &lt;</span><span style="color: #569cd6;">div  </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">class</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"section-body clearfix"   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">style</span><span style="color: #d4d4d4;">=</span><span style="color: #ce9178;">"</span><span style="color: #ce9178;">height:500px"</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">                       &lt;</span><span style="color: rgb(255, 0, 5);">table id="datagrid"</span><span style="color: #808080;">&gt;&lt;<span style="color: rgb(255, 0, 5);">/</span></span><span style="color: rgb(255, 0, 5);">table</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">              &lt;/</span><span style="color: #569cd6;">div</span><span style="color: #808080;">&gt;</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #808080;">      &lt;/</span><span style="color: #569cd6;">div</span><span style="color: #808080;">&gt;</span></div></div></pre></span></div><div id="k_2110094987QRSGSE7DH8" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 12px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110311054SELXFRPD4X">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2110311057PMI1FC5RGN">​<img id="k_2110311058NSO71WO1B6" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABDklEQVQ4T6WSPS8EURSGnzOTwcylkSgkWgkSOzq9XyBRoBLZRCRItlCoJAqJqJRCJbQajV+gZuxGohLZzkcj5lqy5ogNW5ndjXvak+c5H3kFhxIHFjf4mveRgK7HUeQ5wQ4FiD9GeN/JRnJFugk6bDArlnQ6Q5Y9/MWYntt2Aqmi4RNvW0I2qJg1DzuTQbETQePmCtpbJ90G+rsxpRp2VtA5wV+ICe/yNmg+7EdwCFLrIyq9YFcFnRLMfIw8/CVowgl2UtF9wVsPyG4+0BNFTieIDgSp58JlXgufyBHoRoaUPfRY4LyA2csDv2XyX7ABX2J3ffRinOgswS4BAzHRTquJvye4JaxdEFr1nSZ/AXLCXBAZ9kDKAAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21103110595IONZ6ORRV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 55, 255); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110311055SB5AAIK8QJ">编写json参数</span></div><div id="k_21100951416PUVIPMGZQ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 97px; width: 709px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21100951435GDXI6DTNK"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">     var   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">args</span><span style="color: #d4d4d4;"> = {</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              data:</span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">treeData1</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">// data,  treeData</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              cols:</span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">treeCol</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">// treeCol cols</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              isTree:</span><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">true</span><span style="color: #d4d4d4;">,  <span style="color: rgb(15, 224, 0);">//是否树形列表</span></span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              title:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">"树形表格"</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              methodsObject:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">"toolMethods"</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              loadImd:</span><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">true</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">//是否立即加载</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              treeIconColor:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'#0D8AE6'</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              oprCol:</span><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">true</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">//是否需要操作列</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              oprColWidth:</span><span style="color: #d4d4d4;"></span><span style="color: #b5cea8;">200</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">//定义操作列宽</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              fillParent:</span><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">true</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #6a9955;">              //url: 'http://localhost/myui/Handler.ashx?flag=datagridTree', </span></div><div><span style="color: #d4d4d4;"></span><span style="color: #6a9955;">              //toolbar: groupBTs, //toolbars</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              checkBox:</span><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">false</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">//是否需要复选框               </span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              idField:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'id'</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">//id字段名称  </span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              toolbarOpts:</span><span style="color: #d4d4d4;"> {</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                         style:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'min'</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">//工具栏按钮样式</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                         color:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'#EDEDED'</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                         iconColor:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'#BCB9C9'</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">                         fontColor:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'#666666'</span></div><div><span style="color: #d4d4d4;">                },</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">               pgposition:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">"bottom"</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">//both bottom top</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">               iconCls:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'fa-table'</span><span style="color: #d4d4d4;">,</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">               btnStyle:</span><span style="color: #d4d4d4;"></span><span style="color: #ce9178;">'plain'</span><span style="color: #d4d4d4;">, </span><span style="color: #6a9955;">//plain</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #6a9955;">              //splitColLine: 'k_datagrid_td_none_line',</span></div><div><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">              showBtnText:</span><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">true</span><span style="color: #d4d4d4;">,</span><span style="color: rgb(212, 212, 212);"></span></div><div><span style="color: #d4d4d4;">                。。。。。。。。。。。。。。。。。</span></div><div><span style="color: #d4d4d4;">                。。。。。。。。。。。。。。。。。</span></div><div><span style="color: #d4d4d4;">         };</span></div></div></pre></span></div><div id="k_2110095175XF8XMZJOQJ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 12px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 55, 255); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110370933ROPEEDDOO8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2110370938CZHKYQXTX2">​<img id="k_2110370939GSF6KCQV7P" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABDklEQVQ4T6WSPS8EURSGnzOTwcylkSgkWgkSOzq9XyBRoBLZRCRItlCoJAqJqJRCJbQajV+gZuxGohLZzkcj5lqy5ogNW5ndjXvak+c5H3kFhxIHFjf4mveRgK7HUeQ5wQ4FiD9GeN/JRnJFugk6bDArlnQ6Q5Y9/MWYntt2Aqmi4RNvW0I2qJg1DzuTQbETQePmCtpbJ90G+rsxpRp2VtA5wV+ICe/yNmg+7EdwCFLrIyq9YFcFnRLMfIw8/CVowgl2UtF9wVsPyG4+0BNFTieIDgSp58JlXgufyBHoRoaUPfRY4LyA2csDv2XyX7ABX2J3ffRinOgswS4BAzHRTquJvye4JaxdEFr1nSZ/AXLCXBAZ9kDKAAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 55, 255); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110370940CGWD1TO4HK">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 55, 255); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110370935QNY2GB4I6V">根据参数创建datagrid组件实例</span></div><div id="k_2110362263KUL9H19DHF" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 99px; width: 709px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110362264LMMAOOWDI9"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #d4d4d4;"></span></div><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #d4d4d4;"></span><span style="color: #569cd6;">    </span></div><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><span style="color: #569cd6;">          var   </span><span style="color: #d4d4d4;"></span><span style="color: #9cdcfe;">grid</span><span style="color: #d4d4d4;"> = </span><span style="color: #569cd6;">new </span><span style="color: #d4d4d4;"></span><span style="color: #4ec9b0;">$B</span><span style="color: #d4d4d4;">.</span><span style="color: #4ec9b0;">Datagrid</span><span style="color: #d4d4d4;">(</span><span style="color: #dcdcaa;">$</span><span style="color: #d4d4d4;">(</span><span style="color: #ce9178;">"#datagrid"</span><span style="color: #d4d4d4;">), </span><span style="color: #9cdcfe;">args</span><span style="color: #d4d4d4;">);</span></div><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><br></div></pre></span></div><div id="k_2110362210MBIUCZNVX3" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2110362211MTB5FJI3UT">​</span></div><div id="k_22080019828PNBHVZ83T" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2208001987MY2BVHONHI">​</span></div><div id="k_2311523231E6XSWEX4FC" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2311523231Q1QVRGZKBY">​</span></div><div id="k_2609532690S5F8J46ZTT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609532691DX3ZXCV16F">​</span></div><div id="k_211040246254UJD8LAYB" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 559px; height: 165px; position: absolute; top: 835px; left: 440px; transform: rotate(336deg);"><div style="width: 100%; height: 100%; position: absolute; z-index: 20000000; background: rgb(255, 255, 255); top: 0px; left: 0px; opacity: 0.78;"></div><div id="k_21104024633BCZPR11DO" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;" spellcheck="false"><div id="k_2110402464GZPN8DXBPJ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background: none; margin-top: 20px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_21104638349G3XP8UBKD">采用JSON创建组件，意味着你可以在后端控制JSON参数，返回给前端组件用</span></div><div id="k_21104145598GBNM1CZR3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_2110500509OEN7CQFBXQ">什么【</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 107, 23); background: none; font-weight: bold;" id="k_2110500510XJNGUZG65F">UI按钮权限</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_2110500510ORC7G1BBN9">】啦..........</span></div><div id="k_2110421442EC6X696TSP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_2110455804GGVU8HCVLX">根据查询条</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_21104857562HDXQ1JKBZ">件实现【</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 107, 23); background: none; font-weight: bold; letter-spacing: 1px;" id="k_2110492426GJDS9IPGDY">动态列表态表头</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none rgba(0, 0, 0, 0); font-weight: 700; letter-spacing: 1px; font-style: normal; text-decoration: none solid rgb(252, 5, 161); vertical-align: baseline;" id="k_2110492427XYZMD28FWH">】</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_2110493970961QOV7WDH">啦</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_2110493972RBIXOGKK2N">...</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_21104939731WA8TAPP5G">....</span></div><div id="k_2110424197WY1DI2YXQS" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_2110424198YOSKMGO36J">​根据数据场景渲染ui效果啦.............</span></div><div id="k_2110460775LD4JEXSYXQ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: bold;" id="k_211046077747BWGYQQE8">​尽在你的掌控之中！</span></div></div></div><div id="k_2110530092PF7ZR4TMO1" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 327px; height: 88px; position: absolute; top: 156px; left: 631px; transform: rotate(0deg);"><div style="width: 100%; height: 100%; position: absolute; z-index: 20000000; background: rgb(255, 255, 255); top: 0px; left: 0px; opacity: 0.4;"></div><div id="k_2110530094EFK6CF9UFA" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;" spellcheck="false"><div id="k_2110530095JIHSFIOTCV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 98, 0); background: none; font-weight: bold; font-style: italic; letter-spacing: 1px;" id="k_2110534043JLL7ERPYPP">我们不一样！</span></div><div id="k_2110532185ZU4X14BGX3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 0, 135); background: none; font-weight: bold; font-style: italic; letter-spacing: 1px;" id="k_2110540488VWEFDR54LY">这是一个后端程序员写的前端UI框架！</span></div><div id="k_2110542315Y8OXC5K77J" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 0, 135); background: none; font-weight: bold; font-style: italic; letter-spacing: 1px;" id="k_2110553982RTJ6SE2NQA">我深刻体会着前后端结合的痛点</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 0, 135); background: none; font-weight: bold; font-style: italic; letter-spacing: 1px;" id="k_21105539832SKWHE6WKG">！</span></div></div></div>
</div>