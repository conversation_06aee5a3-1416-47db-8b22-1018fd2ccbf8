<div style="position:relative">
<div id="k_260912551784PWLZIMUA" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 240, 240); border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(245, 88, 232); border-image: initial; padding-left: 14px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 240, 240);" id="k_2609144903XETY7X4CEW">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2609144906NTCZ5CFDPL">​<img id="k_2609144907YKDUYIKHRB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABJUlEQVQ4T42SvUoDQRSFvzMJEZR0+RF7W/EF0ob4BKkslFSCWlipiKL4ACnS2FpokdZKSBewDTb6Bm5QREJAi71XVowEIbuZambO+eaegSOmVhmvhUBH4slidocomtan95ocSngxH+ggNt0ZYdQj9JAJgheqgVOJI3faGMcRGs8BJhZPEiyAPmcBk/u/qFnG//ov6KEE6/nAvkNDxvYLukt7TMt4mcAZYmditJj6EN2ng8HPzegr0JTYcmcQG81X9JwKJmIRLy3luAYaQPcrpvWOPjLBCr6mQFdi1Z2TyLgEeSZYzXlTcJsY5/lf4hN4rhq4kDh051HGngVqGL0h6s8swAq+GAfaEq2fCjgDjIMIemlxk4mFSjIRNgRXY+NmhN6yCvENk5liD4H924gAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26091452355A9ODURZRT">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(250, 240, 240);" id="k_2609144904Q9NE64GI7V">介绍</span></div><div id="k_26091303823MSSLHCDXF" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: 397px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 14px; border-top: 1px solid rgb(216, 224, 98); border-right: 1px solid rgb(216, 224, 98); border-bottom: none; border-left: 1px solid rgb(216, 224, 98); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609172436U8NXPWS8P6">1 . </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609182336F2NO9LERIX">利用隐藏iframe构建fo</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609182337T111OGMNVO">rm表单模拟Ajax上传</span></div><div id="k_2609163332TRGPT8JMXR" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: 397px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 14px; border-top: none; border-right: 1px solid rgb(216, 224, 98); border-bottom: none; border-left: 1px solid rgb(216, 224, 98); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26091645356BUWYSBMRF">​2 . </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_260916333467DVEEJPZW">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609163335PRY4UZBE9E">支持选择文件自动上传，支持按钮提交方式上传</span></div><div id="k_2609164542X8K31LSW2O" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: 397px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 14px; border-top: none; border-right: 1px solid rgb(216, 224, 98); border-bottom: none; border-left: 1px solid rgb(216, 224, 98); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609165563LEIAN2K4RZ">​3 . </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609164542KI9VR4W6CG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26091645445VGHN4QSYP">支持附加上传需要用到的额外参数</span></div><div id="k_2609165569Z93NHLBXUX" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: 397px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 14px; border-top: none; border-right: 1px solid rgb(216, 224, 98); border-bottom: 1px solid rgb(216, 224, 98); border-left: 1px solid rgb(216, 224, 98); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609165569UG8WD5B5OO">​4 . </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609182341GTIM1XD98P">支持单文件、多文件上传需</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26091823425DABJC3ZYR">求、支持删除文件回调监听</span></div><div id="k_2609132445FUXIJXFLZH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609132446ZWI6DAUCEJ">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_2609130374CZC4QTEMG3" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 240, 240); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(245, 88, 232); border-image: initial; margin-top: 0px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(250, 240, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2609154264PEUUPG52W4">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2609154267XG22NAJHRP">​<img id="k_2609154267B8W15G4VJN" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABJUlEQVQ4T42SvUoDQRSFvzMJEZR0+RF7W/EF0ob4BKkslFSCWlipiKL4ACnS2FpokdZKSBewDTb6Bm5QREJAi71XVowEIbuZambO+eaegSOmVhmvhUBH4slidocomtan95ocSngxH+ggNt0ZYdQj9JAJgheqgVOJI3faGMcRGs8BJhZPEiyAPmcBk/u/qFnG//ov6KEE6/nAvkNDxvYLukt7TMt4mcAZYmditJj6EN2ng8HPzegr0JTYcmcQG81X9JwKJmIRLy3luAYaQPcrpvWOPjLBCr6mQFdi1Z2TyLgEeSZYzXlTcJsY5/lf4hN4rhq4kDh051HGngVqGL0h6s8swAq+GAfaEq2fCjgDjIMIemlxk4mFSjIRNgRXY+NmhN6yCvENk5liD4H924gAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(250, 240, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2609154268OUTBM3N9HV">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(250, 240, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2609154265SPBR9N8RR1">API 及 构造参数说明</span></div><div id="k_2609131610EQNBN81L5W" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190326091912HGMNYXGE2L63"><tbody><tr id="row_0"><td id="k_2609191208AUW77IA5IR" tabindex="0" row="0" col="0" w="1305" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1307px; background-color: rgb(250, 245, 246);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 245, 246);" id="k_2609191209ACDCCUCI8V"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 245, 246);" id="k_2609191210AK1FWH58AF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 245, 246);" id="k_2609451664FYVM16VRRG">var upload = new $B.MutilUpload(opts)&nbsp; opts 参数说明&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(170, 170, 170); background-color: rgb(250, 245, 246);" id="k_2609451665N9DG7P7LX6"> [&nbsp; 手动提交文件 ： upload.submit()&nbsp; ]</span></p></td></tr><tr id="row_1"><td id="k_2609191218PRLWR3X6GY" tabindex="0" row="1" col="0" w="258" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px; background-color: rgb(250, 245, 246); text-align: center;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(250, 245, 246);" id="k_2609191218G6E4GCYYDY"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 245, 246); text-align: center;" id="k_2609191220B6UNVP5OYE">参数​</span></p></td><td id="k_2609191221JD7XT9OTHD" tabindex="0" row="1" col="1" w="291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px; background-color: rgb(250, 245, 246); text-align: center;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(250, 245, 246);" id="k_2609191222NMC3LOCC1H"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 245, 246); text-align: center;" id="k_26091912236UL3L7X4IV">​参数值</span></p></td><td id="k_2609191224QQDD5TMWYT" tabindex="0" row="1" col="2" w="752" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px; background-color: rgb(250, 245, 246); text-align: center;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(250, 245, 246);" id="k_2609191225TOSQIX1LVP"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 245, 246); text-align: center;" id="k_26091912255HZMOPFS3Y">说明​</span></p></td></tr><tr id="row_2"><td id="k_2609191227PGE7QHASYC" tabindex="0" row="2" col="0" w="258" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26091912285WK3L16HVU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609191228NAA9PK2ACX">​​target</span></p></td><td id="k_2609191230FW2P9H81Q4" tabindex="0" row="2" col="1" w="291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609191231673A5C8FFQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609191231YSI4BENU9W">​</span></p></td><td id="k_26091912356YGK5FQZ8Q" tabindex="0" row="2" col="2" w="752" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609191236GY6D5OSFRU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609191236CO6XLOAX81">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609212910QA3R2AVBE7">​上传控件的容器标签jquery对象，通常是一个div或者td</span></p></td></tr><tr id="row_3"><td id="k_2609191238J9SFYJMI99" tabindex="0" row="3" col="0" w="258" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26091912382VQZU2YSZB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609191239I19869UHWN">​​init</span></p></td><td id="k_26091912415UUPOYOTTG" tabindex="0" row="3" col="1" w="291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609191242DLJ4N7A7CS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609191242KTKBME77GZ">​​默认值:true</span></p></td><td id="k_2609191244GK3HT2GM1W" tabindex="0" row="3" col="2" w="752" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_260919124581NMJC2WYO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26091912451LLI8GOOON">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609223965UH66A7Y2KW">​是否初始化显示文件选择按钮，​动态添加文件时候可以设置为false</span></p></td></tr><tr id="row_4"><td id="k_26091912476IHMG2RRVH" tabindex="0" row="4" col="0" w="258" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26091912487G9NU9J16X"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609191248SAEGXIJ62P">​​timeout</span></p></td><td id="k_26091912503WQTM8URLN" tabindex="0" row="4" col="1" w="291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26091912503WAEKB4HAY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609191251PPYERB79RJ">​​默认值：300秒</span></p></td><td id="k_2609191252TKUXL1G163" tabindex="0" row="4" col="2" w="752" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609191253REV26QSMRZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26091912542KRPQ1X3OC">​​超时时间，单位秒</span></p></td></tr><tr id="row_5"><td id="k_2609191255KEWTJ4R4MZ" tabindex="0" row="5" col="0" w="258" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26091912562ED7XZO7A7"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609191257GAQMSBG6JO">​​immediate</span></p></td><td id="k_2609191258HH44WTF1SU" tabindex="0" row="5" col="1" w="291" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609191260BFNDSM38KE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_260919126081A1P4NI87">​​默认值:false</span></p></td><td id="k_2609191262GP57MGKE5M" tabindex="0" row="5" col="2" w="752" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609191263GMBQILM42K"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26091912643BIJBW2HEA">​​选择文件后是否立即上传文件</span></p></td></tr><tr id="row_6"><td id="k_2609233408XQK6G4DVRS" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" w="258" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609233409MMNB1T4HN6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609233410ESIG3ICBTB">​​url</span></p></td><td id="k_26092334124WBJYKBEIY" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" w="291" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609233413K5H5JOT11O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_260923341362TYATVSAG">​</span></p></td><td id="k_26092334165Q2FOL89DG" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" w="752" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609233417ERGKFT5VKL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609233417JV2WLBVXEO">​​上传Url</span></p></td></tr><tr id="row_7"><td id="k_26092332188VDZBK16BS" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" w="258" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609233219HJ6RWRPBL6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609233220SIVANKBERC">​​files</span></p></td><td id="k_2609233222EFRSWUVX2R" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" w="291" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609233223FQTH332647"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609233224ULJKFXABL6">​​请参考files定义设置</span></p></td><td id="k_2609233227FGGHEFGWMY" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" w="752" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609233228FIE2R5PA7S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609233229JNW8Q683ZG">​​定义需要上传的文件，数组</span></p></td></tr><tr id="row_8"><td id="k_260924518111RQK4KNK6" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" w="258" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609245181PHVF6HVDCJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26092451826FI1B5EIBL">​​success</span></p></td><td id="k_2609245184DRSLTA9O4J" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" w="291" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26092451852O677MJ9ND"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609245186D45XHQT59D">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609245861O4NJQUSLSX">​success = function(&nbsp;​res ){}</span></p></td><td id="k_2609245188IOMMT3MZKJ" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" w="752" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609245189D4BC8LH1OK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609245189OLGF3WMO66">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609255632CPAK7A2XQM">上传成功时候回调 ,</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(255, 255, 255);" id="k_2609255633817F1I5NLD">[ res ]</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609255634S6BOJ7BFPM">&nbsp;成功返回的json结果</span></p></td></tr><tr id="row_9"><td id="k_2609245074YP9IJ2LKKQ" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" w="258" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609245075NLHBHQNOMS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26092615151FDO37D57T">error</span></p></td><td id="k_2609245077VUIZ3C7QHU" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" w="291" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26092450782CSEP2WQML"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609262087AQBZPKBVBU">error</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609262088EAWLBIVLW8">=</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609262089ORFW1WX9JJ">function (res ) {}</span></p></td><td id="k_26092450809STF2E49AL" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" w="752" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609245081DJ44VYU9SZ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609263690S7FWNL17NP">上传</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26092636914XUJAD76BO">失败</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609264189NFD3R9KYMJ">时候回调 ,[ res ]&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609264190L4819H2FX2">错误</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609264191XD972VE3BH">返回的json结果</span></p></td></tr><tr id="row_10"><td id="k_2609244904TP7R9FUZV6" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 260px;" w="258" h="78" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26092449051JQ3IX9UB5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26092700019VH7NVKLIZ">setParams</span></p></td><td id="k_26092449086LR8XUIDEJ" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 293px;" w="291" h="78" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609244908G72KARPY1D"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609270536A2PMHNS274">setParams</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609270537PX1585TP61">=</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26092705389OG48EHNYL"> function (fileName){</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_260927125573I345ATH5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​&nbsp; &nbsp; &nbsp; return {'p1' : 0} ;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2609271440RKILNBR2PR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609271440IYEOXQCLLQ">}</span></p></td><td id="k_2609244911WG3D42MDJD" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 80px; width: 754px;" w="752" h="78" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609244911OF1VSR88Q1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26092449123VRHB6HD4B">​​设置上传需要的额外参数</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_260927452669U9UTIT26"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26092745277OYRYZT2RR">​​fileName：对应files里面配置的name值</span></p></td></tr><tr id="row_11"><td id="k_2609244820G3VMTE1236" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" w="258" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609244821O5U98NNTOW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609281630JCCTEIJH41">ondeleted</span></p></td><td id="k_2609244824J7RFDJZAW4" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" w="291" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609244824NUWGEOKDJQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26092820825BI1472IDT">ondeleted</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609282083JX5TKYN3HO">=</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609282083SFOR4FJN2K"> function (res){}</span></p></td><td id="k_2609244828R8WDV926FL" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" w="752" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609244829RYNSDM5PHT"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609244829ND7MDDCK2K">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26092835614BB3BCYLT1">​删除文件回调，​res：被删除的文件信息</span></p></td></tr><tr id="row_12"><td id="k_260928467423GYS9XNZP" tabindex="0" row="12" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 260px;" w="258" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609284675VSRD4FVG1R"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609284676953331TB2D">​​files</span></p></td><td id="k_2609284679BNQPWR41BG" tabindex="0" row="12" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 293px;" w="291" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609284680PVP1TE9HQ2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_260928468057RALNQ3LV">​请参考《files定义说明》</span></p></td><td id="k_2609284684TXZJNSFYP6" tabindex="0" row="12" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 754px;" w="752" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609284685VXGS157YI6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609284686NJT6IXZRBD">​定义上传的文件</span></p></td></tr></tbody></table></div><div id="k_2609131679MMFICBUKAY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609131680IVOSI58SEV">​</span></div><div id="k_26092429177LJYYLAQXP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190326093030WMFKVEQ66QNB"><tbody><tr id="row_0"><td id="k_2609303011YMCEOQFXW1" tabindex="0" row="0" col="0" w="1305" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1307px; background-color: rgb(250, 245, 246);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 245, 246);" id="k_2609303012BI4526DADA"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 245, 246);" id="k_260930301257K9IOK5Q6">​files定义说明</span></p></td></tr><tr id="row_1"><td id="k_2609303021KQ3UA9LIDI" tabindex="0" row="1" col="0" w="259" h="153" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 155px; width: 261px; background-color: rgba(0, 0, 0, 0);" rowspan="4" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609303022KU7FAVKNAW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_260932357389MVZSNCEO">files = [{&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_26093313296NEZBU2H5Q"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609331330PMQF5LK34B">&nbsp; &nbsp; &nbsp;name: 'xmlfile',&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2609324823S6PVP9K81A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_260932482466PKYVFPEX">&nbsp; &nbsp; &nbsp;type: '.xml',</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2609325163I5XQS9A3F6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26093251656UVZVOUIUK">&nbsp; &nbsp; &nbsp;label: '请选择xml文件</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609325165NH7NEXRHON">',&nbsp;</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_26093253644CFH7BJ4AM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609325365FIKWS912GZ">&nbsp; &nbsp; &nbsp;must: '必须上传xml文件!'</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2609331620GR6PX33SHU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609331621ZF6FWUDM6V">&nbsp;}]</span></p></td><td id="k_2609303024ZOE4YH7ACM" tabindex="0" row="1" col="1" w="290" h="36" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 38px; width: 292px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609303025US4Z8319XR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609303026SC3MPX5ONS">​name</span></p></td><td id="k_26093030285PJV1FQMF8" tabindex="0" row="1" col="2" w="752" h="36" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 38px; width: 754px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609303029DR7YVV4ZU2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609345066UMALKQB88M">​文件input标签的name属性值，服务端依赖这个 </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(160, 32, 240);" id="k_2609345067XWAREEOEZW">【name】</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609345068DMJOLGZ73E"> 获取对应的文件</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></p></td></tr><tr id="row_2"><td id="k_2609303037GDADIHXRCE" tabindex="0" row="2" col="0" w="290" h="37" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 39px; width: 292px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609303038ZV5NAFBX18"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609303039E8LCJNBG3N">​type</span></p></td><td id="k_2609303040N6G4BHLGH7" tabindex="0" row="2" col="1" w="752" h="37" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 39px; width: 754px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609303041MOW9E9M2RE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26093030429KLJWBCG1A">​文件类型，[ .*&nbsp; ]表示任意文件</span></p></td></tr><tr id="row_3"><td id="k_2609303046IZRULE1236" tabindex="0" row="3" col="0" w="290" h="37" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 39px; width: 292px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26093030474LSYBO4QUR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609303048G2L6JN3U7N">​​label</span></p></td><td id="k_2609303049DS14EL9UM7" tabindex="0" row="3" col="1" w="752" h="37" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 39px; width: 754px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609303050DHW1IIBLGY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_260930305143IHY8QT5V">​​上传按钮显示的文本</span></p></td></tr><tr id="row_4"><td id="k_2609303056RDF8HSQMBT" tabindex="0" row="4" col="0" w="290" h="37" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 39px; width: 292px;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2609303057DFVMQ314ND"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609303057KA1R81CW2S">​​must</span></p></td><td id="k_26093030598A225X92CA" tabindex="0" row="4" col="1" w="752" h="37" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 39px; width: 754px;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_26093030602A3HWY3Z2V"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609303061ZUW38JRWTY">​​当必须上传文件时候，请填写必须提示内容即可，否则为不必须上传的文件</span></p></td></tr></tbody></table></div><div id="k_26091322711RW1IMBJGD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609132272FKMATWZUE9">​</span></div><div id="k_26091316266E699KS4CL" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 240, 240); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 2px solid rgb(245, 88, 232); border-image: initial; margin-top: 0px; margin-bottom: 18px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(250, 240, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2609154967ROZXE1QTXS">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2609154970WAEQ4JE7XH">​​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(250, 240, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2609154971VA3NH4CKEV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2609160695QMRPCC5MGP">​<img id="k_2609160696KH7W9T3UNP" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABJUlEQVQ4T42SvUoDQRSFvzMJEZR0+RF7W/EF0ob4BKkslFSCWlipiKL4ACnS2FpokdZKSBewDTb6Bm5QREJAi71XVowEIbuZambO+eaegSOmVhmvhUBH4slidocomtan95ocSngxH+ggNt0ZYdQj9JAJgheqgVOJI3faGMcRGs8BJhZPEiyAPmcBk/u/qFnG//ov6KEE6/nAvkNDxvYLukt7TMt4mcAZYmditJj6EN2ng8HPzegr0JTYcmcQG81X9JwKJmIRLy3luAYaQPcrpvWOPjLBCr6mQFdi1Z2TyLgEeSZYzXlTcJsY5/lf4hN4rhq4kDh051HGngVqGL0h6s8swAq+GAfaEq2fCjgDjIMIemlxk4mFSjIRNgRXY+NmhN6yCvENk5liD4H924gAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(250, 240, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2609160697TYTK163DWC">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(250, 240, 240); font-weight: 400; font-style: normal; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_2609154968QAHS6AXO8D">Demo</span></div><div id="k_2609131719H6PHLCGBP4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609404275C7INWNUKPT">1 </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609404277U8JWG2BHYK">、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609401052U2JBZ7ROYC">定义</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609401053GVIK86TWSU">容器标签及</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609401053GIXNSGA7PY">提交按钮</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(170, 170, 170); background-color: rgb(255, 255, 255);" id="k_2609394267L6AUNLEEHU">( 如定义为自动上传则不需要按钮 )</span></div><div id="k_2609395329414B5YYEDT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span></div><div id="k_2609402149KQCFOXTK8C" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609402151YOP1CHZZIB"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(83, 83, 83); background: none;">&lt;</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">=</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(161, 100, 75); background: none;">"fileUpload2"</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(41, 111, 169); background: none;">div</span><span style="font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; color: rgb(83, 83, 83); background: none;">&gt;</span></div><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"></span><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">button</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">id</span>=<span style="color: rgb(161, 100, 75); background: none;">"uploadFiles"</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">style</span>=<span style="color: rgb(161, 100, 75); background: none;">"</span><span style="color: rgb(161, 100, 75); background: none;">background:#B90524"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;</span><span style="color: rgb(41, 111, 169); background: none;">i</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">class</span>=<span style="color: rgb(161, 100, 75); background: none;">"fa fa-upload"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">i</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span>点击上传<span style="color: rgb(83, 83, 83); background: none;">&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">button</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></pre></span></div><div id="k_2609410067GG4OPHXRBG" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609410069PFFC63UIGH">​</span></div><div id="k_2609401988UPZYWED4OY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609401989L2DVT6YNYP">2 、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2609403618FIXYPRCK96">创</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2609403619S4DRROYZGK">建对象</span></div><div id="k_26091317697MQN5B4YMT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609131770US8ADFF5JB">​</span></div><div id="k_2609405846GKVMA8IO2Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609405847TDWPYLNB8L"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="white-space: normal; color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="white-space: normal; color: rgb(111, 175, 209); background: none;">uploadObj</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">=</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="white-space: normal; color: rgb(41, 111, 169); background: none;">new</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;</span><span style="white-space: normal; color: rgb(33, 156, 131); background: none;">$B</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">.</span><span style="white-space: normal; color: rgb(33, 156, 131); background: none;">MutilUpload</span><span style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">({</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;target:</span>&nbsp;<span style="color: rgb(175, 175, 125); background: none;">$</span>(<span style="color: rgb(161, 100, 75); background: none;">"#fileUpload2"</span>),</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;timeout:</span>&nbsp;<span style="color: rgb(136, 161, 123); background: none;">180</span>,&nbsp;<span style="color: rgb(61, 108, 40); background: none;">//超时时间 秒</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;init:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">true</span>,&nbsp;<span style="color: rgb(61, 108, 40); background: none;">//是否初始化文件选择按钮</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;immediate:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">false</span>,&nbsp;<span style="color: rgb(61, 108, 40); background: none;">//选择文件后是否立即自动上传，即不用用户点击提交按钮就上传</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;url:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">'http://localhost/myui/demo/Handler.ashx?flag=uploadfile'</span>,</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(61, 108, 40); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;// 待上传的文件列表，用于设置input的 name 以在服务器区分文件 &nbsp;</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;files:</span>&nbsp;[{</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">'datafile1'</span>,</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;type:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">'.xml'</span>,</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;label:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">'请选择数据文件'</span>,</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;must:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">'必须上传文件!'</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}, {</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;name:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">'xlsfile12'</span>,</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;type:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">'.xls,.xlsx'</span>,</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;label:</span>&nbsp;<span style="color: rgb(161, 100, 75); background: none;">'请选择xls(x)文件'</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}],</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp; &nbsp; &nbsp; &nbsp;onselected</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">value</span>,&nbsp;<span style="color: rgb(111, 175, 209); background: none;">id</span>,&nbsp;<span style="color: rgb(111, 175, 209); background: none;">accept</span>) {&nbsp;<span style="color: rgb(61, 108, 40); background: none;">//选择文件后的事件请返回true 以便通过验证</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(33, 156, 131); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;console</span>.<span style="color: rgb(175, 175, 125); background: none;">log</span>(<span style="color: rgb(111, 175, 209); background: none;">id</span>&nbsp;+&nbsp;<span style="color: rgb(161, 100, 75); background: none;">" onselected = "</span>&nbsp;+&nbsp;<span style="color: rgb(111, 175, 209); background: none;">value</span>);</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(152, 89, 147); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;return</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">true</span>;</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp; &nbsp; &nbsp; &nbsp;success</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">res</span>) {&nbsp;<span style="color: rgb(61, 108, 40); background: none;">//成功时候的回调</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(33, 156, 131); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; console</span>.<span style="color: rgb(175, 175, 125); background: none;">log</span>(<span style="color: rgb(161, 100, 75); background: none;">"手动上传成功："</span>&nbsp;+&nbsp;<span style="color: rgb(33, 156, 131); background: none;">JSON</span>.<span style="color: rgb(175, 175, 125); background: none;">stringify</span>(<span style="color: rgb(111, 175, 209); background: none;">res</span>));</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp; &nbsp; &nbsp; &nbsp;ondeleted</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">res</span>) {&nbsp;<span style="color: rgb(61, 108, 40); background: none;">//删除回调</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(33, 156, 131); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;console</span>.<span style="color: rgb(175, 175, 125); background: none;">log</span>(<span style="color: rgb(161, 100, 75); background: none;">"手动ondeleted："</span>&nbsp;+&nbsp;<span style="color: rgb(111, 175, 209); background: none;">res</span>);</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp; &nbsp; &nbsp; &nbsp;setParams</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">fileName</span>) {&nbsp;<span style="color: rgb(61, 108, 40); background: none;">//设置参数</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(33, 156, 131); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;console</span>.<span style="color: rgb(175, 175, 125); background: none;">log</span>(<span style="color: rgb(161, 100, 75); background: none;">"手动 setParams "</span>&nbsp;+&nbsp;<span style="color: rgb(33, 156, 131); background: none;">JSON</span>.<span style="color: rgb(175, 175, 125); background: none;">stringify</span>(<span style="color: rgb(111, 175, 209); background: none;">fileName</span>));</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(41, 111, 169); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;var</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">extparams</span>&nbsp;=&nbsp;<span style="color: rgb(175, 175, 125); background: none;">$</span>(<span style="color: rgb(161, 100, 75); background: none;">"#extparams"</span>).<span style="color: rgb(175, 175, 125); background: none;">val</span>();</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(152, 89, 147); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;return</span>&nbsp;{</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; p2:</span>&nbsp;<span style="color: rgb(136, 161, 123); background: none;">6666</span>,</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; extparams:</span>&nbsp;<span style="color: rgb(111, 175, 209); background: none;">extparams</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp; &nbsp; &nbsp; &nbsp; };</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;&nbsp;&nbsp;&nbsp;},</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(175, 175, 125); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; error</span><span style="color: rgb(111, 175, 209); background: none;">:</span>&nbsp;<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;(<span style="color: rgb(111, 175, 209); background: none;">res</span>) {&nbsp;<span style="color: rgb(61, 108, 40); background: none;">//错误回调</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(33, 156, 131); background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; console</span>.<span style="color: rgb(175, 175, 125); background: none;">log</span>(<span style="color: rgb(161, 100, 75); background: none;">"手动 error"</span>&nbsp;+&nbsp;<span style="color: rgb(111, 175, 209); background: none;">res</span>);</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">&nbsp;&nbsp;&nbsp;&nbsp;}</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">});</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(175, 175, 125); background: none;">$</span>(<span style="color: rgb(161, 100, 75); background: none;">"#uploadFiles"</span>).<span style="color: rgb(175, 175, 125); background: none;">click</span>(<span style="color: rgb(41, 111, 169); background: none;">function</span>&nbsp;() {&nbsp; //提交文件</div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;"><span style="color: rgb(111, 175, 209); background: none;">&nbsp; &nbsp; &nbsp; &nbsp;<span style="color: rgb(255, 0, 5); background: none; font-weight: bold;"> uploadObj</span></span><span style="color: rgb(255, 0, 5); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none; font-weight: bold;">.submit();</span></div><div style="color: rgb(167, 167, 167); font-family: Consolas, &quot;font-size:14px;&quot;; font-size: medium; white-space: normal; background: none;">});</div></pre></span></div><div id="k_26091303282KCTA9W2NI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_26091303308R1CFCIWX1">​</span></div><div id="k_260913047589NIF2TMF6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2609130476LJZC57JG5K">​</span></div><div id="k_2610164422Y8IBWYACA4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2610164424T3E2KH6F61">​</span></div>

</div>