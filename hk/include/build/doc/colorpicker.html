<div style="position:relative">

<div id="k_2411575372YWYK2CDODM" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(244, 252, 242); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(10, 202, 250); border-image: initial; padding-left: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(244, 252, 242); letter-spacing: 2px;" id="k_2412031972OEYT3N8ZCV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2412031975IZGCOPSGSX">​<img id="k_2412031976DFXVQJORVC" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA50lEQVQoU52SoW7CUBiFzykUKCQYxJ6BhExtoIchKOTEpkZICAKFmFgmNz23oHBYngHXhschLbTiLGVcAuWyJVx9vnz5z7nEFY9XMPgb8lXzmLwReoRyo7CVX6QSO2TCwguIqoRVVHEf0OD6HNqHAQwIVNKAgC2BcdgsTM0pvyZftTKTDwFPJmwCWcvOVPLjbxLP2bCxCBhumoXZcWH0gvgLwO1BLd2ArO8gwY9ybht3DE+gbOWlIHl1oM/0FpvF2t4RtIwct5u1XIQovcNRP7ovzm3jn+3kBcmEUCd03J7Ncnncf/7WD+XIVQ5LgzgDAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(244, 252, 242); letter-spacing: 2px;" id="k_2412031977RQTQ5BJXVY">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(66, 3, 255); background-color: rgb(244, 252, 242); letter-spacing: 2px;" id="k_2412031973BSYFIFVKZ5">介绍</span></div><div id="k_2411580130VPFD3BMUHE" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 32px; width: 581px; text-align: left; background-color: rgb(240, 255, 250); padding-left: 20px; margin-top: 20px; border-top: 1px dotted rgb(5, 185, 240); border-right: 1px dotted rgb(5, 185, 240); border-bottom: none; border-left: 1px dotted rgb(5, 185, 240); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412101452K1W8JVGH7D">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_24121014547K1JMQG1AW">​<img id="k_2412101455LYVLBSCT8T" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAtklEQVQoU2NkIAMwkqGHAawpweE+B/NPxpT/jAyVjAwMUv8ZGJ4xMDDOYmDk6pt3VPQzusGM0eZv+TiYPi1hYGT0xbD1//9TjP9Yg+eclHmCLMeYbPWwnIHhfwdOZ/5nbJ97XK6agYHxP0wNY7Ll/YMMjIx2uDT9Z2C48v87u9P885Kv4ZqSLB9cZ2Rk0MCj6RnTPwaXOScUrlNmE1l+IhB6h5h/MUbMOqvwHCX0yIonslMEqRoBBpVSDk0WzcQAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412101456G8V7J1JTN9">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412101453LY74YNZQ6F">colorPic</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412094548RBQNXKQ7NI">ker支持快</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412105760EA2J1CLY7E">捷预设置颜</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412105762HHNNPG65GF">色定义。</span></div><div id="k_2412052665ACRDGLSIYS" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 32px; width: 581px; text-align: left; background-color: rgb(240, 255, 250); padding-left: 20px; border-top: none; border-right: 1px dotted rgb(5, 185, 240); border-bottom: none; border-left: 1px dotted rgb(5, 185, 240); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412103123Q4GEMWZH7Q">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2412103126VYAOZWKOKT">​​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412103128QYC4SIH23R">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2412104303LKOY3I7J8R">​<img id="k_2412104304ZMCR4RQUCH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAtklEQVQoU2NkIAMwkqGHAawpweE+B/NPxpT/jAyVjAwMUv8ZGJ4xMDDOYmDk6pt3VPQzusGM0eZv+TiYPi1hYGT0xbD1//9TjP9Yg+eclHmCLMeYbPWwnIHhfwdOZ/5nbJ97XK6agYHxP0wNY7Ll/YMMjIx2uDT9Z2C48v87u9P885Kv4ZqSLB9cZ2Rk0MCj6RnTPwaXOScUrlNmE1l+IhB6h5h/MUbMOqvwHCX0yIonslMEqRoBBpVSDk0WzcQAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_241210430562OB68I9KK">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412103124EOZRD2NBS4">支持记录用户最近使用过的5中颜色（cookie有效期</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412060219DB9QKOKA2L">一</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412060220BRQHYU38B8">天）。</span></div><div id="k_24115801967HYTR2KDFG" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 32px; width: 581px; text-align: left; background-color: rgb(240, 255, 250); padding-left: 20px; border-top: none; border-right: 1px dotted rgb(5, 185, 240); border-bottom: 1px dotted rgb(5, 185, 240); border-left: 1px dotted rgb(5, 185, 240); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_241210523585I3P3KAHQ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2412105237BSTPP1LT88">​<img id="k_2412105238FB5S1M4DW6" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAtklEQVQoU2NkIAMwkqGHAawpweE+B/NPxpT/jAyVjAwMUv8ZGJ4xMDDOYmDk6pt3VPQzusGM0eZv+TiYPi1hYGT0xbD1//9TjP9Yg+eclHmCLMeYbPWwnIHhfwdOZ/5nbJ97XK6agYHxP0wNY7Ll/YMMjIx2uDT9Z2C48v87u9P885Kv4ZqSLB9cZ2Rk0MCj6RnTPwaXOScUrlNmE1l+IhB6h5h/MUbMOqvwHCX0yIonslMEqRoBBpVSDk0WzcQAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412105239X2L7XZBR63">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412105767ZA64LHHCFM">采用setTarget( target ) 动态修饰</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412105768LRXPE3UBM6">对象设</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412094552R37BAGJVER">计，可采用单列模式</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_241209455381MS2V4UVP">减少资源占用</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(240, 255, 250);" id="k_2412091111VXKDXMVJZS">。</span></div><div id="k_2412044697O44IDWLFTQ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412044698K7971G1ZUJ">​</span></div><div id="k_2411580246VMJ5AQ775X" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(244, 252, 242); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(10, 202, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(66, 3, 255); background-color: rgb(244, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(66, 3, 255);" id="k_2412042471SY84Z2OT7F">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_24120424746QX6IVH1IL">​<img id="k_241204247411L35GEFPV" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA50lEQVQoU52SoW7CUBiFzykUKCQYxJ6BhExtoIchKOTEpkZICAKFmFgmNz23oHBYngHXhschLbTiLGVcAuWyJVx9vnz5z7nEFY9XMPgb8lXzmLwReoRyo7CVX6QSO2TCwguIqoRVVHEf0OD6HNqHAQwIVNKAgC2BcdgsTM0pvyZftTKTDwFPJmwCWcvOVPLjbxLP2bCxCBhumoXZcWH0gvgLwO1BLd2ArO8gwY9ybht3DE+gbOWlIHl1oM/0FpvF2t4RtIwct5u1XIQovcNRP7ovzm3jn+3kBcmEUCd03J7Ncnncf/7WD+XIVQ5LgzgDAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(66, 3, 255); background-color: rgb(244, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(66, 3, 255);" id="k_2412042475535M1UZUS8">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(66, 3, 255); background-color: rgb(244, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(66, 3, 255);" id="k_2412044019CTEOXXR25I">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(66, 3, 255); background-color: rgb(244, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(66, 3, 255); letter-spacing: 1px;" id="k_2412044020T99U28Z53V">API及构造参数说明</span></div><div id="k_2411581087D761GZ1O9J" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2411581088I76X7QHCSK">​</span></div><div id="k_2411581054CFS43BRZ42" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324121427WPIWLFLG81R3"><tbody><tr id="row_0"><td id="k_2412142759JEO29X5BMT" tabindex="0" row="0" col="0" w="1313" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1315px; background-color: rgb(183, 214, 247);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(183, 214, 247);" id="k_2412142759UVNOLDCAXC"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(183, 214, 247);" id="k_2412142760TXYD9RRSHL">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 50, 0); background-color: rgb(183, 214, 247);" id="k_2412270053LBRI7RIPKS">var picker = new $B.ColorPicker($("#picker1"),opts);&nbsp; opts说明</span></p></td></tr><tr id="row_1"><td id="k_2412142768M7VQGT467P" tabindex="0" row="1" col="0" w="190" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 192px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24121427691VF4RN3UTI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142770OAU1AIERHB">​​clickHide</span></p></td><td id="k_2412142771AZIV6IF9FE" tabindex="0" row="1" col="1" w="405" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 407px; background-color: rgba(0, 0, 0, 0);"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142772SFHEYCBF4H"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142773FQHLYMWKKM">​​默认值：false</span></p></td><td id="k_2412142774TBSZA5HYTM" tabindex="0" row="1" col="2" w="714" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 716px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241214277514Z2ZWKO4P"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142776A3HIHA9AMQ">​当页面window其他地方发生点击时候，是否隐藏颜色控件</span></p></td></tr><tr id="row_2"><td id="k_2412142777DBUZCHXXOM" tabindex="0" row="2" col="0" w="190" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 192px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24121427783E9756UPWS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142778VHPPE4S7X6">​​update2Target</span></p></td><td id="k_2412142780WN1O82QAJO" tabindex="0" row="2" col="1" w="405" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 407px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142780MFANJ868W1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142781EIF6DSDBOH">​​默认值：true</span></p></td><td id="k_24121427824BWYXQZH5P" tabindex="0" row="2" col="2" w="714" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 716px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142783KOJIA6K7GI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142784P7G2EYV8XT">​​是否更新目标元素的颜色</span></p></td></tr><tr id="row_3"><td id="k_2412142785KY7NWLVL3H" tabindex="0" row="3" col="0" w="190" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 192px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142786UK1MJ81JVI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142786SBJ9WOHNF5">​​positionFix</span></p></td><td id="k_2412142788JNC2APCIVL" tabindex="0" row="3" col="1" w="405" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 407px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142789EBP1ZM4QJ2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412374015AQ1MM1FX49">默认值：undefined</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412374017J6F9C2CHXC">，可设置如</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412342125Z5SXJTPI2M">{top:2,left:2}</span></p></td><td id="k_2412142791RUZCZI9GMY" tabindex="0" row="3" col="2" w="714" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 716px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142791YQAYTX691O"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241214279252BTO3M6QG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412344259W8IG27SUY5">​附加控件的位置修正值</span></p></td></tr><tr id="row_4"><td id="k_2412142794AFOVGGZQ1S" tabindex="0" row="4" col="0" w="190" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 192px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142794JNGO1MIL3J"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142795767SKEMMZ2">​​buttons</span></p></td><td id="k_2412142796Y8WGEKT2DK" tabindex="0" row="4" col="1" w="405" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 407px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142797Z37BGD4REM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241214279898VR5A21YM">​</span></p></td><td id="k_2412142799Z1AUBKQ5NS" tabindex="0" row="4" col="2" w="714" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 716px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142700RKOIZMZQ5N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142700MDOJHAX5RL">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412365640AMWPYFHHGC">​配置预设置的颜色按钮列表，数组:&nbsp; [&nbsp; ​"#ffffff", "#FF0000"&nbsp;]</span></p></td></tr><tr id="row_5"><td id="k_2412142703VVYEPUA1BS" tabindex="0" row="5" col="0" w="190" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 192px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142703KK3BN3Z7AY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142704XPSUYG9NPO">​​change</span></p></td><td id="k_2412142705IQO6EOIWGU" tabindex="0" row="5" col="1" w="405" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 407px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241214270621BD4DT411"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412142707L15XTLHWLG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412372837Q51PJK1HLM">​​默认值：undefined</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2412373619QX7EWNYCQU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412373621F1XV3L2XSG">change = function(color){}</span></p></td><td id="k_241214270837R3DWFLTG" tabindex="0" row="5" col="2" w="714" h="53" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 55px; width: 716px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412142709NY1CR4Q56A"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241214270985S5W4Q3AM">​​颜色变动监听</span></p></td></tr></tbody></table></div><div id="k_2412044884OF9Z5HLVK8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24120448858UNCJEYHQ9">​</span></div><div id="k_2412400437OIEUER8YOR" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: 313px; text-align: left; background-color: rgb(91, 192, 222); padding-left: 12px; border-top: none; border-right: 1px solid rgb(91, 192, 222); border-bottom: none; border-left: 1px solid rgb(91, 192, 222); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(91, 192, 222);" id="k_241243095132ZE5QKYNH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2412430953E1JXDNG5N4">​<img id="k_2412430954ZZ5MZNS95N" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAuElEQVQoU53SMUpDARCE4W8sBe8kCnZWksJaCES8gZBOW8EijbbWkguElKkV9B4KFjYrT54SEx8kb6uF5Z9lZjd6VHow/kBVNcY+LnCDo1b0E1OcJXlfhe6bAZ4xTLJooKoa4RLXSSapqhPstorHGLT9K86TzKvqAHd4SDJuoDfsdXj7BrGzDdRozXC1DdS5qcvTbxhrnpa9VNVPek84TfLyb3or0PKdbnHYzj/wiNHanTb9jl5v9AVPD1EODm1zeAAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(91, 192, 222);" id="k_2412430956EK918MDCID">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(91, 192, 222);" id="k_2412430952SA957K1PGZ">&nbsp;实例API</span></div><div id="k_2412391448PI6P4R3D7Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190324123942KPMFY7IHH81W"><tbody><tr id="row_0"><td id="k_2412394262FNOL5DOYNL" tabindex="0" row="0" col="0" w="324" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 326px; background-color: rgb(91, 192, 222); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(91, 192, 222); color: rgb(255, 255, 255);" id="k_2412394263ARPT6RIOTN"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(91, 192, 222); text-align: center;" id="k_2412394264FPOQR3A44O">API​</span></p></td><td id="k_24123942655G9J1BJ7SV" tabindex="0" row="0" col="1" w="392" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 394px; background-color: rgb(91, 192, 222); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(91, 192, 222); color: rgb(255, 255, 255);" id="k_2412394266HM87WNZ4BE"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(91, 192, 222); text-align: center;" id="k_2412394266T9V2C6N9DZ">参数​</span></p></td><td id="k_2412394268S61FR3ECMI" tabindex="0" row="0" col="2" w="586" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 588px; background-color: rgb(91, 192, 222); text-align: center; color: rgb(255, 255, 255); font-size: 16px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 16px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(91, 192, 222); color: rgb(255, 255, 255);" id="k_24123942681YSM555WK2"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(91, 192, 222); text-align: center;" id="k_2412394269N5LST8H46V">说明​</span></p></td></tr><tr id="row_1"><td id="k_2412394270HIQ7HIYW2C" tabindex="0" row="1" col="0" w="324" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 326px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394271LHO88KOWRW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412451807EX27CWF2S3">setTarget</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412451809NT1G1OOQ3Y">(jqObj)</span></p></td><td id="k_2412394273LKFFFN1ARC" tabindex="0" row="1" col="1" w="392" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 394px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24123942739CR6S1U6WP"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394274MAC47AMZ2M">​jqObj ：需要颜色修饰的对象</span></p></td><td id="k_2412394275PMPCZZQA3B" tabindex="0" row="1" col="2" w="586" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 588px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394275294RUHRJX2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24123942764YA7ULVO9D">​设置目标对象</span></p></td></tr><tr id="row_2"><td id="k_24123942787IK6HRNJD4" tabindex="0" row="2" col="0" w="324" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 326px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394279VEXQF4ZNFO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394280LZA4GFHSQ2">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412464003775W2VVCQO">​unbindTarget()</span></p></td><td id="k_2412394281TF9UO5X9Y9" tabindex="0" row="2" col="1" w="392" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 394px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394282EHJ6BFYOWB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241239428271CHRSYZ7B">​</span></p></td><td id="k_2412394284HOGJ1TPPPX" tabindex="0" row="2" col="2" w="586" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 588px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24123942854Z7FUGX68Q"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394286C3MW1STRHF">​解除目标对象</span></p></td></tr><tr id="row_3"><td id="k_24123942873L81TARROU" tabindex="0" row="3" col="0" w="324" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 326px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394288T4RB7MJLQ6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394288HJ4ZEXL6ME">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412470216OBAK9ASD5E">​setPosition( pos )</span></p></td><td id="k_2412394289UH2SZBS41K" tabindex="0" row="3" col="1" w="392" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 394px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394290L7KIJYI6P2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24123942914I6WVDZJMV">​​pos:{left:xx,top:xx}</span></p></td><td id="k_24123942934UGJF47BVW" tabindex="0" row="3" col="2" w="586" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 588px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394294KJKYSAI4WA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241239429466M8SHZ32Q">​​设置颜色控件的位置，以修饰目标target的位置为标准，加上pos参数偏移量</span></p></td></tr><tr id="row_4"><td id="k_2412394296I1ZY472N21" tabindex="0" row="4" col="0" w="324" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 326px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394297GALNLXHI6S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412474077DC1L2G5LHN">hide</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24124740788KA458KUK2">&nbsp;(isUnbind)</span></p></td><td id="k_2412394299BM5SRGH6DO" tabindex="0" row="4" col="1" w="392" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 394px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394200YWSVB41GOK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394200WJ2GW5CU5N">​inUnbind : 是否解除目标对象</span></p></td><td id="k_2412394202HBR4VWZRQJ" tabindex="0" row="4" col="2" w="586" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 588px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394203G2Z65X99MY"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394204ACFPSSXHZU">​​隐藏颜色控件</span></p></td></tr><tr id="row_5"><td id="k_24123942062QGL8EZEUR" tabindex="0" row="5" col="0" w="324" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 326px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241239420789T3D35F2G"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412482414ICU7ZIVYCE">show</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24124824177BI14LJN1T">(target, offset)</span></p></td><td id="k_241239420925YCCVBMXI" tabindex="0" row="5" col="1" w="392" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 394px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394210OZR96QL25V"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394211SOK9OM4N1I">​​target：修饰目标</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2412483727YCNDH643MQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412483729PQPTCZ8D2T">offset:偏移量</span></p></td><td id="k_2412394212QXSWJ3W1PH" tabindex="0" row="5" col="2" w="586" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 588px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394213LN76FELIWF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24123942144O3856WLDO">​​显示颜色控件，可传参(target, offset)设置修饰目标，及修正位置偏移量</span></p></td></tr><tr id="row_6"><td id="k_2412394997W9N2ZMJZSU" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 326px;" w="324" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394997DOG7K9T3TV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24124915128EPAABXQYM">isShow</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412491515W8GIS5DSNF">()</span></p></td><td id="k_2412394901KHLGQW95LE" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 394px;" w="392" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394901BTTTYW9QS5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394902GG9JUEWXQJ">​</span></p></td><td id="k_24123949043ZTTEWU95I" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 588px;" w="586" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394904M1KGDIP121"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394905UMCF66QX4Y">​判断颜色控件是否是显示状态</span></p></td></tr><tr id="row_7"><td id="k_24123948487XFQUNHB6P" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 326px;" w="324" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24123948482E7OJUX857"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412500392HEEDJJL91N">setValue</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412500394GQYASJ3XID">&nbsp;(value,invokeChange)</span></p></td><td id="k_2412394852SFWMNE9IQ4" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 394px;" w="392" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241239485397R5EB5HZM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394854FSHKPZ661R">​​value = rgb(0,0,1) / #ffffff</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2412512569SDOXT9O37Y"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412512570UOTHAFZS89">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24125209046C9J8HSWW9">invokeChange</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241252090515DPZL45VH">:是否触发 change回调</span></p></td><td id="k_2412394856V9K2LXKXPM" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 588px;" w="586" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394857GTQ3M62WM1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24123948571OSO5ML86U">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412521287HU6M7HEKWA">​设置颜色，​​invokeChange用于避免循环调用</span></p></td></tr><tr id="row_8"><td id="k_2412394717RNVZB8W2M5" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 326px;" w="324" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394718IB46VGMNUS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24125035392KTNBVCW72">getValue</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412503541RKCCUIJM4A">()</span></p></td><td id="k_2412394721IGXTEJ7SXD" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 394px;" w="392" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2412394722TS75WNTAPS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394722D284711V3D">​</span></p></td><td id="k_24123947257MTSA3AD7Y" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 588px;" w="586" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24123947267VYR2HB4SL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412394726EQQ5S48AG1">​获取当前颜色</span></p></td></tr></tbody></table></div><div id="k_2412391487KTGRXH36RU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412391488U1TO4AN1WA">​</span></div><div id="k_241239144594UU1LQGH6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412391446ZN6DLRH74U">​</span></div><div id="k_2411581135925G1S3W6X" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(244, 252, 242); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(10, 202, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(66, 3, 255); background-color: rgb(244, 252, 242); font-weight: 400; font-style: normal; text-decoration: none solid rgb(66, 3, 255); vertical-align: baseline;" id="k_2412043762NCBRLMJ7O8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_24120437651TZ1P32D4N">​<img id="k_24120437666YY7XVLW3D" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA50lEQVQoU52SoW7CUBiFzykUKCQYxJ6BhExtoIchKOTEpkZICAKFmFgmNz23oHBYngHXhschLbTiLGVcAuWyJVx9vnz5z7nEFY9XMPgb8lXzmLwReoRyo7CVX6QSO2TCwguIqoRVVHEf0OD6HNqHAQwIVNKAgC2BcdgsTM0pvyZftTKTDwFPJmwCWcvOVPLjbxLP2bCxCBhumoXZcWH0gvgLwO1BLd2ArO8gwY9ybht3DE+gbOWlIHl1oM/0FpvF2t4RtIwct5u1XIQovcNRP7ovzm3jn+3kBcmEUCd03J7Ncnncf/7WD+XIVQ5LgzgDAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(66, 3, 255); background-color: rgb(244, 252, 242); font-weight: 400; font-style: normal; text-decoration: none solid rgb(66, 3, 255); vertical-align: baseline;" id="k_24120437671LL8OLIBGH">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(66, 3, 255); background-color: rgb(244, 252, 242); font-weight: 400; font-style: normal; text-decoration: none solid rgb(66, 3, 255); vertical-align: baseline;" id="k_2412043763ILGFZUCTMK">&nbsp;Demo</span></div><div id="k_2411581104ZISEHPFNIZ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24115811053USG45EPBL">​</span></div><div id="k_2411580203Q27YVK2BPZ" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2412542576WG9M7I7R4S">1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2411580204MOKZBDS2Q7">准备需要修饰的对象</span></div><div id="k_241253473336F69N5D2O" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 18px; margin-bottom: 18px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2412534759YX64VISF5V"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; box-sizing: border-box; background: none;"><div style="background: none;">​<span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">input</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"picker1"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">type</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"text"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">style</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"</span><span style="color: rgb(161, 100, 75); background: none;">width:200px"</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">value</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">""</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(83, 83, 83); background: none;">/&gt;</span></div></div></pre></span></div><div id="k_2412534766PWGL8FFL74" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2412534768RW55VS7ETM">2、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2412534769BC4DAPON27">​创建colorpicker实例</span></div><div id="k_2412533187ZMK7HU9RGT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412533188FOGCFC7R6E"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">           var </span><span style="color: rgb(111, 175, 209); background: none;">color</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(41, 111, 169); background: none;">new</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(33, 156, 131); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(33, 156, 131); background: none;">ColorPicker</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"#picker1"</span><span style="color: rgb(167, 167, 167); background: none;">), {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">clickHide:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">change</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">val</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"color "</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">val</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            });</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(111, 175, 209); background: none;">color</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">show</span><span style="color: rgb(167, 167, 167); background: none;">();</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(111, 175, 209); background: none;">color</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">setValue</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"rgb(199, 0, 0)"</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div></div></pre></span></div><div id="k_2412533130CP5HS13EA2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2412533131262DH9C5CE">​</span></div><div id="k_2412533146U81SXHUDWH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24125331473QECKCLE97">​</span></div><div id="k_24115802273ZM3XQCNMY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24115802285GDL87DSLX">​</span></div>
</div>