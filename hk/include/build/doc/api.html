<div style="position:relative">
    <div id="k_2120270310L57ZBJN5E5" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal;" id="k_21202703119MXM4WZDIH">$B.request( args )​&nbsp; &nbsp; &nbsp;统一的请求出入口</span></div><div id="k_212029295087BYM7YPI4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 10px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: bold;" id="k_2120574005M8KRDTM2DE">须知：</span></div><div id="k_21204532982YKP4HH1OD" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 68px; width: 668px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: 1px solid rgb(219, 203, 250); border-right: 1px solid rgb(219, 203, 250); border-bottom: none; border-left: 1px solid rgb(219, 203, 250); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120453298BLMZNBBYMP">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2121000783MSFXE2RHRO">        * req</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2121000784N3ECP5E61N">u</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120595142LS54WDKM6C">est</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120595143Q5A96QCYX3">为框架的ajax统一入口</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120472989L4A9C9MAQ1">&nbsp;</span></div><div id="k_21204736941HS4TF3X7A" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 68px; width: 668px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: none; border-right: 1px solid rgb(219, 203, 250); border-bottom: none; border-left: 1px solid rgb(219, 203, 250); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120503955REJGG8Q7AA">&nbsp;* 所有ajax返回均以 res=</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(225, 230, 250);" id="k_2120503956RD4RYL5GAD"> { code: 0 / 1, message: '返回的提示信息'&nbsp; , data: {&nbsp; }&nbsp; }</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120503957K8UGX6KTZE"> 的格式返回</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21204736959O6WAIX65O">&nbsp;</span></div><div id="k_2120473993UPU1MLOANT" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 68px; width: 668px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: none; border-right: 1px solid rgb(219, 203, 250); border-bottom: none; border-left: 1px solid rgb(219, 203, 250); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21205110599QKDT9N3CU">&nbsp;* code ：0 表示服务器无异常运行并返回结果，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21205110602IX7TS8L3V">1</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21205114504HW84D7SZX">表示服务器出现异常并返回提示 ，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120511451LV1V7SHCTD">99999</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120511452LXLZFH8XNE">表示无权限</span></div><div id="k_2120474264488Q6ZEKQH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 68px; width: 668px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: none; border-right: 1px solid rgb(219, 203, 250); border-bottom: none; border-left: 1px solid rgb(219, 203, 250); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120513604GRXQP8XWIV">&nbsp;* message：</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120513605K6KO4PDZAR">服务器返回的信息提示</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120474266ACZH3OA85F">&nbsp;</span></div><div id="k_2120474553UZBCFGKDWE" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 68px; width: 668px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border-top: none; border-right: 1px solid rgb(219, 203, 250); border-bottom: 1px solid rgb(219, 203, 250); border-left: 1px solid rgb(219, 203, 250); border-image: initial; margin-bottom: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120595147JTG3Q7CM1E">&nbsp;* data ：服务器</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120595148DLTQ4XCQ2X">返回的数据，如tre</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21205921657V3GAE2LPT">e组件、datagrid组件返回的数据就保存到data当中</span></div><div id="k_2120451117NBNPXTTO6M" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 12px; margin-top: 19px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: bold;" id="k_2120451118T1KBGQ9KVT">args参数说明：</span></div><div id="k_2120435165PJTKH7AZOM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 66px; width: 812px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120435166YS8FEXYEFA"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: rgb(0, 137, 255);">    <span style="color: rgb(15, 224, 0);">    args={</span></span></div><div><span style="color: rgb(15, 224, 0);">                timeout:   1000 * 60, //超时</span></div><div><span style="color: rgb(15, 224, 0);">                type:   "POST",       //请求方式</span></div><div><span style="color: rgb(15, 224, 0);">                dataType:   'json',   //请求数据类型</span></div><div><span style="color: rgb(15, 224, 0);">                async:   true,        //是否异步</span></div><div><span style="color: rgb(15, 224, 0);">                preRequest:  fn,     //请求前，回调</span></div><div><span style="color: rgb(15, 224, 0);">                url:  '',             //请求url</span></div><div><span style="color: rgb(15, 224, 0);">                data: {},            //请求参数</span></div><div><span style="color: rgb(15, 224, 0);">                ok:  function(message,data){},    //成功回调，message:返回信息，data: 返回数据</span></div><div><span style="color: rgb(15, 224, 0);">                fail:  function(message,res){},   //失败回调，message:返回信息 ,res : 整个返回体{code:'',message:'',data:{}}</span></div><div><span style="color: rgb(15, 224, 0);">                final:  function(res){}           //无论成功/失败都调用的回调 ，res : 整个返回体{code:'',message:'',data:{}}</span></div><div><span style="color: rgb(15, 224, 0);">        }</span></div></div></pre></span></div><div id="k_2120292929MOTF5FNYLA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2120292930TAXYI84U78">​</span></div><div id="k_2120430551N1I9RBBXFE" class="_section_div_" style="line-height: 40.2286px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;">​$B.bindForm(&nbsp;form, dataObj, onchangeFn ) 表单与实体JSON关联双向绑定</span></div><div id="k_2121575620ICWII5ZVJV" class="_section_div_" style="line-height: 22.05px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 65px; width: 812px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 12px; margin-bottom: 10px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2121575621UI944XMKC4"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;">        </span><span style="color: #6a9955;">/***</span></div><div><span style="color: #6a9955;">         * <span style="color: rgb(15, 224, 0);">实现双向绑定的表单填充</span></span></div><div><span style="color: #6a9955;">         *</span><span style="color: #569cd6;">@param</span><span style="color: #6a9955;"> </span><span style="color: #9cdcfe;">form</span><span style="color: #6a9955;"> <span style="color: rgb(15, 224, 0);">待填充的表单，如table、form</span></span></div><div><span style="color: #6a9955;">         *</span><span style="color: #569cd6;">@param</span><span style="color: #6a9955;"> </span><span style="color: #9cdcfe;">dataObj</span><span style="color: #6a9955;"> <span style="color: rgb(15, 224, 0);">填充数据，JSON对象</span></span></div><div><span style="color: #6a9955;">         *</span><span style="color: #569cd6;">@param</span><span style="color: #6a9955;"> </span><span style="color: #9cdcfe;">onchangeFn</span><span style="color: #6a9955;"> <span style="color: rgb(15, 224, 0);">数据变化通知函数</span></span></div><div><span style="color: #6a9955;">         *</span><span style="color: #569cd6;">@return</span><span style="color: #6a9955;">: <span style="color: rgb(15, 224, 0);">返回具有双向联动能力的数据对象 VM ，您需要通过VM.getJson() 获得需要提交的JSON参数</span></span></div><div><span style="color: #6a9955;">         ***/</span></div></div></pre></span></div><div id="k_21215931428W95YPOPZD" class="_section_div_" style="line-height: 22.05px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; border: none; margin-top: 12px; margin-bottom: 10px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_21215931435NCBT74VXV">​</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-weight: 700;">​</span></div><div id="k_2121575427XUAD8RMZ7L" class="_section_div_" style="line-height: 40.2286px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121575428NI2XE7FXJ5">$B.getHttpHost(&nbsp;ctxPath )&nbsp; &nbsp;获取</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121575429U7G5YFDGRW">window.location里的url地址</span></div><div id="k_2120430501Y2ARCCUABZ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 10px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2120430502HSS9ZQT8UD">​须知：</span></div><div id="k_21210956599QCPGEPRYI" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 68px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2121232778O8XRS61KDQ">* ctxPath ： web应用名称，如果不传则检查是否存在window.ctxPath</span></div><div id="k_2121113116PGBRGBIWQS" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 68px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2121113117IPL4DGPTYP">* ​根据当前浏览器url提取出：http://ip:port/ctxPath，用于返回一个当前应用的根url</span></div><div id="k_2120430526KGBS6NRHZO" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121283855U97Y4KIIPE">$B.htmlEncode( strHtml </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121283856OVCS2BQPSP">)&nbsp; 对html中的[ &lt; 、&gt; 、&lt;script&gt; 、eval ]进行过滤清理</span></div><div id="k_212124165832JBSXJ95I" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2121241659Q2RULBOQ1K">​</span></div><div id="k_2121241624CMFQVM8VL5" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121293274UC28WUAW9F">$B.htmlDecode( strHtm</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121293275ENG4L2I45Q">l ) 将上述html编码恢复至原文</span></div><div id="k_2121241681YI8TP5W1WO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121241682JGWYQ9LLA4">​</span></div><div id="k_2121241702S8OGD9O5X5" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121304177YA52F8TKVQ">$B.getAnglePosit</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121304178UR4Q17NWHI">ionOffset( el ) 获取el元素旋转后的位置</span></div><div id="k_2121241760C5PAT5AJQP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121241761E9HE85M3V9">​</span></div><div id="k_2121294557SWEJSJP91K" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121320911Z32PRVUJV2">$B.getMatrix</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_21213209129UGQ4XLXHK">Angle(&nbsp;matrix )获取旋转角度，matrix = css("transform")</span></div><div id="k_2121294531QFMX4XPK71" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121294532TY4IL2RRG7">​</span></div><div id="k_21212945802U5MPLWTGJ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121321532FAPHM83O96">$B.getHashTable()&nbsp; 获取一个HashTable，请参考HashTable说明</span></div><div id="k_2121294530I6HXZQCK2X" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121294531L8QNRP5O53">​</span></div><div id="k_2121322135XQYJCQQMCJ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121322136ZFVV7MUCJW">​$B.isUrl( url ) 判断是否是Url地址</span></div><div id="k_2121322186MHSE8HVYO2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121322188A2SCX8OPCO">​</span></div><div id="k_21213221286Y4MD83SUK" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121345725KVXW2AFQJK">$B.isNotEm</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121345726I4L7F634NZ">pty( str ) 判断str是否非空，即不是null、undefined、""</span></div><div id="k_2121322173PQHCSUF6N4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121322173NF3LSBQPZL">​</span></div><div id="k_21213425063JAAFVU2RB" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121342507DE6IE7JYBO">$B.getScrollWidth() 获取滚动条宽度​</span></div><div id="k_2121342604GNZ2CTJEC1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121342605WYNZ4BHH3W">​</span></div><div id="k_2121350886EZWRUEUWIB" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121380600H5IVHZ7BKD">$B.ge</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121381631QADMBII87R">tUrlP</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121381632ADRMB4LWGN">arams(&nbsp;strUrl )替换url中的参数以json对象返回，strUrl不传则提取当前浏览器url参数</span></div><div id="k_2121350959W7Z172IGBH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_21213509602RDEU4EQVR">​</span></div><div id="k_2121350993O9FIQFR87E" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121410322GK7BGDT41P">$B.</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121410324C8PWG25MK4">mouseCoords(ev) 获取鼠标当前位置，ev事件e参数</span></div><div id="k_2121350947X35U3ST4EN" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_212135094781CXA2ALOR">​</span></div><div id="k_2121342660O1DGH52XIT" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121342661WC66FP2JWC">​$B.getCharWidth( str , fontSize ) 获取文本长度 ，str：文本，fontSize：字体大小</span></div><div id="k_2121342618YP1UKAYA4Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121342619LQZ8TW2XI8">​</span></div><div id="k_2121342735C6TPBUXIYX" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_212143118595GQ3IPYKV">$</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121431186HPW4CK8TH1">B.getUUID() 获取一个UUID</span></div><div id="k_2121322196ORR6491JSI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121322197KYT91NMX18">​</span></div><div id="k_21214202681LYFNGPRSI" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121420269QHHA3B6AWC">$B.generateDateUUID( format , length) 生成一个长度为length，格式为format的 包含时间的UUID ​</span></div><div id="k_2121420318TQ2FLPPR8X" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_21214203187IAYZH18RQ">​</span></div><div id="k_2121420359CX46JHOAVH" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121420360WSPOLSSFRU">​$B.generateMixed(&nbsp; n ) 生成一个n位随机数</span></div><div id="k_2121420394PDXR5TF18E" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2121420394XK7E6H6O63">​</span></div><div id="k_2121442123ROSV6TEZYV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: normal; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2121453162H2CEQHGY5E">$B.htmlLoad( options )html判断加载</span></div><div id="k_2121420315NES66N55V2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 72px; width: 539px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21214203157TWNBVF5FT"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;">        </span><span style="color: #6a9955;">/*** </span></div><div><span style="color: rgb(15, 224, 0);">         某个html标签加载远程html文件</span></div><div><span style="color: rgb(15, 224, 0);">         options={  </span></div><div><span style="color: rgb(15, 224, 0);">            target:  jquery目标对象,</span></div><div><span style="color: rgb(15, 224, 0);">            url:  '远程地址',</span></div><div><span style="color: rgb(15, 224, 0);">            params:{},  //参数</span></div><div><span style="color: rgb(15, 224, 0);">            preload:  function(){.........} , //加载前处理事件</span></div><div><span style="color: rgb(15, 224, 0);">            onLoaded:  function(result){.........}  //加载后处理事件</span></div><div><span style="color: rgb(15, 224, 0);">        } </span></div><div><span style="color: #6a9955;">        ***/</span></div></div></pre></span></div><div id="k_2121420340N6AX9RATAG" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2121420341W9WL7A76KV">​</span></div><div id="k_2120430660TEWNBSHU3S" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2120430661URKHJ5LH34">$B.parseForm(&nbsp;form, entityJson )​&nbsp;将form表单转为json对象</span></div><div id="k_21214625214BZU75WJFQ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 74px; width: 535px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 18px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21214625238KR36TDPEG"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;">        </span><span style="color: #6a9955;">/**</span></div><div><span style="color: #6a9955;">         *<span style="color: rgb(15, 224, 0);">将form表单转为json对象</span></span></div><div><span style="color: #6a9955;">         *</span><span style="color: #569cd6;">@param</span><span style="color: #6a9955;"> </span><span style="color: #9cdcfe;">form</span><span style="color: #6a9955;"> <span style="color: rgb(15, 224, 0);">表单的容器如div、table、form</span></span></div><div><span style="color: #6a9955;">         *</span><span style="color: #569cd6;">@param</span><span style="color: #6a9955;"> </span><span style="color: #9cdcfe;">entityJson</span><span style="color: #6a9955;"> <span style="color: rgb(15, 224, 0);">待form合并的实体json</span></span></div><div><span style="color: #6a9955;">         ***/</span></div></div></pre></span></div><div id="k_2121462569NHMW8FUI5L" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21214625719NVYW6WR38">​</span></div><div id="k_2121503471LWMLPYFEVT" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_21220549246ADVQGHVX9">$</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122055880H2G1I1YPLH">B.f</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_21220558821LP44PNRMO">illV</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122055479CWWD8ND564">iew</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122055483GHKGMX9NKQ">( wrap, dat</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122080633OLGLVKAG1M">aObj ) 将dataO</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122080634LSFLWAX3ZP">bj填充到wrap html容器中，要求字段与标签id一致，常用于详情填充</span></div><div id="k_21215035136XO4PIMBYD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2121503514E8U7QWD1Q9">​</span></div><div id="k_2121503553AGKDL5J6Q6" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122092118JODORTJEBJ">$</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122092120N3HPHT4Z49">B.resetForm</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122072278FK99PMLQDH">( form, defData ) 根据defData数据重置form表单</span></div><div id="k_2121503588NC9Z9NWZGJ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2121503589UBM9RJEAZM">​</span></div><div id="k_2122080304KNZLHV73PO" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122092765HR8ZNQ3TE3">$B.simpalSelect( options )创建select原生标签控件</span></div><div id="k_2122080362TVJBVJ5VUE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 89px; width: 566px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 19px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122080363CCFSV7612Q"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div><span style="color: #d4d4d4;">       </span><span style="color: #6a9955;">/**</span></div><div><span style="color: #6a9955;">       <span style="color: rgb(15, 224, 0);">  简单下拉列表（原生的select）</span></span></div><div><span style="color: rgb(15, 224, 0);">         options={</span></div><div><span style="color: rgb(15, 224, 0);">             target:  id/对象,</span></div><div><span style="color: rgb(15, 224, 0);">             data:  option数据项,</span></div><div><span style="color: rgb(15, 224, 0);">             idField:  'option的value字段',&nbsp;&nbsp;</span></div><div><span style="color: rgb(15, 224, 0);">             textField:  'option显示的字段',&nbsp;&nbsp;&nbsp;</span></div><div><span style="color: rgb(15, 224, 0);">             defaultVal:  '选择的项目的值',&nbsp;&nbsp;  &nbsp;&nbsp;</span></div><div><span style="color: rgb(15, 224, 0);">             onchange:  fn(选择的option) //选择触发函数事件</span></div><div><span style="color: rgb(15, 224, 0);">         }</span></div><div><span style="color: #6a9955;">        *****/</span></div></div></pre></span></div><div id="k_21220804964OYFQ1SAZL" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122080497IQ3M1HROY4">​</span></div><div id="k_23152418732GTELP1Q8D" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315241874DECP6OYHBY">​</span></div><div id="k_2315241850UXVHTY8VZY" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_23152418507WJ4PYZ1NQ">$B.writeCookie( name, value, expiredays ) ​ 写cookie</span></div><div id="k_2315241806Q8AVXDDV9C" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315241806SML6VUD6I1">​</span></div><div id="k_2315250020ZFEPPA1I9H" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2315254223AQS2WWLZ5H">$B.getCookie( name )&nbsp; 获取cookie</span></div><div id="k_21220933993G4RJLQY2R" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span></div><div id="k_21222714591I1HUDSWIV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 242, 249); padding-left: 0px; border-top: none; border-right: none; border-bottom: none; border-left: 2px dotted rgb(76, 8, 250); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 28px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-weight: 400; font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122271460UEJS49HJVW">HashTable说明​</span><span id="k_2122271460MRHFCEOA1I" style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; font-weight: 400; color: rgb(160, 32, 240); background-color: rgb(250, 242, 249); font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;">​</span><span style="background-color: rgb(250, 242, 249); font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; font-weight: 400; color: rgb(160, 32, 240); font-style: normal; text-decoration: none solid rgb(160, 32, 240); vertical-align: baseline;" id="k_2122271461GIH7N1RV3H">​</span></div><div id="k_2122274114OLLR8WQJIY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 62px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;"><span style="background-color: rgba(0, 0, 0, 0); font-family: &quot;Microsoft Yahei&quot;;" id="k_2122274114MJ86YFUV76">​您可以通过 var hash = new $B.hashTable() 或者 var hash = $B.getHashTable() 得到 HashTable实例</span></div><div id="k_2122271426ACJSA6IMMI" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 64px; width: 885px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 20px;" contenteditable="false"><table style="border-collapse: collapse; width: auto; height: auto; " id="20190321222842JL2DYFTC5RHQ"><tbody><tr id="row_0"><td id="k_212228424424C71372Z3" tabindex="0" row="0" col="0" w="882" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 884px; background-color: rgb(142, 219, 152);" colspan="3" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(142, 219, 152);" id="k_212228424546LLJQS2MR"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(142, 219, 152);" id="k_21222842451RM7B64O1Z">​采用{}Object对象模拟实现的HashTable</span></p></td></tr><tr id="row_1"><td id="k_2122284256UPKW27J9W4" tabindex="0" row="1" col="0" w="292" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0); text-align: center;" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgba(0, 0, 0, 0);" id="k_2122284256M6X8A59HZ1"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); text-align: center;" id="k_2122284257YJ218Y8NF5">​实例API</span></p></td><td id="k_21222842595S4ZD3ILAL" tabindex="0" row="1" col="1" w="254" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0); text-align: center;" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgba(0, 0, 0, 0);" id="k_2122284260XZ7TK8348S"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); text-align: center;" id="k_2122284261AT4YQCXX9K">​参数/返回值</span></p></td><td id="k_2122284264IHFPJONTSR" tabindex="0" row="1" col="2" w="332" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0); text-align: center;" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgba(0, 0, 0, 0);" id="k_2122284265EMGPU529EH"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); text-align: center;" id="k_2122284266DGF2PVCM16">​说明</span></p></td></tr><tr id="row_2"><td id="k_21222842675C9EYAXDCH" tabindex="0" row="2" col="0" w="292" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122284268YOU39CRIYB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122284269E5NJSO7TH5">​add(&nbsp;key, value )</span></p></td><td id="k_2122284270W4KD6XUXPT" tabindex="0" row="2" col="1" w="254" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21222842718L9SZ9F8KD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21222842718ALJR8Y8LD">​</span></p></td><td id="k_2122284272SX8T4GM3NZ" tabindex="0" row="2" col="2" w="332" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122284273Z2G9DDEWFF"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122284274JUKPOKK1SH">​增加一个key-value记录</span></p></td></tr><tr id="row_3"><td id="k_21222842757PDDBA6L2L" tabindex="0" row="3" col="0" w="292" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122284276NU9DRGOKDR"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21222842773VP88B6AHQ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21223329754CC7GORZOA">getValue( key )</span></p></td><td id="k_2122284278V2EV5XEMDQ" tabindex="0" row="3" col="1" w="254" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122284279NOK54AYO8D"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122284279L3UDRJHS52">​</span></p></td><td id="k_2122284281JSKD4OEUNZ" tabindex="0" row="3" col="2" w="332" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21222842817IBUHJKFL5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21222842824DADUEINMB">​根据key获取value值</span></p></td></tr><tr id="row_4"><td id="k_2122284283HUVJTRLAW2" tabindex="0" row="4" col="0" w="292" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122284284QNUZIUJ5JE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21222842859M1W3JD2N3">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122335205YT4YEUCHJE">remove( key )</span></p></td><td id="k_2122284286DKXM4966J7" tabindex="0" row="4" col="1" w="254" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122284287VD6FNIB7EV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122284287F7DVGWTGBQ">​</span></p></td><td id="k_2122284289VQLVRGERU8" tabindex="0" row="4" col="2" w="332" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21222842895VVW51OO94"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122284290C36NG35QBJ">​根据key删除一个记录</span></p></td></tr><tr id="row_5"><td id="k_2122284291XBNUQOCCU3" tabindex="0" row="5" col="0" w="292" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122284292DCH598DSOV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122284293I7PEDLZEZY">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122342053OCB7OMDVEM">containsKey( key )</span></p></td><td id="k_2122284294VEKWKASVTH" tabindex="0" row="5" col="1" w="254" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122284296B25IECMOGE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122354709ZHB1UBZMGX">返回 true / false</span></p></td><td id="k_2122284298QQJO5NDF8M" tabindex="0" row="5" col="2" w="332" h="28" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122284298VYDR4WE8WU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122284299WEGMTHHP15">​是否包含key</span></p></td></tr><tr id="row_6"><td id="k_2122344501F2G3UGLNZF" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" w="292" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21223445024E4NL3WYWL"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122344503TQTM4MK8IY">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122345527QDIBPYYKMS">containsValue( value )</span></p></td><td id="k_2122344505RBN2PQVLJ9" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" w="254" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21223445069RAK19JNEJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122344507VIQCFZ4246">​​返回 true / false</span></p></td><td id="k_2122344509QAKC99MB1H" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" w="332" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122344509JPL5M6MPME"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122344510G1LAKHSVES">​是否包含value值</span></p></td></tr><tr id="row_7"><td id="k_2122351257C17PNGNU1Y" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" w="292" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122351257YYVAW925IO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122351258K8FRWVA97C">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122352812O13K8MLVFE">getValues()</span></p></td><td id="k_2122351260QE4JM89HVB" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" w="254" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122351261MTN7FHVXVG"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122351262CJ9TA5G2DO">​返回values 数组集合</span></p></td><td id="k_2122351264SDC2PCZFDQ" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" w="332" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122351265DWE9KUF6XQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21223512657C17JP5Q1S">​获取所有值 数组形式返回</span></p></td></tr><tr id="row_8"><td id="k_2122362204P7ZZP6DUMO" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" w="292" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21223622057K3C8H7P92"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122362206NNEAW1UL59">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122362757XUT8TWGOV8">getKeys()</span></p></td><td id="k_2122362208ZJ1UCAARD3" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" w="254" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21223622091NEUA24LFV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21223622101UTMN8MRTG">​返回keys 数组集合</span></p></td><td id="k_2122362212GH14BHG6AQ" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" w="332" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122362214QZRIODKIS8"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21223622151EHUF7R4RP">​获取所有key 数组形式返回</span></p></td></tr><tr id="row_9"><td id="k_2122370707LAUXXZFUEY" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" w="292" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122370708NE4Q9QLQNK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122370709H1YSNMS2MS">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_212237176541N4XOQZ5Q">getSize()</span></p></td><td id="k_21223707123U7H93FJ1W" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" w="254" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122370713YDCFY5EOIU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122375768KKVJU5CQP9">​</span></p></td><td id="k_21223707162OEH1DGEAJ" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" w="332" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122370716YDVCSSNAE4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122370717F32145JRJ9">​​返回当前hashTable大小</span></p></td></tr><tr id="row_10"><td id="k_21223739702GQIT7W449" tabindex="0" row="10" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" w="292" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_212237397125DDIAXDSS"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122373972FOC8JMWUZZ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122374977KBQKEPQFHN">clear()</span></p></td><td id="k_2122373974QEDXF43JDN" tabindex="0" row="10" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" w="254" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122373975P1IRYQV4TQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_212237397627ZK3QBAPR">​</span></p></td><td id="k_2122373979DHXQW9YUEG" tabindex="0" row="10" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" w="332" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122373980VNWSXW1E1W"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122373981H5H7MBTJ4C">​清空hashTable</span></p></td></tr><tr id="row_11"><td id="k_2122401643TUUOGRL6H6" tabindex="0" row="11" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" w="292" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122401644QPJWO2KGBQ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122401645VNWQZAET12">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122401978WZU3LT8J6D">destroy()</span></p></td><td id="k_2122401646L6V383S24O" tabindex="0" row="11" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" w="254" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122401647Q6RXV62659"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122401649F92C83KSXW">​</span></p></td><td id="k_2122401651KQ2O1QGI3D" tabindex="0" row="11" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" w="332" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122401652WHQSD6JUD3"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21224016533HIZNVFR7R">​销毁</span></p></td></tr><tr id="row_12"><td id="k_2122401400APCL9XWHPH" tabindex="0" row="12" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" w="292" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21224014018Z3U9YJDW6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21224014024QCNQZWLUN">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122403669RK9PH326DB">each(fn)</span></p></td><td id="k_21224014056K1ZFE8PRH" tabindex="0" row="12" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" w="254" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122401406GUHSABISN2"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122401407IYJXEWZ89D">​fn = function( key , value ){}</span></p></td><td id="k_2122401410IPPMZTVJ1U" tabindex="0" row="12" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" w="332" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21224014115P14ZYUBL9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21224014123M4LTX29JI">​遍历hashtable</span></p></td></tr><tr id="row_13"><td id="k_2122411842WAXZB2EJ1A" tabindex="0" row="13" col="0" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 294px; background-color: rgba(0, 0, 0, 0);" w="292" h="28" index="0"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122411842XC7IVTUBG1"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_21224118438L4RY2NVDG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122413989XXJTR59BBQ">getJson()</span></p></td><td id="k_2122411845MENA2KH3HD" tabindex="0" row="13" col="1" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 256px; background-color: rgba(0, 0, 0, 0);" w="254" h="28" index="1"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_21224118469C7SD9G9YN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122411847K67Z4O1BJH">​</span></p></td><td id="k_2122411849Q6FXNNYYJ4" tabindex="0" row="13" col="2" style="border-width: 1px; border-style: solid; border-color: rgb(127, 208, 255); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 334px; background-color: rgba(0, 0, 0, 0);" w="332" h="28" index="2"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2122411850WSU7DKDIUA"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2122411852TNBVQ3N85P">​将hashTable内容以json数据形式返回</span></p></td></tr></tbody></table></div><div id="k_2122274191QJ8EHY7IVM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="background-color: rgba(0, 0, 0, 0); font-family: &quot;Microsoft Yahei&quot;;" id="k_21222741928Y5IXGVVVH">​</span></div><div id="k_2122274160J2AWDZ4JWV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="background-color: rgba(0, 0, 0, 0); font-family: &quot;Microsoft Yahei&quot;;" id="k_2122274161UPLXF1D8CM">​</span></div><div id="k_212227146788EV82THLX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="background-color: rgba(0, 0, 0, 0); font-family: &quot;Microsoft Yahei&quot;;" id="k_21222714681Z16PHV9FT">​</span></div><div id="k_2315223057LZWERVM7BE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2315223057C6OQOY2UC7">​</span></div>
</div>