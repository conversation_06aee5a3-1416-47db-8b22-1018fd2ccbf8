<div style="position:relative">

<div id="k_2418410702JMPGFD9A4P" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(175, 168, 255); padding-left: 20px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(240, 0, 228); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(175, 168, 255);" id="k_2418432505S5UFMNZE5A">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2418441461X1RB61NR6P">​<img id="k_24184414612PER5RGI8R" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAxklEQVQ4T7WRMQ5BURBFzzQK0SnsR6JQqkVBIpGwASvQCw2FQqkgsRVLUIj4Gh3FlZ/8L+N5RMHr5mVO5swdwz1JY6ANlNx3AnTNbO17LS8kTYAOcAMGZraU1AJGwDCtX8AMagJboJo3fgQdNAN2fkIGpvoFoOgmHk3SAajEdpI0zfSvoX4K7mM7OJMNUAv1o+AX+qMXUFIdWAT6Z6BnZqs8sBj49gRpDj8BT0DZHxe4AP3w6E8TA+ChEkv6b2ADmEf0vVxyBx3noceyqgQuAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(175, 168, 255);" id="k_241844146298YUF97UTP">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(175, 168, 255);" id="k_2418432508Q188BVC56L">​​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(175, 168, 255);" id="k_2418432510NBPKUIOSQP">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(175, 168, 255);" id="k_24184359744GQUODPNGM">&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(175, 168, 255);" id="k_2418435975ETIAH47XU6">构造参数及API</span></div><div id="k_2418414418FQ3DVR65OF" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418414419EHVS1PKQFO">​</span></div><div id="k_2418443899LQYFBGOXWR" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 42px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="201903241914388U6BKYJ6I9EV"><tbody><tr id="row_0"><td id="k_2419143876KKI59HQU25" tabindex="0" row="0" col="0" w="1343" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1345px; background-color: rgb(252, 255, 255);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(252, 255, 255);" id="k_2419143877P2UFALAF31"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255);" id="k_2419143878BK8W3FD7SK">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255);" id="k_2419151829LN3VBYZWEP">​var pg = new $B.Pagination($("#pgdiv"), opts); opts 参数</span></p></td></tr><tr id="row_1"><td id="k_2419143893DBRJ5BULPO" tabindex="0" row="1" col="0" w="278" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 280px; background-color: rgb(252, 255, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_24191438949QVI1234N9"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_241914389569IHZJG9C1">参数名​</span></p></td><td id="k_2419143898MZSJF1EAT1" tabindex="0" row="1" col="1" w="409" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 411px; background-color: rgb(252, 255, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_2419143899AA79OUDJ99"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_24191438018NK1VRA74H">参数值​</span></p></td><td id="k_241914380412S8VGLA5E" tabindex="0" row="1" col="2" w="652" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 654px; background-color: rgb(252, 255, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_2419143805GD6V8PQBYB"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_2419143806SAYRD32RFK">说明​</span></p></td></tr><tr id="row_2"><td id="k_2419143808U4PU56GDBY" tabindex="0" row="2" col="0" w="278" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 280px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419143809ANYVPZNSNK"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143810OIQZ1HMPK2">​​height</span></p></td><td id="k_241914381191QY2KKKIY" tabindex="0" row="2" col="1" w="409" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 411px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419143812L64PHKYVES"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143813NJY18PYS9K">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419245787ADMVD32J8N">​默认值：25</span></p></td><td id="k_2419143814K6Y74WACW8" tabindex="0" row="2" col="2" w="652" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 654px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419143815UUKPGRZOXI"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143816NK8ECGT44G">​​分页栏高度</span></p></td></tr><tr id="row_3"><td id="k_2419143819UVSSRFGB77" tabindex="0" row="3" col="0" w="278" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 280px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419143820WTHHR29493"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143821RIRUDMU3U4">​​total</span></p></td><td id="k_2419143824RACVU22ZET" tabindex="0" row="3" col="1" w="409" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 411px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419143824BWMJY8W9DJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143825A2H9ANNX45">​</span></p></td><td id="k_24191438278YDCFDJGPA" tabindex="0" row="3" col="2" w="652" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 654px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241914382836IORE516S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24191438294VSTRQKOZS">​​总记录数</span></p></td></tr><tr id="row_4"><td id="k_241914383187YJYFPMU4" tabindex="0" row="4" col="0" w="278" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 280px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241914383248GNXY5ZIC"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143833VA5WKKTHVL">​​pageSize</span></p></td><td id="k_24191438345JN1U6RQLV" tabindex="0" row="4" col="1" w="409" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 411px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24191438359BYBZDSUM5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143835EMXNKQM6NS">​​默认值：15</span></p></td><td id="k_24191438374LDYRXJ17E" tabindex="0" row="4" col="2" w="652" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 654px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419143837T9Q71D56CX"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143838PYRQFB5ZAY">​​pageSize</span></p></td></tr><tr id="row_5"><td id="k_2419143840CI9DFUHGE9" tabindex="0" row="5" col="0" w="278" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 280px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419143841N24IMAV27N"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143841ONN9BGBJZ7">​​buttons</span></p></td><td id="k_24191438421KQ5VS3HSL" tabindex="0" row="5" col="1" w="409" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 411px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419143843CINNVOENIB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24191438448OQHKYYIU4">默认值：5</span></p></td><td id="k_2419143846H9P2GXL9O7" tabindex="0" row="5" col="2" w="652" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 654px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419143847YTF7W5S3EO"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419143847XZY4NGAA73">​​页码按钮数量</span></p></td></tr><tr id="row_6"><td id="k_2419254181LTNUHXBKVZ" tabindex="0" row="6" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 280px;" w="278" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24192541826DSS4HE937"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419254183MYW1YMUNHS">​​page</span></p></td><td id="k_2419254186IVCDWJCV5O" tabindex="0" row="6" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 411px;" w="409" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419254187J25THYGRRW"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24192541882BMH9I97GX">​​默认值：1</span></p></td><td id="k_2419254190NHN7AS9L7E" tabindex="0" row="6" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 654px;" w="652" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419254190N9RIN1J6KH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24192541912HIKMPBAOZ">​​当前页码</span></p></td></tr><tr id="row_7"><td id="k_24192540559HFHL15A6K" tabindex="0" row="7" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 280px;" w="278" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419254056HN7TTOQPPN"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419254057HJ7P4WQBU1">​​pageList</span></p></td><td id="k_2419254059DW2PGLIGYI" tabindex="0" row="7" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 411px;" w="409" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24192540603BF46OSW9C"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419254060OD1HJD2W3M">​​默认值：[15, 25, 30, 35, 40, 55]</span></p></td><td id="k_2419254062ALQJQXZ3QU" tabindex="0" row="7" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 654px;" w="652" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419254063BFWDXJMJ9F"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419254063J54TY93BPX">​​页大小列表</span></p></td></tr><tr id="row_8"><td id="k_2419270325FOP2SGU1KE" tabindex="0" row="8" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 280px;" w="278" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419270326L7JTUUOJ9S"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419270327J2KGISZLJZ">​​position</span></p></td><td id="k_2419270330TE39WJJAAG" tabindex="0" row="8" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 411px;" w="409" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419270331LBAT6PKBA4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24192703325WEOKH2FJ1">​​默认值：center</span></p></td><td id="k_24192703356F3GTPCCMI" tabindex="0" row="8" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 654px;" w="652" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419270336EQLSENPKCH"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419270337LPN68CE34J">​​分页栏位置，取值[right / left / center]</span></p></td></tr><tr id="row_9"><td id="k_2419270418R3DOKT736H" tabindex="0" row="9" col="0" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 280px;" w="278" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24192704182KK5BDKTOB"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419270419K6H2KGS7HJ">​​summary</span></p></td><td id="k_2419270424RTS6OUOMT7" tabindex="0" row="9" col="1" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 411px;" w="409" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24192704245ZH94N4BG9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419270425SXQZBERXAP">​​默认值：true</span></p></td><td id="k_24192704276D3UJ6BSBH" tabindex="0" row="9" col="2" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 654px;" w="652" h="28"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419270427WIG55Q1NB5"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419270428VCB844R1Q7">​​是否需要页大小，页总数汇总显示</span></p></td></tr></tbody></table></div><div id="k_2418443957UC6ZIYYMFM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418443958TAOHKFF7U1">​</span></div><div id="k_2419281403ZUCY6THVUE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><table style="border-collapse: collapse; width: auto; height: auto; " id="201903241928199ADZ5SQIL9VY"><tbody><tr id="row_0"><td id="k_2419281942Q6XPN5SEOC" tabindex="0" row="0" col="0" w="1295" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 1297px; background-color: rgb(252, 255, 255);" colspan="3"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(252, 255, 255);" id="k_2419281943HMV6WNZBIA"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255);" id="k_2419281944IPY4UUDIHA">​实例API</span></p></td></tr><tr id="row_1"><td id="k_2419281954PL93TERHHO" tabindex="0" row="1" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px; background-color: rgb(252, 255, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_2419281955XQE5HEO77A"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_2419281956QGMWBN8XPB">​API名称</span></p></td><td id="k_2419281958YY32UR29IS" tabindex="0" row="1" col="1" w="395" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 397px; background-color: rgb(252, 255, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_2419281959T2MPCSZGAC"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_2419281960CX8V5OKPJY">​参数</span></p></td><td id="k_241928196263GT1HQP1F" tabindex="0" row="1" col="2" w="626" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 628px; background-color: rgb(252, 255, 255); text-align: center;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background-color: rgb(252, 255, 255);" id="k_2419281964K4VXJBC55H"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 255, 255); text-align: center;" id="k_24192819653R22C3OP7H">​说明</span></p></td></tr><tr id="row_2"><td id="k_241928197199U66KIV9C" tabindex="0" row="2" col="0" w="270" h="128" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419281973G88ZOPWVFU"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419293259D85S5H4DND">update</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419293261WVID5E9CQT">(args)</span></p></td><td id="k_2419281976WOZ9LMPPIV" tabindex="0" row="2" col="1" w="395" h="128" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 397px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_241928197716XDEL8Y3E"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241928197738NXHRX6FZ">​​args = {</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2419295717TYMTJSFPY9"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419295718DMEW4BCR88">&nbsp; &nbsp; total: 总记录数,</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2419295038K6QODVJVWD"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241929503984CKLKTWO3">&nbsp; &nbsp; startpg: 开始页码,</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2419295222FZYDWANASJ"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419295223LLR3RA86ZB">&nbsp; &nbsp; page: 当前页码</span></p><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;" id="k_2419295490ISGHXI1CG4"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419295491B36VL7AN91">}</span></p></td><td id="k_2419281979LZ5ZC61PFM" tabindex="0" row="2" col="2" w="626" h="128" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 628px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419281980PW5XRVONPV"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24192819815HX9I3O9ZT">​​更新分页工具栏信息</span></p></td></tr><tr id="row_3"><td id="k_2419281982P3AZ4BVFKW" tabindex="0" row="3" col="0" w="270" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 272px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419281983HK3PKWEVKE"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419303891SQ8LUWUVY2">getCurPage</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419303893XWAL5YXSEH">()</span></p></td><td id="k_2419281985PEQSYVJEWD" tabindex="0" row="3" col="1" w="395" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 397px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_24192819865KEFC9OYJM"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_241928198739XTGVQF9T">​</span></p></td><td id="k_24192819884EKL3MWNDQ" tabindex="0" row="3" col="2" w="626" h="28" style="border-width: 1px; border-style: dashed; border-color: rgb(175, 201, 222); padding: 2px 3px; line-height: 21px; box-sizing: border-box; height: 30px; width: 628px;"><p style="margin-block-start: 0px; margin-block-end: 0px; margin-inline-start: 0px; margin-inline-end: 0px; line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);" id="k_2419281989XI67PH8RJ6"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419281990EAIXDM79XW">​​获取当前页码</span></p></td></tr></tbody></table></div><div id="k_2418443906MI44EEF5V3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_24184439071L1L3884G9">​</span></div><div id="k_2419281470THLDV3GBRK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419281471HRT2833NS1">​</span></div><div id="k_24184144141PESSDP5XD" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(175, 168, 255); padding-left: 20px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(240, 0, 228); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(175, 168, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2418445462DXQ7K27HAW">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2418445465YDXB8XDYZU">​<img id="k_2418445466FHFANHWG8K" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAxklEQVQ4T7WRMQ5BURBFzzQK0SnsR6JQqkVBIpGwASvQCw2FQqkgsRVLUIj4Gh3FlZ/8L+N5RMHr5mVO5swdwz1JY6ANlNx3AnTNbO17LS8kTYAOcAMGZraU1AJGwDCtX8AMagJboJo3fgQdNAN2fkIGpvoFoOgmHk3SAajEdpI0zfSvoX4K7mM7OJMNUAv1o+AX+qMXUFIdWAT6Z6BnZqs8sBj49gRpDj8BT0DZHxe4AP3w6E8TA+ChEkv6b2ADmEf0vVxyBx3noceyqgQuAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(175, 168, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2418445467MQ2OUCXIA5">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(175, 168, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2418445766TC8XPQRBPS">&nbsp; </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(175, 168, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_2418445767MT4BFT8SD2">Demo</span></div><div id="k_2418415201N1M6NFQH9L" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418415201GKB5V1OJ45">​</span></div><div id="k_2419311250YOSTNH6KEF" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2419312227FBBHOWG3E7">​1、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2419311251E8KIREJ9UF">定义容器div</span></div><div id="k_24193232598SUDNH6LLX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); margin-top: 20px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419323261Z1QKEMA1NP"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="color: rgb(167, 167, 167); background: none;">    </span><span style="color: rgb(83, 83, 83); background: none;">&lt;</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">id</span><span style="color: rgb(167, 167, 167); background: none;">=</span><span style="color: rgb(161, 100, 75); background: none;">"pgdiv"</span><span style="color: rgb(83, 83, 83); background: none;">&gt;&lt;/</span><span style="color: rgb(41, 111, 169); background: none;">div</span><span style="color: rgb(83, 83, 83); background: none;">&gt;</span></div></pre></span></div><div id="k_2419312235TW34UK2FV8" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2419312328VOMV7XTLWY">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2419312236KC9K4WHNZH">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_24193122375VT3HA9N5A">​&nbsp;&nbsp;&nbsp;&nbsp;</span></div><div id="k_24193123396L6UBFLDMZ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 20px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2419312340IQYD65S8U6">2、创建实例</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2419312341FVVFXGYD7N">​</span></div><div id="k_241841525582K4JTTNCA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418415256P2EQPDY6W4">​&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></div><div id="k_2419331535CFXF83D5PL" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 45px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2419331535P8PAUK54UT"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(167, 167, 167);">        </span><span style="background: none; color: rgb(41, 111, 169);">function</span><span style="background: none; color: rgb(167, 167, 167);"> </span><span style="background: none; color: rgb(175, 175, 125);">pageLoaded</span><span style="background: none; color: rgb(167, 167, 167);">() {</span></div><div style="color: rgb(167, 167, 167); background: none; font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;"><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">msg</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"#msg"</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(41, 111, 169); background: none;">var</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">opts</span><span style="color: rgb(167, 167, 167); background: none;"> = {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">total:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">1980</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//总数量</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">pageSize:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">20</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//页大小</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">buttons:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">10</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(61, 108, 40); background: none;">//页按钮数量</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">position:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(161, 100, 75); background: none;">'center'</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(111, 175, 209); background: none;">summary:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">true</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                </span><span style="color: rgb(175, 175, 125); background: none;">onClick</span><span style="color: rgb(111, 175, 209); background: none;">:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(41, 111, 169); background: none;">function</span><span style="color: rgb(167, 167, 167); background: none;"> (</span><span style="color: rgb(111, 175, 209); background: none;">p</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(111, 175, 209); background: none;">ps</span><span style="color: rgb(167, 167, 167); background: none;">, </span><span style="color: rgb(111, 175, 209); background: none;">startpg</span><span style="color: rgb(167, 167, 167); background: none;">) {</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(33, 156, 131); background: none;">console</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">log</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"您点击了第"</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">p</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">"页！当前页大小"</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">ps</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">";开始页="</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">startpg</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">msg</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">html</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"您点击了第"</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">p</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">"页！当前页大小"</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">ps</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(161, 100, 75); background: none;">";开始页="</span><span style="color: rgb(167, 167, 167); background: none;"> + </span><span style="color: rgb(111, 175, 209); background: none;">startpg</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    </span><span style="color: rgb(111, 175, 209); background: none;">pg</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(175, 175, 125); background: none;">update</span><span style="color: rgb(167, 167, 167); background: none;">({</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">total:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(136, 161, 123); background: none;">1010</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">startpg:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">startpg</span><span style="color: rgb(167, 167, 167); background: none;">,</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                        </span><span style="color: rgb(111, 175, 209); background: none;">page:</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(111, 175, 209); background: none;">p</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                    });</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">                }</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            };</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">            </span><span style="color: rgb(111, 175, 209); background: none;">pg</span><span style="color: rgb(167, 167, 167); background: none;"> = </span><span style="color: rgb(41, 111, 169); background: none;">new</span><span style="color: rgb(167, 167, 167); background: none;"> </span><span style="color: rgb(33, 156, 131); background: none;">$B</span><span style="color: rgb(167, 167, 167); background: none;">.</span><span style="color: rgb(33, 156, 131); background: none;">Pagination</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(175, 175, 125); background: none;">$</span><span style="color: rgb(167, 167, 167); background: none;">(</span><span style="color: rgb(161, 100, 75); background: none;">"#pgdiv"</span><span style="color: rgb(167, 167, 167); background: none;">), </span><span style="color: rgb(111, 175, 209); background: none;">opts</span><span style="color: rgb(167, 167, 167); background: none;">);</span></div><div style="background: none;"><span style="color: rgb(167, 167, 167); background: none;">        }</span></div></div></pre></span></div><div id="k_2418415296DT1IO1D62L" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2418415296AYMXEE4KKB">​</span></div>
</div>