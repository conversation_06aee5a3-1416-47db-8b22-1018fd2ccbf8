/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(e,i){"function"==typeof define&&define.amd?define(["$B"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(d,c){var t;function p(){return t||(t=$(d.document.body).css("position","relative")),t}var u={show:!1,fixed:!1,fitWidth:!1,initValue:void 0,shadow:!0,fmt:"yyyy-MM-dd hh:mm:ss",mutil:!1,clickHide:!1,readonly:!0,isSingel:!1,range:{min:void 0,max:void 0},onChange:void 0};function _(t,e){c.extend(this,_),t.addClass("k_calendar_input").attr("autocomplete","off"),this.opts=$.extend({},u,e),this.currentDate=new Date,this.isMonthStyle="yyyy-MM"===this.opts.fmt,this.isDayStyle="yyyy-MM-dd"===this.opts.fmt,this.isSecondStyle="yyyy-MM-dd hh:mm:ss"===this.opts.fmt,this.isTimeStyle="hh:mm:ss"===this.opts.fmt||"hh:mm"===this.opts.fmt,e.fitWidth&&t.addClass("k_box_size").outerWidth(202);var i=t.val();if(""!==i){var a=this._txt2Date(i);a&&(e.initValue=a)}if(e.isSingel){t.data("_calopt",{range:e.range,onChange:e.onChange,initValue:e.initValue}),e.fixed=!1,e.show=!1;var s=d._k_calendar_ins_;if(s)return s.setTarget(t,!0),s;d._k_calendar_ins_=this}var n=c.config.calendar;this.config=n,this.target=t;var r,o,h=this;if(this._initValue(!0),this._bindInputEvents(),this.jqObj=$("<div class='k_calendar_wrap k_box_size clearfix' style='width:202px;z-index:2147483647;'></div>"),this.opts.fixed&&this.jqObj.css("z-index",1),this.opts.shadow&&this.jqObj.addClass("k_dropdown_shadow"),this.opts.fixed&&(this.opts.show=!0),this.opts.show||this.jqObj.hide(),this.isTimeStyle)this.jqObj.outerHeight(250),this.createTimeOptList();else{for(this.headerPanel=$("<div class='k_calendar_pannel k_box_size clearfix' style='padding-left:2px;padding-top:2px;'><span class='_prev_year _event' style='width:24px'><i class='fa fa-angle-double-left'></i></span><span  style='width:21px' class='_prev_month _event'><i class='fa fa-angle-left'></i></span><span class='_cur_year  _event' style='width:46px;'>"+this.year+"</span><span>"+n.year+"</span><span class='_cur_month _event' style='width:28px;' >"+this.month+"</span><span>"+n.month+"</span><span style='width:25px' class='_next_month _event'><i class='fa fa-angle-right'></i></span><span class='_next_year _event' style='width:24px'><i class='fa fa-angle-double-right'></i></span></div>").appendTo(this.jqObj),this.bindHeaderPanelEvents(),this.weekPanel=$("<div class='k_calendar_week k_box_size' style='padding-left:2px;margin-bottom:5px;'></div>"),r=0,o=n.weekArray.length;r<o;++r)$("<span class='k_calendar_week_day'>"+n.weekArray[r]+"</span>").appendTo(this.weekPanel);this.weekPanel.appendTo(this.jqObj),this.daysPanel=$("<div class='k_calendar_days k_box_size clearfix' style='padding-left:2px;'></div>").appendTo(this.jqObj),this.createDays(),this.timePanel=$("<div class='k_calendar_time k_box_size' style='margin:1px 0px;padding-left:2px;text-align:center;'></div>").appendTo(this.jqObj),this.createTimeTools(),this.toolPanel=$("<div class='k_calendar_bools k_box_size clearfix' style='padding-left:1px;'></div>").appendTo(this.jqObj),this.createTools()}this.setPosition(),this.isDayStyle&&this.timePanel.hide(),this.isMonthStyle&&(this.daysPanel.hide(),this.timePanel.hide(),this.headerPanel.children("._cur_month").trigger("click"),this.toolPanel.css("margin-top","168px")),this.jqObj.appendTo(p());var l=c.getUUID();$(document).on("click."+l,function(t){if(h.hide){if(t.target){var e=$(t.target);if(e.hasClass("k_calendar_hours")||e.hasClass("k_calendar_minutes")||e.hasClass("k_calendar_seconds")||e.hasClass("k_calendar_wrap"))return!0}h.hide()}else $(this).off("."+l)}),$(d).on("resize."+l,function(){h.setPositionByTimer?h.setPositionByTimer():$(this).off("."+l)}),h._setBorderColor(),this.target.data("calender",this),this.nsId=l}return _.prototype={createTimeOptList:function(){var t,e,i;"hh:mm"===this.opts.fmt?(t=$("<div class='k_calendar_hours' style='width:50%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj),e=$("<div  class='k_calendar_minutes' style='width:50%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj)):(t=$("<div class='k_calendar_hours' style='width:33.3333%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj),e=$("<div class='k_calendar_minutes' style='width:33.3333%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj),i=$("<div class='k_calendar_seconds' style='width:33.3333%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj));var a,s,n,r=this;$("<a style='position:absolute;top:1px ;right:1px;cursor:pointer;line-height:14px;'><i class='fa fa-cancel-circled'></i></a>").appendTo(this.jqObj).click(function(){return r.hide(),!1}),t&&(a=c.makeNiceScroll(t)),e&&(s=c.makeNiceScroll(e)),i&&(n=c.makeNiceScroll(i));for(var o,h,l={click:function(){var t=$(this);t.addClass("actived").siblings().removeClass("actived");var e=parseInt(t.text());t.hasClass("_k_hours")?r.hour=e:t.hasClass("_k_minutes")?r.minutes=e:r.seconds=e;var i=r._var2Date();return r._setCurrentDate(i),r.update2target(r.currentDate),!1}},d=0;d<24;)(o=d)<10&&(o="0"+d),h=$("<div class='k_calendar_time_opts _k_hours' style='padding-right:15px'>"+o+"</div>").appendTo(a).on(l),d===r.hour&&h.addClass("actived"),d++;this._createMinuAndSecItems(s,"_k_minutes",l),n&&this._createMinuAndSecItems(n,"_k_seconds",l)},_createMinuAndSecItems:function(t,e,i){for(var a,s,n=0;n<60;)(a=n)<10&&(a="0"+n),s=$("<div class='k_calendar_time_opts "+e+"' style='padding-right:15px'>"+a+"</div>").appendTo(t).on(i),"_k_minutes"===e?this.minutes===n&&s.addClass("actived"):this.seconds===n&&s.addClass("actived"),n++},_time2fullDateTxt:function(t){return this.isTimeStyle&&/^(20|21|22|23|[0-1]\d)?:?([0-5]\d)?:?([0-5]\d)?$/.test(t)?this.currentDate.format("yyyy-MM-dd")+" "+t:t},_initValue:function(t){var e=this.target.val().leftTrim().rightTrim();if(""!==e&&(this.currentDate=this._txt2Date(e)),t){var i=this.opts.initValue;this.opts.isSingel&&(i=this.target.data("_calopt").initValue),i&&(this.currentDate="string"==typeof i?this._txt2Date(i):i,this.target.val(this.currentDate.format(this.opts.fmt)))}this._setVarByDate(this.currentDate)},_callWatcher:function(){this.onWatcher&&(this.target.data("no2update",!0),this.onWatcher.call(this),this.target.removeData("no2update"))},_bindInputEvents:function(){var a=this,t=void 0===this.target.data("k_calr_ins");if(this.opts.readonly)this.target.attr("readonly",!0);else if(t){var e=function(){var t=a.target.val(),e=a._txt2Date(t);if(e)a._beforeChange(e)&&(a._setCurrentDate(e),a._setVarByDate(e),a.rebuildDaysUi(),a.updateYearAndMonthUi(a.currentDate),a.isMonthStyle?a._createMonthOpts():a.isTimeStyle?a.activedTimeUi():a._updateMinAndCec(),a._callWatcher());else{c.error(a.config.error,2);var i=a.currentDate.format(a.opts.fmt);a.target.val(i)}};this.hasInputed=!1,this.target.on("input.calendar",function(){a.hasInputed=!0,clearTimeout(a.userInputTimer),a.userInputTimer=setTimeout(function(){e()},1e3)})}t&&(this.target.on("blur.calendar",function(){a._setBorderColor(),a.hasInputed&&(a.hasInputed=!1,clearTimeout(a.userInputTimer),e())}).on("click.calendar",function(){return a._setBorderColor(),a.opts.fixed||a.slideToggle(),!1}).on("focus.calendar",function(){a.opts.isSingel&&a.setTarget($(this),!1),a._setBorderColor()}),this.target.data("k_calr_ins",this),this.target.css("cursor","pointer"))},activedTimeUi:function(){var e=this;this.jqObj.children(".k_calendar_hours").find("div.k_calendar_time_opts").each(function(){var t=$(this);parseInt(t.text())===e.hour?t.addClass("actived"):t.removeClass("actived")}),this.jqObj.children(".k_calendar_minutes").find("div.k_calendar_time_opts").each(function(){var t=$(this);parseInt(t.text())===e.minutes?t.addClass("actived"):t.removeClass("actived")}),this.jqObj.children(".k_calendar_seconds").find("div.k_calendar_time_opts").each(function(){var t=$(this);parseInt(t.text())===e.seconds?t.addClass("actived"):t.removeClass("actived")})},_setBorderColor:function(){var t=this.target.css("border-color");this.jqObj.css("border-color",t)},_getMinMaxCfg:function(t){var e,i,a=this.opts.range;if(this.opts.isSingel&&(a=this.target.data("_calopt").range),a[t])if(-1<a[t].indexOf("#")){var s=this.target.data(t+"_input");s||(s=$(a[t]),this.target.data(t+"_input",s)),i=s.val(),e=this._txt2Date(i)}else"now"===a[t]?e=new Date:(i=a[t],e=this._txt2Date(i));return e},_legalDate:function(t,e,i){return e&&t.getTime()<e.getTime()?this.config.minTip.replace("x",e.format(this.opts.fmt)):i&&t.getTime()>i.getTime()?this.config.maxTip.replace("x",i.format(this.opts.fmt)):""},_beforeChange:function(t){var e=!0,i=this._getMinMaxCfg("min"),a=this._getMinMaxCfg("max"),s=this._legalDate(t,i,a);""!==s&&(c.alert(s,2.5),e=!1);var n=this.opts.onChange;return this.opts.isSingel&&(n=this.target.data("_calopt").onChange),e&&"function"==typeof n&&void 0!==(s=n(t,t.format(this.opts.fmt)))&&""!==s&&(c.alert(s,2.5),e=!1),e||this._setVarByDate(this.currentDate),e},bindHeaderPanelEvents:function(){var o=this,t=function(){o.hourItsPanel&&o.hourItsPanel.hide(),o.mmItsPanel&&o.mmItsPanel.hide();var t=$(this),e=o._var2Date(),i=e.getDate(),a=!0;if(t.hasClass("_prev_year"))e.setFullYear(e.getFullYear()-1);else if(t.hasClass("_prev_month")){var s=new Date(e.getFullYear(),e.getMonth(),0,o.hour,o.minutes,o.seconds);i<s.getDate()&&s.setDate(i),e=s}else if(t.hasClass("_cur_year"))o._hideMonthPanel(),a=!1,o.yearPanel&&"none"!==o.yearPanel.css("display")?o.yearPanel.hide(100):o._createYearOpts(e.getFullYear());else if(t.hasClass("_cur_month")){if(o.yearPanel){if(o.isMonthStyle)return!1;o.yearPanel.hide()}a=!1,o.monthPanel&&"none"!==o.monthPanel.css("display")?o._hideMonthPanel(100):o._createMonthOpts()}else if(t.hasClass("_next_month")){var n=new Date(e.getFullYear(),e.getMonth()+2,0,o.hour,o.minutes,o.seconds);i<n.getDate()&&n.setDate(i),e=n}else t.hasClass("_next_year")&&e.setFullYear(e.getFullYear()+1);if(a&&(o.yearPanel&&o.yearPanel.hide(),o._hideMonthPanel(),o._beforeChange(e)&&(o._updateVar(e),o.opts.mutil||o.update2target(e),o.updateYearAndMonthUi(e),o.rebuildDaysUi(e),o.isMonthStyle))){var r=e.getMonth()+1;o._activeMonthUi(r)}return!1};this.headerPanel.children().first().click(t).next().click(t).next().click(t).next().next().click(t).next().next().click(t).next().click(t)},_yearOnclick:function(t){var e=t.data._this,i=$(this),a=i.children("i");if(0<a.length){var s;s=a.hasClass("fa-angle-double-left")?parseInt(i.siblings(".k_box_size").first().text())-18:parseInt(i.siblings(".k_box_size").last().text())+1;for(var n=i.parent().children(".k_box_size").first().next();0<n.length&&n.hasClass("_year_num");)n.text(s),s===e.year?n.addClass("actived"):n.removeClass("actived"),s++,n=n.next()}else{var r=parseInt(i.text());e.year=r;var o=e._var2Date();e._beforeChange(o)&&(e._setCurrentDate(o),e.rebuildDaysUi(),e.updateYearAndMonthUi(e.currentDate),e.opts.mutil||e.update2target(e.currentDate),e.yearPanel.hide(120))}return!1},_createYearOpts:function(t){var e=!1;if(this.yearPanel)this.yearPanel.children(".k_box_size").remove(),this.yearPanel.show();else{e=!0;var i=this;this.yearPanel=$("<div class='k_box_size clearfix k_calendar_ym_panel k_dropdown_shadow' style='padding-left:12px;position:absolute;top:30px;left:0;z-index:2110000000;width:100%;background:#fff;'></div>").appendTo(this.jqObj),$("<div style='position:absolute;top:-2px;right:5px;width:12px;height:12px;line-height:12px;text-align:center;cursor:pointer;'><i class='fa fa-cancel-2' style='font-size:14px;color:#AAAAB4'></i></div>").appendTo(this.yearPanel).click(function(){return i.yearPanel.hide(),!1})}for(var a,s,n=t-12,r=22;0<r;)22===r&&(a="<i style='font-size:16px;color:#A0BFDE' class='fa fa-angle-double-left'></i>",(s=$("<div class='k_box_size' style='float:left;line-height:20px;padding:5px 5px;cursor:pointer;width:43px;text-align:center;'>"+a+"</div>").appendTo(this.yearPanel)).on("click",{_this:this},this._yearOnclick)),a=n,s=$("<div class='k_box_size _year_num' style='float:left;line-height:20px;padding:5px 5px;cursor:pointer;width:43px;text-align:center;'>"+a+"</div>").appendTo(this.yearPanel),a===t&&s.addClass("actived"),s.on("click",{_this:this},this._yearOnclick),1===r&&(a="<i style='font-size:16px;color:#A0BFDE' class='fa fa-angle-double-right'></i>",(s=$("<div class='k_box_size' style='float:left;line-height:20px;padding:5px 5px;cursor:pointer;width:43px;text-align:center;'>"+a+"</div>").appendTo(this.yearPanel)).on("click",{_this:this},this._yearOnclick)),r--,n++;e&&this.yearPanel.appendTo(this.jqObj)},_activeMonthUi:function(e){this.monthPanel.children().each(function(){var t=$(this);e===parseInt(t.text())?t.addClass("actived"):t.removeClass("actived")})},_monthOnclick:function(t){var e=t.data._this,i=$(this),a=parseInt(i.text());e.month=a;var s=e._var2Date();return e._beforeChange(s)&&(e.isMonthStyle&&(i.addClass("actived").siblings().removeClass("actived"),e.opts.clickHide&&e.hide()),e._setCurrentDate(s),e.rebuildDaysUi(),e.updateYearAndMonthUi(e.currentDate),e.update2target(e.currentDate),e._hideMonthPanel()),!1},_hideMonthPanel:function(){!this.isMonthStyle&&this.monthPanel&&this.monthPanel.hide(120)},_createMonthOpts:function(){var e=this;if(this.monthPanel)this.monthPanel.show(),this.monthPanel.children().each(function(){var t=$(this);parseInt(t.text())===e.month?t.addClass("actived"):t.removeClass("actived")});else{this.monthPanel=$("<div class='k_box_size clearfix k_calendar_ym_panel' style='padding-bottom:4px; padding-top:8px;padding-left: 18px; position: absolute; top: 30px; left: 0px; z-index: 2100000000; width: 100%; background: rgb(255, 255, 255);'></div>").appendTo(this.jqObj);for(var t,i,a=1;a<13;)(t=a)<10&&(t="0"+a),i=$("<div style='float:left;line-height:32px;padding:5px 5px;cursor:pointer;width:45px;text-align:center;'>"+t+"</div>").appendTo(this.monthPanel),a===this.month&&i.addClass("actived"),i.on("click",{_this:this},this._monthOnclick),a++;if(!this.isMonthStyle)this.monthPanel.addClass("k_dropdown_shadow"),$("<div style='position:absolute;top:-2px;right:5px;width:12px;height:12px;line-height:12px;text-align:center;cursor:pointer;'><i class='fa fa-cancel-2' style='font-size:14px;color:#AAAAB4'></i></div>").appendTo(this.monthPanel).click(function(){return e.monthPanel.hide(),!1})}},createTools:function(){var a=this,t=function(){var t=$(this);if(a.yearPanel&&a.yearPanel.hide(),!a.isMonthStyle&&a.monthPanel&&a.monthPanel.hide(),t.hasClass("002")){var e=new Date;a.year=e.getFullYear(),a.month=e.getMonth()+1,a.day=e.getDate(),a.hour=e.getHours(),a.minutes=e.getMinutes(),a.seconds=e.getSeconds();var i=a._var2Date();a._beforeChange(i)&&(a._setCurrentDate(i),a.rebuildDaysUi(),a.updateYearAndMonthUi(a.currentDate),a.update2target(a.currentDate),a._updateTimeUi(),a.isMonthStyle&&a._activeMonthUi(a.month),a.opts.clickHide&&a.hide())}else t.hasClass("003")?(a.target.val(""),a.daysPanel.children().removeClass("activted")):a.hide();return!1},e=$("<div class='002'>"+this.config.now+"</div>").appendTo(this.toolPanel).click(t),i=$("<div class='003'>"+this.config.clear+"</div>").appendTo(this.toolPanel).click(t);this.opts.fixed?(e.width(99),i.width(99)):$("<div class='004'>"+this.config.close+"</div>").appendTo(this.toolPanel).click(t)},_getStrTime:function(){var t=this.hour,e=this.minutes,i=this.seconds;return t<10&&(t="0"+t),e<10&&(e="0"+e),i<10&&(i="0"+i),{h:t,m:e,s:i}},createTimeTools:function(){var n=this,r=function(){var t=$(this),e=t.text();"_k_cal_hour"===n.userInput.attr("id")?n.hour=parseInt(e):"_k_cal_minu"===n.userInput.attr("id")?n.minutes=parseInt(e):n.seconds=parseInt(e);var i=n._var2Date();return n._beforeChange(i)&&(n.userInput.val(e).removeClass("actived"),n._setCurrentDate(i),n.update2target(i),t.parent().hide()),!1},t=function(){var t=$(this);n.yearPanel&&n.yearPanel.hide(),n.monthPanel&&!n.isMonthStyle&&n.monthPanel.hide();var e,i,a=!0,s=t.val();if("_k_cal_hour"===t.attr("id")){if(n.mmItsPanel&&n.mmItsPanel.hide(),n.hourItsPanel)"none"===n.hourItsPanel.css("display")?n.hourItsPanel.show():(n.hourItsPanel.hide(),a=!1);else{for(n.hourItsPanel=$("<div class='k_box_size clearfix k_calendar_hours_panel k_dropdown_shadow' style='padding-bottom:2px;padding-left: 12px; position: absolute; top: 30px; left: 0px; z-index: 2112000000; width: 100%; background: rgb(255, 255, 255);'></div>"),e=0;e<24;e++)$("<span>"+(e<10?"0"+e:e)+"</span>").appendTo(n.hourItsPanel).click(r);n.hourItsPanel.appendTo(n.jqObj),$("<div style='position:absolute;top:-2px;right:5px;width:12px;height:12px;line-height:12px;text-align:center;cursor:pointer;'><i class='fa fa-cancel-2' style='font-size:14px;color:#AAAAB4'></i></div>").appendTo(n.hourItsPanel).click(function(){return n.hourItsPanel.hide(),n.userInput.removeClass("actived"),!1})}a&&(i=n.hourItsPanel.children("span"))}else{if(n.hourItsPanel&&n.hourItsPanel.hide(),n.mmItsPanel)"none"===n.mmItsPanel.css("display")?n.mmItsPanel.show():n.userInput&&n.userInput[0]===t[0]&&(n.mmItsPanel.hide(),a=!1);else{for(n.mmItsPanel=$("<div class='k_box_size clearfix k_calendar_mm_panel k_dropdown_shadow' style='padding-top:0px;padding-left: 12px; position: absolute; top: 29px; left: 0px; z-index: 2112000000; width: 100%; background: rgb(255, 255, 255);'></div>"),e=0;e<60;e++)$("<span>"+(e<10?"0"+e:e)+"</span>").appendTo(n.mmItsPanel).click(r);n.mmItsPanel.appendTo(n.jqObj),$("<div style='position:absolute;top:-2px;right:5px;width:12px;height:12px;line-height:12px;text-align:center;cursor:pointer;'><i class='fa fa-cancel-2' style='font-size:14px;color:#AAAAB4'></i></div>").appendTo(n.mmItsPanel).click(function(){return n.mmItsPanel.hide(),n.userInput.removeClass("actived"),!1})}a&&(i=n.mmItsPanel.children("span"))}return n.userInput=t,a?(t.addClass("actived").siblings().removeClass("actived"),i.each(function(){var t=$(this);t.text()===s?t.addClass("actived"):t.removeClass("actived")})):t.removeClass("actived"),!1},e=this._getStrTime();this.$hour=$("<input readonly='readonly' style='width:30px;text-align:center;' id='_k_cal_hour' value='"+e.h+"'/>").appendTo(this.timePanel).click(t),this.timePanel.append("<span>：</span>"),this.$minutes=$("<input  readonly='readonly' style='width:30px;text-align:center' id='_k_cal_minu' value='"+e.m+"'/>").appendTo(this.timePanel).click(t),this.isSecondStyle&&(this.timePanel.append("<span>：</span>"),this.$seconds=$("<input  readonly='readonly' style='width:30px;text-align:center' id='_k_cal_secs' value='"+e.s+"'/>").appendTo(this.timePanel).click(t))},_updateTimeUi:function(){var t=this._getStrTime();this.$hour&&this.$hour.val(t.h),this.$minutes&&this.$minutes.val(t.m),this.$seconds&&this.$seconds.val(t.s)},_updateMinAndCec:function(){var t=this._getStrTime();this.$hour&&this.$hour.val(t.h),this.$minutes&&this.$minutes.val(t.m),this.$seconds&&this.$seconds.val(t.s)},rebuildDaysUi:function(t){if(t&&(this.year=t.getFullYear(),this.month=t.getMonth()+1),this.daysPanel)for(var e,i,a,s=this._getStartDate(),n=this.daysPanel.children().first();0<n.length;)e=s.getDate(),i=s.getMonth()+1,a=s.format("yyyy-MM-dd"),i!==this.month?n.addClass("day_dull").removeClass("activted").attr("title",a):(n.removeClass("day_dull").removeAttr("title"),e===this.day?n.addClass("activted"):n.removeClass("activted")),n.text(e).attr("t",a),s.setDate(s.getDate()+1),n=n.next()},updateYearAndMonthUi:function(t){if(this.headerPanel){var e=t.getFullYear(),i=t.getMonth(),a=this.config.monthArray[i];this.headerPanel.children("._cur_year").text(e).next().next().text(a)}},update2target:function(t,e){var i=t.format(this.opts.fmt);this.target.val(i),this._callWatcher()},_getStartDate:function(){for(var t=this.month-1,e=new Date(this.year,t,1),i=e.getDay(),a=new Date(e);0<i;)a.setDate(a.getDate()-1),i--;return a},_dayOnclick:function(t){var e=t.data._this,i=$(this),a=i.attr("t").split("-");e.year=parseInt(a[0]),e.month=parseInt(a[1]),e.day=parseInt(a[2]);var s=e._var2Date();return e._beforeChange(s)&&(e._setCurrentDate(s),e.updateYearAndMonthUi(e.currentDate),e.update2target(e.currentDate),i.addClass("activted").siblings().removeClass("activted"),e.opts.clickHide&&e.hide()),!1},createDays:function(){for(var t,e,i,a,s=this.month-1,n=this._getStartDate(),r=42;0<r;)t=n.format("yyyy-MM-dd"),i=n.getDate(),a=n.getMonth(),e=$("<span t='"+t+"'>"+i+"</span>").appendTo(this.daysPanel),s!==a?e.addClass("day_dull").attr("title",t):i===this.day&&e.addClass("activted"),e.on("click",{_this:this},this._dayOnclick),n.setDate(n.getDate()+1),r--},_txt2Date:function(t){if(""!==t){var e=(t=this._time2fullDateTxt(t)).match(/^(\s*\d{4}\s*)(-|\/)\s*(0?[1-9]|1[0-2]\s*)(-|\/)?(\s*[012]?[0-9]|3[01]\s*)?\s*([01]?[0-9]|2[0-4]\s*)?:?(\s*[012345]?[0-9]|\s*)?:?(\s*[012345]?[0-9]\s*)?$/);if(null!==e&&(e[1]||e[3])){var i=parseInt(e[1]),a=parseInt(e[3])-1,s=1,n=0,r=0,o=0;return e[5]&&(s=parseInt(e[5])),e[6]&&(n=parseInt(e[6])),e[7]&&(r=parseInt(e[7])),e[8]&&(o=parseInt(e[8])),new Date(i,a,s,n,r,o)}}},_setVarByDate:function(t){this.year=t.getFullYear(),this.month=t.getMonth()+1,this.day=t.getDate(),this.hour=t.getHours(),this.minutes=t.getMinutes(),this.seconds=t.getSeconds()},_var2Date:function(){try{var t=new Date(this.year,this.month,0).getDate();this.day>t&&(this.day=t);var e=new Date(this.year,this.month-1,this.day,this.hour,this.minutes,this.seconds);return e}catch(t){return void c.error(this.config.error)}return e},_setCurrentDate:function(t){this.currentDate=t,this.target.hasClass("k_input_value_err")&&(this.target.removeClass("k_input_value_err"),this.target.siblings(".k_validate_tip_wrap").remove())},_updateVar:function(t){this._setCurrentDate(t),this._setVarByDate(t)},setPositionByTimer:function(){var t=this;if(t.t1){var e=new Date;1e3<e.getTime()-t.t1.getTime()&&(t.setPosition(),t.t1=e)}else t.t1=new Date;clearTimeout(this.positionTimer),this.positionTimer=setTimeout(function(){t.setPosition&&t.setPosition()},500)},setPosition:function(){var t,e,i=this.target.offset();this._isTopPosition()?(e=this._getPanelHeight(),t={top:i.top-e,left:i.left}):(e=this.target.outerHeight()-1,t={top:i.top+e,left:i.left}),this.jqObj.css(t)},slideToggle:function(){"none"===this.jqObj.css("display")?this.show():this.hide()},_getPanelHeight:function(){var t=276;return(this.isMonthStyle||this.isDayStyle||this.isTimeStyle)&&(t=250),t},_isTopPosition:function(){var t=p().height();return 0<this.target.offset().top+this.target.outerHeight()+this._getPanelHeight()-t},hide:function(){this.opts.fixed||(this.hourItsPanel&&this.hourItsPanel.hide(),this.mmItsPanel&&this.mmItsPanel.hide(),this._isTopPosition()?this.jqObj.hide():this.jqObj.slideUp(200),this.yearPanel&&this.yearPanel.hide(),this._hideMonthPanel())},show:function(){this.opts.fixed||(this.setPosition(),this._isTopPosition()?this.jqObj.show():this.jqObj.slideDown(200))},setValue:function(t){var e;e="string"==typeof t?this._txt2Date(t):t,this._setCurrentDate(e),this._setVarByDate(e),this.rebuildDaysUi(),this.updateYearAndMonthUi(this.currentDate),this._updateMinAndCec(),this.opts.mutil||this.update2target(this.currentDate)},setTarget:function(t,e){this.target&&this.target[0]!==t[0]&&"none"!==this.jqObj.css("display")&&this.jqObj.hide(),this.target=t,this._bindInputEvents(),this._initValue(e),this.rebuildDaysUi(),this.updateYearAndMonthUi(this.currentDate),this._updateMinAndCec()},destroy:function(){this.target.off(".calendar"),this.target.removeData("k_calr_ins"),this.target.removeData("calender"),this.target.removeData("_calopt"),$(document).off("."+this.nsId),this.super.destroy.call(this)}},c.Calendar=_});