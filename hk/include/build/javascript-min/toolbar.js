/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(o,e){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B"],function(t){return e(o,t)}):(o.$B||(o.$B={}),e(o,o.$B))}("undefined"!=typeof window?window:this,function(n,c){var t;function T(){if(!t&&!(t=$(n.document.body).css("position","relative")).attr("_k_d_w_c")){if(t.click(function(){t.children("#k_toolbar_drop_wrap").hide()}),n.top!==n)try{$(n.top.document.body).click(function(){t.children("#k_toolbar_drop_wrap").hide()})}catch(t){}t.attr("_k_d_w_c",1)}return t}var j="<button  style='{bg}' cmd='{cmd}' id='{id}' class='k_toolbar_button k_box_size btn k_toolbar_button_{cls}'>{text}</button>",B="k_toolbar_button_disabled";function O(t){var o=$(this);if(o.hasClass(B))return!1;if(!t.data.priv)return c.alert({message:c.permission}),!1;var e=o,a=t.data._t;a.opts.context&&(e=a.opts.context);var s=o.data("click"),r=o.data("methodsObject");if(r||(r=a.opts.methodsObject),"function"==typeof s)setTimeout(function(){s.call(e,o.data("params"))},1);else{var i=n[r];i&&"function"==typeof i[s]&&setTimeout(function(){i[s].call(e,o.data("params"))},1)}return t.data.wrap&&t.data.wrap.hide(),a.opts.onOperated&&setTimeout(function(){a.opts.onOperated.call(e,o.data("params"))},1),!1}function r(t,o,e){var a,s,w=null,r=this.opts.showText?o:"",i=this;""===(a=t.color?"background-color:"+t.color+";":"")&&this.opts.color&&""!==this.opts.color&&(a="background-color:"+this.opts.color+";"),""===(s=t.fontColor?"color:"+t.fontColor:"")&&this.opts.fontColor&&""!==this.opts.fontColor&&(s="color:"+this.opts.fontColor+";"),""!==s&&(a+=s);var n=j.replace("{cmd}",t.cmd).replace("{id}",t.id).replace("{cls}",this.opts.style).replace("{text}",r).replace("{bg}",a);this.opts.params&&delete this.opts.params.Toolbar;var l=$(n).appendTo(this.buttonWrap).data("params",$.extend({cmd:t.cmd},this.opts.params,t.params)),c=l;if(e&&l.css({"margin-right":0,"border-radius":0,"-moz-border-radius":0,"-webkit-border-radius":0}),t.disabled&&l.addClass(B),""===r&&l.attr("title",t.text),l.data("click",t.click),t.iconCls&&""!==t.iconCls){var d="plain"===i.opts.style;d&&l.attr("title",o).css("background","none");var p="";i.opts.fontSize&&(p="style='font-size:"+i.opts.fontSize+"px'"),t.childrens&&0<t.childrens.length?l.append('<i style="padding-left:4px" '+p+' class="fa '+t.iconCls+'"></i>&nbsp'):l.prepend("<i "+p+' class="fa '+t.iconCls+'"></i>&nbsp');var h="#ffffff";t.iconColor?h=t.iconColor:d&&t.color?h=t.color:this.opts.iconColor&&(h=this.opts.iconColor),l.children("i").css("color",h),d&&l.css("color",h)}var y,u=l.data("params");if(u){var f=u.privilage,b=!0;void 0!==f&&"0"===f&&(b=!1,delete l.data("params").privilage,l.addClass("k_no_privilage_cls").css("color","#D1D1D1"))}(l.on("click",{_t:i,priv:b},O).data("methodsObject",t.methodsObject),t.childrens&&0<t.childrens.length)&&l.mouseenter(function(t){var o=$(this),e=o.data("ins"),a=o.offset(),s=o.data("childrens"),r=o.outerWidth();0===(y=T().children("#k_toolbar_drop_wrap")).length?(y=$("<div id='k_toolbar_drop_wrap' style='width:auto;position:absolute;display:none;top:-1000px;' class='k_box_size k_box_shadow'></div>").appendTo(T())).mouseenter(function(){clearTimeout(w)}).mouseout(function(t){var o=$(this),e=t.pageX,a=t.pageY,s=o.outerWidth(),r=o.outerHeight(),i=o.offset();e>=i.left&&e<=i.left+s&&a>i.top&&a<=i.top+r||(w=setTimeout(function(){y&&y.hide()},1500))}):y.children().remove();var i=a.top+o.outerHeight()-1,n={top:"-1000px",left:a.left,"min-width":r};y.css(n).show();for(var l=0,c=s.length;l<c;++l){var d=s[l],p=e.opts.showText?d.text:"",h=(d.toolMethods,d.color?"background-color:"+d.color:e.opts.color),u=j.replace("{cmd}",d.cmd).replace("{id}",d.id).replace("{cls}",e.opts.style).replace("{text}",p).replace("{bg}",h),f=$(u).appendTo(y).data("params",$.extend({cmd:d.cmd},e.opts.params,d.params));d.disabled&&f.addClass(B),""===p&&f.attr("title",d.text),f.data("click",d.click);var b=f.data("params").privilage,v=!0;if(void 0!==b&&"0"===b&&(v=!1,delete f.data("params").privilage,f.addClass("k_no_privilage_cls").css("color","#D1D1D1")),f.on("click",{_t:e,wrap:y,priv:v},O).data("methodsObject",d.methodsObject),d.iconCls&&""!==d.iconCls){"plain"===e.opts.style&&f.attr("title",d.text);var m="";e.opts.fontSize&&(m="style='font-size:"+e.opts.fontSize+"px'");var _=d.color?d.color:"#666666";f.prepend('<i style="color:'+_+';" '+m+' class="fa '+d.iconCls+'"></i>&nbsp')}}var g=y.outerHeight(),x=g+i,k=T().width(),C=y.outerWidth()+10-(k-a.left);return 0<C&&y.css("left",a.left-C),x>T().height()&&(i=a.top-g),y.css("top",i),y.children().mouseover(function(){clearTimeout(w)}),!1}).data("childrens",t.childrens).data("ins",i).parent().mouseenter(function(){y&&y.hide()});return c}var d=function(t,o){var e={params:null,methodsObject:"methodsObject",align:"left",style:"normal",showText:!0,onOperated:void 0,buttons:[]};c.extend(this,d),this.jqObj=t.addClass("k_toolbar_main clearfix"),this.buttonWrap=$("<div></div>").appendTo(this.jqObj),this.opts=$.extend({},e,o),"center"===this.opts.align?(this.jqObj.css("text-align","center"),this.buttonWrap.css("width","100%")):this.buttonWrap.css("float",this.opts.align);for(var a=0,s=this.opts.buttons.length;a<s;++a){var r=this.opts.buttons[a];if($.isArray(r)){for(var i,n=0,l=r.length;n<l;++n)i=this._createButtonByopt(r[n],!0);a!==s-1&&i.css("border-right","1px solid #C1C1C1")}else this._createButtonByopt(r,!1)}};return d.prototype={constructor:d,_createButtonByopt:function(t,o){var e=!0;if(void 0!==t.visualable&&(e=t.visualable),e)return r.call(this,t,t.text,o)},enableButtons:function(t){for(var o=0,e=t.length;o<e;++o){var a=t[o];this.buttonWrap.children("#"+a).removeClass("k_toolbar_button_disabled")}},disableButtons:function(t){for(var o=0,e=t.length;o<e;++o){var a=t[o];this.buttonWrap.children("#"+a).addClass("k_toolbar_button_disabled")}},delButtons:function(t){for(var o=0,e=t.length;o<e;++o){var a=t[o];this.buttonWrap.children("#"+a).remove()}},addButtons:function(t){for(var o=0,e=t.length;o<e;++o){var a=t[o],s=this.opts.showText?a.text:"";r.call(this,a,s)}},updateParams:function(t){$.extend(this.opts.params,t)}},c?c.Toolbar=d:console.log("exception >>>"),d});