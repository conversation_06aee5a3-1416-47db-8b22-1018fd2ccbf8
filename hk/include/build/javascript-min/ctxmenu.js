/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(t,e){"function"==typeof define&&define.amd?define(["$B"],function(n){return e(t,n)}):(t.$B||(t.$B={}),e(t,t.$B))}("undefined"!=typeof window?window:this,function(n,e){var t,o=n.document;function i(){return t||(t=$(o.body).css("position","relative")),t}var s=!1,c=!0;function u(n,t){s=!0,e.extend(this,u),this.$body=i();var m=this;this.$body.css("position","relative").on("click._k_c_menu",function(n){return m.$body.children(".k_context_menu_container").remove(),!1}),1===arguments.length?(this.jqObj=this.$body,this.opts=n):(this.jqObj=n,this.opts=t),this.opts.push({text:e.config.closeLable,iconCls:"fa-minus",click:function(){m.$body.children(".k_context_menu_container").remove()}});var f=function(r,d){var n=$('<div class="k_context_menu_item_cls"><span class="k_context_menu_item_ioc"><i class="k_ctxmenu_i fa '+r.iconCls+'"></i></span><span class="k_context_menu_item_txt">'+r.text+"</span></div>").appendTo(d).on({click:function(){var n=$(this);n.parent().remove(),r.click&&setTimeout(function(){r.click.call(n)},10)}});r.items?(n.append('<span class="k_context_menu_more_ioc"><i class="fa fa-angle-double-right k_ctxmenu_i"></i></span>'),n.mouseenter(function(){var n,t,e=$(this),o=(e.data("items"),d.data("submenu")),i=e.offset(),s=i.left+e.outerWidth()+3,c=i.top;o?((t=(n=o).data("submenu"))&&(t.remove(),n.removeData("submenu")),o.children().remove(),o.css({left:s+"px",top:c+"px"}).show()):(o=$("<div style='position:absolute;z-index:99999999999999999999;height:auto;' class='k_context_menu_container k_box_shadow k_box_radius'/>").appendTo(m.$body).css({left:s+"px",top:c+"px"}),d.data("submenu",o));for(var u=0,a=r.items.length;u<a;++u){var _=r.items[u];f(_,o)}}).data("items",r.items)):n.mouseenter(function(){$(this);var n=d.data("submenu");n&&n.hide()})};this.mousedown=function(n){var t=n.clientX,e=n.clientY;m.$body.children(".k_context_menu_container").remove();var o=$("<div style='position:absolute;z-index:-1000;height:auto;' class='k_context_menu_container k_box_shadow k_box_radius'/>").appendTo(m.$body).css({left:t+"px",top:e+"px"});$.each(m.opts,function(){f(this,o)});var i=o.outerWidth(),s=m.$body.width()-t-i,c={"z-index":2147483647};s<0&&(c.left=t+s),o.css(c)},this.jqObj.on({mousedown:function(n){3===n.which&&(c=!1,m.mousedown(n),setTimeout(function(){c=!0},300))},mouseup:function(){return!1}}),m.$body.on("contextmenu",function(n){return!1})}return n._ctxmenu_close_fn=function(){s&&c&&i().children(".k_context_menu_container").hide()},$(n).on("mouseup._ctxmenu_close_fn",function(){s&&n._ctxmenu_close_fn(),setTimeout(function(){n.parent!==n.self&&n.parent._ctxmenu_close_fn&&n.parent._ctxmenu_close_fn()},10)}),u.prototype={bandTarget:function(n){var t=this;n.on({mousedown:function(n){3===n.which&&(c=!1,t.mousedown(n),setTimeout(function(){c=!0},300))},contextmenu:function(n){return!1}})},hide:function(){this.$body.children(".k_context_menu_container").remove()}},e.Ctxmenu=u});