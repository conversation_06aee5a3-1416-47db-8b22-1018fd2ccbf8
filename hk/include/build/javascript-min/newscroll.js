/*! bui - v0.0.1 - 2019-04-03 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(t,o,n){var e={slider:{"background-color":"#6F6F6F","border-radius":"8px"},size:"6px",hightColor:"#000000",bar:{"background-color":"#e4e4e4","border-radius":"8px",opacity:.5},display:"show",wheelStep:60,axis:"xy",checkItv:0,minHeight:50,minWidth:50};function h(t,i){this.opts=i,this.yscroll=i.yscroll,this.xscroll=i.xscroll,this.$doc=n(o),this.jqObj=t,this.$scrollWrap=this.jqObj.children(".k_scroll_wrap"),this.scrollHeight=this.$scrollWrap[0].scrollHeight,this.scrollWidth=this.$scrollWrap[0].scrollWidth,this.yscroll&&(this.$vbar=this.jqObj.children(".k_scroll_v_bar").css(this.opts.bar),this.$vbtn=this.$vbar.children("div").css(this.opts.slider),this._initVbtnHeight(),this._initVbtnDragEvent(),this._bindMousewheel(),this._hightLine(this.$vbtn)),this.xscroll&&(this.$Hbar=this.jqObj.children(".k_scroll_h_bar").css(this.opts.bar),this.$Hbtn=this.$Hbar.children("div").css(this.opts.slider),this._hightLine(this.$Hbtn),this._initHbtnWidth(),this._initHbtnDragEvent()),this._bindContentScrollListner();var s=this;0<this.opts.checkItv&&(this.yscroll||this.xscroll)&&(this.ivt=setInterval(function(){0===s.jqObj.parent().length?s.destroy():s.resetSliderPosition()},this.opts.checkItv)),this.showOrHideVbtn(),this.showOrHideHbtn(),"auto"===this.opts.display&&(this.yscroll||this.xscroll)&&(this.fadeOut(),this.jqObj.on({mouseenter:function(){s.showOrHideVbtn(),s.showOrHideHbtn(),s.autoHide=!1},mouseleave:function(){s.scrolling?s.autoHide=!0:s.fadeOut()}}))}h.prototype={_initHbtnWidth:function(){var t=this.scrollWidth,i=this.$scrollWrap.width()/t,s=this.$Hbar.width(),o=s/2,l=i*s;l<this.opts.minWidth?l=this.opts.minWidth:o<l&&(l=o),this.$Hbtn.css("width",l)},_initHbtnDragEvent:function(){var s,o,l,r=this,t=this.$Hbtn,n=this.$doc;function e(t){t.preventDefault(),null!=s&&(r.$Hbtn.data("ishl")||(r.$Hbtn.css({"background-color":r.opts.hightColor}).data("ishl",!0),r.$Hbar.css({opacity:.9})),r.scrollLeft(o+(t.pageX-s)*l))}t.on("mousedown",function(t){t.preventDefault(),s=t.pageX,o=r.$scrollWrap[0].scrollLeft;var i=r.getMaxScrollLeftPosition();l=i/r.getMaxHbtnPosition(),r.scrolling=!0,n.on("mousemove.kscroll",e).on("mouseup.kscroll",function(){n.off(".kscroll"),r.maxScrollLeft=void 0,r.maxHbtnPosition=void 0,r.$Hbtn.css({"background-color":r.opts.slider["background-color"]}).removeData("ishl"),r.$Hbar.css({opacity:r.opts.bar.opacity}),r.scrolling=!1,r.autoHide&&r.fadeOut()})})},getMaxScrollLeftPosition:function(){if(void 0===this.maxScrollLeft){var t=this.$scrollWrap.width();this.maxScrollLeft=Math.max(t,this.scrollWidth)-t}return this.maxScrollLeft},getMaxHbtnPosition:function(){return void 0===this.maxHbtnPosition&&(this.maxHbtnPosition=this.$Hbar.width()-this.$Hbtn.width()),this.maxHbtnPosition},getHbtnPosition:function(){var t=this.getMaxHbtnPosition();return Math.min(t,t*this.$scrollWrap[0].scrollLeft/this.getMaxScrollLeftPosition())},showOrHideHbtn:function(t){this.xscroll&&(t||(t=this.scrollWidth),t<=this.$scrollWrap.width()?(this.$Hbar.hide(),this.jqObj.css("padding-bottom",0)):(this.$Hbar.show(),this.jqObj.css("padding-bottom",this.$Hbar.height()+"px")))},scrollLeft:function(t){this.$scrollWrap.animate({scrollLeft:t},80)},_initVbtnHeight:function(){var t=this.scrollHeight,i=this.$vbar.height(),s=i/2,o=this.$scrollWrap.height()/t*i;o<this.opts.minHeight?o=this.opts.minHeight:s<o&&(o=s),this.$vbtn.css("height",o)},_initVbtnDragEvent:function(){var s,o,l,r=this,t=this.$vbtn,n=this.$doc;function e(t){t.preventDefault(),null!=s&&(r.$vbtn.data("ishl")||(r.$vbtn.css({"background-color":r.opts.hightColor}).data("ishl",!0),r.$vbar.css({opacity:.9})),r.scrollTop(o+(t.pageY-s)*l))}t.on("mousedown",function(t){t.preventDefault(),s=t.pageY,o=r.$scrollWrap[0].scrollTop;var i=r.getMaxScrollTopPosition();l=i/r.getMaxVbtnPosition(),r.scrolling=!0,n.on("mousemove.kscroll",e).on("mouseup.kscroll",function(){n.off(".kscroll"),r.maxScrollTop=void 0,r.maxVbtnPosition=void 0,r.$vbtn.css({"background-color":r.opts.slider["background-color"]}).removeData("ishl"),r.$vbar.css({opacity:r.opts.bar.opacity}),r.scrolling=!1,r.autoHide&&r.fadeOut()})})},getMaxScrollTopPosition:function(){if(void 0===this.maxScrollTop){var t=this.$scrollWrap.height();this.maxScrollTop=Math.max(t,this.scrollHeight)-t}return this.maxScrollTop},getVbtnPosition:function(){var t=this.getMaxVbtnPosition();return Math.min(t,t*this.$scrollWrap[0].scrollTop/this.getMaxScrollTopPosition())},getMaxVbtnPosition:function(){return void 0===this.maxVbtnPosition&&(this.maxVbtnPosition=this.$vbar.height()-this.$vbtn.height()),this.maxVbtnPosition},_bindMousewheel:function(){var o=this;o.$scrollWrap.on("mousewheel DOMMouseScroll",function(t){t.preventDefault();var i=t.originalEvent,s=i.wheelDelta?-i.wheelDelta/120:(i.detail||0)/3;o.scrollTop(o.$scrollWrap[0].scrollTop+s*o.opts.wheelStep)})},showOrHideVbtn:function(t){this.yscroll&&(t||(t=this.scrollHeight),t<=this.$scrollWrap.height()?(this.$vbar.hide(),this.jqObj.css("padding-right",0)):(this.$vbar.show(),this.jqObj.css("padding-right",this.$vbar.width()+"px")))},scrollTop:function(t){this.$scrollWrap.animate({scrollTop:t},80)},resetSliderPosByTimer:function(){var t=this;clearTimeout(this.sliderPosByTimer),this.sliderPosByTimer=setTimeout(function(){t.resetSliderPosition()},500)},resetSliderPosition:function(){if(this.yscroll){var t=this.$scrollWrap[0].scrollHeight;this.scrollHeight=t,this.maxScrollTop=void 0,(this.maxVbtnPosition=void 0)!==this.lastScrollHeight&&this.lastScrollHeight!==t&&"0px"!==this.$vbtn[0].style.top&&(this.$vbtn[0].style.top=this.getVbtnPosition()+"px"),this.lastScrollHeight=t,this.showOrHideVbtn(t)}if(this.xscroll){var i=this.$scrollWrap[0].scrollWidth;this.scrollWidth=i,this.maxScrollLeft=void 0,(this.maxHbtnPosition=void 0)!==this.lastScrollWidth&&this.lastScrollWidth!==i&&"0px"!==this.$Hbtn[0].style.left&&(this.$Hbtn[0].style.left=this.getHbtnPosition()+"px"),this.lastScrollWidth=i,this.showOrHideHbtn(i)}},_hightLine:function(t){var i=this;t.on({mouseenter:function(){t.css({"background-color":i.opts.hightColor}).data("ishl",!0),t.parent().css({opacity:.9})},mouseleave:function(){t.css({"background-color":i.opts.slider["background-color"]}).removeData("ishl"),t.parent().css({opacity:i.opts.bar.opacity})}})},_bindContentScrollListner:function(){var t=this;t.$scrollWrap.on("scroll",function(){t.yscroll&&(t.$vbtn[0].style.top=t.getVbtnPosition()+"px"),t.xscroll&&(t.$Hbtn[0].style.left=t.getHbtnPosition()+"px")})},fadeOut:function(){this.yscroll&&this.$vbar.fadeOut(),this.xscroll&&this.$Hbar.fadeOut()},destroy:function(){for(var t in clearInterval(this.ivt),clearTimeout(this.sliderPosByTimer),this)this.hasOwnProperty(t)&&delete this[t]}},n.fn.kscrollbar=function(t){var i=n.extend({},e,t),s=n("<div style='width:100%;height:100%;position:relative;' class='k_scroll_main_wrap k_box_size'></div>"),o=n("<div style='position:relative;width:100%;height:100%;overflow:hidden;' class='k_scroll_wrap k_box_size'></div>").appendTo(s),l=this.parent();this.detach().appendTo(o),i.yscroll="xy"===i.axis||"y"===i.axis,i.xscroll="xy"===i.axis||"x"===i.axis,i.yscroll&&(s.css("padding-right",i.size),n("<div class='k_scroll_v_bar' style='overflow:hidden;position: absolute;top: 0;right: 0;width:"+i.size+";height: 100%;background-color: #e4e4e4;'><div style='cursor:pointer; position: absolute;top: 0;left: 0;width:"+i.size+";height: 30px;background-color: #525252;'></div></div>").appendTo(s)),i.xscroll&&(s.css("padding-bottom",i.size),n("<div class='k_scroll_h_bar' style='overflow:hidden;position: absolute;left: 0;bottom: 0;width:100%;height:"+i.size+";background-color: #e4e4e4;'><div style='cursor:pointer; position: absolute;top: 0;left: 0;width:50px;height:"+i.size+";background-color: #525252;'></div></div>").appendTo(s)),l.append(s);var r=new h(s,i);return this.data("scrollIns",r),this},n.fn.getMyScrollIns=function(){return this.data("scrollIns")}}(window,document,jQuery);