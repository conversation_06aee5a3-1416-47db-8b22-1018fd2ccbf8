/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(i,e){"function"==typeof define&&define.amd?define(["$B","panel"],function(t){return e(i,t)}):(i.$B||(i.$B={}),e(i,i.$B))}("undefined"!=typeof window?window:this,function(y,v){var w=function(t,i){v.extend(this,w),this.jqObj=t.addClass("k_box_size k_layout_main"),this.opts=i,this.panels=[];for(var e=this,n={title:"",iconCls:null,toolbar:null,width:"auto",height:"100%",shadow:!1,radius:!1,header:!0,content:null,dataType:"html",closeable:!1,expandable:!1,onLoaded:null,onExpanded:null},a=this.anylisLayoutSize(),s=function(){0<this.data("i")&&this.css("border-left","none"),"function"==typeof e.opts.onCreated&&e.opts.onCreated.call(this,{i:this.data("i"),title:this.data("title")})},o=function(){e.resize()},l=this.opts.onLoaded,d=function(t){if("function"==typeof l){var i=this.parent();l.call(this,t,{i:i.data("i"),title:i.data("title")})}},h=0,r=this.opts.items.length;h<r;++h){var u=$.extend({},n,this.opts.items[h]),f=$("<div class='k_layout_item k_box_size'></div>").appendTo(this.jqObj).data("width",u.width);u.width=a[h],u.draggable=!1,f.data("i",h),f.data("title",u.title),u.onCreated=s,u.onExpanded=o,u.onLoaded=d;var p=new v.Panel(f,u);this.panels.push(p)}var c=v.getUUID();$(y).on("resize."+c,v.delayFun(function(){e.resize?e.resize():$(this).off("."+c)},10))};return w.prototype={constructor:w,anylisLayoutSize:function(){for(var t={},i=0,e=0,n=0,a=this.opts.items.length;n<a;++n){var s=this.opts.items[n].width,o=!("string"==typeof s||!s);t[n]=o?parseInt(s):"auto",o?i+=t[n]:e++}if(0<e){var l=(this.jqObj.width()-i)/e;for(var d in t)"auto"===t[d]&&(t[d]=l)}return t},resize:function(){var t=this.jqObj.width(),i=0,e=[];this.jqObj.children(".k_layout_item").each(function(){var t=$(this);"auto"===t.data("width")&&e.push(t),i+=t.outerWidth()});for(var n=e.length,a=(t-i)/n,s=0,o=e.length;s<o;++s){var l=e[s].width();e[s].width(l+a)}},setTitle:function(t,i){this.panels[i].setTitle(t)},load:function(t,i){(t.title||t.iconCls)&&this.setTitle(t,i),this.panels[i].load(t)},updateContent:function(t,i){this.panels[i].updateContent(t)},getIfr:function(t){return this.panels[t].getIfr()},getUrl:function(t){return this.panels[t].opts.url},destroy:function(){for(var t=0,i=this.panels.length;t<i;++t)this.panels[t].destroy();this.super.destroy.call(this),this.panels=null}},v.Layout=w});