/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(t,i){"function"==typeof define&&define.amd?define(["$B","config"],function(e){return i(t,e)}):(t.$B||(t.$B={}),i(t,t.$B))}("undefined"!=typeof window?window:this,function(e,b){var t,h=b.config.valid.message,c=b.config.valid.regex;function T(){return t||(t=$(e.document.body)),t}function a(e,t){var i=$._data(e[0],"events");i&&i.click&&i.mouseout&&i.keyup||(e.on({click:function(){var e=$(this);e.siblings(".k_validate_tip_wrap").remove(),e.removeClass("k_input_value_err"),t.onTipFn&&t.onTipFn.call(e,"")},keyup:function(){var e=$(this);n(e,t)&&(e.siblings(".k_validate_tip_wrap").remove(),e.removeClass("k_input_value_err"))}}),e.on("mouseleave.kvalidate",function(e){n($(this),t)}))}function x(n,s,l,o){clearTimeout(s.data("remote_req_timer"));var e=setTimeout(function(){var e=s.val(),t=s.attr("id");if(t&&""!==t||(t=s.attr("name")),""!==e){var i={};i[t]=e;var a={url:n.rule.remote,data:i,ok:function(e){var t;if("string"==typeof e&&""!==e?t=e:$.isPlainObject(e)&&1===e.code&&(t=e.message),t){var i=b.getCharWidth(t)+10,a=s.parent();l.appendTo(a).children(".k_validate_tip_content").css("max_width",i).html(t),s.addClass("k_input_value_err");var n=l.offset();if((o.$winBody?o.$winBody.offset().left+o.$winBody.width()-n.left:T().width()-n.left)<i){var r=l.outerHeight();l.css({left:0,top:-r}),setTimeout(function(){l.remove()},2e3)}}},fail:function(e){}};b.request(a)}},800);s.data("remote_req_timer",e)}function F(e,t,i){var a=[];if($.isPlainObject(e.rule)){var n=typeof e.rule.regex;if("undefined"!==n){var r=null;try{r="string"===n?new RegExp(e.rule.regex,"i"):e.rule.regex}catch(e){}null===r||""===t||r.test(t)||a.push(void 0===e.msg?h.regex:e.msg)}if(void 0!==e.rule.minlength&&""!==t&&t.length<e.rule.minlength&&a.push(void 0===e.msg?h.minlength.replace("{1}",e.rule.minlength):e.msg),void 0!==e.rule.maxlength&&""!==t&&t.length>e.rule.maxlength&&a.push(void 0===e.msg?h.maxlength.replace("{1}",e.rule.maxlength):e.msg),void 0!==e.rule.range){var s=e.rule.range,l=s[0],o=s[1];if(""!==t)if(c.number.test(t)){var d=parseInt(t);(d<l||o<d)&&a.push(void 0===e.msg?h.range.replace("{1}",l).replace("{2}",o):e.msg)}else a.push("只能输入数字文本！")}}else{var u=!0;switch(e.rule){case"url":""!==t&&(c.url.test(t)||(u=!1));break;case"require":""===t&&(u=!1);break;case"email":""!==t&&(c.email.test(t)||(u=!1));break;case"number":""!==t&&(c.number.test(t)||(u=!1));break;case"digits":""!==t&&(c.digits.test(t)||(u=!1));break;case"phone":""!==t&&(c.phone.test(t)||(u=!1));break;case"chchar":""!==t&&c.chchar.test(t)&&(u=!1);break;case"wchar":""!==t&&(c.wchar.test(t)||(u=!1));break;case"telphone":""!==t&&(c.telphone.test(t)||(u=!1));break;case"enchar":""!==t&&(c.enchar.test(t)||(u=!1))}u||a.push(void 0===e.msg?h[e.rule]:e.msg)}return a}function n(s,l){var o=!0,d=s.parent().css("position","relative"),e=s.position(),t=s.outerWidth()+1,i=(s.outerHeight(),{top:e.top+3+"px",left:e.left+t+"px"}),a=s.attr("id");a&&""!==a||(a=s.attr("name"));var n="validate_"+a;d.children("."+n).remove();var u,h=$("<div style='padding:1px 3px;' class='k_validate_tip_wrap "+n+"'><div class='k_validate_tip_top'></div><div style='white-space:nowrap;padding:0' class='k_validate_tip_content'></div></div>").css(i),r=l.rules[a];if($.isPlainObject(r)||$.isArray(r))if(u=(s.tagName,$.trim(s.val())),s.hasClass("k_combox_input")&&(u=s.data("id")),$.isArray(r)){var c,f,p,v=null;$.each(r,function(){if($.isPlainObject(this.rule)&&void 0!==this.rule.remote)v=this;else{var e=F(this,u);if(0<e.length){o=!1;var t=e.join("");if(l.onTipFn&&""!==t)l.onTipFn.call(s,t);else{var i=b.getCharWidth(t)+10;s.hasClass("k_combox_input")&&(d=d.parent().css("position","relative"));var a=d.children(".k_validate_tip_wrap");0<a.length?(h=a.children(".k_validate_tip_content").css("max-width",i).html(t),h=a):h.appendTo(d).children(".k_validate_tip_content").css("max-width",i).html(t);var n=h.offset();if((l.$winBody?l.$winBody.offset().left+l.$winBody.width()-n.left:T().width()-n.left)<i){var r=h.outerHeight();h.css({left:0,top:-r}),setTimeout(function(){h.remove()},2e3)}}}else l.onTipFn&&l.onTipFn.call(s,"")}}),o&&null!==v&&(c="#old_"+a,p=!0,0<(f=l.form.find(c)).length&&f.val()===u&&(p=!1),p&&x(v,s,h,l))}else if(void 0!==r.remote)c="#old_"+a,p=!0,0<(f=l.form.find(c)).length&&f.val()===u&&(p=!1),p&&x(r,s,h,l);else{var m=F(r,u);if(0<m.length){o=!1;var _=m.join("");if(l.onTipFn&&""!==_)l.onTipFn.call(s,_);else{var g=b.getCharWidth(_)+10;h.appendTo(d).children(".k_validate_tip_content").css("max-width",g).html(_);var k=h.offset();if((l.$winBody?l.$winBody.offset().left+l.$winBody.width()-k.left:T().width()-k.left)<g){var w=h.outerHeight();h.css({left:0,top:-w}),setTimeout(function(){h.remove()},2e3)}}}else l.onTipFn&&l.onTipFn.call(s,"")}if(s.attr("valid",o),o){var y=s.parent();s.hasClass("k_combox_input")&&(y=y.parent()),y.children(".k_validate_tip_wrap").remove(),s.removeClass("k_input_value_err")}else s.addClass("k_input_value_err");return o}return b.Validate=function(e,t,i){for(var a,n=t.parent();0<n.length;){if(n.hasClass("k_panel_content")){a=n;break}if("BODY"===(n=n.parent())[0].tagName)break}a&&(this.$winBody=a),this.rules=e,t&&(this.form=t,this.findFormTag(this.form)),i&&(this.onTipFn=i)},b.Validate.prototype.setTipFn=function(e){this.onTipFn=e},b.Validate.prototype.deleteRule=function(e){this.rules&&delete this.rules[e]},b.Validate.prototype.addRule=function(e,t){this.rules||(this.rules={}),this.rules[e]=t},b.Validate.prototype.valid=function(e){if(!this.form&&void 0===e)return alert("验证组件必须传入表单对象！"),!1;this.form||(this.form=e);var t=this,i=this.findFormTag(this.form),a=!0;return $.each(i,function(){n(this,t)||(a=!1)}),a},b.Validate.prototype.findFormTag=function(e){var t=this,i=[];return e.find("input[type=text]").each(function(){var e=$(this);a(e,t),i.push(e)}),e.find("input[type=password]").each(function(){var e=$(this);a(e,t),i.push(e)}),e.find("input[type=file]").each(function(){var e=$(this);a(e,t),i.push(e)}),e.find("textarea").each(function(){var e=$(this);a(e,t),i.push(e)}),e.find("select").each(function(){var e=$(this);a(e,t),i.push(e)}),i},b.Validate});