/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(i,e){"function"==typeof define&&define.amd?define(["$B","tree"],function(t){return e(i,t)}):(i.$B||(i.$B={}),e(i,i.$B))}("undefined"!=typeof window?window:this,function(r,u){var t;function e(){return t||(t=$(r.document.body).css("position","relative")),t}var c={data:[],default:{id:"",text:u.config.comboxPlaceholder},mutilChecked:!0,checkfather:!1,onlyNodeData:!0,tree2list:!0,disabled:!1,initShow:!1,isTreeData:!0,placeholder:u.config.comboxPlaceholder,plainStyle:!0,extParamFiled:[],search:void 0,localSearchFiled:["text"],url:"",readonly:!0,textField:"text",idField:"id",onCheck:null,onClick:null};function l(t,i){u.extend(this,l);var n=this;if(this.jqObj=t.addClass("k_combox_input k_box_size").wrap("<div class='k_combox_input_wrap'></div>"),setTimeout(function(){n.jqObj.off("mouseover.textbox"),n.jqObj.off("mouseout.textbox"),n.jqObj.off("input.textbox")},500),this.jqObj.attr("autocomplete","off"),this.iptWrap=this.jqObj.parent(),this.width=this.jqObj.outerWidth(),this.height=this.jqObj.outerHeight(),0===this.width||0===this.height)var e=setInterval(function(){n.width=n.jqObj.outerWidth(),n.height=n.jqObj.outerHeight(),0===n.width&&0===n.height||(clearInterval(e),n.size.width=n.width,n.size.height=n.height-1,n.icon.css("left",n.size.width-12),n.dropList.children("ul").css("min-width",n.width),n._fixLeftTop())},200);this.opts=$.extend({},c,i);var h=this.opts.onClick;this.opts.onClick=function(t,i){var e=t[0],o=e.data;if(!n.opts.mutilChecked)if(!1===n.opts.checkfather&&i.isParent);else{var s=e[n.opts.idField],a=e[n.opts.textField];void 0===s&&(s=o[n.opts.idField]),a||(a=o[n.opts.textField]),n.jqObj.val(a).data("id",s),"function"==typeof n.onWatcher&&(n.jqObj.data("no2update",!0),n.onWatcher.call(n),setTimeout(function(){n.jqObj.removeData("no2update")},1)),n._fireValidFn()}n.hide(function(){"function"==typeof h&&setTimeout(function(){try{h(o)}catch(t){n.error("onClick is error "+t.message)}},1)})},this._onCheckFn=this.opts.onCheck,this.opts.onCheck=function(t,i,e){n._setInputValue(!0),"function"==typeof n._onCheckFn&&setTimeout(function(){n._onCheckFn.call(n,t,i,e)},1),"function"==typeof n.onWatcher&&n.onWatcher.call(n)},this.opts.checkbox=!0===this.opts.mutilChecked,this.size={width:this.jqObj.outerWidth(),height:this.jqObj.outerHeight()-1},n.opts.isTree||(this.opts.plain=!0),this.body=$(document.body).css("position","relative");var o=$(r),s=u.getUUID();o.on("resize."+s,function(){n.repositon?n.repositon():$(this).off("."+s)}),o.on("click."+s,function(){n.hide?n.hide():$(this).off("."+s)});var a,d=this.jqObj.css("border-color");this.dropList=$("<div class='k_box_size k_combox_wrap' style='z-index: 2147483647;position:abosulte;top:-100000px;min-width:"+this.size.width+"px;border-color:"+d+";'><ul></ul></div>").appendTo(this.body).hide(),this.dropList.data("direction","d").data("combox",this),this.jqObj.on({click:function(){return"none"===n.dropList.css("display")?n.show():n.hide(),!1}}),this.opts.disabled&&this.jqObj.attr("disabled","disabled"),this.jqObj.attr("placeholder",this.opts.placeholder),this.jqObj.val(this.opts.default.text).data("id",this.opts.default.id),this._createTree(),setTimeout(function(){n.repositon()},1),!0===this.opts.readonly&&this.jqObj.attr("readonly","readonly").css("cursor","pointer"),this.icon=$("<div style='top:4px;left:"+(this.width-12)+"px;background:none;' class='k_tree_combox k_combox_input_icon btn'><i style='font-size:14px;float:right;padding-right:3px;font-size:12px;margin-top:5px;' class='fa fa-down-dir'></i></div>").appendTo(this.iptWrap),this.icon.click(function(){return n.opts.disabled||n.jqObj.trigger("click"),!1}),this.initing=!0,!this.opts.readonly&&this.opts.search&&this.jqObj.on("input",function(){if(n.tree&&!n.initing){var t=$.trim(n.jqObj.val().replace(/'/g,""));clearTimeout(a),a=u.isUrl(n.opts.url)&&"remote"===n.opts.search?setTimeout(function(){n.tree.reload(n.dropList.children("ul"),{keyword:t})},500):setTimeout(function(){n._localSearch(t)},500)}}),this.jqObj.data("combox",this),this.jqObj.mouseenter(function(){var t=$(this);t.attr("title",t.val())})}return l.prototype={constructor:l,_setInputValue:function(t){var a,n,h,d=this;if(d.opts.checkbox){var i={onlyChild:!d.opts.checkfather};a=d.tree.getCheckedData(i);var e=function(){for(var t=[],i=[],e=0,o=a.length;e<o;++e){var s=a[e];n=s.id,h=s.text,!n&&s.data&&(n=s.data[d.opts.idField]),!h&&s.data&&(h=s.data[d.opts.textField]),t.push(n),i.push(h)}d.jqObj.val(i.join(",")).data("id",t.join(",")),d._fireValidFn()};t?e():(clearTimeout(d._setInputValueTimer),d._setInputValueTimer=setTimeout(e,100))}else this.tree.clickedItem?(a=this.tree.clickedItem.data("data"),n=a.id,h=a.text,n&&h||(a.data&&!$.isEmptyObject(a.data)&&(a=a.data),n||(n=a[d.opts.idField]),h||(h=a[d.opts.textField])),d.jqObj.val(h).data("id",n)):this.jqObj.val(this.opts.default.text).data("id",this.opts.default.id),d._fireValidFn()},_fireValidFn:function(){var t=this;setTimeout(function(){t.jqObj.trigger("mouseleave.kvalidate")},200)},_localSearch:function(t){var i,e,o,s,a,n;"none"===this.dropList.css("display")&&this.show(),this.tree.jqObj.children("._nodata_tip").remove();var h,d,r=this.tree.childNodesArray,c=this.tree.parentNodesArray,l=0;for(i=0,e=c.length;i<e;++i)a=(s=c[i].show()).children("div"),l<(h=parseInt(a.attr("deep")))&&(l=h),"none"===s.children("ul").css("display")&&(0===(n=a.children("._line_node_")).length&&(n=a.children("._node_")),n.trigger("click"));for(i=0,e=r.length;i<e;++i){var p=(o=r[i]).children("div").data("data");this._isLike(p,t)?o.show():o.hide()}for(this._fixLeftTop(),l++;0<l;){for(d=[],i=0,e=c.length;i<e;++i)a=(s=c[i].show()).children("div"),0===s.children("ul").height()?s.hide():d.push(s);c=d,l--}if(0===this.tree.jqObj.height()){var f=$("<li class='_nodata_tip'>"+u.config.noData+"</li>").appendTo(this.tree.jqObj);setTimeout(function(){try{f.remove()}catch(t){}},3e3)}},_isLike:function(t,i){if(""===i)return!0;for(var e=!1,o=0,s=this.opts.localSearchFiled.length;o<s;o++){var a=this.opts.localSearchFiled[o];if(t[a]&&0<=t[a].indexOf(i)){e=!0;break}if(!e&&t.data[a]&&0<=t.data[a].indexOf(i)){e=!0;break}}return e},_createTree:function(){var e=this;0===this.opts.data.length&&""!==this.opts.url?(this.jqObj.val(""),this.jqObj.attr("placeholder",u.config.loading),this.ajax({url:this.opts.url,ok:function(t,i){e.opts.data=t,e._bindTree()},fail:function(t){},final:function(t){e.jqObj.val(e.opts.default.text).data("id",e.opts.default.id),e.jqObj.attr("placeholder",u.config.comboxPlaceholder),e.opts.onReqloaded&&e.opts.onReqloaded(t)}})):this._bindTree()},_isFined:function(t,i){for(var e=!0,o=0,s=i.length;o<s;++o){if(i[o].id===t){e=!1;break}if(i[o].children&&!(e=this._isFined(t,i[o].children)))break}return e},_bindTree:function(){if(!$.isEmptyObject(this.opts.default)&&(this.opts.default.data||(this.opts.default.data={}),""!==this.opts.default.text&&this._isFined(this.opts.default.id,this.opts.data))){var t={};t[this.opts.idField]=this.opts.default.id,t[this.opts.textField]=this.opts.default.text,t.data=this.opts.default.data,this.opts.data.unshift(t)}var i=this.opts.search&&"local"===this.opts.search;this.opts.isLocalSearch=i;var e=this;this.opts.onTreeCreated=function(){e.initing&&(e._setInputValue(!0),e.opts.initShow?e.show():e.hide()),e.initing=!1,e.opts.onCreatedCall&&e.opts.onCreatedCall()},this.opts.onToggle=function(){e._fixLeftTop()},this.opts.onloaded=function(){e._setInputValue()},this.tree=new u.Tree(this.dropList.children("ul"),this.opts),this.opts.mutilChecked||setTimeout(function(){e.tree.setClickedItem(e.opts.default.id)},1e3)},repositon:function(){var t=this.body.width(),i=this.body[0].scrollHeight,e=this.jqObj.offset(),o=t-e.left,s=i-e.top-this.height;e.top;this.dropList.data("direction","d");var a=e.top+this.size.height;this.dropList.css({top:a,overflow:"auto",left:e.left,"max-width":o,"max-height":s})},_fixLeftTop:function(){var t=this.jqObj.offset(),i=t.top+this.size.height;this.dropList.css({top:i,left:t.left})},reset:function(){this._setBreakHide(),this.jqObj.val(this.opts.default.text).data("id",this.opts.default.id),this.tree.reset()},_setBreakHide:function(){var t=this;this._breakHide=!0,setTimeout(function(){t._breakHide=!1},300)},getCheckedData:function(){return this._setBreakHide(),this.tree?this.opts.mutilChecked?this.tree.getCheckedData():this.tree.getClickItem():[]},getCheckedIds:function(){return this._setBreakHide(),this.tree?this.opts.mutilChecked?this.tree.getCheckedData({onlyId:!0}):this.jqObj.data("id"):[]},hide:function(i){this._breakHide||("d"===this.dropList.data("direction")?this.dropList.slideUp(200,function(){"function"==typeof i&&i();var t=this;e().children(".k_combox_wrap").each(function(){t!==this&&$(this).data("combox")._fixLeftTop()})}):(this.dropList.hide(),"function"==typeof i&&i()),this.icon&&this.icon.children().addClass("fa-down-dir").removeClass("fa-up-dir").css("margin-top","5px"))},show:function(){var i=this;this.repositon(),"d"===this.dropList.data("direction")?this.dropList.slideDown(200,function(){i._fixLeftTop();var t=this;e().children(".k_combox_wrap").each(function(){t!==this&&$(this).data("combox")._fixLeftTop()})}):this.dropList.css({display:"block",top:0}),this.icon&&this.icon.children().removeClass("fa-down-dir").addClass("fa-up-dir").css("margin-top","5px")},val:function(){return this.jqObj.data("id")},setCheckDatas:function(t){if(this.tree){var i=this;if(0<this.tree.running)return void setTimeout(function(){i.setCheckDatas(t)},150);this.opts.mutilChecked?this.tree.setCheckDatas(t):this.tree.setClickedItem(t),this._setInputValue(!0),"function"==typeof this.onWatcher&&this.onWatcher.call(this)}},destroy:function(){this.dropList.remove(),this.super.destroy.call(this)}},u.Combox=l});