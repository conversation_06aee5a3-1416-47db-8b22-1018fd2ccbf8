/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

function _createDropDownListFn(d){d.fn.dropDownList=function(n){var a,l,c=d(window.document.body).css("position","relative");function h(){clearTimeout(l),l=setTimeout(function(){a.hide()},1e3)}return this.each(function(){var t=d(this),i=d("<div style='display: inline-block'></div>"),o=(i=t.wrap(i).parent()).height(),e=t.css("color");i.append("<i class='fa fa-down-dir' style='line-height:"+o+"px;color:"+e+";font-size:1.2em;padding-left:5px;'></i>");var s=$B.getUUID();i.data("_droplist_id",s),i.css("cursor","pointer").mouseenter(function(){(function(){var t=this.offset(),i=this.data("_droplist_id");if(0===(a=c.children("#"+i)).length){var o;a=d("<div id='"+i+"' style='height:auto;position:absolute;z-index:**********;top:-10000px;display:none;' class='k_context_menu_container k_box_shadow k_box_radius'></div>");for(var e=0,s=n.length;e<s;++e)o=n[e],d('<div class="k_context_menu_item_cls"><span class="k_toolbar_img_black k_context_menu_item_ioc btn"><i class="fa '+o.icon+'"></i></span><span class="k_context_menu_item_txt">'+o.text+"</span></div>").click(o.click).appendTo(a);a.appendTo(c),a.on("mouseenter",function(){clearTimeout(l)}).on("mouseleave",function(){h()})}var r={top:t.top+this.outerHeight(),left:t.left};a.css(r).show()}).call(d(this))}).mouseleave(function(){h.call(d(this))})})}}function _makeJquerySubmitEevent(o){o.fn.submit=function(i){return this.each(function(){o(this).click(function(){var t=o(this);window._submit_queues||(window._submit_queues=[],setInterval(function(){for(var t=[],i=new Date,o=0,e=window._submit_queues.length;o<e;++o){var s=window._submit_queues[o];i-s.date<2e3?t.push(s):(s.btn=void 0,s.date=void 0)}window._submit_queues=t},3e3)),window._submit_queues.push({btn:t,date:new Date}),i.call(t)})})}}!function(i,o){"function"==typeof define&&define.amd&&!window._all_in_?define(["jquery"],function(t){return o(i,t)}):o(i,$)}("undefined"!=typeof window?window:this,function(w,y){_createDropDownListFn(y),_makeJquerySubmitEevent(y);var t,x=y(w.document);function T(){return t||(t=y(w.document.body).css("position","relative")),t}var H={mouseenter:function(t,i){var o=y(this),e=o.attr("_tip");e||(e=o.data("_tip")),e||(e=o.text());var s=o.data("tipopts");if(e){clearTimeout(w._clsTipTimer_);var r=0,n=0,a=T();if(w.top!==w.self)if("visible"===a.css("overflow")){var l=$B.getIfrId(),c=y(parent.document.body).find("#"+l).parent();c.hasClass("k_panel_content")&&(r=c.scrollTop(),n=c.scrollLeft())}else r=x.scrollTop(),n=x.scrollLeft();else r=x.scrollTop(),n=x.scrollLeft();(function(t,i,o,e,s){var r;"string"==typeof i&&(i=i.replace(/\r\n/g,"<br/>")).indexOf("<br/>")&&(i=i.split("<br/>"));var n=14;s.fontSize&&(n=s.fontSize);var a=1.2*n,l=""!==s.title,c="padding:2px 10px;";l&&(c="padding:2px 22px;");if(y.isArray(i)){r=[];for(var h=0,d=i.length;h<d;++h)r.push("<p style='font-size:"+n+"px;color:#fff;line-height: 1.3;"+c+"'>"+i[h]+"</p>");r=r.join("")}else r="<p style='font-size:"+n+"px;color:#fff;line-height: 1.3;"+c+"'>"+i+"</p>";var p=T(),u=p.children("#k_mouse_tip_wrap"),f=!0;0===u.length?(u=y("<div style='font-size: "+n+"px;line-height: 1.3;position:absolute;z-index: **********; height: auto;' id='k_mouse_tip_wrap'><div class='k_box_shadow' style='border-radius: 4px;position:absolute;top:0;left:0;width:100%;height:100%;background:#303133;opacity:0.84'></div><div style='position:relative;padding: 6px 0px;' class='tip_content'></div></div>"),f=!1,u.on({mouseenter:function(){clearTimeout(w._clsTipTimer_)},mouseleave:H.mouseleave})):0<u.next().length&&(u.detach(),f=!1);var v=u.children(".tip_content");v.children().remove(),v.html(r),l&&v.prepend("<h6 style='border-bottom:1px dashed #CECECE ;font-size:"+a+"px;text-align:left;padding-left:6px;color:#fff;font-weight: normal;'>"+s.title+"</h6>");var g={top:-1e4,left:0};s.maxWidth&&(t["max-width"]=s.maxWidth,g["max-width"]=s.maxWidth);s.maxWidth&&(t["min-width"]=s.minWidth,g["min-width"]=s.minWidth);w.$curTip_=f?u.css(g).show():u.css(g).appendTo(p);var b=p.outerHeight(),m=p.outerWidth(),x=u.outerHeight(),_=u.outerWidth();b-(t.top-e.top)<x&&(t.top=o.top-x,t.top<0&&(t.top=0));m-(t.left-e.left)<_&&(t.left=o.left-_,t.left<0&&(t.left=0));u.css(t)})(t.isTrigger?i:{left:t.pageX,top:t.pageY},e,o.offset(),{top:r,left:n},s),w._clsTipAct_&&w._clsTipAct_.css("box-shadow","none"),w._clsTipAct_=o.css("box-shadow","0 0 2px 2px "+s.activedColor)}},mouseleave:function(){w.$curTip_&&(w._clsTipTimer_=setTimeout(function(){w.$curTip_.hide(),w.$curTip_=void 0,w._clsTipAct_&&(w._clsTipAct_.css("box-shadow","none"),w._clsTipAct_=void 0)},650))}};var i={nameSpace:"draggable",which:void 0,defaultZindex:999999,holdTime:void 0,isProxy:!(y.fn.mousetip=function(t){var i=y.extend({title:"",activedColor:"#FFD334"},t);return this.each(function(){var t=y(this);t.data("tipopts")||t.on(H).data("tipopts",i)}),this}),disabled:!1,handler:void 0,cursor:"move",axis:void 0,onDragReady:void 0,onStartDrag:void 0,onDrag:void 0,onStopDrag:void 0,onMouseUp:void 0};function _(t){var i=t.data,o=i.options,e=i._data;if(!i.hasCallStartFn){var s=t.pageX-e.startX,r=t.pageY-e.startY;if(0===s&&0===r)return void console.log("过滤没有发生实际移动的 _docMove");if(i.hasCallStartFn=!0,i.target.data("_mvdata",e),i.callStartDragFn){var n=i.movingTarget.css("z-index");"auto"===n&&(n=o.defaultZindex),i.zIndex=n,i.movingTarget.css("z-index",**********);try{o.onStartDrag({state:i})}catch(t){console.error?console.error("onStartDrag error "+t):console.log("onStartDrag error "+t)}}}if(function(t){var i=t.data,o=i._data,e=i.options,s=t.pageX-o.startX,r=t.pageY-o.startY;"v"===e.axis?s=0:"h"===e.axis&&(r=0);var n=o.startLeft+s,a=o.startTop+r;o.leftOffset=s,o.topOffset=r,o.oldLeft=o.left,o.oldTop=o.top,o.left=n,o.top=a;var l=!0;if(i.onDragFn){var c={state:i,which:e.which},h=e.onDrag(c);void 0!==h&&(l=h)}o.apply=l}(t),e.apply&&i.movingTarget){var a={left:e.left+e.fixLeft,top:e.top+e.fixTop,cursor:i.options.cursor};i.movingTarget.css(a)}void 0===o.notClearRange&&(document.selection?document.selection.empty():w.getSelection&&w.getSelection().removeAllRanges())}function $(t){var i=t.data,o=i._data,e=i.options,s=e.nameSpace,r=i.target;if(x.off("."+s),r.css("cursor",o.srcsor),e.isProxy){i.movingTarget.remove();var n={left:o.left,top:o.top};r.css(n)}var a,l=e.onStopDrag;if("function"==typeof l&&i.hasCallStartFn&&(a=l({state:i})),i.target.removeData("_mvdata"),i.hasCallStartFn){var c=i.zIndex;i.movingTarget.css("z-index",c)}else e.onMouseUp&&e.onMouseUp({state:i});if(o.leftOffset=0,o.topOffset=0,T().css("cursor","default"),void 0!==a)return a}function k(t){var i=t.data,o=i.options,e=i.target;if(o.isProxy){var s=i.target.offset(),r=i.target.outerWidth(),n=i.target.outerHeight();e=y("<div style='cursor:"+o.cursor+";position:absolute;width:"+r+"px;height:"+n+"px;top:"+s.top+"px;left:"+s.left+"px' class='k_draggable_proxy'></div>").appendTo(T())}"function"==typeof o.onDrag&&(i.onDragFn=!0),i.movingTarget=e,T().css("cursor",o.cursor)}function n(t){var i=y(this).data("dragtimer");if(i){clearTimeout(i);var o=t.data;if(o.target.removeData("_mvdata"),!o.hasCallStartFn){var e=o.options;e.onMouseUp&&e.onMouseUp({state:o})}}}function a(t,i){i&&!t.pageX&&(t.pageX=i.pageX,t.pageY=i.pageY,t.which=i.which);var o=t.data,e=o.target,s=o.options;if(!(s.which&&s.which!==t.which||s.disabled)){var r=!0;if("function"==typeof s.onDragReady&&(r=s.onDragReady.call(e,o)),r){var n=e.position(),a=$B.getAnglePositionOffset(e),l=a.fixTop,c=a.fixLeft,h=e.parent(),d=0,p=0;h[0]!==document.body&&(d=h.scrollLeft(),p=h.scrollTop()),e.css({position:"absolute",top:n.top+l+p,left:n.left+c+d});var u=e.css("cursor");u||(u="default");var f=e.position(),v={startLeft:f.left+d,startTop:f.top+p,scrollLeft:d,scrollTop:p,left:f.left+d,top:f.top+p,oldLeft:void 0,oldTop:void 0,startX:t.pageX,startY:t.pageY,width:e.outerWidth(),height:e.outerHeight(),fixTop:l,fixLeft:c,srcsor:u,leftOffset:0,topOffset:0};o.hasCallStartFn=!1,o._data=v,o.parent=h,o.callStartDragFn="function"==typeof s.onStartDrag;var g=s.nameSpace;if(void 0!==s.holdTime){var b=y(this),m=setTimeout(function(){e.css("cursor",o.options.cursor),k({data:o}),x.on("mousemove."+g,o,_),x.on("mouseup."+g,o,$)},s.holdTime);b.data("dragtimer",m)}else e.css("cursor",o.options.cursor),x.on("mousedown."+g,o,k),x.on("mousemove."+g,o,_),x.on("mouseup."+g,o,$)}}}y.fn.draggable=function(e){var s="draggable";if("string"==typeof e)switch(1<arguments.length&&(s=arguments[1]),e){case"enable":return this.each(function(){var t=y(this),i=t.data(s);i&&(i.options.disabled=!1),t.css("cursor",i.options.cursor)});case"disable":return this.each(function(){var t=y(this),i=t.data(s);i&&(i.options.disabled=!0),t.css("cursor","default")});case"unbind":return this.each(function(){var t=y(this),i=t.data(s);i.handler.off("."+s),t.removeData(s),delete i.handler,delete i.target,delete i.options,delete i.parent});default:throw new Error("不支持:"+e)}var r=y.extend({},i,e);return r.nameSpace&&(s=r.nameSpace),this.each(function(){var t=y(this),i=t;r.handler&&(i="string"==typeof r.handler?t.find(r.handler):r.handler).css("cursor",e.cursor);var o=t.data(s);o&&(o.handler.off("."+s),delete o.handler,delete o.target,delete o.parent,delete o.options,delete o._data),o={handler:i,target:t,options:r},t.data(s,o),i.on("mousedown."+s,o,a),void 0!==r.holdTime&&i.on("mouseup."+s,o,n)})},y.fn.loading=function(t){var i=$B.config.loading;t&&(i=t);var o="<div style='width:auto;z-index:**********;' class='k_datagrid_loading'><i class='fa fa-cog fa-spin fa-1.6x fa-fw margin-bottom'></i>"+i+"</div>";return this.each(function(){y(this).html(o)})};var l={slider:{"background-color":"#6F6F6F","border-radius":"8px"},size:"6px",hightColor:"#05213B",bar:{"background-color":"#e4e4e4","border-radius":"8px",opacity:.5},setPadding:!0,display:"show",wheelStep:60,axis:"xy",checkItv:0,minHeight:50,minWidth:50};function c(t,i){this.opts=i,this.yscroll=i.yscroll,this.xscroll=i.xscroll,this.$doc=y(document),this.jqObj=t,this.$scrollWrap=this.jqObj.children(".k_scroll_wrap"),this.scrollHeight=this.$scrollWrap[0].scrollHeight,this.scrollWidth=this.$scrollWrap[0].scrollWidth,this.yscroll&&(this.$vbar=this.jqObj.children(".k_scroll_v_bar").css(this.opts.bar),this.$vbtn=this.$vbar.children("div").css(this.opts.slider),this._initVbtnHeight(),this._initVbtnDragEvent(),this._bindMousewheel(),this._hightLine(this.$vbtn)),this.xscroll&&(this.$Hbar=this.jqObj.children(".k_scroll_h_bar").css(this.opts.bar),this.$Hbtn=this.$Hbar.children("div").css(this.opts.slider),this._hightLine(this.$Hbtn),this._initHbtnWidth(),this._initHbtnDragEvent()),this._bindContentScrollListner();var o=this;0<this.opts.checkItv&&(this.yscroll||this.xscroll)&&(this.ivt=setInterval(function(){0===o.jqObj.parent().length?o.destroy():o.resetSliderPosition()},this.opts.checkItv)),this.showOrHideVbtn(),this.showOrHideHbtn(),"auto"===this.opts.display&&(this.yscroll||this.xscroll)&&(this.fadeOut(),this.jqObj.on({mouseenter:function(){o.showOrHideVbtn(),o.showOrHideHbtn(),o.autoHide=!1},mouseleave:function(){o.scrolling?o.autoHide=!0:o.fadeOut()}})),y(w).resize(function(){o.resetSliderPosByTimer()})}return c.prototype={_initHbtnWidth:function(){var t=this.scrollWidth,i=this.$scrollWrap.width()/t,o=this.$Hbar.width(),e=o/2,s=i*o;s<this.opts.minWidth?s=this.opts.minWidth:e<s&&(s=e),this.$Hbtn.css("width",s)},_initHbtnDragEvent:function(){var o,e,s,r=this,t=this.$Hbtn,n=this.$doc;function a(t){t.preventDefault(),null!=o&&(r.$Hbtn.data("ishl")||(r.$Hbtn.css({"background-color":r.opts.hightColor}).data("ishl",!0),r.$Hbar.css({opacity:.9})),r.scrollLeft(e+(t.pageX-o)*s))}t.on("mousedown",function(t){t.preventDefault(),o=t.pageX,e=r.$scrollWrap[0].scrollLeft;var i=r.getMaxScrollLeftPosition();s=i/r.getMaxHbtnPosition(),r.scrolling=!0,n.on("mousemove.kscroll",a).on("mouseup.kscroll",function(){n.off(".kscroll"),r.maxScrollLeft=void 0,r.maxHbtnPosition=void 0,r.$Hbtn.css({"background-color":r.opts.slider["background-color"]}).removeData("ishl"),r.$Hbar.css({opacity:r.opts.bar.opacity}),r.scrolling=!1,r.autoHide&&r.fadeOut()})}),t.on("mouseup",function(){n.off("mousemove.kscroll"),n.off("mouseup.kscroll")})},getMaxScrollLeftPosition:function(){if(void 0===this.maxScrollLeft){var t=this.$scrollWrap.width();this.maxScrollLeft=Math.max(t,this.scrollWidth)-t}return this.maxScrollLeft},getMaxHbtnPosition:function(){return void 0===this.maxHbtnPosition&&(this.maxHbtnPosition=this.$Hbar.width()-this.$Hbtn.width()),this.maxHbtnPosition},getHbtnPosition:function(){var t=this.getMaxHbtnPosition();return Math.min(t,t*this.$scrollWrap[0].scrollLeft/this.getMaxScrollLeftPosition())},showOrHideHbtn:function(t){this.xscroll&&(t||(t=this.scrollWidth),t<=this.$scrollWrap.width()?(this.$Hbar.hide(),this.jqObj.css("padding-bottom",0)):(this.$Hbar.show(),this.jqObj.css("padding-bottom",this.$Hbar.height()+"px")))},scrollLeft:function(t){this.$scrollWrap.scrollLeft(t)},_initVbtnHeight:function(){var t=this.scrollHeight,i=this.$vbar.height(),o=i/2,e=this.$scrollWrap.height()/t*i;e<this.opts.minHeight?e=this.opts.minHeight:o<e&&(e=o),this.$vbtn.css("height",e)},_initVbtnDragEvent:function(){var o,e,s,r=this,t=this.$vbtn,n=this.$doc;function a(t){t.preventDefault(),null!=o&&(r.$vbtn.data("ishl")||(r.$vbtn.css({"background-color":r.opts.hightColor}).data("ishl",!0),r.$vbar.css({opacity:.9})),r.scrollTop(e+(t.pageY-o)*s))}t.on("mousedown",function(t){t.preventDefault(),o=t.pageY,e=r.$scrollWrap[0].scrollTop;var i=r.getMaxScrollTopPosition();s=i/r.getMaxVbtnPosition(),r.scrolling=!0,n.on("mousemove.kscroll",a).on("mouseup.kscroll",function(){n.off(".kscroll"),r.maxScrollTop=void 0,r.maxVbtnPosition=void 0,r.$vbtn.css({"background-color":r.opts.slider["background-color"]}).removeData("ishl"),r.$vbar.css({opacity:r.opts.bar.opacity}),r.scrolling=!1,r.autoHide&&r.fadeOut()})}),t.on("mouseup",function(){n.off("mousemove.kscroll"),n.off("mouseup.kscroll")})},getMaxScrollTopPosition:function(){if(void 0===this.maxScrollTop){var t=this.$scrollWrap.height();this.maxScrollTop=Math.max(t,this.scrollHeight)-t}return this.maxScrollTop},getVbtnPosition:function(){var t=this.getMaxVbtnPosition();return Math.min(t,t*this.$scrollWrap[0].scrollTop/this.getMaxScrollTopPosition())},getMaxVbtnPosition:function(){return void 0===this.maxVbtnPosition&&(this.maxVbtnPosition=this.$vbar.height()-this.$vbtn.height()),this.maxVbtnPosition},_bindMousewheel:function(){var e=this;e.$scrollWrap.on("mousewheel DOMMouseScroll",function(t){t.preventDefault();var i=t.originalEvent,o=i.wheelDelta?-i.wheelDelta/120:(i.detail||0)/3;e.scrollTop(e.$scrollWrap[0].scrollTop+o*e.opts.wheelStep),"none"===e.$vbar.css("display")&&(e.showOrHideVbtn(),e.xscroll&&e.showOrHideHbtn())})},showOrHideVbtn:function(t){this.yscroll&&(t||(t=this.scrollHeight),t<=this.$scrollWrap.height()?(this.$vbar.hide(),this.jqObj.css("padding-right",0)):(this.$vbar.show(),"auto"!==this.opts.display||this.opts.setPadding?this.jqObj.css("padding-right",this.$vbar.width()+"px"):this.jqObj.css("padding-right",0)))},scrollTop:function(t){this.$scrollWrap.scrollTop(t)},resetSliderPosByTimer:function(){var t=this;clearTimeout(this.sliderPosByTimer),this.sliderPosByTimer=setTimeout(function(){t.resetSliderPosition()},500)},resetSliderPosition:function(){if(this.yscroll){var t=this.$scrollWrap[0].scrollHeight;this.scrollHeight=t,this.maxScrollTop=void 0,(this.maxVbtnPosition=void 0)!==this.lastScrollHeight&&this.lastScrollHeight!==t&&"0px"!==this.$vbtn[0].style.top&&(this.$vbtn[0].style.top=this.getVbtnPosition()+"px"),this.lastScrollHeight=t,this.showOrHideVbtn(t)}if(this.xscroll){var i=this.$scrollWrap[0].scrollWidth;this.scrollWidth=i,this.maxScrollLeft=void 0,(this.maxHbtnPosition=void 0)!==this.lastScrollWidth&&this.lastScrollWidth!==i&&"0px"!==this.$Hbtn[0].style.left&&(this.$Hbtn[0].style.left=this.getHbtnPosition()+"px"),this.lastScrollWidth=i,this.showOrHideHbtn(i)}},_hightLine:function(t){var i=this;t.on({mouseenter:function(){t.css({"background-color":i.opts.hightColor}).data("ishl",!0),t.parent().css({opacity:.9})},mouseleave:function(){t.css({"background-color":i.opts.slider["background-color"]}).removeData("ishl"),t.parent().css({opacity:i.opts.bar.opacity})}})},_bindContentScrollListner:function(){var t=this;t.$scrollWrap.on("scroll",function(){t.yscroll&&(t.$vbtn[0].style.top=t.getVbtnPosition()+"px"),t.xscroll&&(t.$Hbtn[0].style.left=t.getHbtnPosition()+"px")})},fadeOut:function(){this.yscroll&&this.$vbar.fadeOut(),this.xscroll&&this.$Hbar.fadeOut()},destroy:function(){for(var t in clearInterval(this.ivt),clearTimeout(this.sliderPosByTimer),this)this.hasOwnProperty(t)&&delete this[t]}},y.fn.myscrollbar=function(t,i){var o=y.extend({},l,t),e=y("<div style='width:100%;height:100%;position:relative;' class='k_scroll_main_wrap k_box_size'></div>"),s=y("<div style='position:relative;width:100%;height:100%;overflow:hidden;' class='k_scroll_wrap k_box_size'></div>").appendTo(e),r=this.parent();this.detach().appendTo(s),o.yscroll="xy"===o.axis||"y"===o.axis,o.xscroll="xy"===o.axis||"x"===o.axis,o.yscroll&&(e.css("padding-right",o.size),"auto"!==o.display||o.setPadding||e.css("padding-right",0),y("<div class='k_scroll_v_bar' style='overflow:hidden;position: absolute;top: 0;right: 0;width:"+o.size+";height: 100%;background-color: #e4e4e4;'><div style='cursor:pointer; position: absolute;top: 0;left: 0;width:"+o.size+";height: 30px;background-color: #525252;'></div></div>").appendTo(e)),o.xscroll&&(e.css("padding-bottom",o.size),y("<div class='k_scroll_h_bar' style='overflow:hidden;position: absolute;left: 0;bottom: 0;width:100%;height:"+o.size+";background-color: #e4e4e4;'><div style='cursor:pointer; position: absolute;top: 0;left: 0;width:50px;height:"+o.size+";background-color: #525252;'></div></div>").appendTo(e)),r.append(e);var n=new c(e,o);return this.data("scrollIns",n),this},y.fn.getMyScrollIns=function(){return this.data("scrollIns")},y.fn.myslider=function(c,h){var d,p="string"==typeof c;p||(d=y.extend({},{width:"100%",height:16,start:0,end:100},c),this.data("opts",d)),this.each(function(){var s,r,t=y(this);if(p){if("setValue"===c||"setValueSilent"===c){var i=t.data("opts"),o=t.children("div"),e=o.children("span");r||(s=o.width()-e.width(),r=s/(i.end-i.start));var n=h*r;e.css("left",n),i.onChange&&"setValueSilent"!==c&&i.onChange(h,!0)}return!0}var a=y("<div style='z-index:1; position:relative;border:1px solid #c5c5c5;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius: 3px;'></div>");a.css({width:d.width,height:d.height});var l=y("<span tabindex='0' style='cursor:pointer;background:#fff;position:absolute;z-index:2; border:1px solid #c5c5c5;top:-4px;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius: 3px;width:12px;height:"+(d.height+6)+"px'/>").appendTo(a);a.appendTo(t),l.draggable({axis:"h",onStartDrag:function(t){r||(s=a.width()-l.width(),r=s/(d.end-d.start))},onDrag:function(t){var i=t.state._data;i.left<0?i.left=0:i.left>s&&(i.left=s);var o=parseInt(i.left/r);d.onChange&&d.onChange(o)}}).keydown(function(t){var i,o=t.which,e=y(this);39===o?(i=e.position().left+1,void 0===s&&(s=a.width()-l.width()),i<=s&&(i<0&&(i=0),s<i&&(i=s),e.css("left",i))):37===o&&((i=e.position().left-1)<0&&(i=0),0<=i&&e.css("left",i)),void 0!==i&&(r||(r=s/(d.end-d.start)),(i=parseInt(i/r))<d.start&&(i=d.start),i>d.end&&(i=d.end),d.onChange&&d.onChange(i))})})},y});