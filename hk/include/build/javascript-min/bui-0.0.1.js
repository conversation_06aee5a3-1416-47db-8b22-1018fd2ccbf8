/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

function _createDropDownListFn(c){c.fn.dropDownList=function(n){var r,l,d=c(window.document.body).css("position","relative");function h(){clearTimeout(l),l=setTimeout(function(){r.hide()},1e3)}return this.each(function(){var t=c(this),e=c("<div style='display: inline-block'></div>"),i=(e=t.wrap(e).parent()).height(),a=t.css("color");e.append("<i class='fa fa-down-dir' style='line-height:"+i+"px;color:"+a+";font-size:1.2em;padding-left:5px;'></i>");var o=$B.getUUID();e.data("_droplist_id",o),e.css("cursor","pointer").mouseenter(function(){(function(){var t=this.offset(),e=this.data("_droplist_id");if(0===(r=d.children("#"+e)).length){var i;r=c("<div id='"+e+"' style='height:auto;position:absolute;z-index:**********;top:-10000px;display:none;' class='k_context_menu_container k_box_shadow k_box_radius'></div>");for(var a=0,o=n.length;a<o;++a)i=n[a],c('<div class="k_context_menu_item_cls"><span class="k_toolbar_img_black k_context_menu_item_ioc btn"><i class="fa '+i.icon+'"></i></span><span class="k_context_menu_item_txt">'+i.text+"</span></div>").click(i.click).appendTo(r);r.appendTo(d),r.on("mouseenter",function(){clearTimeout(l)}).on("mouseleave",function(){h()})}var s={top:t.top+this.outerHeight(),left:t.left};r.css(s).show()}).call(c(this))}).mouseleave(function(){h.call(c(this))})})}}function _makeJquerySubmitEevent(i){i.fn.submit=function(e){return this.each(function(){i(this).click(function(){var t=i(this);window._submit_queues||(window._submit_queues=[],setInterval(function(){for(var t=[],e=new Date,i=0,a=window._submit_queues.length;i<a;++i){var o=window._submit_queues[i];e-o.date<2e3?t.push(o):(o.btn=void 0,o.date=void 0)}window._submit_queues=t},3e3)),window._submit_queues.push({btn:t,date:new Date}),e.call(t)})})}}!function(i,a){"function"==typeof define&&define.amd&&!window._all_in_?define(["jquery","config"],function(t,e){return a(i,t,e)}):a(i,$)}("undefined"!=typeof window?window:this,function(window,$,cfg){"use strict";var $B=window.$B?window.$B:{};window.$B=$B;var config=$B.config,document=window.document,charSpan="<span style='position:absolute;white-space:nowrap;top:-10000000px;left:-10000000px' id='{id}'></span>",$body,_char_id_="__getcharwidth__",_ellipsis_char_id="_ellipsis_char_",$spen_CharWidth,$ellipsisCharDiv=null,loadingHtml="<div style='padding-left:16px;' class='loading'>"+config.loading+"</div>",chars=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],srcollArray=[],SCROLLCHECKER,removeClearTimer;function BaseControl(){this._version="bui-0.0.1",this._release="2019-10-24",this._author="www.vvui.net"}function _getBody(){return $body||($body=$(document.body).css("position","relative")),$body}BaseControl.prototype={constructor:BaseControl,version:function(){$B.debug(this._version),$B.debug(this._release),$B.debug(this._author)},ajax:function(t){$B.request(t)},debug:function(t){$B.debug(t)},error:function(t){$B.debug("error:"+t)},clear:function(){this.jqObj&&this.jqObj.children().remove(),this.delProps()},regiterFn:function(t,e){this[t]=e},destroy:function(t){this.jqObj&&this.jqObj.remove(),this.delProps(t),this.__proto__={}},delProps:function(t){for(var e in this)this.hasOwnProperty(e)&&(null!==this[e]&&void 0!==this[e]&&"super"!==e&&"function"==typeof this[e].destroy&&(t&&t===this[e]||this[e].destroy(this)),delete this[e])}};var TextEvents={input:function(){if(""!==$(this).val()){var t=_getBody().children("#k_text_clear_btn");0!==t.length&&t.data("target")===this||TextEvents.mouseover.call(this)}},mouseover:function(){clearTimeout(removeClearTimer);var t=$(this);if(!t.attr("readonly")){var e=_getBody(),i=e.children("#k_text_clear_btn").hide();if(""!==t.val()){var a=t.offset(),o=t.outerWidth()+a.left-15,s=t.outerHeight()/2+a.top-12;t.hasClass("k_combox_input")&&(o-=18),0===i.length?(i=$("<div id='k_text_clear_btn' style='cursor:pointer;position:absolute;top:"+s+"px;left:"+o+"px;width:14px;height:14px;z-index:**********;'><i style='color:#C1C1C1' class='fa fa-cancel-2'></i></div>")).appendTo(e).on({click:function(){var t=$(this).data("target");t.val(""),t.trigger("input.mvvm"),$(this).hide(),t.focus()}}).data("target",t):i.data("target",t).css({top:s,left:o}).show()}}},mouseout:function(t){var e=_getBody();removeClearTimer=setTimeout(function(){e.children("#k_text_clear_btn").hide()},800)}},ajaxOpts={timeout:12e4,type:"POST",dataType:"json",async:!0,error:function(xhr,status,errorThrown){var res={message:config.permission+xhr.status};try{res=eval("("+xhr.responseText+")")}catch(t){window.console&&console.log(xhr.responseText)}200===xhr.status?this.success(res):$B.error(config.requestError),this.recoverButton(),this.final(status)},success:function(res){if(this.recoverButton(),this.final(res),void 0!==res.code)if(0===res.code){var data=res.data;res.strConvert&&(data=eval("("+res.data+")")),this.ok(res.message,data,res)}else 99999===res.code?"notlogin"===res.data?($B.error(res.message),setTimeout(function(){window.ctxPath&&(window.top.location=$B.getHttpHost(window.ctxPath))},1600)):$B.error(config.permission):this.fail(res.message,res);else this.ok(res,res)},ok:function(t,e){},recoverButton:function(){this.target&&(this.target.removeAttr("disabled"),"INPUT"===this.target[0].tagName?this.target.val(this.recoverText):this.target.html(this.recoverText),this.target=void 0)},fail:function(t,e){0===_getBody().children("._request_fail_window").length&&$B.alert(t).jqObj.addClass("_request_fail_window")},final:function(t){}},reg=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;function HashTable(){var i,a,o;this.size=0,this.entry={},"function"!=typeof this.add&&(HashTable.prototype.add=function(t,e){this.containsKey(t)||this.size++,this.entry[t]=e}),"function"!=typeof this.getValue&&(HashTable.prototype.getValue=function(t){return this.containsKey(t)?this.entry[t]:null}),"function"!=typeof this.remove&&(HashTable.prototype.remove=function(t){this.containsKey(t)&&delete this.entry[t]&&this.size--}),"function"!=typeof this.containsKey&&(HashTable.prototype.containsKey=function(t){return t in this.entry}),"function"!=typeof this.containsValue&&(HashTable.prototype.containsValue=function(t){var e=Object.keys(this.entry);for(i=0,a=e.length;i<a;++i)if(o=e[i],this.entry[o]===t)return!0;return!1}),"function"!=typeof this.getValues&&(HashTable.prototype.getValues=function(){var t=[],e=Object.keys(this.entry);for(i=0,a=e.length;i<a;++i)o=e[i],t.push(this.entry[o]);return t}),"function"!=typeof this.getKeys&&(HashTable.prototype.getKeys=function(){return Object.keys(this.entry)}),"function"!=typeof this.getSize&&(HashTable.prototype.getSize=function(){return this.size}),"function"!=typeof this.clear&&(HashTable.prototype.clear=function(){this.size=0,this.entry={}}),"function"!=typeof this.joinValue&&(HashTable.prototype.joinValue=function(t,e,i){var a=void 0===i?",":i,o=this.getValue(t);if(null===o)this.add(t,e),this.size++;else{var s=o.split(a);s.push(e),this.add(t,s.join(a))}}),"function"!=typeof this.destroy&&(HashTable.prototype.destroy=function(){this.size=0,this.entry=null}),"function"!=typeof this.each&&(HashTable.prototype.each=function(t){var e=Object.keys(this.entry);for(i=0,a=e.length;i<a;++i)t(o=e[i],this.entry[o])}),"function"!=typeof this.getJson&&(HashTable.prototype.getJson=function(t){return $.extend(!0,{},this.entry)})}function NiceScroll(t,e){var i=$.extend({"border-radius":"6px",position:"absolute",width:"6px",background:"#BCDBF7",opacity:.8},e);this.jqObj=t,this.vbar=$("<div style='top:0;right:1px;display:none;cursor:pointer;'></div>").css(i).insertAfter(t);var a=this;this.fireOnScroll=!0,this.jqObj.on("scroll",function(){a.fireOnScroll&&a.onScroll()}),this.hideTimer=void 0,this.isDraging=!1,this.jqObj.on({mouseenter:function(){a.isDraging||(a.vbar.css("opacity","0.8"),a.lastScrollHeight=0,a.updateUi().show())},mouseleave:function(){a.hide()}}),this.vbar.on("mouseenter",function(){a.show(),a.vbar.css("opacity","1")}).on("mouseleave",function(){a.hide()}).draggable({cursor:"pointer",axis:"v",onStartDrag:function(t){a.maxTop=parseInt(a.clientHeight-a.vbar.height())+1,a.fireOnScroll=!1,a.isDraging=!0},onDrag:function(t){var e=t.state;e._data.top<0?e._data.top=0:e._data.top>a.maxTop&&(e._data.top=a.maxTop);var i=e._data.top/a.scrollRate;a.jqObj.scrollTop(i)},onStopDrag:function(){return a.fireOnScroll=!0,a.isDraging=!1}}),this.vbar.click(function(){return!1})}return String.prototype.toHexColor=function(){var t=this;if(/^(rgb|RGB)/.test(t)){for(var e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(","),i="#",a=0;a<e.length;a++){var o=Number(e[a]).toString(16);1===o.length&&(o="0"+o),"0"===o&&(o+=o),i+=o}return 7!==i.length&&(i=t),i.toUpperCase()}if(!reg.test(t))return t.toUpperCase();var s=t.replace(/#/,"").split("");if(6===s.length)return t.toUpperCase();if(3===s.length){for(var n="#",r=0;r<s.length;r+=1)n+=s[r]+s[r];return n.toUpperCase()}},String.prototype.toRgbColor=function(){var t=this.toLowerCase();if(t&&reg.test(t)){if(4===t.length){for(var e="#",i=1;i<4;i+=1)e+=t.slice(i,i+1).concat(t.slice(i,i+1));t=e}for(var a=[],o=1;o<7;o+=2)a.push(parseInt("0x"+t.slice(o,o+2)));return"RGB("+a.join(",")+")"}return t.toUpperCase()},Array.prototype.unique=function(){this.sort();for(var t=[this[0]],e=1;e<this.length;e++)this[e]!==t[t.length-1]&&t.push(this[e]);return t},Date.prototype.format=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),"S+":this.getMilliseconds()};for(var i in/(y+)/i.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),e)new RegExp("("+i+")").test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?e[i]:("00"+e[i]).substr((""+e[i]).length)));return t},String.prototype.trim=function(){return this.replace(/(^\s*)|(\s*$)/g,"")},String.prototype.leftTrim=function(){return this.replace(/(^\s*)/g,"")},String.prototype.rightTrim=function(){return this.replace(/(\s*$)/g,"")},$B.HashTable=HashTable,$.extend($B,{request:function(){var t,e=arguments[0];for(var i in"function"==typeof(t=void 0!==e?$.extend({},ajaxOpts,e):ajaxOpts).preRequest&&t.preRequest(),t.data)null===t.data[i]?delete t.data[i]:t.data[i]=this.htmlEncode(t.data[i]);var a,o=window._submit_queues;if(o&&0<o.length){var s=o.length-1;if(o[s].date-new Date<=500)a=o[s].btn,o.shift().btn=void 0}if(1<arguments.length||a){var n=1<arguments.length?arguments[1]:a;if((t.target=n).attr("disabled","disabled"),"INPUT"===n[0].tagName)t.recoverText=n.val(),n.val(config.busy);else{t.recoverText=n.html(),n.html(config.busy);var r=n.css("color");n.prepend("<i style='padding:0;margin-right:3px;color:"+r+";' class='fa fa-spin6 animate-spin'></i>")}}$.ajax(t)},lightenDarkenColor:function(t,e){var i=!1;"#"===t[0]&&(t=t.slice(1),i=!0);var a=parseInt(t,16),o=(a>>16)+e;255<o?o=255:o<0&&(o=0);var s=(a>>8&255)+e;255<s?s=255:s<0&&(s=0);var n=(255&a)+e;return 255<n?n=255:n<0&&(n=0),(i?"#":"")+String("000000"+(n|s<<8|o<<16).toString(16)).slice(-6)},isContrastYIQ:function(t){var e=t;-1<t.indexOf("#")&&(e=t.toRgbColor());var i,a=e.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);return i=299*a[1]+587*a[2]+114*a[3],.5<=(i/=255e3)?"light":"dark"},changeButtonStatus:function(t){var e,i=t.children("i");t.attr("disabled")?(t.removeAttr("disabled"),e=t.data("clazz"),t.removeData("clazz").removeClass("k_toolbar_button_disabled"),i.attr("class",e)):(t.prop("disabled",!0),e=i.attr("class"),t.data("clazz",e).addClass("k_toolbar_button_disabled"),i.attr("class","fa fa-spin6 fa-spin"))},getHttpHost:function(t){var e,i=window.location.protocol+"//"+window.location.host;return!t&&window.ctxPath?e=window.ctxPath:t&&(e=t),e&&(i+=e),i},htmlEncode:function(t){if(!t||void 0===t.replace)return t;return 0===t.length?"":t.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/eval\((.*)\)/g,"").replace(/<.*script.*>/,"")},htmlDecode:function(t){if(void 0===t.replace)return t;return 0===t.length?"":t.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#39;/g,"'").replace(/&quot;/g,'"')},getAnglePositionOffset:function(t){var e=t.css("transform"),i={fixTop:0,fixLeft:0};if(e&&"none"!==e){var a=t.position();if(0!==$B.getMatrixAngle(e)){var o=$("<div />"),s=t.attr("style");o.attr("style",s).css({filter:"alpha(opacity=0)","-moz-opacity":"0",opacity:"0",position:"absolute","z-index":-111}),o.css("transform","rotate(0deg)"),o.appendTo(t.parent());var n=o.position();i.fixTop=n.top-a.top,i.fixLeft=-(a.left-n.left),o.remove()}}return i},getMatrixAngle:function(t){if("none"===t)return 0;var e=t.split("(")[1].split(")")[0].split(","),i=e[0],a=e[1];return Math.round(Math.atan2(a,i)*(180/Math.PI))},debug:function(t){console.log("debug:"+t)},extend:function(t,e,i,a){i||(i=BaseControl),i.call(t,a),Object.keys(i.prototype).forEach(function(t){e.prototype[t]||(e.prototype[t]=i.prototype[t])}),e.prototype.constructor=e,t.super=new i},scrollbar:function(t){t.mCustomScrollbar||t.css("overflow","auto")},getHashTable:function(){return new HashTable},isUrl:function(t){return/^((http(s)?|ftp):\/\/)?([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?(:\d+)?/.test(t)},writeCookie:function(t,e,i){this.isNotEmpty(i)||(i=1);try{var a=new Date;a.setDate(a.getDate()+i),document.cookie=t+"="+window.escape(e)+";expires="+a.toGMTString()}catch(t){}},getCookie:function(t){if(0<document.cookie.length){var e=document.cookie.indexOf(t+"=");if(-1!==e){e=e+t.length+1;var i=document.cookie.indexOf(";",e);return-1===i&&(i=document.cookie.length),window.unescape(document.cookie.substring(e,i))}}return""},isIE:function(){var t=navigator.userAgent.toLowerCase();return 0<t.indexOf("rv:11.0")||/msie/.test(t)},isNotEmpty:function(t){return null!==t&&"undefiend"!=typeof t&&""!==t},getScrollWidth:function(){var t,e,i=document.createElement("DIV");return i.style.cssText="position:absolute; top:-1000px; width:100px; height:100px; overflow:hidden;",t=document.body.appendChild(i).clientWidth,i.style.overflowY="scroll",e=i.clientWidth,document.body.removeChild(i),t-e},delayFun:function(a,o,s){var n;return function(){var t=this,e=arguments,i=s&&!n;clearTimeout(n),n=setTimeout(function(){n=null,s||a.apply(t,e)},o),i&&a.apply(t,e)}},tagMessage:function(t,e,i){},getUrlParams:function(t){var e,i={},a=(e=void 0!==t?t:location.search).indexOf("?");if(-1!==a)for(var o=e.substr(a+1).split("&"),s=0;s<o.length;s++)i[o[s].split("=")[0]]=unescape(o[s].split("=")[1]);return i},mouseCoords:function(t){return t.pageX||t.pageY?{x:t.pageX,y:t.pageY}:{x:t.clientX+document.body.scrollLeft-document.body.clientLeft,y:t.clientY+document.body.scrollTop-document.body.clientTop}},getEllipsisChar:function(t,e,i,a){null===$ellipsisCharDiv&&0===($ellipsisCharDiv=_getBody().children("#"+_ellipsis_char_id)).length&&($ellipsisCharDiv=$("<div style='height:"+a+"px;width:"+i+"px;position:absolute;top:0;z-index:0;overflow:auto;top:-1000px;' id='"+_ellipsis_char_id+"'></div>").appendTo(_getBody())),$ellipsisCharDiv.css("font-size",e).html("");for(var o=t.length,s=0,n=$ellipsisCharDiv[0];s<o;s++)if(n.innerHTML=t.substr(0,s),n.scrollHeight>a){n.innerHTML=t.substr(0,s-2);break}return n.innerHTML+"..."},getCharWidth:function(t,e){void 0===e&&(e=$B.config.fontSize),$spen_CharWidth||($spen_CharWidth=$(charSpan.replace(/{id}/,_char_id_)).appendTo(_getBody())),$spen_CharWidth.css({"font-size":e});var i=20;try{$spen_CharWidth.html(this.htmlEncode(t)),i=$spen_CharWidth.outerWidth(),setTimeout(function(){$spen_CharWidth.html("")},1)}catch(t){this.error(t)}return i},getIfrId:function(){return _getBody().children("#_window_ifr_id_").text()},getUUID:function(){return this.generateDateUUID()},generateDateUUID:function(t,e){var i=e||12,a=t||"yyyyMMddhhmmss";return(new Date).format(a)+this.generateMixed(i)},generateMixed:function(t){for(var e=t||6,i=[],a=0;a<e;a++){var o=Math.ceil(35*Math.random());i.push(chars[o])}return i.join("")},htmlLoad:function(){var o=arguments[0];o.target.children().remove(),o.target.html(loadingHtml),"function"==typeof o.preload&&o.preload.call(o.target);var t=o.url;t=0<t.indexOf("?")?t+"&_c_1="+this.generateMixed(5):t+"?_c_1="+this.generateMixed(5),o.target.load(t,o.params,function(t,e,i){if("error"===e)if(t){var a=new RegExp("<body>(.+)</body>","gi").exec(t);2<=a.length?o.target.html(a[1]):o.target.html(t)}else{0<=$B.getHttpHost().indexOf("file:")?o.target.html($B.config.crossError):o.target.html($B.config.htmlLoadError)}else"function"==typeof o.onLoaded&&o.onLoaded.call(o.target)})},getJqObject:function(t){return"string"==typeof t?$(t):t},parseForm:function(t,e){var i=this.getJqObject(t);function s(t,e){var i=t.attr("id");if(i||(i=t.attr("name")),void 0!==i){var a=t.val();return e.add(i,a),i+"="+a}return null}var a=i.find("input[type=text]"),o=i.find("input[type=password]"),n=i.find("textarea"),r=i.find("input[type=radio]"),l=i.find("input[type=checkbox]"),d=i.find("select"),h=i.find("input[type=hidden]"),c=i.find("input[type=file]"),p=this.getHashTable();if($.each(h,function(t,e){s($(e),p)}),$.each(o,function(t,e){s($(e),p)}),$.each(a,function(t,e){s($(e),p)}),$.each(c,function(t,e){s($(e),p)}),$.each(n,function(t,e){s($(e),p)}),$.each(r,function(t,e){p.add($(e).attr("name"),null)}),$.each(r,function(t,e){var i=$(e);i.is(":checked")&&p.joinValue(i.attr("name"),i.val())}),0<l.length){var f={};for(var u in $.each(l,function(t,e){var i=$(e),a=i.attr("name");f[a]||(f[a]=[]),i.is(":checked")&&f[a].push(i.val())}),f)if(this.hasOwnProperty(u)){var g=f[u].join(",");""!==g&&p.add(u,g)}}$.each(d,function(t,e){var i=$(e);if(!0===i.attr("multiple")){var a=[];p.add(i.attr("name"),"");var o=i.children("option[selected]");$.each(o,function(t,e){a.push($(e).value)}),p.joinValue(i.attr("name"),a.join(","))}else s(i,p)});var v=p.getJson();return p.destroy(),e?$.extend(!0,{},e,v):v},bindForm:function(t,n,e){var i=n,s={};if($B.Mvvm){var a=t.find("input[type=text]"),o=function(t,e){var i=t.attr("id");if(i||(i=t.attr("name")),void 0!==n[i]&&e(t,i),0===i.indexOf("old_")){var a=i.replace("old_",""),o=n[a];t.val(o),s[i]=o}};a.each(function(){var t=$(this);o(t,function(t,e){t.hasClass("k_combox_input")?t.attr("watcher","kcomboxWatcher").attr("express","{{this.kcomboxExpress(this.data."+e+",el)}}"):t.hasClass("k_calendar_input")?t.attr("watcher","kcalendarWatcher").attr("express","{{this.kcalendarExpress(this.data."+e+",el)}}"):t.hasClass("k_window_input")?t.attr("watcher","kwindowInputWatcher").attr("express","{{this.kwindowInputExpress(this.data."+e+",el)}}"):t.attr("value","{{this.data."+e+"}}")})}),t.find("input[type=password]").each(function(){var t=$(this);o(t,function(t,e){t.attr("value","{{this.data."+e+"}}")})}),t.find("input[type=hidden]").each(function(){var t=$(this);o(t,function(t,e){t.attr("value","{{this.data."+e+"}}")})}),t.find("textarea").each(function(){var t=$(this);o(t,function(t,e){t.text("{{this.data."+e+"}}")})}),t.find("input[type=radio]").each(function(){var t=$(this),e=t.attr("name"),i=t.parent();i.hasClass("k_radio_label")&&void 0!==n[e]&&i.attr("watcher","kRadioWatcher").attr("express","{{this.kRadioExpress(this.data."+e+",el)}}")}),t.find("input[type=checkbox]").each(function(){var t=$(this),e=t.attr("name"),i=t.parent();i.hasClass("k_checkbox_label")&&void 0!==n[e]&&i.attr("watcher","kcheckBoxWatcher").attr("express","{{this.kcheckBoxExpress(this.data."+e+",el)}}")}),t.find("select").each(function(){var t=$(this),o=t.attr("id");if(n[o]){var s=[];t.children().each(function(){var t=$(this),e=t.val(),i=t.text(),a='<option value="'+e+'" selected="'+("{{this.data."+o+" === '"+e+"' ? true:false}}")+'">'+i+"</option>";s.push(a)}),t.html(s.join(""))}}),t.find("ul.k_tree_root").each(function(){o($(this),function(t,e){t.attr("watcher","ktreeWatcher").attr("express","{{this.ktreeExpress(this.data."+e+",el)}}")})});var r=new $B.Mvvm({el:t[0],data:i,onChanged:e,onGetJson:function(t){$.extend(t,s)}});return window.curMvvm=r}console.log("没有加载mvvm组件")},fillView:function(t,e){for(var i in e)if(this.hasOwnProperty(i)){var a=e[i];null==a&&(a="");var o=t.find("#"+i);0<o.length&&("INPUT"===o[0].tagName?o.value(a):o.text(a))}},resetForm:function(t,e){},simpalSelect:function(s){var n;(n="string"==typeof s.target?$(s.target):s.target).children().remove();var r=null;r=void 0!==s.promptMsg?void 0!==s.promptValue?$("<option  value='"+s.promptValue+"'>"+s.promptMsg+"</option>").appendTo(n):$("<option  value=''>"+s.promptMsg+"</option>").appendTo(n):n,$.isArray(s.data)&&$.each(s.data,function(t,e){var i=e[s.idField],a=!1;$.isArray(s.selected)&&$.each(s.selected,function(t,e){i===e&&(a=!0)});var o=null;(o=a?$("<option value='"+i+"' selected='selected'>"+e[s.textField]+"</option>").appendTo(n):$("<option value='"+i+"' >"+e[s.textField]+"</option>").appendTo(n)).data("data",e),"string"!=typeof s.defaultVal&&"number"!=typeof s.defaultVal||s.defaultVal===i&&(r=o)}),r.attr("selected","selected"),"function"==typeof s.onchange&&n.on("change",function(){var t=$(this).children("option:selected");s.onchange($(t))})},encryptData:function(t,e){if(void 0===window.CryptoJS)return console.log("没有加载 》》 CryptoJS"),t;e||(e=window.SRCUUID);var i=window.CryptoJS.enc.Utf8.parse(e);return window.CryptoJS.DES.encrypt(t,i,{mode:window.CryptoJS.mode.ECB,padding:window.CryptoJS.pad.Pkcs7}).toString()},_toTreeJson:function(c,p,f){var u=this;setTimeout(function(){var t,e,i,a,o,s,n,r,l,d,h=Object.keys(c);for(o=0,s=h.length;o<s;++o){if(t=h[o],e=c[t],i={id:d="_j"+f+o,text:t,data:{}},$.isArray(e))for(i.children=[],0<f&&(i.closed=!0),n=0,r=e.length;n<r;++n)l={id:d+n,text:t+"["+n+"]",data:{}},i.children.push(l),a=e[n],$.isPlainObject(a)?(l.children=[],1<f&&(l.closed=!0),u._toTreeJson(a,l.children,f++)):l.text=l.text+" : "+a;else $.isPlainObject(e)?(i.children=[],1<f&&(i.closed=!0),u._toTreeJson(e,i.children,f++)):i.text=t+" : "+e;p.push(i)}u._createJsonTree()},0)},jsonViewer:function(t,e,i){var a=$("<ul class='k_json_view_root'/>"),o=[];this._toTreeJson(t,o,1),this.$jsonTreeUl=a,this.treeJson=o;var s=this,n=$("<div style='padding:6px 12px;'></div>");n.append("<div><button class='k_icon_fff'><i class='fa fa-docs'></i>"+$B.config.copy+"</button></div>"),this.copyContent=JSON.stringify(t),n.find("button").click(function(t){var e=s.$jsonTreeUl.next("textarea");0<e.length?(e.remove(),s.$jsonTreeUl.show(),$(this).html("<i class='fa fa-docs'></i>"+$B.config.copy)):($(this).html("<i class='fa fa-docs'></i>"+$B.config.recoverCopy),s.$jsonTreeUl.hide(),$("<textarea style='width:100%;height:100%'>"+s.copyContent+"</textarea>").insertAfter(s.$jsonTreeUl))}),n.append(a);var r=$.extend({width:"70%",height:"80%",content:n,onClose:function(){s.$jsonTreeUl.data("treeIns").destroy(),s.$jsonTreeUl=void 0,s.treeJson=void 0,s.copyContent=void 0}},e);this.window(r),this.$jsonTreeUl.html("<li>please waiting.....</li>")},_createJsonTree:function(){clearTimeout(this.jsonViewTimer);var t=this;this.jsonViewTimer=setTimeout(function(){new $B.Tree(t.$jsonTreeUl,{plainStyle:!0,checkbox:!1,data:t.treeJson}),t.treeJson=void 0},300)},decryptData:function(t,e){if(void 0===window.CryptoJS)return t;e||(e=window.SRCUUID);var i=window.CryptoJS.enc.Utf8.parse(e);return window.CryptoJS.DES.decrypt({ciphertext:window.CryptoJS.enc.Base64.parse(t)},i,{mode:window.CryptoJS.mode.ECB,padding:window.CryptoJS.pad.Pkcs7}).toString(window.CryptoJS.enc.Utf8)},getLoadingMask:function(){return $("<div  style='position:absolute;z-index: 2147483645;top:0;left:0;width:100%;height:100%;display:block;'><div id='k_window_mask_bg' style='width:100%;height:100%;position:absolute;top:0;left:0;z-index: 2147483646;display:block'></div><div style='text-align:center;width:100%;height:20px;line-height:20px;position:absolute;top:0;left:0;z-index: **********;background: #EEEEEE;'><i class='fa fa-spin5 animate-spin'></i><span style='padding-left:8px;'>正在处理...</span></div></div>")},_dyFormHclick:function(){var t=$(this),e=t.children("i");e.hasClass("fa-down-open-big")?(e.removeClass("fa-down-open-big").addClass("fa-right-open-big").css("padding-left","5px"),t.next().slideUp(200,function(){$(this).hide()})):(e.removeClass("fa-right-open-big").addClass("fa-down-open-big").css("padding-left","0"),t.next().show().slideDown(200))},_dyFormLoad:function(t,e,i,a,o,r){var s,l,d,h,c,p;s=t,l=e,d=i,h=a,c=o,p=this,$B.request({url:s,ok:function(t,e){delete p.reqQ[h];for(var i=l,a=l.parent();"TABLE"!==a[0].nodeName;)a=a.parent();if(0!==a.parent().length){for(var o=[],s=0,n=e.length;s<n;++s)switch(e[s].checked&&o.push(e[s].id),d){case"select":e[s].selected&&o.push(e[s].id),i.append("<option value='"+e[s].id+"'>"+e[s].text+"</option>");break;case"radio":e[s].checked&&o.push(e[s].id),i.append('<label class="k_radio_label k_radio_anim"><input type="radio" name="'+c+'" value="'+e[s].id+'" /><i class="k_radio_i"></i>'+e[s].text+"</label>");break;case"checkbox":e[s].checked&&o.push(e[s].id),i.append('<label class="k_checkbox_label k_checkbox_anim"><input type="checkbox" name="'+c+'" value="'+e[s].id+'" /><i class="k_checkbox_i"></i>'+e[s].text+"</label>")}0<o.length&&(r.defaultVal=o.join(","))}},final:function(){delete p.reqQ[h]}})},_dyInputWindowClick:function(){var t=$(this).data("params"),e=t.filedName,i=t.url;i=0<i.indexOf("?")?i+"&filed="+e:i+"?filed="+e,window._curDyFiled=e,window._curDyWindow=$B.window({width:"60%",height:"70%",dataType:"html",title:t.title,url:i})},clone:function(t){if($.isArray(t)){for(var e=[],i=0,a=t.length;i<a;++i)e.push($.extend(!0,{},t[i]));return e}return $.extend(!0,{},t)},dyForm:function(t,e,i,a){var o,s,n,r,l,d,h,c,p,f,u,g,v,_,m,b,k,y;clearInterval(this._ivt),t.children().remove(),t.width()<=500&&(y="99%");var x={},w=!1;$.isEmptyObject(i)&&(w=!0);var C=this;window._curDyDataObj=i,this.reqQ={};for(var T=function(t,e,i,a,o){var s={checkbox:t,requestfinal:function(){delete C.reqQ[i]}};"string"==typeof e?(s.url=e,C.reqQ[i]=!0):(delete e.unique,s.data=e),new $B.Tree(a,s)},j=function(t,e,i,a,o,s,n){var r={placeholder:t,mutilchecked:e,checkfather:i,readonly:a,onReqloaded:function(){delete C.reqQ[s]}};"string"==typeof o?(r.url=o,C.reqQ[s]=!0):delete(r.data=o).unique,new $B.Combox(n,r)},O=0,P=e.length;O<P;++O){(o=e[O]).gName&&""!==o.gName&&((r=$(' <h6 style="font-size:14px;padding:4px;font-weight: normal;cursor: pointer;"><i style="padding-right:4px;" class="fa  fa-down-open-big"></i>'+o.gName+"</h6>")).appendTo(t).click(this._dyFormHclick),0<O&&r.css("border-top","1px solid #CCCCCC")),s=o.forms,l=$('<table style="width:100%;" class="form_table k_dy_form_table"></table>');for(var I=0,D=s.length;I<D;++I)switch(f=(n=s[I]).attrs,k=n.ftype,h=n.tip?n.tip:"",c=n.data?n.data:"",b=n.defaultValue,p=n.filedName,n.valid&&""!==n.valid&&(x[p]=n.valid),w&&("label"===k||"hidden"===k||"window"===k||"input"===k||"textarea"===k||"date"===k?i[p]=c:"select"!==k&&"radio"!==k&&"checkbox"!==k&&"combox"!==k&&"tree"!==k||(i[p]=b||"")),(d=$("<tr/>").appendTo(l)).append('<td style="width:60px;text-align: right;padding:2px 2px;">'+n.title+"</td>").width(a),n.ftype){case"label":d.append("<td>"+c+"</td>");break;case"input":d.append('<td><input type="text" id="'+p+'" placeholder="'+h+'" value="'+c+'"/></td>'),g=d.find("input"),f&&g.attr(f),y&&g.width(y);break;case"window":(g=d.append('<td><input class="k_window_input" readonly="readonly" type="text" id="'+p+'"  placeholder="'+h+'" value="'+c+'"/></td>').find("input")).click(this._dyInputWindowClick).data("params",{filedName:p,url:n.url,title:n.title}),y&&g.width(y);break;case"select":if(g=$('<td><select id="'+p+'"  placeholder="'+h+'"/></td>').appendTo(d).children("select"),$.isArray(c))for(v=0,_=c.length;v<_;++v)g.append("<option value='"+c[v].id+"'>"+c[v].text+"</option>");else u=$B.getUUID(),this.reqQ[u]=!0,this._dyFormLoad(c,g,"select",u,p,n);f&&g.attr(f),y&&g.width(y);break;case"textarea":d.append('<td><textarea  id="'+p+'"  placeholder="'+h+'"/>'+c+"</td>"),g=d.find("textarea"),f&&g.attr(f),y&&g.width(y);break;case"radio":if(g=$("<td></td>").appendTo(d),"string"==typeof c)u=$B.getUUID(),this.reqQ[u]=!0,this._dyFormLoad(c,g,"radio",u,p,n);else for(delete c.unique,v=0,_=c.length;v<_;++v)g.append('<label class="k_radio_label k_radio_anim"><input type="radio" name="'+p+'" value="'+c[v].id+'" /><i class="k_radio_i"></i>'+c[v].text+"</label>");break;case"checkbox":if(g=$("<td></td>").appendTo(d),"string"==typeof c)u=$B.getUUID(),this.reqQ[u]=!0,this._dyFormLoad(c,g,"checkbox",u,p,n);else for(delete c.unique,v=0,_=c.length;v<_;++v)g.append('<label class="k_checkbox_label k_checkbox_anim"><input type="checkbox" name="'+p+'" value="'+c[v].id+'" /><i class="k_checkbox_i"></i>'+c[v].text+"</label>");break;case"tree":d.children("td").css("vertical-align","baseline"),g=d.append('<td><ul id="'+p+'"/></td>').find("ul"),u=$B.getUUID(),T(n.mutilChecked,c,u,g);break;case"combox":g=d.append('<td><input type="text" id="'+p+'"  placeholder="'+h+'"/></td>').find("input"),u=$B.getUUID(),j(h,n.mutilchecked,n.checkfather,n.readonly,c,u,g),y&&g.width(y);break;case"date":m={fmt:n.fmt,readonly:!0},""===c&&(m.initValue=new Date),g=d.append('<td><input type="text" id="'+p+'"  placeholder="'+h+'" value="'+c+'"/></td>').find("input"),new $B.Calendar(g,m);break;case"file":console.log("附件上传控件");break;case"time":console.log("待完成....")}l.appendTo(t)}var z,S=1;$.isEmptyObject(C.reqQ)||(z=this.getLoadingMask()).appendTo(t),t.data("min")&&t.children().hide(),C._ivt=setInterval(function(){2e3<S&&(clearInterval(C._ivt),$B.error($B.config.requestError),z&&z.remove()),console.log("waiting for load the datas ....."+JSON.stringify(C.reqQ)),$.isEmptyObject(C.reqQ)&&(clearInterval(C._ivt),z&&z.remove(),$.isEmptyObject(x)||new $B.Validate(x,t),i&&$B.bindForm(t,i,function(t,e,i,a){})),S++},150)},makeNiceScroll:function(t,e){t.css({overflow:"hidden",position:"relative"});var i=$("<div class='k_box_size' style='margin-bottom: -17px; margin-right: -17px;overflow: scroll;height:100%;overflow-x:auto;'></div>").appendTo(t),a=new NiceScroll(i,e);return srcollArray.push(a),setTimeout(function(){a.updateUi()},500),SCROLLCHECKER||(SCROLLCHECKER=setInterval(function(){for(var t=[],e=0,i=srcollArray.length;e<i;++e)srcollArray[e].isLive()&&(srcollArray[e].updateUi(),t.push(srcollArray[e]));srcollArray=t},700)),i}}),$B.bindTextClear=function(t){var e=function(){var t=$(this);t.hasClass("k_combox_input")||(t.on("mouseenter.textbox",TextEvents.mouseover),t.on("input.textbox",TextEvents.input),t.on("mouseleave.textbox",TextEvents.mouseout))};t.find("input[type=text]").each(e),t.find("input[type=password]").each(e)},$(function(){setTimeout(function(){$B.bindTextClear($("body"))},500)}),window.$B=$B,NiceScroll.prototype={onScroll:function(){if(!(this.vscrollSize<=0)){"none"===this.vbar.css("display")&&this.vbar.show();var t=this.jqObj[0].scrollTop;this.posY=t*this.scrollRate,this.vbar.css("top",this.posY)}},show:function(){return clearTimeout(this.hideTimer),this.vbar.show(),this},hide:function(){var t=this;return this.isDraging||(t.hideTimer=setTimeout(function(){t.vbar.hide(50)},800)),this},isLive:function(){for(var t=this.jqObj.parent(),e=!1;0<t.length;){if("BODY"===t[0].tagName){e=!0;break}t=t.parent()}return e},updateUi:function(){var t,e=this.jqObj[0],i=e.scrollHeight,a=e.clientHeight;if(void 0!==this.lastScrollHeight&&this.lastScrollHeight===i&&(t=100*a/i,this.heightPercentage&&t===this.heightPercentage))return this;if(this.lastScrollHeight=i,this.clientHeight=a,0<i){this.heightPercentage=t;var o=a*(this.heightPercentage/100);this.vscrollSize=Math.max(a,i)-a,0<this.vscrollSize&&(this.scrollRate=(a-o)/this.vscrollSize,this.posY=e.scrollTop*this.scrollRate,this.vbar.css({height:this.heightPercentage+"%",top:this.posY}))}else this.vbar.hide();return this}},$B}),function(e,i){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B","plugin","panel","config"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(m,b){var k,y=b.config,x=m.document;function i(t,e){var i=$.extend({},b.config.curdDatagridOpts,e);this.opts=b.config.curdOpts,this.id=i.id,this.curWindow=null,this.idField=i.idField;var a=b.getHttpHost();0<=i.url.indexOf(a)?this.url=i.url:this.url=a+i.url;var o=this.url;this.url&&!/gridid=\w+/.test(this.url)&&(o=0<this.url.indexOf("?")?this.url+"&gridid="+this.id:this.url+"?gridid="+this.id),i.url=o,this.dg=new b.Datagrid(t,i)}return $.extend(b,{window:function(e){var t,i=!0;e.isTop?t=$(m.top.document.body).css("position","relative"):(k||(k=$(m.document.body).css("position","relative")),t=k);var a=parseInt(t.outerWidth()),o=parseInt(t.outerHeight());"string"==typeof e.width&&(0<e.width.indexOf("%")?e.width=a*(parseInt(e.width.replace("%",""))/100):e.width=parseInt(e.width.replace("px",""))),"string"==typeof e.height&&(0<e.height.indexOf("%")?e.height=o*(parseInt(e.height.replace("%",""))/100):e.height=parseInt(e.height.replace("px",""))),void 0!==e.mask&&(i=e.mask),e.full&&(e.width=a,e.height=o,i=!1,e.draggable=!1,e.collapseable=!1,e.maxminable=!1),e.width>a&&(e.width=a),e.height>o&&(e.height=o);var s=(a-e.width)/2,n=(o-e.height)/2;n+=m.pageYOffset||x.documentElement.scrollTop||x.body.scrollTop||0;var r=t.children("#k_window_mask_bg"),l=this.generateMixed(6);if(i){var d=parseInt(t[0].scrollWidth-1),h=parseInt(t[0].scrollHeight-1);0===r.length?r=$("<div style='z-index:218000000;position:abosulte;top:-100000px;width:"+d+"px;height:"+h+"px' for='"+l+"' id='k_window_mask_bg'></div>").appendTo(t):r.css({width:d,height:h}),e.opacity&&r.css("opacity",e.opacity),"none"===r.css("display")&&r.css("top",0).show().attr("for",l)}var c,p,f,u,g=$("<div  id='"+l+"' style='position:absolute;z-index:2190000000;' class='k_window_main_wrap'></div>");e.position?(p=$.isPlainObject(e.position))?g.css(e.position).appendTo(t):"bottom"===e.position?(c=t.css("overflow"),t.css("overflow","hidden"),g.css({bottom:-1200,right:0}).appendTo(t)):g.css({top:-1200,left:s}).appendTo(t):g.css({top:n,left:s}).appendTo(t),e.full&&(e.maxminable=!1);var v={width:e.width?e.width:600,height:e.height?e.height:300,zIndex:**********,title:e.title,closeType:void 0!==e.closeType?e.closeType:"destroy",iconCls:void 0!==e.iconCls?e.iconCls:"fa-window-restore",iconColor:e.iconColor,shadow:void 0===e.shadow||e.shadow,radius:void 0!==e.radius&&e.radius,header:void 0===e.header||e.header,content:e.content,url:e.url?e.url:"",position:e.position,dataType:void 0!==e.dataType?e.dataType:"iframe",draggableHandler:e.draggableHandler,moveProxy:void 0!==e.moveProxy&&e.moveProxy,draggable:void 0===e.draggable||e.draggable,closeable:void 0===e.closeable||e.closeable,expandable:!1,maxminable:void 0===e.maxminable||e.maxminable,collapseable:void 0===e.collapseable||e.collapseable,onResized:"function"==typeof e.onResized?e.onResized:void 0,onLoaded:"function"==typeof e.onLoaded?e.onLoaded:void 0,onStartDrag:"function"==typeof e.onStartDrag?e.onStartDrag:void 0,onDrag:"function"==typeof e.onDrag?e.onDrag:void 0,onStopDrag:"function"==typeof e.onStopDrag?e.onStopDrag:void 0,toolbar:e.toolbar?e.toolbar:void 0,toolbarAlign:e.toolbarAlign?e.toolbarAlign:"center",onClose:function(){var t=!0;return"function"==typeof e.onClose&&(t=e.onClose()),t&&clearTimeout(f),t},onClosed:function(){r.attr("for")===this.attr("id")&&r.hide(),"function"==typeof e.onClosed&&e.onClosed()}};if(e.createToolsFn&&(v.createToolsFn=e.createToolsFn),e.fixed&&(v.expandable=!1,v.collapseable=!1,v.maxminable=!1),v=$.extend({},y.winDefOpts,v),e.timeout&&e.timeout<5&&(v.closeable=!1),(u=new b.Panel(g,v)).$header&&u.$header.addClass("k_window_header_wrap"),e.timeout&&(f=setTimeout(function(){"destroy"===v.closeType?u.destroy():u.close(!0)},1e3*e.timeout)),e.position&&!p)if("bottom"===e.position)g.show(),g.show().animate({bottom:0},300,function(){t.css("overflow",c)});else{var _=$(x).scrollTop()+1;g.show().animate({top:_},300)}return u},message:function(t){var e,i,a,o="fa-mail-alt";"function"==typeof t.message?"string"!=typeof(i=t.message())&&(e=i.children(".k_window_content_icon")):"string"==typeof t&&(t={message:t},2===arguments.length&&(t.timeout=arguments[1])),i=$('<div class="k_window_content_wrap clearfix"><div class="k_window_content_icon"></div><p class="k_box_size k_window_content_p">'+t.message+"</p></div>");var s=t.contentIcon?t.contentIcon:"fa-chat-empty",n=$("<i class='fa "+s+"'>​</i>").appendTo(i.children(".k_window_content_icon"));t.iconColor&&n.css("color",t.iconColor),delete t.iconColor,e=i.children(".k_window_content_icon"),a=t.position,t.iconCls&&(o=t.iconCls);var r={width:400,height:200,position:a,title:y.messageTitle,iconCls:o,shadow:!0,timeout:t.timeout?t.timeout:0,mask:!0,draggableHandler:"header",radius:!1,header:!0,content:i,draggable:!0,closeable:!0,expandable:!1,maxminable:!1,collapseable:!1};$.extend(r,t);var l=$.extend(r,t),d=i.children("p");e&&(l.onResized=function(){var t=d.height()+"px";e.children("i").css({"line-height":t,height:t,display:"block"})});var h,c=this.window(l),p=i.height(),f=e?e.outerWidth():0,u=d.outerWidth()+f,g=i.width()-u;0<g&&(e&&0<e.length?e.css("margin-left",g/2):d.css("margin-left",g/2));var v=d.outerHeight();if(p<v)h=v-p+20+l.height,c.resize({height:h});else{var _=parseInt(d.css("padding-top").replace("px"));d.css("margin-top",(p-v)/2-_/2),e&&e.children("i").css({"line-height":p+"px",height:p,display:"block"})}return c},success:function(t){var e={width:400,height:180,title:y.successTitle,iconCls:" fa-check",shadow:!0,timeout:0,mask:!0,contentIcon:"fa-ok-circled",iconColor:"#3BB208",draggableHandler:"header"};return"string"==typeof t?(e.message=t,2===arguments.length&&(e.timeout=arguments[1])):$.extend(e,t),this.message(e)},alert:function(t){var e={width:400,height:150,title:y.warnTitle,iconCls:"fa-attention-circled",shadow:!0,timeout:0,mask:!0,contentIcon:"fa-attention-1",iconColor:"#BFBC03",draggableHandler:"header"};return"string"==typeof t?(e.message=t,2===arguments.length&&(e.timeout=arguments[1])):$.extend(e,t),this.message(e)},error:function(t){var e={width:400,height:150,title:y.errorTitle,iconCls:"fa-attention-1",shadow:!0,timeout:0,mask:!0,contentIcon:"fa-cancel-circled",iconColor:"#F7171C",draggableHandler:"header"};return"string"==typeof t?(e.message=t,2===arguments.length&&(e.timeout=arguments[1])):$.extend(e,t),this.message(e)},confirm:function(t){var i,e={width:400,height:150,title:y.confirmTitle,iconCls:"fa-question",shadow:!0,timeout:0,mask:!0,contentIcon:"fa-help-1",draggableHandler:""},a=t.okFn,o=t.noFn;t.buttonColor&&t.buttonColor,delete t.okFn,delete t.noFn,delete t.buttonColor;var s=t.okIcon?t.okIcon:"fa-ok-circled",n=t.okText?t.okText:y.buttonOkText,r=t.noIcon?t.noIcon:"fa-reply-all",l=t.noText?t.noText:y.buttonCancleText;return delete t.okIcon,delete t.okText,delete t.noIcon,delete t.noText,"string"==typeof t?(e.message=t,2===arguments.length&&(e.timeout=arguments[1])):$.extend(e,t),"function"==typeof t.createToolsFn?e.createToolsFn=t.createToolsFn:e.createToolsFn=function(){var t=$("<div class='k_confirm_buttons_wrap'></div>").appendTo(this);$("<button class='yes'><i  class='fa "+s+"'>​</i>"+n+"</button>").appendTo(t).click(function(){var t=!0;if("function"==typeof a){var e=a();void 0!==e&&(t=e)}t&&i.close(!0)}),$("<button class='no'><i  class='fa "+r+"'>​</i>"+l+"</button>").appendTo(t).click(function(){var t=!0;if("function"==typeof o){var e=o();void 0!==e&&(t=e)}t&&i.close(!0)});return t},i=this.message(e)}}),i.prototype={constructor:i,getDataGrid:function(){return this.dg},openInner:function(t){this.dg.openInner(t)},window:function(t,e){if($.isPlainObject(t.params)){for(var i=[],a=Object.keys(t.params),o=0,s=a.length;o<s;++o)i.push(a[o]+"="+t.params[a[o]]);t.params=i.join("&")}if(e){var n;if(t.rowData)n=t.rowData[this.idField];else{var r=this.dg.getCheckedId();if(0===r.length){var l=void 0===t.message?b.config.need2CheckedData:t.message;return void b.alert(l)}if(1<r.length)return void b.alert(b.config.onlyGetOneData);n=r[0]}void 0!==t.params&&""!==t.params?t.params=t.params+"&id="+n:t.params="id="+n,t.iconCls||(t.iconCls="fa-edit")}var d=$.extend({},b.config.curdWinDefOpts,t),h=d.pageName&&""!==d.pageName?d.pageName:"form";return d.url=this.url.replace(/\?\S+/,"").replace(/list/,b.config.curdOpts.page_action)+"/"+h,d.params&&""!==d.params&&(d.url=d.url+"?"+d.params),delete d.pageName,delete d.params,this.curWindow=b.window(d),this.curWindow},setOpenWindow:function(t){this.curWindow=t},close:function(){null!==this.curWindow&&this.curWindow.close&&this.curWindow.close(),this.curWindow=null},closeWindow:function(){this.close()},add:function(t,e){var i=this.url.replace(/\?\S+/,"").replace(/list/,b.config.curdOpts.add_action);this._save(t,i,e)},update:function(t,e){var i=this.url.replace(/\?\S+/,"").replace(/list/,b.config.curdOpts.update_action);this._save(t,i,e)},_save:function(t,e,a){var i,o=this;(i=!t.data||void 0===t.url&&"function"!=typeof t.fail&&"function"!=typeof t.ok&&"final"!=typeof t.ok?{data:t,ok:function(t,e,i){"function"==typeof a&&a(i),o.close(),o.reload({page:1})},fail:function(t,e){"function"==typeof a?a(e):b.error(t)}}:t).url=e,b.request(i)},deleteData:function(t,e){this._del({},t,e)},delChecked:function(t){var e=this.dg.getCheckedId();this._del(t,e)},_del:function(t,s,n){if(0!==s.length){var r=this;b.confirm({message:b.config.confirmDelete,okFn:function(){var i=r.dg.getRowCount()===s.length,t=r.url.replace(/\?\S+/,"").replace(/list/,b.config.curdOpts.del_action),e={idList:s.join(",")};n&&$.extend(e,n);var a=b.window({fixed:!0,position:"top",header:!1,width:220,height:35,content:'<p style="line-height:30px;margin-top:2px;text-align:center;"><i style="color:#1296DB" class="fa fa-spin5 animate-spin"></i><span style="padding-left:12px;color:#1296DB">'+b.config.processing+"</span></p>"}),o={url:t,data:e,ok:function(t,e){r.close(),i?r.reload({page:1}):r.refresh()},final:function(){setTimeout(function(){a.close()},10)}};b.request(o)}})}else{var e=t&&void 0!==t.message?t.message:b.config.need2CheckForDel;b.alert(e)}},getCheckedData:function(){return this.dg.getCheckedData()},getCheckedId:function(){return this.dg.getCheckedId()},getData:function(){return this.dg.getCheckedData(!0)},query:function(t){},get:function(t){},reload:function(t){this.dg.reload(t)},refresh:function(){this.dg.refresh()}},b.CURD=i,b.getCURD=function(t,e){return new i(t,e)},b}),function(e,i){"function"==typeof define&&define.amd?define(["$B"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(t,p){function f(t){var e={header:$("<h6 class='k_accordion_header k_box_size'>"+t.name+"</h6>").appendTo(this.jqObj),body:$("<div class='k_accordion_body k_box_size'></div>").appendTo(this.jqObj).hide()};return e.header.data("title",t.name),t.url&&""!==t.url?e.body.data("url",t.url).data("type",t.type?t.type:"iframe"):t.content&&e.body.append(t.content),"function"==typeof this.opts.onCreate&&this.opts.onCreate.call(e.body,t.name),t.icon&&""!==t.icon&&e.header.prepend("<i style='padding-right:4px' class='fa "+t.icon+"'></i>"),e}function u(t){var e=$(this),i=e.data("index"),a=e.data("_this");if(i!==a.actived.index){var o=0,s=a.actived.body,n=a.items[i].body,r=n.css("box-sizing"),l=n.show().outerHeight();a.actived.header.removeClass("k_accordion_header_actived").children(".k_accordion_header_icon").children("i").removeClass(a.opts.iconClsAct).addClass(a.opts.iconCls),a._retoreStyle(a.actived.header),a.items[i].header.addClass("k_accordion_header_actived").children(".k_accordion_header_icon").children("i").removeClass(a.opts.iconCls).addClass(a.opts.iconClsAct),a.actived=a.items[i],a._setActivedStyle(),s.animate(a.hideProps,{duration:void 0,easing:void 0,step:function(t,e){e.now=Math.round(t)}}),n.hide().animate(a.showProps,{duration:void 0,easing:void 0,complete:function(){a._onOpened()},step:function(t,e){e.now=Math.round(t),"height"!==e.attr?"content-box"===r&&(o+=e.now):"content"!==a.opts.heightStyle&&(e.now=Math.round(l-s.outerHeight()-o),o=0)}})}}function g(t,e){p.extend(this,g),this.jqObj=t.addClass("k_box_size k_accordion_main_wrap"),this.opts=$.extend(!0,{heightStyle:"auto",iconCls:"fa-angle-double-right",iconClsAct:"fa-angle-double-down",iconPositon:"right",iconColor:"#666666",fontStyle:{"font-size":"14px","font-weight":"bold",color:"#666666"},activedStyle:{background:"#71B9EA",color:"#FFFFFF",iconColor:"#FFFFFF"},accordionStyle:{background:"#F6F6F6",border:"1px solid #C5C5C5"}},e),void 0!==this.opts.width&&this.jqObj.outerWidth(this.opts.width),void 0!==this.opts.height?this.jqObj.outerHeight(this.opts.height):this.jqObj.outerHeight(this.jqObj.parent().height()),this.hideProps={borderTopWidth:"hide",borderBottomWidth:"hide",paddingTop:"hide",paddingBottom:"hide",height:"hide"},this.showProps={borderTopWidth:"show",borderBottomWidth:"show",paddingTop:"show",paddingBottom:"show",height:"show"},this.items={};var i=this;this.actived=void 0,this.bodyHeight=0;var a=this.jqObj.height(),o="14";"left"===this.opts.iconPositon&&(o="0");for(var s=0,n=this.opts.items.length;s<n;++s){var r=this.opts.items[s],l=f.call(this,r);l.header.css(this.opts.fontStyle).css(this.opts.accordionStyle),l.body.css("border",this.opts.accordionStyle.border).css("border-top","none").attr("_title",l.name),l.index=s;var d=l.header.outerHeight();if(a=a-d-parseInt(l.header.css("margin-top").replace("px","")),(this.items[s]=l).header.click(u).data("index",s).data("_this",i),this.opts.iconCls&&""!==this.opts.iconCls){var h=$("<div style='margin-left:"+o+"px;margin-right:12px;float:"+this.opts.iconPositon+";' class='k_accordion_header_icon'><i class='fa "+this.opts.iconCls+"'></i></div>");("boolean"==typeof r.actived&&r.actived||0===s)&&(this.actived=l),h.appendTo(l.header).css("color",this.opts.iconColor);var c=(d-h.height())/2;h.css("margin-top",c)}}this.bodyHeight=a,this.actived.header.addClass("k_accordion_header_actived"),this.actived.body.outerHeight(this.bodyHeight).show(),this.actived.header.children(".k_accordion_header_icon").children("i").removeClass(this.opts.iconCls).addClass(this.opts.iconClsAct),Object.keys(this.items).forEach(function(t){i.items[t].body.outerHeight(i.bodyHeight)}),this._setActivedStyle(),this._onOpened()}return g.prototype={_onOpened:function(){"function"==typeof this.opts.onOpened&&this.opts.onOpened.call(this.actived.body,this.actived.header.data("title"));var i=this.opts,t=this.actived.body.data("url");if(t&&(t=0<t.indexOf("?")?t+"&_t_="+p.generateMixed(5):t+"?_t_="+p.generateMixed(5))&&0===this.actived.body.children().length){var a=this.actived.body.data("type"),o=this.actived.body,e=$("<div style='padding-left:16px;'><i class='fa fa-cog fa-spin fa-1.6x fa-fw margin-bottom'></i>"+p.config.loading+"</div>").appendTo(o);if("html"===a)p.htmlLoad({target:o,url:t,loaded:function(){e.remove(),"function"==typeof i.onLoaded&&i.onLoaded.call(o,a),p.bindTextClear(o)}});else if("iframe"===a){var s=$("<iframe  frameborder='0' style='overflow:visible' scrolling='auto' width='100%' height='100%' src='' ></iframe>").appendTo(o.css("overflow","hidden")),n=s[0],r=p.getUUID();s.attr({name:r,id:r}),s.on("load",function(){e.remove();try{$(this.contentDocument.body).append("<span id='_window_ifr_id_' style='display:none'>"+r+"</span>")}catch(t){}"function"==typeof i.onLoaded&&i.onLoaded.call(o)}),n.src=t}else{var l={async:!0,url:t,ok:function(t,e){"function"==typeof i.onLoaded&&i.onLoaded.call(o,a,e)},final:function(t){e.remove()}};this.ajax(l)}}},_retoreStyle:function(t){t.css(this.opts.fontStyle).css("background",this.opts.accordionStyle.background).children("i").css("color",this.opts.iconColor),t.children().children("i").css("color",this.opts.iconColor)},_setActivedStyle:function(){this.actived.header.css(this.opts.activedStyle).children("i").css("color",this.opts.activedStyle.iconColor),this.actived.header.children().children("i").css("color",this.opts.activedStyle.iconColor),this.actived.body.css(this.opts.accordionStyle)}},p.Accordion=g,p.Accordion}),function(e,i){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B","panel"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(t,c){var p=$(t.document),f={animationSpeed:10,animationEasing:"swing",change:null,control:"hue",defaultValue:"",letterCase:"lowercase",opacity:!0,update2Target:!0,showCaret:!1,position:"bottom left",positionFix:void 0,onfocus:null,onStartFn:null,onEndFn:null,onHideFn:null,theme:"default",buttons:["transparent","#ffffff","#000000","#aaaaaa","#CCCCCC","#dddddd","#FF0505","#DE0D4F","#FC05A1","#F558E8","#1803FF","#633AE0","#A8A8FA","#BECEFA","#03DE24","#00EBE3","#8EDB98","#DBE803","#D8E062","#E8EAAD","#FAF0E6","#A020F0","#990033","#FFC0CB","#428BCA","#5CB85C","#5BC0DE","#8B6D38","#0376F0","#878F97","#6C757D","#F7F4F8","#F3DEDE","#303133","#E4E7ED","#E1F3D8"]};function w(t,e,i){return t<e&&(t=e),i<t&&(t=i),t}function C(t){return e=function(t){var e={},i=Math.round(t.h),a=Math.round(255*t.s/100),o=Math.round(255*t.b/100);if(0===a)e.r=e.g=e.b=o;else{var s=o,n=(255-a)*o/255,r=i%60*(s-n)/60;360===i&&(i=0),i<60?(e.r=s,e.b=n,e.g=n+r):i<120?(e.g=s,e.b=n,e.r=s-r):i<180?(e.g=s,e.r=n,e.b=n+r):i<240?(e.b=s,e.r=n,e.g=s-r):i<300?(e.b=s,e.g=n,e.r=n+r):i<360?(e.r=s,e.g=n,e.b=s-r):(e.r=0,e.g=0,e.b=0)}return{r:Math.round(e.r),g:Math.round(e.g),b:Math.round(e.b)}}(t),i=[e.r.toString(16),e.g.toString(16),e.b.toString(16)],$.each(i,function(t,e){1===e.length&&(i[t]="0"+e)}),"#"+i.join("");var e,i}function b(t){var e=function(t){var e=t,i={h:0,s:0,b:0},a=Math.min(e.r,e.g,e.b),o=Math.max(e.r,e.g,e.b),s=o-a;i.b=o,i.s=0!==o?255*s/o:0,0!==i.s?e.r===o?i.h=(e.g-e.b)/s:e.g===o?i.h=2+(e.b-e.r)/s:i.h=4+(e.r-e.g)/s:i.h=-1;i.h*=60,i.h<0&&(i.h+=360);return i.s*=100/255,i.b*=100/255,i}(i(t));return 0===e.s&&(e.h=360),e}function i(t){return{r:(t=parseInt(-1<t.indexOf("#")?t.substring(1):t,16))>>16,g:(65280&t)>>8,b:255&t}}function T(t,e){var i,a;return t.length&&e?(i=t.offset().left,a=t.offset().top,{x:i-e.offset().left+t.outerWidth()/2,y:a-e.offset().top+t.outerHeight()/2}):null}function a(t){var e,i,a,o,s,n,r,l=t.hex,d=t.opacity,h=this.jqObj,c=this.opts,p=h.find(".k_minicolors_swatch"),f=h.find(".k_minicolors_grid"),u=h.find(".k_minicolors_slider"),g=h.find(".minicolors_opacity_slider"),v=f.find("[class$=_picker]"),_=u.find("[class$=_picker]"),m=g.find("[class$=_picker]");switch(l||(n=function(t,e){if(void 0===(t=t.replace(/[^A-F0-9]/gi,""))||""===t)return"";var i,a=t.length,o=6-a;for(o&&(i=t[a-1]);0<o;)t+=i,o--;return"#"+t}(c.defaultValue),l="uppercase"===c.letterCase?n.toUpperCase():n.toLowerCase()),e=b(l),c.opacity&&(isNaN(d)&&(d=1),p.find("SPAN").css("opacity",d),a=w(g.height()-g.height()*d,0,g.height()),m.css("top",a+"px")),p.find("SPAN").css("backgroundColor",l),c.control){case"wheel":o=w(Math.ceil(.75*e.s),0,f.height()/2),s=e.h*Math.PI/180,i=w(75-Math.cos(s)*o,0,f.width()),r={top:(a=w(75-Math.sin(s)*o,0,f.height()))+"px",left:i+"px"},a=150-e.b/(100/f.height()),""===l&&(a=0),_.css("top",a+"px"),u.css("backgroundColor",C({h:e.h,s:e.s,b:100}));break;case"saturation":i=w(5*e.h/12,0,150),r={top:(a=w(f.height()-Math.ceil(e.b/(100/f.height())),0,f.height()))+"px",left:i+"px"},a=w(u.height()-e.s*(u.height()/100),0,u.height()),_.css("top",a+"px"),u.css("backgroundColor",C({h:e.h,s:100,b:e.b})),h.find(".minicolors-grid-inner").css("opacity",e.s/100);break;case"brightness":i=w(5*e.h/12,0,150),r={top:(a=w(f.height()-Math.ceil(e.s/(100/f.height())),0,f.height()))+"px",left:i+"px"},a=w(u.height()-e.b*(u.height()/100),0,u.height()),_.css("top",a+"px"),u.css("backgroundColor",C({h:e.h,s:e.s,b:100})),h.find(".minicolors-grid-inner").css("opacity",1-e.b/100);break;default:i=w(Math.ceil(e.s/(100/f.width())),0,f.width()),r={top:(a=w(f.height()-Math.ceil(e.b/(100/f.height())),0,f.height()))+"px",left:i+"px"},a=w(u.height()-e.h/(360/u.height()),0,u.height()),_.css("top",a+"px"),f.css("backgroundColor",C({h:e.h,s:100,b:100}))}v.css(r)}function v(t,e){var i,a,o,s,n,r,l,d,h=t.attr("data-opacity"),c=this.jqObj,p=this.opts,f=c.find(".k_minicolors_swatch"),u=c.find(".k_minicolors_grid"),g=c.find(".k_minicolors_slider"),v=c.find(".k_minicolors_opacity_slider"),_=u.find("[class$=_picker]"),m=g.find("[class$=_picker]"),b=v.find("[class$=_picker]"),k=T(_,u),y=T(m,g),x=T(b,v);if(t.val&&(d=t.val()),e.is(".k_minicolors_grid, .k_minicolors_slider"))switch(p.control){case"wheel":s=u.width()/2-k.x,n=u.height()/2-k.y,r=Math.sqrt(s*s+n*n),(l=Math.atan2(n,s))<0&&(l+=2*Math.PI),75<r&&(r=75,k.x=69-75*Math.cos(l),k.y=69-75*Math.sin(l)),a=w(r/.75,0,100),d=C({h:i=w(180*l/Math.PI,0,360),s:a,b:o=w(100-Math.floor(y.y*(100/g.height())),0,100)}),g.css("backgroundColor",C({h:i,s:a,b:100}));break;case"saturation":d=C({h:i=w(parseInt(k.x*(360/u.width()),10),0,360),s:a=w(100-Math.floor(y.y*(100/g.height())),0,100),b:o=w(100-Math.floor(k.y*(100/u.height())),0,100)}),g.css("backgroundColor",C({h:i,s:100,b:o})),c.find(".k_minicolors_grid_inner").css("opacity",a/100);break;case"brightness":d=C({h:i=w(parseInt(k.x*(360/u.width()),10),0,360),s:a=w(100-Math.floor(k.y*(100/u.height())),0,100),b:o=w(100-Math.floor(y.y*(100/g.height())),0,100)}),g.css("backgroundColor",C({h:i,s:a,b:100})),c.find(".k_minicolors_grid_inner").css("opacity",1-o/100);break;default:d=C({h:i=w(360-parseInt(y.y*(360/g.height()),10),0,360),s:a=w(Math.floor(k.x*(100/u.width())),0,100),b:o=w(100-Math.floor(k.y*(100/u.height())),0,100)}),u.css("backgroundColor",C({h:i,s:100,b:100}))}e.is(".k_minicolors_opacity_slider")&&(h=p.opacity?parseFloat(1-x.y/v.height()).toFixed(2):1,p.opacity&&t.attr("data-opacity",h)),f.find("SPAN").css({backgroundColor:d,opacity:h}),j.call(this,d,h,"#000000")}function j(t,e,i){-1<t.indexOf("rgb")&&(t=t.toHexColor());var a=this.target,o=this.opts,s=a.data("minicolors-lastChange"),n={color:this.getContrastColor(t),"background-color":t,filter:"alpha(opacity="+100*e+")","-moz-opacity":e,"-khtml-opacity":e,opacity:e};this.values=n,o.update2Target&&this.target.css(n),"INPUT"===this.target[0].tagName&&this.target.val(t),s&&s.hex===t&&s.opacity===e||(a.data("minicolors-lastChange",{hex:t,opacity:e}),"transparent"!==t&&(this.currentColor=t),o.change&&o.change.call(a,t,e))}function u(t,e,i){if(this.target){var a,o,s,n,r=this.target,l=this,d=this.opts,h=t.find("[class$=_picker]"),c=t.offset().left,p=t.offset().top,f=Math.round(e.pageX-c),u=Math.round(e.pageY-p),g=i?d.animationSpeed:0;return e.originalEvent.changedTouches&&(f=e.originalEvent.changedTouches[0].pageX-c,u=e.originalEvent.changedTouches[0].pageY-p),f<0&&(f=0),u<0&&(u=0),f>t.width()&&(f=t.width()),u>t.height()&&(u=t.height()),t.parent().is(".k_minicolors_slider_wheel")&&h.parent().is(".k_minicolors_grid")&&(a=75-f,o=75-u,s=Math.sqrt(a*a+o*o),(n=Math.atan2(o,a))<0&&(n+=2*Math.PI),75<s&&(f=(s=75)-75*Math.cos(n),u=75-75*Math.sin(n)),f=Math.round(f),u=Math.round(u)),t.is(".k_minicolors_grid")?h.stop(!0).animate({top:u+"px",left:f+"px"},g,d.animationEasing,function(){v.call(l,r,t)}):h.stop(!0).animate({top:u+"px"},g,d.animationEasing,function(){v.call(l,r,t)}),!1}}function g(t,e){c.extend(this,g),this.lastColors=[],this.colorCookieName="colorpicker_";var i,a,o=this;function s(){o.target.removeData("minicolors-lastChange"),o.buttonClick($(this).css("background-color"))}this.opts=$.extend(!0,{},f,e),t&&this.setTarget(t),this.jqObj=$('<div tabindex="0" class="k_minicolors k_box_shadow" />').addClass("k_minicolors_theme_"+this.opts.theme).toggleClass("k_minicolors_with_opacity",this.opts.opacity).appendTo($("body")),this.jqObj.append('<div tabindex="0" class="k_minicolors_panel  k_minicolors_slider_'+this.opts.control+'"><div tabindex="0" class="k_minicolors_slider"><div class="k_minicolors_picker"></div></div><div tabindex="0"  class="k_minicolors_opacity_slider"><div  tabindex="0"  class="k_minicolors_picker"></div></div><div  tabindex="0" class="k_minicolors_grid"><div  tabindex="0"  class="k_minicolors_grid_inner"></div><div tabindex="0" class="k_minicolors_picker"><div  tabindex="0" ></div></div></div></div>'),this.opts.showCaret&&$("<div style='height:12px;width:100%;margin-top:-12px;padding-left:2px'><i style='font-size:16px;color:#3E4043;line-height:12px;position:relative;top:1px' class='fa fa-up-dir'></i></div>").prependTo(this.jqObj),this.isBodyClick=!0,this.jqObj.children(".k_minicolors_panel").mousedown(function(){p.data("_colorpicker_",o),p.on("mousedown.k_minicolors touchstart.k_minicolors",".k_minicolors_grid, .k_minicolors_slider, .k_minicolors_opacity_slider",function(t){var e=$(this),i=p.data("_colorpicker_");p.data("k_minicolors_target",e),u.call(i,e,t,!0),i.opts.onStartFn&&i.opts.onStartFn()}).on("mousemove.k_minicolors touchmove.k_minicolors",function(t){var e=p.data("_colorpicker_"),i=p.data("k_minicolors_target");i&&u.call(e,i,t)}).on("mouseup.k_minicolors touchend.k_minicolors",function(t){var e=p.data("_colorpicker_");e&&(p.data("k_minicolors_target")&&($(this).removeData("k_minicolors_target"),e.opts.onEndFn&&e.opts.onEndFn()),e.isBodyClick=!1,setTimeout(function(){e.isBodyClick=!0},300))})}).mouseup(function(){p.off("mousedown.k_minicolors touchstart.k_minicolors",".k_minicolors_grid, .k_minicolors_slider, .k_minicolors_opacity_slider"),setTimeout(function(){p.removeData("_colorpicker_")},300)}),this.colorsBtnWrap=$("<div  tabindex='0'  class='k_minicolors_color_buttons'></div>").appendTo(this.jqObj);for(var n=0,r=this.opts.buttons.length;n<r;++n){var l=$("<div  tabindex='0' style='background-color:"+this.opts.buttons[n]+"'></div>").appendTo(this.colorsBtnWrap).click(s);0===n&&l.addClass("k_mincolors_btn_nonecolor")}this.opts.onfocus&&this._bindFocus(this.jqObj),this.opts.mouseenter&&this.jqObj.mouseenter(this.opts.mouseenter),this.opts.clickHide&&(i=o,a=c.getUUID(),$("body").on("click."+a,function(){i.isBodyClick?i.hide():$(this).off("."+a)}));var d=c.getCookie(this.colorCookieName);if(""!==d){var h=d.split(",");this.lastColors=h,this._setLastColors()}}return g.prototype={constructor:g,getContrastColor:function(t){if(!t)return"rgb(105, 118, 166)";var e=t;return t.toLowerCase().indexOf("rgb")<0&&(e=i(t)),e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,["rgb(",e.r,",",e.g,",",e.b,")"].join("")},_bindFocus:function(t){var e=this;t.on("focus",function(t){e.opts.onfocus()}),t.children().each(function(){e._bindFocus($(this))})},setTarget:function(t){return this.target=t.css("background-image","none").addClass("k_color_picker_cls"),this.target.data("color_picker_fire")||(this.target.on("click.colorpicker",function(t){var e=$(this);if(!e.data("break")){var i=e.data("picker");i.isShow()?i.hide():i.show()}}).data("picker",this),this.target.data("color_picker_fire",!0)),this},unbindTarget:function(){return this.target&&(this.target.removeData("color_picker_fire"),this.target.removeData("picker"),this.target.off("click.colorpicker").removeClass("k_color_picker_cls"),this.target=void 0),this},setPosition:function(t){return t.top=t.top+this.target.outerHeight(),this.opts.positionFix&&(this.opts.positionFix.top&&(t.top=t.top+this.opts.positionFix.top),this.opts.positionFix.left&&(t.left=t.left+this.opts.positionFix.left)),this.jqObj.css(t).show(),this},hideImd:function(t){t&&this.unbindTarget(),this.jqObj.hide(),this.opts.onHideFn&&this.opts.onHideFn()},hide:function(t){if(this.currentColor){-1<this.currentColor.indexOf("rgb")&&(this.currentColor=this.currentColor.toHexColor()),this.currentColor=this.currentColor.toLowerCase();var e,i=0,a=!0;for(i=0,e=this.lastColors.length;i<e;++i)if(this.currentColor===this.lastColors[i]){a=!1;break}a&&(5===this.lastColors.length&&this.lastColors.pop(),this.lastColors.unshift(this.currentColor),this._setLastColors())}this.currentColor=void 0,clearTimeout(this.hidetimer);var o=this;return this.hidetimer=setTimeout(function(){t&&o.unbindTarget(),o.jqObj.slideUp(100,function(){o.opts.onHideFn&&o.opts.onHideFn()})},100),this},_setLastColors:function(){for(var t=this.colorsBtnWrap.children().last(),e=0,i=this.lastColors.length;e<i;++e)t.css("background-color",this.lastColors[e]),t=t.prev();var a=this.lastColors.join(",");c.writeCookie(this.colorCookieName,a,3)},show:function(t,e){if(!this.target||!t||this.target[0]!==t[0]){clearTimeout(this.showtimer);var i=this;t&&(t.data("break",!0),i.setTarget(t));var a=i.target.offset();return e&&(e.top&&(a.top=a.top+e.top),e.left&&(a.left=a.left+e.left)),this.showtimer=setTimeout(function(){i.setPosition(a),i.target.removeData("break")},200),this}},isShow:function(){return"block"===this.jqObj.css("display")},buttonClick:function(t){(j.call(this,t,1,void 0),"string"==typeof t)&&(-1<t.toLowerCase().indexOf("rgb")&&(t=t.toHexColor()),a.call(this,{hex:t}));return this},setValue:function(t,e){return"string"==typeof t&&(-1<t.toLowerCase().indexOf("rgb")&&(t=t.toHexColor()),t={hex:t}),a.call(this,t),this.target&&(void 0===e||e)&&j.call(this,t.hex,1,void 0),this},getValue:function(){return this.values}},c.ColorPicker=g}),function(e,i){"function"==typeof define&&define.amd?define(["$B","tree"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(d,u){var t;function i(){return t||(t=$(d.document.body).css("position","relative")),t}var h={data:[],default:{id:"",text:u.config.comboxPlaceholder},mutilChecked:!0,checkfather:!1,onlyNodeData:!0,tree2list:!0,disabled:!1,initShow:!1,isTreeData:!0,placeholder:u.config.comboxPlaceholder,plainStyle:!0,extParamFiled:[],search:void 0,localSearchFiled:["text"],url:"",readonly:!0,textField:"text",idField:"id",onCheck:null,onClick:null};function c(t,e){u.extend(this,c);var n=this;if(this.jqObj=t.addClass("k_combox_input k_box_size").wrap("<div class='k_combox_input_wrap'></div>"),setTimeout(function(){n.jqObj.off("mouseover.textbox"),n.jqObj.off("mouseout.textbox"),n.jqObj.off("input.textbox")},500),this.jqObj.attr("autocomplete","off"),this.iptWrap=this.jqObj.parent(),this.width=this.jqObj.outerWidth(),this.height=this.jqObj.outerHeight(),0===this.width||0===this.height)var i=setInterval(function(){n.width=n.jqObj.outerWidth(),n.height=n.jqObj.outerHeight(),0===n.width&&0===n.height||(clearInterval(i),n.size.width=n.width,n.size.height=n.height-1,n.icon.css("left",n.size.width-12),n.dropList.children("ul").css("min-width",n.width),n._fixLeftTop())},200);this.opts=$.extend({},h,e);var r=this.opts.onClick;this.opts.onClick=function(t,e){var i=t[0],a=i.data;if(!n.opts.mutilChecked)if(!1===n.opts.checkfather&&e.isParent);else{var o=i[n.opts.idField],s=i[n.opts.textField];void 0===o&&(o=a[n.opts.idField]),s||(s=a[n.opts.textField]),n.jqObj.val(s).data("id",o),"function"==typeof n.onWatcher&&(n.jqObj.data("no2update",!0),n.onWatcher.call(n),setTimeout(function(){n.jqObj.removeData("no2update")},1)),n._fireValidFn()}n.hide(function(){"function"==typeof r&&setTimeout(function(){try{r(a)}catch(t){n.error("onClick is error "+t.message)}},1)})},this._onCheckFn=this.opts.onCheck,this.opts.onCheck=function(t,e,i){n._setInputValue(!0),"function"==typeof n._onCheckFn&&setTimeout(function(){n._onCheckFn.call(n,t,e,i)},1),"function"==typeof n.onWatcher&&n.onWatcher.call(n)},this.opts.checkbox=!0===this.opts.mutilChecked,this.size={width:this.jqObj.outerWidth(),height:this.jqObj.outerHeight()-1},n.opts.isTree||(this.opts.plain=!0),this.body=$(document.body).css("position","relative");var a=$(d),o=u.getUUID();a.on("resize."+o,function(){n.repositon?n.repositon():$(this).off("."+o)}),a.on("click."+o,function(){n.hide?n.hide():$(this).off("."+o)});var s,l=this.jqObj.css("border-color");this.dropList=$("<div class='k_box_size k_combox_wrap' style='z-index: **********;position:abosulte;top:-100000px;min-width:"+this.size.width+"px;border-color:"+l+";'><ul></ul></div>").appendTo(this.body).hide(),this.dropList.data("direction","d").data("combox",this),this.jqObj.on({click:function(){return"none"===n.dropList.css("display")?n.show():n.hide(),!1}}),this.opts.disabled&&this.jqObj.attr("disabled","disabled"),this.jqObj.attr("placeholder",this.opts.placeholder),this.jqObj.val(this.opts.default.text).data("id",this.opts.default.id),this._createTree(),setTimeout(function(){n.repositon()},1),!0===this.opts.readonly&&this.jqObj.attr("readonly","readonly").css("cursor","pointer"),this.icon=$("<div style='top:4px;left:"+(this.width-12)+"px;background:none;' class='k_tree_combox k_combox_input_icon btn'><i style='font-size:14px;float:right;padding-right:3px;font-size:12px;margin-top:5px;' class='fa fa-down-dir'></i></div>").appendTo(this.iptWrap),this.icon.click(function(){return n.opts.disabled||n.jqObj.trigger("click"),!1}),this.initing=!0,!this.opts.readonly&&this.opts.search&&this.jqObj.on("input",function(){if(n.tree&&!n.initing){var t=$.trim(n.jqObj.val().replace(/'/g,""));clearTimeout(s),s=u.isUrl(n.opts.url)&&"remote"===n.opts.search?setTimeout(function(){n.tree.reload(n.dropList.children("ul"),{keyword:t})},500):setTimeout(function(){n._localSearch(t)},500)}}),this.jqObj.data("combox",this),this.jqObj.mouseenter(function(){var t=$(this);t.attr("title",t.val())})}return c.prototype={constructor:c,_setInputValue:function(t){var s,n,r,l=this;if(l.opts.checkbox){var e={onlyChild:!l.opts.checkfather};s=l.tree.getCheckedData(e);var i=function(){for(var t=[],e=[],i=0,a=s.length;i<a;++i){var o=s[i];n=o.id,r=o.text,!n&&o.data&&(n=o.data[l.opts.idField]),!r&&o.data&&(r=o.data[l.opts.textField]),t.push(n),e.push(r)}l.jqObj.val(e.join(",")).data("id",t.join(",")),l._fireValidFn()};t?i():(clearTimeout(l._setInputValueTimer),l._setInputValueTimer=setTimeout(i,100))}else this.tree.clickedItem?(s=this.tree.clickedItem.data("data"),n=s.id,r=s.text,n&&r||(s.data&&!$.isEmptyObject(s.data)&&(s=s.data),n||(n=s[l.opts.idField]),r||(r=s[l.opts.textField])),l.jqObj.val(r).data("id",n)):this.jqObj.val(this.opts.default.text).data("id",this.opts.default.id),l._fireValidFn()},_fireValidFn:function(){var t=this;setTimeout(function(){t.jqObj.trigger("mouseleave.kvalidate")},200)},_localSearch:function(t){var e,i,a,o,s,n;"none"===this.dropList.css("display")&&this.show(),this.tree.jqObj.children("._nodata_tip").remove();var r,l,d=this.tree.childNodesArray,h=this.tree.parentNodesArray,c=0;for(e=0,i=h.length;e<i;++e)s=(o=h[e].show()).children("div"),c<(r=parseInt(s.attr("deep")))&&(c=r),"none"===o.children("ul").css("display")&&(0===(n=s.children("._line_node_")).length&&(n=s.children("._node_")),n.trigger("click"));for(e=0,i=d.length;e<i;++e){var p=(a=d[e]).children("div").data("data");this._isLike(p,t)?a.show():a.hide()}for(this._fixLeftTop(),c++;0<c;){for(l=[],e=0,i=h.length;e<i;++e)s=(o=h[e].show()).children("div"),0===o.children("ul").height()?o.hide():l.push(o);h=l,c--}if(0===this.tree.jqObj.height()){var f=$("<li class='_nodata_tip'>"+u.config.noData+"</li>").appendTo(this.tree.jqObj);setTimeout(function(){try{f.remove()}catch(t){}},3e3)}},_isLike:function(t,e){if(""===e)return!0;for(var i=!1,a=0,o=this.opts.localSearchFiled.length;a<o;a++){var s=this.opts.localSearchFiled[a];if(t[s]&&0<=t[s].indexOf(e)){i=!0;break}if(!i&&t.data[s]&&0<=t.data[s].indexOf(e)){i=!0;break}}return i},_createTree:function(){var i=this;0===this.opts.data.length&&""!==this.opts.url?(this.jqObj.val(""),this.jqObj.attr("placeholder",u.config.loading),this.ajax({url:this.opts.url,ok:function(t,e){i.opts.data=t,i._bindTree()},fail:function(t){},final:function(t){i.jqObj.val(i.opts.default.text).data("id",i.opts.default.id),i.jqObj.attr("placeholder",u.config.comboxPlaceholder),i.opts.onReqloaded&&i.opts.onReqloaded(t)}})):this._bindTree()},_isFined:function(t,e){for(var i=!0,a=0,o=e.length;a<o;++a){if(e[a].id===t){i=!1;break}if(e[a].children&&!(i=this._isFined(t,e[a].children)))break}return i},_bindTree:function(){if(!$.isEmptyObject(this.opts.default)&&(this.opts.default.data||(this.opts.default.data={}),""!==this.opts.default.text&&this._isFined(this.opts.default.id,this.opts.data))){var t={};t[this.opts.idField]=this.opts.default.id,t[this.opts.textField]=this.opts.default.text,t.data=this.opts.default.data,this.opts.data.unshift(t)}var e=this.opts.search&&"local"===this.opts.search;this.opts.isLocalSearch=e;var i=this;this.opts.onTreeCreated=function(){i.initing&&(i._setInputValue(!0),i.opts.initShow?i.show():i.hide()),i.initing=!1,i.opts.onCreatedCall&&i.opts.onCreatedCall()},this.opts.onToggle=function(){i._fixLeftTop()},this.opts.onloaded=function(){i._setInputValue()},this.tree=new u.Tree(this.dropList.children("ul"),this.opts),this.opts.mutilChecked||setTimeout(function(){i.tree.setClickedItem(i.opts.default.id)},1e3)},repositon:function(){var t=this.body.width(),e=this.body[0].scrollHeight,i=this.jqObj.offset(),a=t-i.left,o=e-i.top-this.height;i.top;this.dropList.data("direction","d");var s=i.top+this.size.height;this.dropList.css({top:s,overflow:"auto",left:i.left,"max-width":a,"max-height":o})},_fixLeftTop:function(){var t=this.jqObj.offset(),e=t.top+this.size.height;this.dropList.css({top:e,left:t.left})},reset:function(){this._setBreakHide(),this.jqObj.val(this.opts.default.text).data("id",this.opts.default.id),this.tree.reset()},_setBreakHide:function(){var t=this;this._breakHide=!0,setTimeout(function(){t._breakHide=!1},300)},getCheckedData:function(){return this._setBreakHide(),this.tree?this.opts.mutilChecked?this.tree.getCheckedData():this.tree.getClickItem():[]},getCheckedIds:function(){return this._setBreakHide(),this.tree?this.opts.mutilChecked?this.tree.getCheckedData({onlyId:!0}):this.jqObj.data("id"):[]},hide:function(e){this._breakHide||("d"===this.dropList.data("direction")?this.dropList.slideUp(200,function(){"function"==typeof e&&e();var t=this;i().children(".k_combox_wrap").each(function(){t!==this&&$(this).data("combox")._fixLeftTop()})}):(this.dropList.hide(),"function"==typeof e&&e()),this.icon&&this.icon.children().addClass("fa-down-dir").removeClass("fa-up-dir").css("margin-top","5px"))},show:function(){var e=this;this.repositon(),"d"===this.dropList.data("direction")?this.dropList.slideDown(200,function(){e._fixLeftTop();var t=this;i().children(".k_combox_wrap").each(function(){t!==this&&$(this).data("combox")._fixLeftTop()})}):this.dropList.css({display:"block",top:0}),this.icon&&this.icon.children().removeClass("fa-down-dir").addClass("fa-up-dir").css("margin-top","5px")},val:function(){return this.jqObj.data("id")},setCheckDatas:function(t){if(this.tree){var e=this;if(0<this.tree.running)return void setTimeout(function(){e.setCheckDatas(t)},150);this.opts.mutilChecked?this.tree.setCheckDatas(t):this.tree.setClickedItem(t),this._setInputValue(!0),"function"==typeof this.onWatcher&&this.onWatcher.call(this)}},destroy:function(){this.dropList.remove(),this.super.destroy.call(this)}},u.Combox=c}),function(e,i){"function"==typeof define&&define.amd?define(["$B"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(t,i){var e,a=t.document;function o(){return e||(e=$(a.body).css("position","relative")),e}var s=!1,n=!0;function r(t,e){s=!0,i.extend(this,r),this.$body=o();var p=this;this.$body.css("position","relative").on("click._k_c_menu",function(t){return p.$body.children(".k_context_menu_container").remove(),!1}),1===arguments.length?(this.jqObj=this.$body,this.opts=t):(this.jqObj=t,this.opts=e),this.opts.push({text:i.config.closeLable,iconCls:"fa-minus",click:function(){p.$body.children(".k_context_menu_container").remove()}});var f=function(h,c){var t=$('<div class="k_context_menu_item_cls"><span class="k_context_menu_item_ioc"><i class="k_ctxmenu_i fa '+h.iconCls+'"></i></span><span class="k_context_menu_item_txt">'+h.text+"</span></div>").appendTo(c).on({click:function(){var t=$(this);t.parent().remove(),h.click&&setTimeout(function(){h.click.call(t)},10)}});h.items?(t.append('<span class="k_context_menu_more_ioc"><i class="fa fa-angle-double-right k_ctxmenu_i"></i></span>'),t.mouseenter(function(){var t,e,i=$(this),a=(i.data("items"),c.data("submenu")),o=i.offset(),s=o.left+i.outerWidth()+3,n=o.top;a?((e=(t=a).data("submenu"))&&(e.remove(),t.removeData("submenu")),a.children().remove(),a.css({left:s+"px",top:n+"px"}).show()):(a=$("<div style='position:absolute;z-index:99999999999999999999;height:auto;' class='k_context_menu_container k_box_shadow k_box_radius'/>").appendTo(p.$body).css({left:s+"px",top:n+"px"}),c.data("submenu",a));for(var r=0,l=h.items.length;r<l;++r){var d=h.items[r];f(d,a)}}).data("items",h.items)):t.mouseenter(function(){$(this);var t=c.data("submenu");t&&t.hide()})};this.mousedown=function(t){var e=t.clientX,i=t.clientY;p.$body.children(".k_context_menu_container").remove();var a=$("<div style='position:absolute;z-index:-1000;height:auto;' class='k_context_menu_container k_box_shadow k_box_radius'/>").appendTo(p.$body).css({left:e+"px",top:i+"px"});$.each(p.opts,function(){f(this,a)});var o=a.outerWidth(),s=p.$body.width()-e-o,n={"z-index":**********};s<0&&(n.left=e+s),a.css(n)},this.jqObj.on({mousedown:function(t){3===t.which&&(n=!1,p.mousedown(t),setTimeout(function(){n=!0},300))},mouseup:function(){return!1}}),p.$body.on("contextmenu",function(t){return!1})}return t._ctxmenu_close_fn=function(){s&&n&&o().children(".k_context_menu_container").hide()},$(t).on("mouseup._ctxmenu_close_fn",function(){s&&t._ctxmenu_close_fn(),setTimeout(function(){t.parent!==t.self&&t.parent._ctxmenu_close_fn&&t.parent._ctxmenu_close_fn()},10)}),r.prototype={bandTarget:function(t){var e=this;t.on({mousedown:function(t){3===t.which&&(n=!1,e.mousedown(t),setTimeout(function(){n=!0},300))},contextmenu:function(t){return!1}})},hide:function(){this.$body.children(".k_context_menu_container").remove()}},i.Ctxmenu=r}),function(e,i){"function"==typeof define&&define.amd?define(["$B","tree","toolbar","pagination"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(B,nt){var t,d;$(document);function h(){return t||(t=$(B.document.body).css("position","relative")),t}var rt={title:"",field:"",width:"auto",rowspan:0,colspan:0,align:"left",sortable:!0,resizable:!1,formatter:null},c={title:"",data:null,url:"",fillParent:!1,loadImd:!0,toolbar:null,oprCol:!0,treeIconColor:void 0,oprColWidth:"auto",cols:[],idField:"id",checkBox:!0,isTree:!1,pgposition:"bottom",pageSize:30,page:1,startPage:1,btnStyle:"plain",showBtnText:!0,pgBtnNum:10,iconCls:void 0,setParams:void 0,splitColLine:"k_datagrid_td_all_line",pageList:[15,20,25,30,35,40,55,70,80,90,100],sortField:void 0,onDbClickRow:null,onClickCell:null,onCheck:null,onRowRender:null,onLoaded:null},lt=25,W={dblclick:function(t){var e=$(this),i=e.data("_this");clearTimeout(i.clickTimer),"function"==typeof i.opts.onDbClickRow&&setTimeout(function(){i.opts.onDbClickRow.call(e)},1)}},dt={mousemove:function(t){var e=$(this);if(e.data("_this").opts.checkBox&&e.position().left<32)return!0;var i=e.offset().left,a=e.width()+i,o=t.pageX;e.css("cursor","default").removeData("splitX"),i-3<=o&&o<=i+3?e.css("cursor","w-resize").data("splitX",o):a-3<=o&&o<=a+3&&e.css("cursor","w-resize").data("splitX",o)},mouseover:function(t){var e=$(this);if(60<e.height()){var i=e.children(".k_datagrid_cell_text");if(!i.data("tipopts")){var a=i.text(),o=nt.getCharWidth(a,i.css("font-size")),s=300;o<s&&(s=o),o>i.width()&&(i.mousetip({maxWidth:h().width()/3,minWidth:s,activedColor:"#A2D8FF"}),i.trigger("mouseenter",{left:t.pageX,top:t.pageY}))}}},mouseout:function(t){},dblclick:function(t){},mousedown:function(t){var e=$(this),l=e.data("_this"),i=e.data("splitX");if(i){var a,o=Object.keys(l.moveXofs),s=l.scrollLeft;i+=s;for(var n=0,r=o.length;n<r;++n){var d=parseInt(o[n]);if(i-6<=d&&d<=i+6){a=l.moveXofs[d];break}}if(a){var h=l.hideTrTdArray.eq(a),c=h.prev(),p=h.data("minWidth"),f=c.data("minWidth"),u={minLeft:c.position().left+f,preTdMinWidth:f,targetTdMinWidth:p,targetTd:h,preTd:c},g=h.position().left;l.splitMovingLine||(l.splitMovingLine=$("<div style='position:absolute;top:0;'  class='k_datagrid_moving_line'></div>").appendTo(l.$scrollWrap),l.splitMovingLine.draggable({axis:"h",cursor:"w-resize",onStartDrag:function(t){var e=t.state,i=e._data,a=e.movingTarget.data("dragParams");i.dragParams=a,l.isDragResize=!0},onDrag:function(t){var e=t.state._data;e.leftOffset<0&&e.left<e.dragParams.minLeft&&(e.left=e.dragParams.minLeft)},onStopDrag:function(t){var e=t.state._data,i=e.leftOffset,a=e.dragParams,o=a.preTd,s=a.targetTd,n=o.outerWidth(),r=s.outerWidth();i<0?(r-=i,(n+=i)<a.preTdMinWidth&&(r=r+(n=a.preTdMinWidth)-f),o.outerWidth(n),s.outerWidth(r)):(n+=i,(r-=i)>=a.targetTdMinWidth&&(o.outerWidth(n),s.outerWidth(r))),e.dragParams=void 0,l._onResize(),l.splitMovingLine.hide(),l.isDragResize=!1},onMouseUp:function(t){l.splitMovingLine.hide()}})),l.splitMovingLine.css("left",g-s).show().trigger("mousedown.draggable",{pageX:t.pageX,pageY:t.pageY,which:t.which}).data("dragParams",u)}}},click:function(t){var e=$(this),i=e.data("_this");i.isDragResize||(clearTimeout(i.clickTimer),i.clickTimer=setTimeout(function(){"function"==typeof i.opts.onClickCell&&i.opts.onClickCell.call(e,e.attr("filed"),e.children("div").text())},200))}};function ht(t){var e,i=$(this),a=i.children(),o=i.parent().data("opt");a.hasClass("fa-down")?(a.removeClass("fa-down").addClass("fa-up"),e="desc"):(a.removeClass("fa-up").addClass("fa-down"),e="asc");var s=t.data.ins;if(""!==s.opts.url){var n="_col_sort_"+o.field,r=$.extend({pageSize:s.pageSize},s.lastParams);r[n]=e,r.page=1,p.call(t.data.ins,function(){v.call(t.data.ins)},r)}return!1}function ct(){$(this).children(".k_datagrid_cell_sort").width(12).slideDown(100)}function pt(t){$(this).children(".k_datagrid_cell_sort").slideUp(100)}function ft(t){var e=$(this),i=e.parent().parent(),a=e.children("i");if(e.hasClass("k_datagrid_chkbox_disabled"))return!1;var o,s=!1;a.hasClass("fa-check-empty")?(o="fa-check",a.removeClass("fa-check-empty fa-ok-squared").addClass(o),s=!0):(o="fa-check-empty",a.removeClass("fa-check fa-ok-squared").addClass(o));var n=e.data("flag"),r=e.data("_this"),l=r.$table.children("tbody").children(),d=[];if(n)l.each(function(){var t=$(this),e=t.children().first().children("div");if(!e.hasClass("k_datagrid_chkbox_disabled")){t.data("isCheck",s);var i=e.children("i");s?i.removeClass("fa-check-empty fa-ok-squared").addClass(o):i.removeClass("fa-check fa-ok-squared").addClass(o),d.push($.extend(!0,{},t.data("data")))}});else{if(i.data("isCheck",s),d.push($.extend(!0,{},i.data("data"))),r.opts.isTree){var h=i.data("treeDeep");if(i.data("isparent"))for(var c=i.next();0<c.length&&c.data("treeDeep")>h;)d.push($.extend(!0,{},i.data("data"))),c.data("isCheck",s),c.children().first().children().children().removeClass("fa-check-empty  fa-check fa-ok-squared").addClass(o),c=c.next()}var p,f=l.length,u=0;l.each(function(){$(this).data("isCheck")&&u++}),p=0===u?"fa-check-empty":u!==f?"fa-ok-squared":"fa-check",r.$header.children().children().last().children().first().children("div").children("i").removeClass("fa-check-empty  fa-check fa-ok-squared").addClass(p)}"function"==typeof r.opts.onCheck&&setTimeout(function(){r.opts.onCheck(s,d)},10)}function q(t){var e,o=t.data._this,i=$(this),s=i.parent().parent().parent(),n=s.data("treeDeep"),a=i.children("i");a.hasClass("fa-folder-open")?(a.removeClass("fa-folder-open").addClass("fa-folder"),e=!0):(a.removeClass("fa-folder").addClass("fa-folder-open"),e=!1),s.data("closed",e);var r,l=s.next(),d=s.data("data");if(!e&&$.isArray(d.children)&&0===d.children.length){var h=i.parent().css("padding-left"),c=$("<tr><td style='padding-left:"+h+"' class='k_box_size' colspan='"+s.children().length+"'><i class='fa fa-cog fa-spin fa-1.6x fa-fw margin-bottom'></i>"+nt.config.loading+"</td></tr>"),p=o.opts;c.insertAfter(s);var f=$.extend({pid:d.data[p.idField]},o.lastParams);"function"==typeof p.setParams&&$.extend(f,o.opts.setParams());var u={async:!0,url:p.url,data:f,ok:function(t,e){0<e.length&&(e.children=e);for(var i=0,a=e.length;i<a;++i)F.call(o,e,s,parseInt(n)+1,!1);p.onLoaded&&setTimeout(function(){p.onLoaded(e)},10)},final:function(t){var e=!1;void 0!==t.data?0===t.data.length&&(e=!0):$.isArray(t)&&0===t.length&&(e=!0),e?(c.children().html("<i style='padding-right:6px;' class='fa fa-info _no_data'></i>"+nt.config.noData),setTimeout(function(){c.remove(),a.removeClass("fa-folder-open").addClass("fa-folder")},1600)):c.remove()}};o.ajax(u)}else{var g,v,_,m=[],b={};for(b[n]=e;0<l.length&&!((g=l.data("treeDeep"))<=n);)e?l.hide():(g===n+1&&l.show(),l.data("isparent")&&(m.push(l),b[g]=l.data("closed"))),l=l.next();for(var k=0,y=m.length;k<y;++k)if(g=(v=m[k]).data("treeDeep"),e=v.data("closed"),_=b[g-1],!e&&!_)for(g+=1,l=v.next();0<l.length&&l.data("treeDeep")===g;)l.show(),l=l.next();o._onResize()}B.getSelection?r=B.getSelection():document.selection&&(r=document.selection.createRange());try{r.removeAllRanges()}catch(t){}return!1}function F(t,e,i,a){for(var o,s,n,r,l=this,d=this.opts.isTree,h=Object.keys(this.titleTDs),c=[],p="TBODY"===e[0].nodeName,f=0,u=t.length;f<u;++f){o=$("<tr/>"),s=t[f],0,o.data("data",s).data("opts",this.opts).data("treeDeep",i),"k_datagrid_td_all_line"!==l.opts.splitColLine&&o.addClass("k_datagrid_old_even_cls");for(var g=0,v=h.length;g<v;++g){var _=l.titleTDs[g],m=_.data("opt"),b=l.colsWidthArray[g],k=b-2;0===f&&c.push(b);var y=m.field?m.field:"",x=$("<td filed='"+y+"' class='k_box_size'></td>");if(x.outerWidth(b),0===g&&x.css("border-left","none"),g===v-1&&x.css("border-right","none"),l.opts.splitColLine&&x.addClass(l.opts.splitColLine),l.opts.checkBox&&0===g)$('<div class="k_box_size k_datagrid_cell_text k_datagrid_cell_chkbox"><i class="fa  fa-check-empty"></i></div>').appendTo(x),r=x.css("text-align","center").data("isCheckBox",!0).children("div").data("data",s).data("_this",l).data("treeDeep",i),d&&s.children&&(r.data("isparent",!0),l.opts.onlyChkChild&&r.addClass("k_datagrid_cell_chkbox_disabled")),r.click(ft).dblclick(function(){return!1});else{var w,C="center",T=_.data("operator");if(T?(w="",x.css("text-align","center")):(w=d?s.data[m.field]:s[m.field],m.align&&(C=m.align),void 0===w&&(w="")),r=$('<div style="text-align:'+C+';" class="k_box_size k_datagrid_cell_text k_mutilline_ellipsis"></div>').appendTo(x),m&&!T&&(n=void 0,"function"==typeof m.formatter?n=m.formatter.call(x,w,s,m.field):"function"==typeof B[m.formatter]&&(n=B[m.formatter].call(x,w,s,m.field)),n&&(w=n)),r.html(w),r.appendTo(x),x.data("txt",r.text()),d)if(l.opts.checkBox&&1===g||!l.opts.checkBox&&0===g){for(var j,O=i,P=0;0<O;)P+=20,O--;if(0===P&&(P=2),r.css({"text-align":"left","padding-left":P}),s.children){var I="fa-folder",D=!0;0<s.children.length&&!s.closed&&(D=!(I="fa-folder-open")),o.data("closed",D),(j=$("<span style='cursor:pointer;'><i style='padding:2px 6px 2px 2px;cursor:pointer;' class='fa "+I+"'></i></span>").prependTo(r)).on("click",{_this:l},q).dblclick(function(){return!1}),j=j.children()}else j=$("<i style='padding:2px 6px 2px 2px;' class='fa fa-doc'></i>").prependTo(r);l.opts.treeIconColor&&j.css("color",l.opts.treeIconColor)}if(T){var z=s.toolbar;z&&(delete s.toolbar,l.rowToolbars.push(new nt.Toolbar(r,{context:o,params:s,align:"center",style:l.opts.btnStyle,showText:l.opts.showBtnText,methodsObject:l.opts.methodsObject,fontSize:"16",iconColor:nt.config.rowBtnColor,buttons:z})),x.data("isOpr",!0),x.data("oprCount",z.length))}else x.on(dt).data("opts",l.opts).data("field",m.field).data("_this",l)}x.children(".k_datagrid_cell_text").outerWidth(k),o.append(x),1}if(p?e.append(o):o.insertAfter(e),o.on(W).data("_this",l),l.opts.onRowRender&&l.opts.onRowRender.call(o,s,f),d&&(a&&o.hide(),s.children)){var S=s.children.length;o.data("isparent",!0).data("childrens",S).data("checked",0),0<S&&(void 0!==a&&a&&(s.closed=a),F.call(this,s.children,p?e:o,i+1,s.closed))}}}function v(){var t,e,o=this;e=this.opts.isTree?this.opts.data:this.opts.data.resultList;for(var i=0,a=this.rowToolbars.length;i<a;++i)this.rowToolbars[i].destroy();if(o.rowToolbars=[],!this.$toolwrap)if(this.$toolwrap=[],t=o.showToolbar?"block":"none",this.$title){var s=$("<div style='display:"+t+"' class='k_datagrid_toolbar_wrap k_box_size clearfix'></div>").insertAfter(this.$title);this.$toolwrap.push(s)}else this.$toolwrap.push($("<div  style='display:"+t+"'  class='k_datagrid_toolbar_wrap k_box_size clearfix'></div>").prependTo(this.jqObj));if(!this.opts.isTree){for(var n=0,r=o.pgList.length;n<r;++n)o.pgList[n].clear();o.pgList=[];var l={height:25,page:this.opts.data.currentPage,total:this.opts.data.totalSize,pageSize:this.opts.pageSize,buttons:this.opts.pgBtnNum?this.opts.pgBtnNum:10,startpg:this.opts.startPage>this.opts.data.currentPage?this.opts.data.currentPage:this.opts.startPage,position:"right",summary:!0,onClick:function(t,e,i){o.opts.startPage=i,o.opts.page=t,o.opts.pageSize=e,o.pageSize=e;var a=$.extend(o.lastParams,{page:t,pageSize:e});p.call(o,function(){v.call(o)},a)}};if("both"===this.opts.pgposition||"top"===this.opts.pgposition){var d=this.$toolwrap[0].children("k_datagrid_pagination_top_wrap");0===d.length&&(d=$("<div class='k_datagrid_pagination_top_wrap' style='float:right'></div>").appendTo(this.$toolwrap[0])),o.pgList.push(new nt.Pagination(d,l))}if("both"===this.opts.pgposition||"bottom"===this.opts.pgposition){if(1===this.$toolwrap.length){var h="";o.opts.fillParent&&(h="k_datagrid_tool_tottom_border"),this.$toolwrap.push($("<div class='k_datagrid_toolbar_wrap k_box_size "+h+"'></div>").appendTo(this.jqObj))}l.position="left",o.pgList.push(new nt.Pagination(this.$toolwrap[1],l))}}if(0<e.length){this.$table.children("tbody").remove();var c=$("<tbody/>");this.updateColsWidthArray(),F.call(this,e,c,0),c.children().first().children().each(function(){$(this).css("border-top","none")}),this.$table.append(c),o._onResize()}}function p(t,i){B.$curTip_&&(B.$curTip_.hide(),B.$curTip_=void 0);var o=this,a=this.opts;if(""!==a.url){this.titleCheckBox&&this.titleCheckBox.removeClass("fa-check fa-ok-squared").addClass("fa-check-empty"),"function"==typeof a.setParams&&$.extend(i,this.opts.setParams());var e=this.$bodyWrap.parent().parent();if(!this.$mask){var s=nt.config.loading,n="<div style='width:"+(nt.getCharWidth(s)+20)+"px;z-index:**********;' class='k_datagrid_loading'><i class='fa fa-cog fa-spin fa-1.6x fa-fw margin-bottom'></i>"+s+"</div>";this.$mask=$("<div class='k_datagrid_load_mask'><div style='display:block;' class='k_model_mask'></div>"+n+"</div>").appendTo(e)}var r=0;this.$scrollWrap.prevAll().each(function(){r+=$(this).outerHeight()}),r+=this.$headWrap.outerHeight();var l=this.$bodyWrap.height(),d=!1;if(0===l&&(this.$bodyWrap.css("padding-bottom",40),d=!0),this.$mask.children(".k_datagrid_loading").css("margin-top",r+10),this.$mask.show().fadeIn(100),o.lastParams=i,this.opts.sortField)for(var h,c=Object.keys(this.opts.sortField),p=0,f=c.length;p<f;++p){var u="_col_sort_"+(h=c[p]);i[h]||i[u]||(i[u]=this.opts.sortField[h])}var g={async:!0,url:a.url,data:i,ok:function(t,e){o.opts.data=e,o.opts.data.currentPage=i.page,o.opts.data.totalSize=e.totalSize,o.opts.pageSize=i.pageSize,v.call(o),a.onLoaded&&setTimeout(function(){a.onLoaded(e)},10),nt.isIE()&&o._fixIE()},final:function(t){var e=!1;if(void 0!==t.data?(o.opts.isTree&&0===t.data.length||!t.data.totalSize)&&(e=!0):(o.opts.isTree&&$.isArray(t)&&0===t.length||!t.totalSize)&&(e=!0),e){o.$table.children("tbody").remove();var i=o.$bodyWrap.width()-2,a=$("<tbody><tr><td style='text-align:center;width:"+i+"px' class='k_box_size' colspan='"+o.$header.children().children().first().children().length+"'><i style='padding-right:6px;' class='fa fa-info  _no_data'></i>"+nt.config.noData+"</td></tr></tbody>");o.$table.append(a)}d&&o.$bodyWrap.css("padding-bottom",0),o.$mask.fadeOut(200,function(){o.$mask.hide()})}};this.ajax(g)}}function f(t,e){nt.extend(this,f),this.opts=$.extend({},c,e),$.isArray(this.opts.cols[0])||(this.opts.cols=[this.opts.cols]),d||(d=nt.getScrollWidth()),this.tdTip=h().children("#k_datagrid_td_tip"),0===this.tdTip.length&&(this.tdTip=$("<div id='k_datagrid_td_tip' style='width:auto;min-width:150px;display:none;top:-1000px;' class=' k_box_shadow'><p></p></div>").appendTo(h())),this.showToolbar=!(!$.isArray(this.opts.toolbar)||0===this.opts.toolbar.length),this.showToolbar||(this.showToolbar="top"===this.opts.pgposition||"both"===this.opts.pgposition),this.page=this.opts.page,this.pageSize=this.opts.pageSize,t.addClass("k_datagrid_table_body"),this.jqObj=$('<div class="k_datagrid_main_wrap k_box_size"><div class="k_datagrid_scroll_wrap k_box_size"><div class="k_datagrid_head_wrap k_box_size"></div><div class="k_datagrid_body_wrap k_box_size">'+t[0].outerHTML+"</div></div></div>"),this.$bodyWrap=this.jqObj.find(".k_datagrid_body_wrap"),this.$scrollWrap=this.jqObj.find(".k_datagrid_scroll_wrap"),this.$headWrap=this.jqObj.find(".k_datagrid_head_wrap"),this.$table=this.$bodyWrap.find("table"),this.jqObj.insertAfter(t),t.remove(),this.pgList=[],this.rowToolbars=[],this.$bodyWrap.addClass("k_datagrid_body_border_fix");var i=this;$.isArray(this.opts.toolbar)&&0<this.opts.toolbar.length&&function(){this.$toolwrap||(this.$toolwrap=[],this.$toolwrap.push($("<div class='k_datagrid_toolbar_wrap k_box_size'></div>").prependTo(this.jqObj)));for(var t=0,e=this.$toolwrap.length;t<e;++t)if(this.$toolwrap[t].children().remove(),0<this.opts.toolbar.length){var i={style:this.opts.btnStyle,showText:!0,methodsObject:this.opts.methodsObject,buttons:this.opts.toolbar};nt.config.toolbarOpts&&$.extend(i,nt.config.toolbarOpts),new nt.Toolbar(this.$toolwrap[t],i)}else this.$toolwrap[t].hide()}.call(this),this.opts.title&&""!==this.opts.title&&(this.$title=$("<div class='k_datagrid_head_title k_box_size'></div>").prependTo(this.jqObj),this.$title.append("<h6>"+this.opts.title+"</h6>").appendTo(this.$title),""!==this.opts.iconCls&&this.$title.children().prepend('<i class="fa '+this.opts.iconCls+'"></i>&nbsp'));var a,o=!1;if(""!==this.opts.url){for(var s=0,n=this.opts.pageList.length;s<n;++s)if(this.opts.pageList[s]===this.opts.pageSize){o=!0;break}o||this.opts.pageList.push(this.opts.pageSize)}if(function(){var a=this;this.titleTDs={},this.$header=$("<table></table>");for(var t,e,i,o,s,n,r,l,d,h=$("<tbody></tbody>").appendTo(this.$header),c=[],p={},f=a.opts.cols,u=[],g=[],v=[],_=a.opts.checkBox?1:0,m=f.length-1,b=a.opts.checkBox,k=function(t){var e=0;return t.children().each(function(){var t=$(this).attr("colspan");e+=t?parseInt(t):1}),e},y=""!==a.opts.url,x=0,w=f.length;x<w;++x){t=f[x],e=$("<tr>"),i=0;var C=x===m;C&&(i=t.length-1);for(var T=0,j=t.length;T<j;++T){""===(o=C?$.extend({},rt,t[T]):t[T]).width?o.width="auto":o.width&&!$.isNumeric(o.width)&&"auto"!==o.width&&(o.width=parseFloat(o.width.replace("px",""))),s=o.colspan?'colspan="'+o.colspan+'"':"",n=o.rowspan?'rowspan="'+o.rowspan+'"':"",r=$("<td "+n+" "+s+" class='k_box_size'></td>"),0===x&&r.css("border-top","none"),0===T&&r.css("border-left","none"),T===j-1&&r.css("border-right","none"),$("<div class='k_box_size k_datagrid_cell_text'></div>").appendTo(r).append("<div style='width:100%' class='k_datagrid_title_text k_box_size'>"+o.title+"</div>"),""!==n&&g.push(r),o.sortable&&y&&(l=$("<div  class='k_datagrid_cell_sort k_box_size'><i class='k_datagrid_i_icon fa'></i></div>").appendTo(r).on("click",{ins:a},ht),"desc"===a.opts.sortOrder?l.children("i").addClass("fa-down"):l.children("i").addClass("fa-up"),r.mouseenter(ct),r.mouseleave(pt),v.push(r)),r.outerWidth(o.width).on(dt).data("_this",a);var O=k(e);if(r.data("index",{r:x,c:O}).data("opt",o),x===m){var P=_+T;this.titleTDs[P]=r}var I=p[O];if(I&&I.span&&x<=I.rowIdx&&(r.hide(),u.push(r),x===m)){var D=I.td.data("opt"),z=r.data("opt"),S=$.extend({},z,D);delete S.rowspan,delete S.colspan,I.td.data("opt",S),I.td.outerWidth(S.width),d=_+T,this.titleTDs[d]=I.td}o.rowspan&&(p[O]={span:!0,rowIdx:x+o.rowspan-1,td:r}),e.append(r)}c.push(e)}for(var B=0,W=u.length;B<W;++B)u[B].remove();for(var q,F,A,M=0,H=c.length;M<H;++M){if(q=M===H-1,b&&((F=$("<td style='border-left:none;' class='k_box_size' ><div style='width:"+(lt-2)+"px' class='k_box_size k_datagrid_cell_chkbox'></div></td>")).data("opt",{width:lt}),F.outerWidth(lt),c[M].prepend(F),i+=1),a.opts.oprCol&&0===M){var L="auto";a.opts.oprColWidth&&(L=a.opts.oprColWidth),A=$("<td rowspan='"+H+"' class='k_box_size'  style='border-right:none;'><div  class='k_box_size k_datagrid_cell_text'><div class='k_datagrid_title_text k_box_size'>"+nt.config.oprColName+"</div></div></td>"),c[M].append(A.outerWidth(L)),g.push(A),d=i+1,(this.titleTDs[d]=A).data("operator",!0).data("opt",{width:L,isOpr:!0})}h.append(c[M]),b&&q&&(F=c[M].children().first().children().append("<i class='fa  fa-check-empty'></i>").click(ft).data("flag",!0).data("_this",a),this.titleTDs[0]=F.parent(),F.data("opt",{width:lt}),this.titleCheckBox=F.children("i")),a.opts.onHeadRender&&a.opts.onHeadRender.call(c[M])}for(var E,U,N,R,V,Y,J,X,K,Q,G=this.$headWrap.width(),Z=$("<tr style='height:0;'/>"),tt=[],et=Object.keys(this.titleTDs),it=0,at=0,ot=et.length;at<ot;++at)E={height:0,"border-top":"none","border-bottom":"none"},U=this.titleTDs[et[at]],0===at&&(E["border-left"]="none"),at===ot-1&&(E["border-right"]="none"),N=(X=U.data("opt")).width,R=$("<td class='k_datagrid_header_hide_td'/>").css(E).appendTo(Z).outerWidth(N).data("i",at).data("opt",X),"auto"===N?(Y=U.find(".k_datagrid_title_text").css("font-size"),V=void 0,(J=X.minWidth)&&""!==J?($.isNumeric(J)||(J=parseInt(J.replace("px",""))),V=J):V=nt.getCharWidth(X.title,Y)+16,it+=V,tt.push(R)):(V=nt.getCharWidth(X.title,Y)+16,it+=$.isNumeric(N)?N:parseFloat(N.replace("px",""))),R.data("minWidth",V),U.data("minWidth",V),U.outerWidth("auto");if(G<it)for(K=0,Q=tt.length;K<Q;++K)tt[K].outerWidth(tt[K].data("minWidth"));this.autoTdArray=tt,this.minTableWidth=parseInt(it+1),Z.prependTo(this.$header),this.$header.appendTo(this.$headWrap);var st=this.$header.outerWidth();if(this.$header.width()<it&&this.$header.width(it),0<G-st)for(this.$header.outerWidth("100%"),K=0,Q=tt.length;K<Q;++K)(r=tt[K]).outerWidth()<r.data("minWidth")&&r.outerWidth(r.data("minWidth"));this.hideTrTdArray=Z.children(),this.updateColsWidthArray(),Z.children().eq(_),setTimeout(function(){a._setColPosition();var t,e=nt.isIE();for(K=0,Q=v.length;K<Q;++K){var i=(r=v[K]).children(".k_datagrid_cell_sort");t=e?r.outerHeight()-1:r.height(),i.height(t)}},300)}.call(this),i._fillParent(),null!==this.opts.data)setTimeout(function(){v.call(i)},0);else if(""!==this.opts.url&&this.opts.loadImd){var r={page:1,pageSize:this.opts.pageSize};p.call(i,function(){v.call(i)},r)}this.showToolbar||""!==this.opts.title||this.jqObj.css("border-top","none");var l=nt.getUUID();$(B).on("resize."+l,function(){i._onResize?(i._onResize(),clearTimeout(a),a=setTimeout(function(){i._onResize()},200)):$(this).off("."+l)}),this.scrollLeft=0;this.$bodyWrap.on("scroll",function(){i._hideToolBtnList();var t=$(this).scrollLeft();i.$header.css("left",-t),i.scrollLeft=t})}return f.prototype={_fixIE:function(){if(!this.hasFixIe&&(this.hasFixIe=!0,1<this.hideTrTdArray.length)){var t=this.hideTrTdArray.eq(1),e=this.hideTrTdArray.eq(0);e.outerWidth(e.outerWidth()+1),t.outerWidth(t.outerWidth()-1),this._onResize()}},_hideToolBtnList:function(){this.opts.oprCol&&h().children("#k_toolbar_drop_wrap").hide()},_fillParent:function(){if(this.opts.fillParent){var t=this.jqObj.parent().height(),e=this.$headWrap.outerHeight();"bottom"!==this.opts.pgposition&&"both"!==this.opts.pgposition||(e+=40),this.$scrollWrap.siblings().each(function(){var t=$(this);t.hasClass("k_datagrid_load_mask")||(e+=t.outerHeight())});var i=t-e;if(this.$bodyWrap.outerHeight(i).css({"overflow-y":"auto"}),parseInt(i/38)>this.opts.pageSize){for(var a,o=0,s=this.opts.pageList.length;o<s;++o)if(this.opts.pageList[o]===this.opts.pageSize){if(o<s-2){a=this.opts.pageList[o+1];break}a=this.opts.pageList[s-1]}a&&(this.opts.pageSize=a,this.pageSize=a)}}},_setColPosition:function(){var t=this.hideTrTdArray.first().next();this.opts.checkBox&&(t=t.next());var e=function(t,e){for(var i={},a=e.scrollLeft;0<t.length;)i[parseInt(t.offset().left+a)]=t.data("i"),t=t.next();return i}(t,this);this.moveXofs=e},updateColsWidthArray:function(){for(var t=[],e=Object.keys(this.titleTDs),i=0,a=e.length;i<a;++i)t.push(this.titleTDs[e[i]].outerWidth());return this.colsWidthArray=t,this.colsWidthArray},_onResize:function(){var a=this,o=!1;this.$table.outerHeight()>this.$bodyWrap.height()&&(o=!0);var t,e,i,s,n=this.$header.parent().width();if(this.$header.data("isFixWidth")){if(n>this.minTableWidth)for(this.$header.removeData("isFixWidth"),e=0,i=this.autoTdArray.length;e<i;++e)(t=this.autoTdArray[e]).css("width","auto")}else if(n<this.minTableWidth)for(this.$header.data("isFixWidth",!0),e=0,i=this.autoTdArray.length;e<i;++e)s=(t=this.autoTdArray[e]).data("minWidth"),t.css("width",s);this.updateColsWidthArray(),this.$table.children("tbody").children().each(function(){var t=$(this).children(),i=t.length;if(1===i&&0<t.find("._no_data").length)return!0;t.each(function(t){var e=a.colsWidthArray[t];o&&t===i-1&&(e-=d),$(this).outerWidth(e).children().outerWidth(e-2)})}),clearTimeout(this._setColPositionTimer),this._setColPositionTimer=setTimeout(function(){a._setColPosition(),a._hideToolBtnList()},100)},getData:function(){return this.opts.data},openInner:function(t,e){if(!t.next().hasClass("tr_inner")){this.innerTr&&this.innerTr.remove();var i=this,a=t.children().length,o=$("<tr class='tr_inner'><td colspan='"+a+"' style='position:relative;'><div class='tr_inner_content'></div></td></tr>").insertAfter(t),s=o.children(),n=s.children();if($("<a style='position:absolute;top:0;right:0;cursor: pointer;' title='"+nt.config.closeLable+"'><i class='fa fa-cancel-2'></i></a>").appendTo(s).click(function(){o.remove(),i.innerTr=void 0}),this.innerTr=o,"html"===e.type)nt.htmlLoad({target:n,url:e.content,loaded:function(){"function"==typeof e.onLoaded&&e.onLoaded.call(n)}});else if("iframe"===e.type){var r=$("<div style='padding-left:16px;'><i class='fa fa-cog fa-spin fa-1.6x fa-fw margin-bottom'></i>"+nt.config.loading+"</div>").appendTo(n),l=$("<iframe  frameborder='0' style='overflow:visible' scrolling='auto' width='100%' height='100%' src='' ></iframe>").appendTo(n.css("overflow","hidden")),d=l[0];l.on("load",function(){r.remove(),"function"==typeof e.onLoaded&&e.onLoaded.call(n)}),d.src=e.content}else n.html(e.content)}},reload:function(t){var e=this;e.lastParams={};var i=$.extend(!0,{page:1,pageSize:this.opts.pageSize},t);p.call(e,function(){v.call(e)},i)},refresh:function(){var t=this;p.call(t,function(){v.call(t)},$.extend({page:1,pageSize:this.opts.pageSize},t.lastParams))},getCheckedData:function(i){var a=[];return this.$table.children("tbody").children().each(function(){var t=$(this);if(t.data("isCheck")||i){var e=$.extend(!0,{},t.data("data"));delete e.children,delete e.toolbar,a.push(e)}}),a},getCheckedId:function(t){var i=[],a=this;return this.$table.children("tbody").children().each(function(){var t,e=$(this);e.data("isCheck")&&(t=a.opts.isTree?e.data("data").data[a.opts.idField]:e.data("data")[a.opts.idField],i.push(t))}),t?i.join(";"):i},getRowCount:function(){return this.$table.children("tbody").children().length}},nt.Datagrid=f}),function(e,i){"function"==typeof define&&define.amd?define(["$B"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(t,f){var u="<iframe  class='panel_content_ifr' frameborder='0' style='overflow:visible;display:block;vertical-align:top;' scroll='none'  width='100%' height='100%' src='' ></iframe>",g="<div class='k_box_size' style='position:absolute;z-index:2147483600;width:100%;height:26px;top:2px;left:0;' class='loading'><div class='k_box_size' style='filter: alpha(opacity=50);-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;position:absolute;top:0;left:0;width:100%;height:100%;z-index:2147483600;background:"+f.config.loadingBackground+";'></div><div class='k_box_size' style='width:100%;height:100%;line-height:26px;padding-left:16px;position:absolute;width:100%;height:100%;z-index:2147483611;color:#fff;text-align:center;'><i style='color:#fff;font-size:16px;' class='fa animate-spin fa-spin6'></i><span style='padding-left:5px;font-weight:bold;color:#fff;'>"+f.config.loading+"</span></div></div>";function v(t,s){f.extend(this,v),this.jqObj=t.addClass("k_labeltab_main"),this.opts=s,this.wrap=$("<div class='k_labeltab_wrap k_box_size'></div>").appendTo(this.jqObj),this.title=$("<div class='k_labeltab_title k_box_size'></div>").appendTo(this.wrap);var e=this.title.outerHeight();this.body=$("<div class='k_labeltab_body k_box_size'></div>").appendTo(this.wrap).css("border-top",e+"px solid #fff");var i=null,a=s.tabs.length-1,n=this;this.activedItem=null;for(var o=function(){var t=$(this),e=(t.width()-11)/2+t.position().left+5,i=parseInt(t.attr("i")),a=n.body.children(".k_labeltab_body_item").eq(i);if(null!==n.activedItem&&n.activedItem.hide(),n.activedItem=a.show(),n.$actived.animate({left:e},220,function(){0===n.activedItem.children().length&&void 0!==n.activedItem.data("data").dataType&&function(i){var e,a=this,o=this.data("data"),s="function"==typeof i;if(a.children().remove(),e=$(g).appendTo(a),"html"===o.dataType)f.htmlLoad({target:a,url:o.url,onLoaded:function(){e.fadeOut(function(){$(this).remove()}),s&&i.call(a,o.title),f.bindTextClear(a)}});else if("json"===o.dataType)f.request({dataType:"json",url:o.url,ok:function(t,e){s&&i.call(a,o.title,e)},final:function(t){e.fadeOut(function(){$(this).remove()})}});else{var t=$(u),n=t[0],r=f.getUUID();t.attr({name:r,id:r}),t.on("load",function(){e.fadeOut(function(){$(this).remove()});try{$(this.contentDocument.body).append("<span id='_window_ifr_id_' style='display:none'>"+r+"</span>")}catch(t){}s&&i.call(a,o.title)}),n.src=o.url,t.appendTo(a)}}.call(n.activedItem,n.opts.onLoaded)}),s.onclick){var o=t.attr("_title");setTimeout(function(){s.onclick.call(t,o)},10)}},r=0,l=s.tabs.length;r<l;r++){var d=s.tabs[r],h=d.title,c=$("<a i='"+r+"' _title='"+h+"'>"+h+"</a>").appendTo(this.title).click(o);r<a&&this.title.append("|");var p=$("<div class='k_labeltab_body_item'></div>").appendTo(this.body);(d.actived||0===r)&&(i=c),d.url&&""!==d.url?p.data("data",{url:d.url,dataType:d.dataType,title:d.title}):(f.scrollbar(p),p.append(d.content)),d.iconCls&&""!==d.iconCls&&c.addClass("btn").prepend("<i class='fa "+d.iconCls+"'></i>&nbsp")}this.$actived=$('<div style="height: 6px; left:0px;" class="actived"></div>').appendTo(this.title),null!==i&&i.trigger("click")}return f.Labeltab=v}),function(e,i){"function"==typeof define&&define.amd?define(["$B","panel"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(g,v){var _=function(t,e){v.extend(this,_),this.jqObj=t.addClass("k_box_size k_layout_main"),this.opts=e,this.panels=[];for(var i=this,a={title:"",iconCls:null,toolbar:null,width:"auto",height:"100%",shadow:!1,radius:!1,header:!0,content:null,dataType:"html",closeable:!1,expandable:!1,onLoaded:null,onExpanded:null},o=this.anylisLayoutSize(),s=function(){0<this.data("i")&&this.css("border-left","none"),"function"==typeof i.opts.onCreated&&i.opts.onCreated.call(this,{i:this.data("i"),title:this.data("title")})},n=function(){i.resize()},r=this.opts.onLoaded,l=function(t){if("function"==typeof r){var e=this.parent();r.call(this,t,{i:e.data("i"),title:e.data("title")})}},d=0,h=this.opts.items.length;d<h;++d){var c=$.extend({},a,this.opts.items[d]),p=$("<div class='k_layout_item k_box_size'></div>").appendTo(this.jqObj).data("width",c.width);c.width=o[d],c.draggable=!1,p.data("i",d),p.data("title",c.title),c.onCreated=s,c.onExpanded=n,c.onLoaded=l;var f=new v.Panel(p,c);this.panels.push(f)}var u=v.getUUID();$(g).on("resize."+u,v.delayFun(function(){i.resize?i.resize():$(this).off("."+u)},10))};return _.prototype={constructor:_,anylisLayoutSize:function(){for(var t={},e=0,i=0,a=0,o=this.opts.items.length;a<o;++a){var s=this.opts.items[a].width,n=!("string"==typeof s||!s);t[a]=n?parseInt(s):"auto",n?e+=t[a]:i++}if(0<i){var r=(this.jqObj.width()-e)/i;for(var l in t)"auto"===t[l]&&(t[l]=r)}return t},resize:function(){var t=this.jqObj.width(),e=0,i=[];this.jqObj.children(".k_layout_item").each(function(){var t=$(this);"auto"===t.data("width")&&i.push(t),e+=t.outerWidth()});for(var a=i.length,o=(t-e)/a,s=0,n=i.length;s<n;++s){var r=i[s].width();i[s].width(r+o)}},setTitle:function(t,e){this.panels[e].setTitle(t)},load:function(t,e){(t.title||t.iconCls)&&this.setTitle(t,e),this.panels[e].load(t)},updateContent:function(t,e){this.panels[e].updateContent(t)},getIfr:function(t){return this.panels[t].getIfr()},getUrl:function(t){return this.panels[t].opts.url},destroy:function(){for(var t=0,e=this.panels.length;t<e;++t)this.panels[t].destroy();this.super.destroy.call(this),this.panels=null}},v.Layout=_}),function(e,i){"function"==typeof define&&define.amd?define(["jquery"],function(t){return i(e,t)}):i(e,$)}("undefined"!=typeof window?window:this,function(t,$){"use strict";var u={type:!0,name:!0,watcher:!0,id:!0},e=t.$B?t.$B:{};String.prototype.trim=function(){return this.replace(/(^\s*)|(\s*$)/g,"")},String.prototype.leftTrim=function(){return this.replace(/(^\s*)/g,"")},String.prototype.rightTrim=function(){return this.replace(/(\s*$)/g,"")};var n,g=/\{\s*\{(.*)\}\s*\}/,r={};function C(t){if(r[t])return r[t];for(var e=t.match(/\{\{((?!\}\}).)+\}+/g),i=[],a=0,o=e.length;a<o;++a){var s=e[a].replace(/\s*\{\{\s*/,"").replace(/\s*\}\}\s*/,"");i.push(s)}return r[t]=i,clearTimeout(n),n=setTimeout(function(){r={}},1200),i}var i,a={};function T(t){var e=a[t];return e||(e=t.replace(/{\s*\{/g,"").replace(/\}\s*\}/g,"").trim(),a[t]=e,clearTimeout(i),i=setTimeout(function(){a={}},1200),e)}function l(t){for(var e,i,a=Object.keys(t),o={},s=0,n=a.length;s<n;++s)"toJson"!=typeof(i=t[e=a[s]])&&"function"!=typeof i&&"_$watchers"!==e&&"_$path"!==e&&($.isPlainObject(i)?o[e]=l(i):o[e]=i);return o}function v(t,e,i,a){this.vm=t,this.attrName=a?a.toLowerCase():a,this.el=e,this.$el=$(this.el),this.nodeName=e.nodeName;var o,s=C(this.replaceContent=i);this.methodKeys=[],this.propPathArray=[],this.propObjectArray=[];var n,r,l={};for(n=0,r=s.length;n<r;++n){o=T(o=s[n]),this.methodKeys.push(o);for(var d=o.match(/this\.data\.[^\s*]+/g),h=0,c=d.length;h<c;++h)if(!l[o=d[h]]){l[o]=!0;for(var p=this.vm.extractPropObject(o),f=0,u=p.length;f<u;f++){var g=p[f],v=g.propObject;this.propPathArray.push(g.propPath),v._$watchers||(v._$watchers=[]),v._$watchers.push(this),this.propObjectArray.push(v)}}}var _=this;if("express"===this.attrName){var m=this.$el.attr("watcher");m&&"function"==typeof this.vm[m]&&this.vm[m](this)}else{var b=1===this.el.nodeType,k="TEXTAREA"===this.el.parentNode.nodeName;if(k&&(b=!0),b){var y=100;this.userInputTimer;var x,w=this.$el;"value"===this.attrName||k?(x="input",k&&(w=$(this.el.parentNode))):"checked"!==this.attrName&&"selected"!==this.attrName||(y=0,x="change","OPTION"===this.nodeName&&"selected"===this.attrName&&((w=this.$el.parent()).data("hasBindWatcher")?x=void 0:w.data("hasBindWatcher",!0))),x&&w.on(x+".mvvm",function(){if(_.propChangeUpdating)return!1;var e=this,t=$(this),o=t.val(),i=t.attr("type");if(i&&(i=i.toLowerCase()),clearTimeout(_.userInputTimer),"radio"===i){var a=t.attr("name"),s=_.vm.$form.find("input[name="+a+"]");s.each(function(){var t=$(this);this!==e?t.removeAttr("checked"):t.attr("checked","checked").prop("checked",!0)}),s.data("no2update",!0),setTimeout(function(){s.removeData("no2update")},100)}else if("SELECT"===t[0].nodeName){var n=t.children().data("no2update",!0);n.each(function(){var t=$(this);o===t.val()?t.prop("selected",!0).attr("selected","selected"):t.prop("selected",!1).removeAttr("selected")}),setTimeout(function(){n.removeData("no2update")},100)}_.userInputTimer=setTimeout(function(){var t=_.propPathArray[0].split("."),e=t[t.length-1],i=_.propObjectArray[0],a=i[e];$.isPlainObject(a)||("number"==typeof a&&""!==o&&(o=a%1==0?parseInt(o):parseFloat(o)),i[e]=o)},y)})}}}function o(t){this.vm=t,this.el=t.el,this.$el=t.$form,this.data=t.data,this.watchers=[],this.compile(this.el)}function s(t,e){this.data=t,this.vm=e;for(var i,a,o,s=Object.keys(t),n=0,r=s.length;n<r;++n)"_$watchers"!==(a=s[n])&&"_$path"!==a&&"toJson"!==a&&(i=[],o=t[a],$.isPlainObject(o)&&i.push(a),this.getSetBuild(t,a,o,i))}function d(t){this.el="string"==typeof t.el?document.getElementById(t.el):t.el,this.$form=$(this.el),this.options=t,this.data=t.data,this.onChanged=t.onChanged,this.expressMethodCache={},this.pathObjectsCache={},this.initing=!0,this._attachDiyRegiste(this.options.registeWatchers),this._attachDiyRegiste(this.options.registeExpress),this.observer=new s(this.data,this),this.compiler=new o(this);var e=this;setTimeout(function(){e.initing=!0},50),"function"!=typeof this.data.toJson&&(this.data.toJson=function(){for(var t,e,i={},a=Object.keys(this),o=0,s=a.length;o<s;++o)"function"!=typeof(t=this[e=a[o]])&&"toJson"!==e&&"_$watchers"!==e&&"_$path"!==e&&($.isPlainObject(t)?i[e]=l(t):i[e]=t);return i})}return v.prototype={update:function(t,e,i,a){if(!this.$el.data("no2update")){this.propChangeUpdating=!0;var o=t._$path?t._$path+"."+e:e;if(this.need2Update(o)&&i!==a){"express"===this.attrName?this.expressUpater():3===this.el.nodeType?this.textUpdater():"OPTION"===this.nodeName&&"selected"===this.attrName.toLowerCase()?this.selectUpdater():"INPUT"===this.nodeName&&"radio"===this.$el.attr("type")?this.radioUpdater():this.elUpdater();var s=this;setTimeout(function(){s.propChangeUpdating=!1},20)}else this.propChangeUpdating=!1}},need2Update:function(t){for(var e=!1,i=0,a=this.propPathArray.length;i<a;++i){var o=this.propPathArray[i];if(o===t){e=!0;break}if(0===o.indexOf(t+".")){e=!0;break}}return e},expressUpater:function(){for(var t,e=0,i=this.methodKeys.length;e<i;++e)t=this.methodKeys[e],this.vm.callExpressFuntion(t,this.$el)},radioUpdater:function(){var i=this;i._invokeMethods(function(t,e){e?i.$el.attr("checked","checked").prop("checked",!0):i.$el.removeAttr("checked").prop("checked",!1)})},selectUpdater:function(){var i=this;i._invokeMethods(function(t,e){e?i.$el.prop("selected",!0).attr("selected","selected"):i.$el.prop("selected",!1).removeAttr("selected")})},textUpdater:function(){var a=this.replaceContent,o=!1;this._invokeMethods(function(t,e){if(void 0!==e){t=t.replace(/\+/g,"\\+").replace(/\-/g,"\\-").replace(/\*/g,"\\*");var i=new RegExp("{\\s*{\\s*"+t+"\\s*}\\s*}","g");a=a.replace(i,e),o=!0}}),o&&(this.el.textContent=a,this.el.nodeValue=a,this.el.data=a,"TEXTAREA"===this.el.parentNode.nodeName&&(this.el.parentNode.value=a,this.el=this.el.parentNode.firstChild))},elUpdater:function(){var i=this;this._invokeMethods(function(t,e){i.$el.attr(i.attrName,e),"value"===i.attrName&&i.$el.val(e)})},_invokeMethods:function(t){for(var e,i=0,a=this.methodKeys.length;i<a;++i)t(e=this.methodKeys[i],this.vm.callExpressFuntion(e))},destroy:function(){for(var t in this)this.hasOwnProperty(t)&&delete this[t]}},o.prototype={compile:function(t){3===(this.compileingEl=t).nodeType?this.compileTextNode(t):1===t.nodeType&&this.compileElement(t)},compileElement:function(t){var e=t.nodeName;if("SCRIPT"!==e&&"STYLE"!==e){var i,a,o,s=t.attributes,n=this,r=$(t);for(a=0,o=s.length;a<o;++a)if(i=s[a]){var l=i.name;if(!u[l]){var d,h=i.value;if(g.test(h))if(h=(h=h.replace(/\{\s*\{/g,"{{")).replace(/\}\s*\}/g,"}}"),"INPUT"===e&&"value"===l)d=n.invokeExpressMethod(h),r.attr("value",d),n.watchers.push(new v(n.vm,t,h,l));else if("INPUT"===e&&"checked"===l)d=n.invokeExpressMethod(h),r.removeAttr("checked"),d&&(r.attr("checked","checked"),r.prop("checked",!0)),n.watchers.push(new v(n.vm,t,h,l));else if("OPTION"===e&&"selected"===l)d=n.invokeExpressMethod(h),r.removeAttr("selected"),d&&(r.attr("selected","selected"),r.prop("selected",!0)),n.watchers.push(new v(n.vm,t,h,l));else if("express"===l){var c=T(h);n.makeExpressionFunction(c).call(n.vm,r),n.watchers.push(new v(n.vm,t,h,l))}else d=n.invokeExpressMethod(h),r.attr(i.name,d),n.watchers.push(new v(n.vm,t,h,l))}}var p,f=t.childNodes;for(a=0,o=f.length;a<o;++a)p=f[a],this.compile(p)}},compileTextNode:function(t){var e=t.textContent;if(g.test(e)){e=(e=e.replace(/\{\s*\{/g,"{{")).replace(/\}\s*\}/g,"}}");var i=this.invokeExpressMethod(e);void 0!==i&&(t.textContent=i,this.watchers.push(new v(this.vm,t,e)))}},getExpressMethod:function(t){return this.invokeExpressMethod(t,!0)},invokeExpressMethod:function(t,e){var i=C(t);if(i){for(var a,o,s,n,r,l=t,d=0,h=i.length;d<h;++d){if(o=T(a=i[d]),(s=this.vm.expressMethodCache[o])||(s=this.makeExpressionFunction(o)),e)return s;if("boolean"==typeof(n=s.call(this.vm)))return n;a=(a=(a=a.replace(/\+/g,"\\+").replace(/\-/g,"\\-").replace(/\*/g,"\\*")).replace(/\(/g,"\\(").replace(/\)/g,"\\)")).replace(/\{/g,"\\{").replace(/\}/g,"\\}"),r=new RegExp(a,"g"),l=l.replace(r,n)}return l=l.replace(/\{\{/g,"").replace(/\}\}/g,"")}},makeExpressionFunction:function(t){var e,i=this.vm.expressMethodCache[t];i||(e=0<t.indexOf("return")?t:"return "+t+";",i=new Function("el",e),this.vm.expressMethodCache[t]=i);return i},destroy:function(){for(var t=0,e=this.watchers.length;t<e;++t)this.watchers[t].destroy();for(var i in this)this.hasOwnProperty(i)&&delete this[i]}},s.prototype={forProps:function(t,e){if(t&&$.isPlainObject(t)){t._$path=e.join(".");for(var i,a,o,s=Object.keys(t),n=0,r=s.length;n<r;++n)"_$watchers"!==(i=s[n])&&"_$path"!==i&&"toJson"!==i&&(a=t[i],o=e.slice(),$.isPlainObject(a)&&o.push(i),this.getSetBuild(t,i,a,o))}},getSetBuild:function(t,i,a,o){var s=this;this.forProps(a,o),Object.defineProperty(t,i,{enumerable:!0,configurable:!0,set:function(t){if(a+""!=t+""){var e=a;$.isPlainObject(a)&&$.isPlainObject(t)&&s.copyWatcher(a,t),a=t,s.forProps(a,o),s.onSet(this,i,t,e)}},get:function(){return a}})},copyWatcher:function(t,e){t._$watchers&&(e._$watchers=t._$watchers,t._$watchers=void 0),t._$path&&(e._$path=t._$path,t._$path=void 0);for(var i,a,o=Object.keys(t),s=0,n=o.length;s<n;++s)if("object"==typeof(i=t[a=o[s]])){var r=e[a];"object"==typeof r&&this.copyWatcher(i,r)}},onSet:function(t,e,i,a){if(t._$watchers)for(var o=0,s=t._$watchers.length;o<s;++o){t._$watchers[o].update(t,e,i,a)}"function"==typeof this.vm.onChanged&&this.vm.onChanged.call(this.vm,t,e,i,a)},destroy:function(){for(var t in this)this.hasOwnProperty(t)&&delete this[t]}},d.prototype={destroy:function(){for(var t in function t(e){if(e)for(var i,a,o=Object.keys(e),s=0,n=o.length;s<n;++s)"_$watchers"===(i=o[s])?e[i]=[]:"toJson"!=typeof(a=e[i])&&"function"!=typeof a&&"_$path"!==i&&$.isPlainObject(a)&&t(a)}(this.data),this.observer.destroy(),this.compiler.destroy(),this)this.hasOwnProperty(t)&&delete this[t]},extractPropObject:function(t){var e=t,i=this.pathObjectsCache[e];if(i)return i;var a=t.match(/this\.data\.[^\s+|^,]+/g);i=[];for(var o,s,n=this.data,r=0,l=a.length;r<l;++r){if(o=n,0<(t=a[r].replace("this.data.","")).indexOf("."))for(var d=0,h=(s=t.split(".")).length-1;d<h;++d)d<h&&(o=o[s[d]]);i.push({propObject:o,propPath:t})}return this.pathObjectsCache[e]=i},_attachDiyRegiste:function(t){var e,i,a,o,s;if($.isPlainObject(t))for(i=0,a=(e=Object.keys(t)).length;i<a;++i)o=t[s=e[i]],"function"!=typeof this[s]?"function"==typeof o&&(this[s]=o):console.log("key["+s+"]已经存在！")},registeWatcher:function(t,e){return"function"==typeof this[t]&&console.log("watcher["+t+"]已经存在！"),this[t]=e,this},registeExpress:function(t,e){return"function"==typeof this[t]&&console.log("express["+t+"]已经存在！"),this[t]=e,this},compile:function(t){return t.css&&(t=t[0]),this.compiler.compile(t),this},callExpressFuntion:function(t,e){var i=this.expressMethodCache[t];if(i)return i.call(this,e)},_forProps:function(t,e){for(var i,a,o=Object.keys(t),s=0,n=o.length;s<n;++s)"function"!=typeof(i=t[a=o[s]])&&"_$watchers"!==a&&"_$path"!==a&&($.isPlainObject(i)?(e[a]={},this._forProps(i,e[a])):e[a]=i)},getJson:function(){for(var t,e,i={},a=Object.keys(this.data),o=0,s=a.length;o<s;++o)e=a[o],"toJson"!=typeof(t=this.data[e])&&"function"!=typeof t&&"_$watchers"!==e&&"_$path"!==e&&($.isPlainObject(t)?(i[e]={},this._forProps(t,i[e])):i[e]=t);return this.options.onGetJson&&this.options.onGetJson(i),i},checkboxWather:function(t){var d=t.propObjectArray[0],e=t.propPathArray[0].split("."),h=e[e.length-1];t.$el.on("click",function(){var t=$(this),e=t.val(),i=d[h],a=!1,o=$.isArray(i),s=!1;o?0<i.length&&(a=$.isNumeric(i[0])):(s=0<i.indexOf(";"),a=!1,i=""===i?[]:s?i.split(";"):i.split(",")),a&&(e=parseInt(e));var n=i;if(t.prop("checked"))t.attr("checked","checked"),i.push(e);else{t.removeAttr("checked"),n=[];for(var r=0,l=i.length;r<l;++r)""!==i[r]&&i[r]!==e&&n.push(i[r])}o||(d[h]=s?n.join(";"):n.join(","))})},checkboxExpress:function(t,e){var i=e.val();$.isArray(t)||(0<t.indexOf(",")?t=t.split(","):0<t.indexOf(";")&&(t=t.split(";")));for(var a=!1,o=0,s=t.length;o<s;++o)if(i===t[o]+""){a=!0;break}a?e.prop("checked",!0).attr("checked","checked"):e.prop("checked",!1).removeAttr("checked")},kRadioWatcher:function(t){var e=t.$el,a=t.propObjectArray[0],o=t.propPathArray[0];e.children("input").on("change",function(){var t=$(this).val(),e=a[o],i=$.isNumeric(e);a[o]=i?parseInt(t):t})},kRadioExpress:function(e,t){$(t).find("input[type=radio]").each(function(){var t=$(this);t.val()===e+""?t.attr("checked","checked").prop("checked",!0):t.removeAttr("checked").prop("checked",!1)})},kcheckBoxWatcher:function(t){var e=t.$el,l=t.propObjectArray[0],d=t.propPathArray[0];e.children("input").on("change",function(){var t,e=$(this),i=l[d],a=[];""!==i&&(0<i.indexOf(";")?(a=i.split(";"),t=";"):(a=i.split(","),t=","));var o=e.val();if(e.prop("checked"))a.push(o);else{for(var s=[],n=0,r=a.length;n<r;++n)o!==a[n]&&s.push(a[n]);a=s}l[d]=a.join(t)})},kcheckBoxExpress:function(i,t){$(t).find("input[type=checkbox]").each(function(){var t=$(this),e=t.val();(0<=i.indexOf(";")?new RegExp(";"+e+";|;"+e+"|"+e+";").test(i):0<=i.indexOf(",")?new RegExp(","+e+",|,"+e+"|"+e+",").test(i):i===e)?t.attr("checked","checked").prop("checked",!0):t.removeAttr("checked").prop("checked",!1)})},kcomboxWatcher:function(e){var i,a,t,o,s,n=e.$el.data("combox"),r=e.propObjectArray[0],l=e.propPathArray[0];if(n.opts.checkbox){for(t=n.getCheckedIds(),a=[],o=0,s=t.length;o<s;++o)""!==t[o].id&&a.push(t[o].id);i=a.join(",")}else i=n.jqObj.data("id");r[l]+""!==i&&(e.$el.data("no2update",!0),r[l]=i,e.$el.removeData("no2update")),n.regiterFn("onWatcher",function(){if(this.opts.checkbox){var t=this.getCheckedIds();for(a=[],o=0,s=t.length;o<s;++o)""!==t[o].id&&a.push(t[o].id);i=a.join(",")}else i=this.jqObj.data("id");r[l]+""!==i&&(e.$el.data("no2update",!0),r[l]=i,e.$el.removeData("no2update"))})},kcomboxExpress:function(t,e){e.data("combox").setCheckDatas(t)},kcalendarWatcher:function(t){var e=t.$el.data("calender"),i=t.propObjectArray[0],a=t.propPathArray[0];e.regiterFn("onWatcher",function(){t.$el.data("no2update",!0),i[a]=this.target.val(),t.$el.removeData("no2update")});var o=t.$el.val();""!==o&&o!==i[a]&&(t.$el.data("no2update",!0),i[a]=o,t.$el.removeData("no2update"))},kcalendarExpress:function(t,e){""!==t&&$(e).data("calender").setValue(t)},ktreeExpress:function(t,e){$(e).data("treeIns").setCheckDatas(t)},ktreeWatcher:function(o){var t=o.$el.data("treeIns"),s=o.propObjectArray[0],n=o.propPathArray[0],r=s[n];t.regiterFn("onWatcher",function(){for(var t=this.getCheckedData({onlyId:!0}),e=[],i=0,a=t.length;i<a;++i)e.push(t[i].id);r=e.join(","),s[n]+""!==r&&(o.$el.data("no2update",!0),s[n]=r,o.$el.removeData("no2update"))})},kwindowInputExpress:function(t,e){for(var i,a=t.split(","),o=[],s=0,n=a.length;s<n;++s)i=a[s].split("___"),o.push(i[1]);e.val(o.join(",")).attr("_val",t)},kwindowInputWatcher:function(r){var t=r.$el,l=r.propObjectArray[0],d=r.propPathArray[0];t.on("change",function(){console.log("wather input change");for(var t,e=$(this),i=e.attr("_val"),a=i.split(","),o=[],s=0,n=a.length;s<n;++s)t=a[s].split("___"),o.push(t[1]);e.val(o.join(",")),r.$el.data("no2update",!0),l[d]=i,r.$el.removeData("no2update")})}},e.Mvvm=d,t.$B=e,d}),function(e,i){"function"==typeof define&&define.amd?define(["$B"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(t,_){var a={pageSize:15,height:25,pageList:[15,20,25,30,35,40,55,70,80,90,100],page:1,startpg:1,buttons:5,position:"right",summary:!0};function o(t,e){if(_.extend(this,o),this.opts=$.extend({},a,e),this.jqObj=t.addClass("k_pagination_wrap clearfix"),this.jqObj.append("<div class='k_pagination_"+this.opts.position+"'></div>"),e.height){var i=e.height+"";i.indexOf("px")<0&&(i+="px"),this.jqObj.height(e.height).css("line-height",i)}(function(){var n,r,l,d,h=$("<div style='float:left;' class='k_pagination_num_wrap k_box_size'></div>"),c=this.opts;function s(t){var e,i=$(this),a=i.data("pg");if(!i.hasClass("k_pagination_disabled")){if("r"===a)i.data("isTrigger")?(u(c.startpg),v()):setTimeout(function(){c.onClick(c.page,c.pageSize,c.startpg)},10);else if(1===a)c.startpg=1,c.page=1,u(c.startpg),setTimeout(function(){c.onClick(c.page,c.pageSize,c.startpg)},10);else if("mn"===a)c.startpg=c.startpg+c.buttons,c.startpg<=0&&(c.startpg=1),c.page=c.startpg,u(c.startpg),setTimeout(function(){c.onClick(c.page,c.pageSize,c.startpg)},10);else if("mp"===a)c.startpg=c.startpg-c.buttons,c.startpg<=0&&(c.startpg=1),c.page=c.startpg,u(c.startpg),setTimeout(function(){c.onClick(c.page,c.pageSize,c.startpg)},10);else if("l"===a)c.startpg=c.pageCount-c.buttons+1,c.startpg<=0&&(c.startpg=1),c.page=c.pageCount,u(c.startpg),setTimeout(function(){c.onClick(c.page,c.pageSize,c.startpg)},10);else if("p"===a){var o=(e=h.children(".actived").removeClass("actived")).prev();0<o.length?(c.page=o.addClass("actived").data("pg"),1===c.page&&(d.addClass("k_pagination_disabled"),l.addClass("k_pagination_disabled"))):(c.page=e.data("pg")-1,c.page<1&&(c.page=1),c.startpg=c.page-c.buttons+1,u(c.startpg)),setTimeout(function(){c.onClick(c.page,c.pageSize,c.startpg)},2)}else if("n"===a){var s=(e=h.children(".actived").removeClass("actived")).next();0<s.length?(c.page=s.addClass("actived").data("pg"),c.page===c.pageCount&&(n.addClass("k_pagination_disabled"),r.addClass("k_pagination_disabled"))):(c.page=e.data("pg")+1,c.startpg=c.page,u(c.startpg)),setTimeout(function(){c.onClick(c.page,c.pageSize,c.startpg)},10)}return!1}}c.pageCount=Math.ceil(c.total/c.pageSize)||1;var p=this.jqObj.children();p.children().remove();var f=p.height();function u(t){t<1&&(t=1),h.children().remove(),p.children(".k_pagination_more").remove(),1!==t&&t!==c.startpg?($("<a style='line-height:"+f+"px;height:"+f+"px' class='k_pagination_more k_box_size'>...<div class='a_div'></div></a>").insertBefore(h).click(s).data("pg","mp"),d.removeClass("k_pagination_disabled"),l.removeClass("k_pagination_disabled")):(d.addClass("k_pagination_disabled"),l.addClass("k_pagination_disabled")),n&&(t+c.buttons>=c.pageCount?(n.addClass("k_pagination_disabled"),c.page===c.pageCount&&r.addClass("k_pagination_disabled")):(n.removeClass("k_pagination_disabled"),r.removeClass("k_pagination_disabled")));for(var e=c.buttons,i=function(){var t=$(this);c.page=t.addClass("actived").data("pg"),t.siblings().removeClass("actived"),1===c.page?(d.addClass("k_pagination_disabled"),l.addClass("k_pagination_disabled")):(d.removeClass("k_pagination_disabled"),l.removeClass("k_pagination_disabled")),c.page===c.pageCount?(n.addClass("k_pagination_disabled"),r.addClass("k_pagination_disabled")):(n.removeClass("k_pagination_disabled"),r.removeClass("k_pagination_disabled")),setTimeout(function(){c.onClick(c.page,c.pageSize,c.startpg)},10)};0<e&&t<=c.pageCount;){var a=$("<a style='line-height:"+f+"px;height:"+f+"px' class='k_box_size k_pg_number'>"+t+"<div class='a_div'></div></a>").appendTo(h).data("pg",t);t===c.page&&a.addClass("actived"),a.click(i),t++,e--}if(t<c.pageCount&&$("<a style='line-height:"+f+"px;height:"+f+"px' class='k_pagination_more k_box_size'>...<div class='a_div'></div></a>").insertAfter(h).click(s).data("pg","mn"),"center"===c.position){var o=0;p.children().each(function(){var t=$(this);o=o+t.outerWidth()+3}),p.width(o)}}$("<a style='line-height:"+f+"px;height:"+f+"px' class='k_pagination_fresh k_box_size'><i style='line-height:"+f+"px' class='fa fa-refresh'></i><div class='a_div'></div></a>").appendTo(p).click(s).data("pg","r").hide(),(d=$("<a style='line-height:"+f+"px;height:"+f+"px' class='k_pagination_first k_box_size'><i style='line-height:"+f+"px' class='fa fa-angle-double-left'></i><div class='a_div'></div></a>").appendTo(p)).children("i").removeAttr("class").text(_.config.firstPage),d.hide(),d.click(s).data("pg",1),(l=$("<a style='line-height:"+f+"px;height:"+f+"px' class='k_pagination_prev k_box_size'><i style='line-height:"+f+"px' class='fa fa-angle-left'></i><div class='a_div'></div></a>").appendTo(p)).children("i").removeAttr("class").text(_.config.prevPage),l.click(s).data("pg","p"),1===c.page&&(d.addClass("k_pagination_disabled"),l.addClass("k_pagination_disabled")),h.insertAfter(l),u(c.startpg),(r=$("<a style='line-height:"+f+"px;height:"+f+"px' class='k_pagination_prev k_box_size'><i style='line-height:"+f+"px' class='fa fa-angle-right'></i><div class='a_div'></div></a>").appendTo(p).data("pg","n")).click(s),r.children("i").removeAttr("class").text(_.config.nextPage),1===c.pageCount&&r.addClass("k_pagination_disabled");var t=$("<a style='line-height:"+f+"px;height:"+f+"px' class='k_pagination_last k_box_size'><i style='line-height:"+f+"px' class='fa fa-angle-double-right'></i><div class='a_div'></div></a>").appendTo(p).data("pg","l");function g(t,e){return t<e?-1:e<t?1:0}function v(){if(c.summary){p.children(".k_pagination_sum").remove();var t=["<div class='k_pagination_sum'>"+_.config.pageSum.replace("{total}",c.total)+"<select>"],e=c.pageList.slice(0,c.pageList.length);e.push(c.pageSize),(e=e.unique()).sort(g);for(var i=0,a=e.length;i<a;++i){var o="";e[i]===c.pageSize&&(o="selected=selected"),t.push("<option "+o+" value='"+e[i]+"'>"+e[i]+"</option>")}t.push("</select>"+_.config.pageSumSuffix+"</div>"),$(t.join("")).prependTo(p).children("select").change(function(){var t=parseInt($(this).val());c.pageSize=t,setTimeout(function(){c.onClick(1,c.pageSize,1)},10)})}}if(t.click(s),t.children("i").removeAttr("class").text(_.config.lastPage),n=t.hide(),c.startpg<c.pageCount?t.removeClass("k_pagination_disabled"):t.addClass("k_pagination_disabled"),$("<span class='k_pagination_input_wrap'>"+_.config.go2page+"&nbsp<input type='text'/>&nbsp"+_.config.go2pageSuffix+"&nbsp<button class='k_pagination_input_button'>"+_.config.buttonOkText+"</button></span>").appendTo(p).children("button").click(function(){var t=$(this),e=t.data("opts"),i=t.siblings("input").val();if(""!==i)try{e.startpg=parseInt(i),(e.startpg>e.pageCount||e.startpg<1)&&(e.startpg=1),e.page=e.startpg,e.onClick(e.page,e.pageSize,e.startpg)}catch(t){}}).data("opts",c),v(),"center"===c.position){var e=0;p.children().each(function(){var t=$(this);e=e+t.outerWidth()+3}),p.width(e)}this.first=d,this.last=t,this.next=r,this.prev=l,c.page===c.pageCount?(1<c.page?(this.first.removeClass("k_pagination_disabled"),this.prev.removeClass("k_pagination_disabled")):(this.first.addClass("k_pagination_disabled"),this.prev.addClass("k_pagination_disabled")),this.last.addClass("k_pagination_disabled"),this.next.addClass("k_pagination_disabled")):1===c.page?c.page<c.pageCount?(this.first.addClass("k_pagination_disabled"),this.prev.addClass("k_pagination_disabled"),this.last.removeClass("k_pagination_disabled"),this.next.removeClass("k_pagination_disabled")):(this.first.addClass("k_pagination_disabled"),this.prev.addClass("k_pagination_disabled"),this.last.addClass("k_pagination_disabled"),this.next.addClass("k_pagination_disabled")):(this.first.removeClass("k_pagination_disabled"),this.prev.removeClass("k_pagination_disabled"),this.last.removeClass("k_pagination_disabled"),this.next.removeClass("k_pagination_disabled"))}).call(this)}return o.prototype={constructor:o,update:function(t){this.opts.page=1,this.opts.startpg=1,$.extend(this.opts,t),this.opts.pageCount=Math.ceil(this.opts.total/this.opts.pageSize)||1,this.opts.startpg>=this.opts.pageCount&&(this.opts.startpg=1);var e=this.jqObj.children().children(".k_pagination_fresh").data("isTrigger",!0);e.trigger("click"),e.data("isTrigger",!1),1!==this.opts.page?(this.first.removeClass("k_pagination_disabled"),this.prev.removeClass("k_pagination_disabled")):(this.first.addClass("k_pagination_disabled"),this.prev.addClass("k_pagination_disabled")),this.opts.page===this.opts.pageCount?(this.last.addClass("k_pagination_disabled"),this.next.addClass("k_pagination_disabled")):(this.last.removeClass("k_pagination_disabled"),this.next.removeClass("k_pagination_disabled"))},getCurPage:function(){return this.opts.page}},_.Pagination=o}),function(e,i){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B","toolbar","plugin"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(r,k){var y={title:"",iconCls:null,iconColor:void 0,toolbar:null,width:"auto",height:"auto",shadow:!0,radius:!0,header:!0,zIndex:999999,content:null,url:"",dataType:"html",draggable:!1,moveProxy:!1,draggableHandler:"header",closeable:!1,closeType:"hide",expandable:!1,maxminable:!1,collapseable:!1,onResized:null,onLoaded:null,onClose:null,onClosed:null,onExpanded:null,onCollapsed:null,onCreated:null},t=k.config,a="<div class='k_box_size' style='position:absolute;z-index:2147483600;width:100%;height:26px;top:2px;left:0;' class='loading'><div class='k_box_size' style='filter: alpha(opacity=50);-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;position:absolute;top:0;left:0;width:100%;height:100%;z-index:2147483600;background:"+t.loadingBackground+";'></div><div class='k_box_size' style='width:100%;height:100%;line-height:26px;padding-left:16px;position:absolute;width:100%;height:100%;z-index:2147483611;color:#fff;text-align:center;'><i style='color:#fff;font-size:16px;' class='fa animate-spin fa-spin6'></i><span style='padding-left:5px;font-weight:bold;color:#fff;'>"+k.config.loading+"</span></div></div>",x='<div class="k_panel_foot_toolbar k_box_size" ></div>',w='<div class="k_panel_content k_box_size" >{c}</div>',C='<div style="padding-right:1px;height:100%;text-align:center;" class="k_panel_header_icon menu_img btn"><i style="font-size:16px;line-height:1.5em;" class="fa {icon}">​</i></div>',T='<div class="k_panel_header"><div class="k_panel_header_content"><h1>{title}</h1><div class="k_panel_header_toolbar k_box_size"></div></div></div>';function j(t,e){k.extend(this,j);var r=this,i="",a=0,o=0;if(this.jqObj=t.addClass("k_panel_main k_box_size"),this.opts=$.extend({},y,e),this.opts.shadow&&this.jqObj.addClass("k_box_shadow"),this.opts.radius&&this.jqObj.addClass("k_box_radius"),this.opts.header){"string"==typeof this.opts.title?this.$header=$(T.replace("{title}",this.opts.title)).appendTo(this.jqObj):(this.$header=$(T.replace("{title}","")),this.$header.find("h1").append(this.opts.title),this.$header.appendTo(this.jqObj)),a=r.$header.outerHeight();var s=this.$header.children();o=(a-20)/2,s.children(".k_panel_header_toolbar").css("padding-top",o)}var n,l,d=!1,h="string"==typeof this.opts.content;(h&&(d=k.isUrl(this.opts.content)),d||h&&(i=this.opts.content),this.$body=$(w.replace("{c}",i)).appendTo(this.jqObj).css({"border-top":"solid #ccc "+a+"px"}),this.opts.bodyCss&&this.$body.css(this.opts.bodyCss),h||d||this.$body.append(this.opts.content),this.toolArray=[],"function"==typeof this.opts.createToolsFn)?(n=$(x).appendTo(this.jqObj),this.opts.createToolsFn.call(n,this.opts.toolbar)):this.opts.toolbar&&(n=$(x).appendTo(this.jqObj),$.isArray(this.opts.toolbar)?(l={buttons:this.opts.toolbar,align:"center"},this.opts.toolbarAlign&&(l.align=this.opts.toolbarAlign)):l=this.opts.toolbar,this.toolArray.push(new k.Toolbar(n,l)));var c={height:this.opts.height,width:this.opts.width};if(this.jqObj.outerWidth(this.opts.width).outerHeight(this.opts.height).data("src_size",c),null!=this.opts.iconCls&&this.opts.header){var p=$(C.replace("{icon}",this.opts.iconCls)).prependTo(this.$header.children(".k_panel_header_content")).children("i");this.opts.iconColor&&p.css("color",this.opts.iconColor)}if(this.opts.header){var f=this.$header.children(".k_panel_header_content").children(".k_panel_header_toolbar");if(this.opts.closeable&&$("<a class='k_panel_tool_icon k_panel_close btn'><i class='fa fa-cancel'></i></a>").appendTo(f).click(function(){return"hide"===r.opts.closeType?r.close():r.destroy(),!1}),this.opts.expandable){var u="function"==typeof r.opts.onExpanded,g=function(t){var e=t.children();r.jqObj.removeClass("k_panel_expandable_bg"),r.jqObj.animate({width:r.jqObj.data("lsWidth")},10,function(){t.siblings().show(),t.parent().prevAll().show(),r.$header.css("border-bottom-width","1px"),e.removeClass("fa-right-open-1").addClass("fa-left-open-2"),r.jqObj.css("cursor","default").off("click").children("div:gt(0)").fadeIn(100),u&&setTimeout(function(){r.opts.onExpanded("right")},1)})};$("<a class='k_panel_tool_icon k_panel_slide_left btn'><i class='fa fa-left-open-2'></i></a>").appendTo(f).click(function(){var t=$(this),e=t.children();return e.hasClass("fa-left-open-2")?r.jqObj.children("div:gt(0)").fadeOut(100,function(){t.siblings().hide(),t.parent().prevAll().hide(),r.$header.css("border-bottom-width","0px"),r.jqObj.data("lsWidth",r.jqObj.width()),r.jqObj.animate({width:25},10,function(){r.jqObj.addClass("k_panel_expandable_bg"),u&&setTimeout(function(){r.opts.onExpanded("left")},1)}).css("cursor","pointer").on("click",function(){g(t)}),e.removeClass("fa-left-open-2").addClass("fa-right-open-1")}):g(t),!1})}if(this.opts.maxminable){var v=$("<a class='k_panel_tool_icon k_panel_maxbtn'><i class='fa fa-resize-full'></i></a>").appendTo(f).click(function(){var t,e,i=$(this).children();if(i.hasClass("fa-resize-full")){var a=r.jqObj.parent(),o=r.jqObj.css("z-index");r.jqObj.data("zIndex",o),r.jqObj.css("z-index",r.opts.zIndex),e={width:a.width(),height:a.height()},r.opts.draggable&&(e.top=0,e.left=0,r.jqObj.data("src_pos",r.jqObj.position())),r.jqObj.animate(e,100),i.removeClass("fa-resize-full").addClass("fa-resize-small"),t="max"}else{var s=r.jqObj.data("src_size");if(e={width:s.width,height:s.height},r.opts.draggable){var n=r.jqObj.data("src_pos");e.top=n.top,e.left=n.left}r.jqObj.css("z-index",r.jqObj.data("zIndex")),r.jqObj.animate(e,100),i.removeClass("fa-resize-small").addClass("fa-resize-full"),t="min"}return"function"==typeof r.opts.onResized&&r.opts.onResized(t),!1});this.$header&&(this.$header.data("_ck",v),this.$header.click(function(){return!1}).dblclick(function(){return $(this).data("_ck").trigger("click"),!1}))}this.opts.collapseable&&$("<a class='k_panel_tool_icon  btn'><i class='fa fa-down-open-2'></i></a>").appendTo(f).click(function(){var t,e=$(this).children();return e.hasClass("fa-down-open-2")?(r.jqObj.children("div:gt(0)").fadeOut(100,function(){r.jqObj.data("lsHeight",r.jqObj.height()),r.jqObj.animate({height:a},200),e.removeClass("fa-down-open-2").addClass("fa fa-up-open-2")}),t="down"):(r.jqObj.animate({height:r.jqObj.data("lsHeight")},100,function(){r.jqObj.children("div:gt(0)").fadeIn(100),e.removeClass("fa-up-open-2").addClass("fa-down-open-2")}),t="up"),"function"==typeof r.opts.onCollapsed&&setTimeout(function(){r.opts.onCollapsed(t)},1),!1})}if(this.opts.draggable){var _={isProxy:this.opts.moveProxy,onDragReady:this.opts.onDragReady,onStartDrag:this.opts.onStartDrag,onDrag:this.opts.onDrag,onStopDrag:this.opts.onStopDrag};_.isProxy&&this.opts.header&&(_.onMouseDown=function(t,e){if("I"===e.target.nodeName)return!1}),"header"===this.opts.draggableHandler&&this.opts.header&&(_.handler=this.$header),t.draggable(_)}if(n){var m=n.outerHeight();r.$body.css({"border-bottom-width":m+"px","border-bottom-style":"solid","border-bottom-color":"#ffffff"})}if("function"==typeof this.opts.onCreated&&this.opts.onCreated.call(this.jqObj,this.$body,this.$header),r.$body.css("overflow","auto"),k.isUrl(this.opts.content)||""!==this.opts.url){var b=this.opts.url;k.isUrl(this.opts.content)&&(b=this.opts.content),this.load({url:b,dataType:this.opts.dataType})}}return j.prototype={constructor:j,load:function(t){var e=this.opts.url;k.isUrl(this.opts.content)&&(e=this.opts.content);var i=this.opts.dataType,o=this;t&&(e=t.url,i=t.dataType);var s="function"==typeof this.opts.onLoaded;e=0<e.indexOf("?")?e+"&_t_="+k.generateMixed(5):e+"?_t_="+k.generateMixed(5),this.url=e;var n=$(a);n.appendTo(o.$body),setTimeout(function(){if("html"===i)k.htmlLoad({target:o.$body,url:e,onLoaded:function(){n.fadeOut(function(){$(this).remove()}),s&&o.opts.onLoaded.call(o.$body,{}),k.bindTextClear(o.$body)}});else if("json"===i)k.request({dataType:"json",url:e,ok:function(t,e){s&&o.opts.onLoaded.call(o.$body,e)},final:function(t){n.fadeOut(function(){$(this).remove()})}});else{if(!o.iframe){o.iframe=$("<iframe  class='panel_content_ifr' frameborder='0' style='overflow:visible;height:100%;width:100%;display:block;vertical-align:top;'  src='' ></iframe>");var a=k.getUUID();o.iframe.attr({name:a,id:a}),o.iframe.on("load",function(t){if(""!==$(this).attr("src")){s&&o.opts.onLoaded.call(o.$body,{});try{n.fadeOut(function(){$(this).remove()})}catch(t){}var e=-1!==r.navigator.userAgent.indexOf("Firefox");try{var i=$(this.contentDocument.body);i.append("<span id='_window_ifr_id_' style='display:none'>"+a+"</span>"),e&&i.find("a").each(function(){var t=$(this);-1<t.attr("href").toLowerCase().indexOf("javascript")&&t.attr("href","#")})}catch(t){}}})}o.iframe[0].src=e,o.iframe.appendTo(o.$body)}},10)},updateContent:function(t){this.$body.html(t)},setTitle:function(t){this.opts.header&&("string"==typeof t?this.$header.children().children("h1").html(t):(t.title&&this.$header.children().children("h1").html(t.title),t.iconCls&&this.$header.children().children(".k_panel_header_icon").find("i.fa").attr("class","fa "+t.iconCls)))},close:function(t){if(this.opts)if($("body").children("#k_text_clear_btn").hide(),"destroy"!==this.opts.closeType){var e=this,i=!0;if("function"==typeof e.opts.onClose&&(i=e.opts.onClose()),t||i){var a=function(){e._invokeClosedFn()};e.opts.position?"bottom"===e.opts.position?this.jqObj.fadeOut(200,a):this.jqObj.animate({top:-2e3},500,a):this.jqObj.fadeOut(200,a)}}else this.destroy()},show:function(){this.jqObj&&this.jqObj.fadeIn(200,function(){})},_invokeClosedFn:function(){if("iframe"!==this.opts.dataType){var e="hide"===this.opts.closeType;this.$body.find(".k_color_picker_cls").each(function(){var t=$(this).data("picker");t&&t.destroy&&(e?t.hide():t.destroy())}),this.$body.find(".k_calendar_input").each(function(){var t=$(this).data("calender");t&&t.destroy&&(e?t.hide():t.destroy())}),this.$body.find(".k_combox_input").each(function(){var t=$(this).data("combox");t&&t.destroy&&(e?t.hide():t.destroy())})}this.jqObj.hide();var i=this;if("function"==typeof i.opts.onClosed)try{i.opts.onClosed.call(this.jqObj)}catch(t){i.error(t)}},find:function(t){return this.$body.find(t)},destroy:function(){for(var t=this,e=0,i=this.toolArray.length;e<i;++e)this.toolArray[e].destroy();if("function"==typeof t.opts.onClose&&t.opts.onClose(),"block"===this.jqObj.css("display")){this.jqObj.fadeOut("fast",function(){t._invokeClosedFn(),t.jqObj.remove(),t.super.destroy.call(t)})}else t._invokeClosedFn(),this.jqObj.remove(),this.super.destroy.call(this)},resize:function(t,e){var i={},a=this;t.width&&(i.width=t.width),t.height&&(i.height=t.height),this.jqObj.css(i),"function"==typeof a.opts.onResized&&setTimeout(function(){a.opts.onResized()},10)},getSize:function(){return{width:this.jqObj.outerWidth(),height:this.jqObj.outerHeight()}},getIfr:function(){if("iframe"===this.opts.dataType)return this.$body.children("iframe")[0]},hide:function(){this.close(!0)},setAttr:function(t){this.jqObj.attr(t)}},k.Panel=j}),function(e,i){"function"==typeof define&&define.amd&&!window._all_in_?define(["jquery"],function(t){return i(e,t)}):i(e,$)}("undefined"!=typeof window?window:this,function(y,x){_createDropDownListFn(x),_makeJquerySubmitEevent(x);var t,b=x(y.document);function w(){return t||(t=x(y.document.body).css("position","relative")),t}var $={mouseenter:function(t,e){var i=x(this),a=i.attr("_tip");a||(a=i.data("_tip")),a||(a=i.text());var o=i.data("tipopts");if(a){clearTimeout(y._clsTipTimer_);var s=0,n=0,r=w();if(y.top!==y.self)if("visible"===r.css("overflow")){var l=$B.getIfrId(),d=x(parent.document.body).find("#"+l).parent();d.hasClass("k_panel_content")&&(s=d.scrollTop(),n=d.scrollLeft())}else s=b.scrollTop(),n=b.scrollLeft();else s=b.scrollTop(),n=b.scrollLeft();(function(t,e,i,a,o){var s;"string"==typeof e&&(e=e.replace(/\r\n/g,"<br/>")).indexOf("<br/>")&&(e=e.split("<br/>"));var n=14;o.fontSize&&(n=o.fontSize);var r=1.2*n,l=""!==o.title,d="padding:2px 10px;";l&&(d="padding:2px 22px;");if(x.isArray(e)){s=[];for(var h=0,c=e.length;h<c;++h)s.push("<p style='font-size:"+n+"px;color:#fff;line-height: 1.3;"+d+"'>"+e[h]+"</p>");s=s.join("")}else s="<p style='font-size:"+n+"px;color:#fff;line-height: 1.3;"+d+"'>"+e+"</p>";var p=w(),f=p.children("#k_mouse_tip_wrap"),u=!0;0===f.length?(f=x("<div style='font-size: "+n+"px;line-height: 1.3;position:absolute;z-index: **********; height: auto;' id='k_mouse_tip_wrap'><div class='k_box_shadow' style='border-radius: 4px;position:absolute;top:0;left:0;width:100%;height:100%;background:#303133;opacity:0.84'></div><div style='position:relative;padding: 6px 0px;' class='tip_content'></div></div>"),u=!1,f.on({mouseenter:function(){clearTimeout(y._clsTipTimer_)},mouseleave:$.mouseleave})):0<f.next().length&&(f.detach(),u=!1);var g=f.children(".tip_content");g.children().remove(),g.html(s),l&&g.prepend("<h6 style='border-bottom:1px dashed #CECECE ;font-size:"+r+"px;text-align:left;padding-left:6px;color:#fff;font-weight: normal;'>"+o.title+"</h6>");var v={top:-1e4,left:0};o.maxWidth&&(t["max-width"]=o.maxWidth,v["max-width"]=o.maxWidth);o.maxWidth&&(t["min-width"]=o.minWidth,v["min-width"]=o.minWidth);y.$curTip_=u?f.css(v).show():f.css(v).appendTo(p);var _=p.outerHeight(),m=p.outerWidth(),b=f.outerHeight(),k=f.outerWidth();_-(t.top-a.top)<b&&(t.top=i.top-b,t.top<0&&(t.top=0));m-(t.left-a.left)<k&&(t.left=i.left-k,t.left<0&&(t.left=0));f.css(t)})(t.isTrigger?e:{left:t.pageX,top:t.pageY},a,i.offset(),{top:s,left:n},o),y._clsTipAct_&&y._clsTipAct_.css("box-shadow","none"),y._clsTipAct_=i.css("box-shadow","0 0 2px 2px "+o.activedColor)}},mouseleave:function(){y.$curTip_&&(y._clsTipTimer_=setTimeout(function(){y.$curTip_.hide(),y.$curTip_=void 0,y._clsTipAct_&&(y._clsTipAct_.css("box-shadow","none"),y._clsTipAct_=void 0)},650))}};var e={nameSpace:"draggable",which:void 0,defaultZindex:999999,holdTime:void 0,isProxy:!(x.fn.mousetip=function(t){var e=x.extend({title:"",activedColor:"#FFD334"},t);return this.each(function(){var t=x(this);t.data("tipopts")||t.on($).data("tipopts",e)}),this}),disabled:!1,handler:void 0,cursor:"move",axis:void 0,onDragReady:void 0,onStartDrag:void 0,onDrag:void 0,onStopDrag:void 0,onMouseUp:void 0};function k(t){var e=t.data,i=e.options,a=e._data;if(!e.hasCallStartFn){var o=t.pageX-a.startX,s=t.pageY-a.startY;if(0===o&&0===s)return void console.log("过滤没有发生实际移动的 _docMove");if(e.hasCallStartFn=!0,e.target.data("_mvdata",a),e.callStartDragFn){var n=e.movingTarget.css("z-index");"auto"===n&&(n=i.defaultZindex),e.zIndex=n,e.movingTarget.css("z-index",**********);try{i.onStartDrag({state:e})}catch(t){console.error?console.error("onStartDrag error "+t):console.log("onStartDrag error "+t)}}}if(function(t){var e=t.data,i=e._data,a=e.options,o=t.pageX-i.startX,s=t.pageY-i.startY;"v"===a.axis?o=0:"h"===a.axis&&(s=0);var n=i.startLeft+o,r=i.startTop+s;i.leftOffset=o,i.topOffset=s,i.oldLeft=i.left,i.oldTop=i.top,i.left=n,i.top=r;var l=!0;if(e.onDragFn){var d={state:e,which:a.which},h=a.onDrag(d);void 0!==h&&(l=h)}i.apply=l}(t),a.apply&&e.movingTarget){var r={left:a.left+a.fixLeft,top:a.top+a.fixTop,cursor:e.options.cursor};e.movingTarget.css(r)}void 0===i.notClearRange&&(document.selection?document.selection.empty():y.getSelection&&y.getSelection().removeAllRanges())}function C(t){var e=t.data,i=e._data,a=e.options,o=a.nameSpace,s=e.target;if(b.off("."+o),s.css("cursor",i.srcsor),a.isProxy){e.movingTarget.remove();var n={left:i.left,top:i.top};s.css(n)}var r,l=a.onStopDrag;if("function"==typeof l&&e.hasCallStartFn&&(r=l({state:e})),e.target.removeData("_mvdata"),e.hasCallStartFn){var d=e.zIndex;e.movingTarget.css("z-index",d)}else a.onMouseUp&&a.onMouseUp({state:e});if(i.leftOffset=0,i.topOffset=0,w().css("cursor","default"),void 0!==r)return r}function T(t){var e=t.data,i=e.options,a=e.target;if(i.isProxy){var o=e.target.offset(),s=e.target.outerWidth(),n=e.target.outerHeight();a=x("<div style='cursor:"+i.cursor+";position:absolute;width:"+s+"px;height:"+n+"px;top:"+o.top+"px;left:"+o.left+"px' class='k_draggable_proxy'></div>").appendTo(w())}"function"==typeof i.onDrag&&(e.onDragFn=!0),e.movingTarget=a,w().css("cursor",i.cursor)}function n(t){var e=x(this).data("dragtimer");if(e){clearTimeout(e);var i=t.data;if(i.target.removeData("_mvdata"),!i.hasCallStartFn){var a=i.options;a.onMouseUp&&a.onMouseUp({state:i})}}}function r(t,e){e&&!t.pageX&&(t.pageX=e.pageX,t.pageY=e.pageY,t.which=e.which);var i=t.data,a=i.target,o=i.options;if(!(o.which&&o.which!==t.which||o.disabled)){var s=!0;if("function"==typeof o.onDragReady&&(s=o.onDragReady.call(a,i)),s){var n=a.position(),r=$B.getAnglePositionOffset(a),l=r.fixTop,d=r.fixLeft,h=a.parent(),c=0,p=0;h[0]!==document.body&&(c=h.scrollLeft(),p=h.scrollTop()),a.css({position:"absolute",top:n.top+l+p,left:n.left+d+c});var f=a.css("cursor");f||(f="default");var u=a.position(),g={startLeft:u.left+c,startTop:u.top+p,scrollLeft:c,scrollTop:p,left:u.left+c,top:u.top+p,oldLeft:void 0,oldTop:void 0,startX:t.pageX,startY:t.pageY,width:a.outerWidth(),height:a.outerHeight(),fixTop:l,fixLeft:d,srcsor:f,leftOffset:0,topOffset:0};i.hasCallStartFn=!1,i._data=g,i.parent=h,i.callStartDragFn="function"==typeof o.onStartDrag;var v=o.nameSpace;if(void 0!==o.holdTime){var _=x(this),m=setTimeout(function(){a.css("cursor",i.options.cursor),T({data:i}),b.on("mousemove."+v,i,k),b.on("mouseup."+v,i,C)},o.holdTime);_.data("dragtimer",m)}else a.css("cursor",i.options.cursor),b.on("mousedown."+v,i,T),b.on("mousemove."+v,i,k),b.on("mouseup."+v,i,C)}}}x.fn.draggable=function(a){var o="draggable";if("string"==typeof a)switch(1<arguments.length&&(o=arguments[1]),a){case"enable":return this.each(function(){var t=x(this),e=t.data(o);e&&(e.options.disabled=!1),t.css("cursor",e.options.cursor)});case"disable":return this.each(function(){var t=x(this),e=t.data(o);e&&(e.options.disabled=!0),t.css("cursor","default")});case"unbind":return this.each(function(){var t=x(this),e=t.data(o);e.handler.off("."+o),t.removeData(o),delete e.handler,delete e.target,delete e.options,delete e.parent});default:throw new Error("不支持:"+a)}var s=x.extend({},e,a);return s.nameSpace&&(o=s.nameSpace),this.each(function(){var t=x(this),e=t;s.handler&&(e="string"==typeof s.handler?t.find(s.handler):s.handler).css("cursor",a.cursor);var i=t.data(o);i&&(i.handler.off("."+o),delete i.handler,delete i.target,delete i.parent,delete i.options,delete i._data),i={handler:e,target:t,options:s},t.data(o,i),e.on("mousedown."+o,i,r),void 0!==s.holdTime&&e.on("mouseup."+o,i,n)})},x.fn.loading=function(t){var e=$B.config.loading;t&&(e=t);var i="<div style='width:auto;z-index:**********;' class='k_datagrid_loading'><i class='fa fa-cog fa-spin fa-1.6x fa-fw margin-bottom'></i>"+e+"</div>";return this.each(function(){x(this).html(i)})};var l={slider:{"background-color":"#6F6F6F","border-radius":"8px"},size:"6px",hightColor:"#05213B",bar:{"background-color":"#e4e4e4","border-radius":"8px",opacity:.5},setPadding:!0,display:"show",wheelStep:60,axis:"xy",checkItv:0,minHeight:50,minWidth:50};function d(t,e){this.opts=e,this.yscroll=e.yscroll,this.xscroll=e.xscroll,this.$doc=x(document),this.jqObj=t,this.$scrollWrap=this.jqObj.children(".k_scroll_wrap"),this.scrollHeight=this.$scrollWrap[0].scrollHeight,this.scrollWidth=this.$scrollWrap[0].scrollWidth,this.yscroll&&(this.$vbar=this.jqObj.children(".k_scroll_v_bar").css(this.opts.bar),this.$vbtn=this.$vbar.children("div").css(this.opts.slider),this._initVbtnHeight(),this._initVbtnDragEvent(),this._bindMousewheel(),this._hightLine(this.$vbtn)),this.xscroll&&(this.$Hbar=this.jqObj.children(".k_scroll_h_bar").css(this.opts.bar),this.$Hbtn=this.$Hbar.children("div").css(this.opts.slider),this._hightLine(this.$Hbtn),this._initHbtnWidth(),this._initHbtnDragEvent()),this._bindContentScrollListner();var i=this;0<this.opts.checkItv&&(this.yscroll||this.xscroll)&&(this.ivt=setInterval(function(){0===i.jqObj.parent().length?i.destroy():i.resetSliderPosition()},this.opts.checkItv)),this.showOrHideVbtn(),this.showOrHideHbtn(),"auto"===this.opts.display&&(this.yscroll||this.xscroll)&&(this.fadeOut(),this.jqObj.on({mouseenter:function(){i.showOrHideVbtn(),i.showOrHideHbtn(),i.autoHide=!1},mouseleave:function(){i.scrolling?i.autoHide=!0:i.fadeOut()}})),x(y).resize(function(){i.resetSliderPosByTimer()})}return d.prototype={_initHbtnWidth:function(){var t=this.scrollWidth,e=this.$scrollWrap.width()/t,i=this.$Hbar.width(),a=i/2,o=e*i;o<this.opts.minWidth?o=this.opts.minWidth:a<o&&(o=a),this.$Hbtn.css("width",o)},_initHbtnDragEvent:function(){var i,a,o,s=this,t=this.$Hbtn,n=this.$doc;function r(t){t.preventDefault(),null!=i&&(s.$Hbtn.data("ishl")||(s.$Hbtn.css({"background-color":s.opts.hightColor}).data("ishl",!0),s.$Hbar.css({opacity:.9})),s.scrollLeft(a+(t.pageX-i)*o))}t.on("mousedown",function(t){t.preventDefault(),i=t.pageX,a=s.$scrollWrap[0].scrollLeft;var e=s.getMaxScrollLeftPosition();o=e/s.getMaxHbtnPosition(),s.scrolling=!0,n.on("mousemove.kscroll",r).on("mouseup.kscroll",function(){n.off(".kscroll"),s.maxScrollLeft=void 0,s.maxHbtnPosition=void 0,s.$Hbtn.css({"background-color":s.opts.slider["background-color"]}).removeData("ishl"),s.$Hbar.css({opacity:s.opts.bar.opacity}),s.scrolling=!1,s.autoHide&&s.fadeOut()})}),t.on("mouseup",function(){n.off("mousemove.kscroll"),n.off("mouseup.kscroll")})},getMaxScrollLeftPosition:function(){if(void 0===this.maxScrollLeft){var t=this.$scrollWrap.width();this.maxScrollLeft=Math.max(t,this.scrollWidth)-t}return this.maxScrollLeft},getMaxHbtnPosition:function(){return void 0===this.maxHbtnPosition&&(this.maxHbtnPosition=this.$Hbar.width()-this.$Hbtn.width()),this.maxHbtnPosition},getHbtnPosition:function(){var t=this.getMaxHbtnPosition();return Math.min(t,t*this.$scrollWrap[0].scrollLeft/this.getMaxScrollLeftPosition())},showOrHideHbtn:function(t){this.xscroll&&(t||(t=this.scrollWidth),t<=this.$scrollWrap.width()?(this.$Hbar.hide(),this.jqObj.css("padding-bottom",0)):(this.$Hbar.show(),this.jqObj.css("padding-bottom",this.$Hbar.height()+"px")))},scrollLeft:function(t){this.$scrollWrap.scrollLeft(t)},_initVbtnHeight:function(){var t=this.scrollHeight,e=this.$vbar.height(),i=e/2,a=this.$scrollWrap.height()/t*e;a<this.opts.minHeight?a=this.opts.minHeight:i<a&&(a=i),this.$vbtn.css("height",a)},_initVbtnDragEvent:function(){var i,a,o,s=this,t=this.$vbtn,n=this.$doc;function r(t){t.preventDefault(),null!=i&&(s.$vbtn.data("ishl")||(s.$vbtn.css({"background-color":s.opts.hightColor}).data("ishl",!0),s.$vbar.css({opacity:.9})),s.scrollTop(a+(t.pageY-i)*o))}t.on("mousedown",function(t){t.preventDefault(),i=t.pageY,a=s.$scrollWrap[0].scrollTop;var e=s.getMaxScrollTopPosition();o=e/s.getMaxVbtnPosition(),s.scrolling=!0,n.on("mousemove.kscroll",r).on("mouseup.kscroll",function(){n.off(".kscroll"),s.maxScrollTop=void 0,s.maxVbtnPosition=void 0,s.$vbtn.css({"background-color":s.opts.slider["background-color"]}).removeData("ishl"),s.$vbar.css({opacity:s.opts.bar.opacity}),s.scrolling=!1,s.autoHide&&s.fadeOut()})}),t.on("mouseup",function(){n.off("mousemove.kscroll"),n.off("mouseup.kscroll")})},getMaxScrollTopPosition:function(){if(void 0===this.maxScrollTop){var t=this.$scrollWrap.height();this.maxScrollTop=Math.max(t,this.scrollHeight)-t}return this.maxScrollTop},getVbtnPosition:function(){var t=this.getMaxVbtnPosition();return Math.min(t,t*this.$scrollWrap[0].scrollTop/this.getMaxScrollTopPosition())},getMaxVbtnPosition:function(){return void 0===this.maxVbtnPosition&&(this.maxVbtnPosition=this.$vbar.height()-this.$vbtn.height()),this.maxVbtnPosition},_bindMousewheel:function(){var a=this;a.$scrollWrap.on("mousewheel DOMMouseScroll",function(t){t.preventDefault();var e=t.originalEvent,i=e.wheelDelta?-e.wheelDelta/120:(e.detail||0)/3;a.scrollTop(a.$scrollWrap[0].scrollTop+i*a.opts.wheelStep),"none"===a.$vbar.css("display")&&(a.showOrHideVbtn(),a.xscroll&&a.showOrHideHbtn())})},showOrHideVbtn:function(t){this.yscroll&&(t||(t=this.scrollHeight),t<=this.$scrollWrap.height()?(this.$vbar.hide(),this.jqObj.css("padding-right",0)):(this.$vbar.show(),"auto"!==this.opts.display||this.opts.setPadding?this.jqObj.css("padding-right",this.$vbar.width()+"px"):this.jqObj.css("padding-right",0)))},scrollTop:function(t){this.$scrollWrap.scrollTop(t)},resetSliderPosByTimer:function(){var t=this;clearTimeout(this.sliderPosByTimer),this.sliderPosByTimer=setTimeout(function(){t.resetSliderPosition()},500)},resetSliderPosition:function(){if(this.yscroll){var t=this.$scrollWrap[0].scrollHeight;this.scrollHeight=t,this.maxScrollTop=void 0,(this.maxVbtnPosition=void 0)!==this.lastScrollHeight&&this.lastScrollHeight!==t&&"0px"!==this.$vbtn[0].style.top&&(this.$vbtn[0].style.top=this.getVbtnPosition()+"px"),this.lastScrollHeight=t,this.showOrHideVbtn(t)}if(this.xscroll){var e=this.$scrollWrap[0].scrollWidth;this.scrollWidth=e,this.maxScrollLeft=void 0,(this.maxHbtnPosition=void 0)!==this.lastScrollWidth&&this.lastScrollWidth!==e&&"0px"!==this.$Hbtn[0].style.left&&(this.$Hbtn[0].style.left=this.getHbtnPosition()+"px"),this.lastScrollWidth=e,this.showOrHideHbtn(e)}},_hightLine:function(t){var e=this;t.on({mouseenter:function(){t.css({"background-color":e.opts.hightColor}).data("ishl",!0),t.parent().css({opacity:.9})},mouseleave:function(){t.css({"background-color":e.opts.slider["background-color"]}).removeData("ishl"),t.parent().css({opacity:e.opts.bar.opacity})}})},_bindContentScrollListner:function(){var t=this;t.$scrollWrap.on("scroll",function(){t.yscroll&&(t.$vbtn[0].style.top=t.getVbtnPosition()+"px"),t.xscroll&&(t.$Hbtn[0].style.left=t.getHbtnPosition()+"px")})},fadeOut:function(){this.yscroll&&this.$vbar.fadeOut(),this.xscroll&&this.$Hbar.fadeOut()},destroy:function(){for(var t in clearInterval(this.ivt),clearTimeout(this.sliderPosByTimer),this)this.hasOwnProperty(t)&&delete this[t]}},x.fn.myscrollbar=function(t,e){var i=x.extend({},l,t),a=x("<div style='width:100%;height:100%;position:relative;' class='k_scroll_main_wrap k_box_size'></div>"),o=x("<div style='position:relative;width:100%;height:100%;overflow:hidden;' class='k_scroll_wrap k_box_size'></div>").appendTo(a),s=this.parent();this.detach().appendTo(o),i.yscroll="xy"===i.axis||"y"===i.axis,i.xscroll="xy"===i.axis||"x"===i.axis,i.yscroll&&(a.css("padding-right",i.size),"auto"!==i.display||i.setPadding||a.css("padding-right",0),x("<div class='k_scroll_v_bar' style='overflow:hidden;position: absolute;top: 0;right: 0;width:"+i.size+";height: 100%;background-color: #e4e4e4;'><div style='cursor:pointer; position: absolute;top: 0;left: 0;width:"+i.size+";height: 30px;background-color: #525252;'></div></div>").appendTo(a)),i.xscroll&&(a.css("padding-bottom",i.size),x("<div class='k_scroll_h_bar' style='overflow:hidden;position: absolute;left: 0;bottom: 0;width:100%;height:"+i.size+";background-color: #e4e4e4;'><div style='cursor:pointer; position: absolute;top: 0;left: 0;width:50px;height:"+i.size+";background-color: #525252;'></div></div>").appendTo(a)),s.append(a);var n=new d(a,i);return this.data("scrollIns",n),this},x.fn.getMyScrollIns=function(){return this.data("scrollIns")},x.fn.myslider=function(d,h){var c,p="string"==typeof d;p||(c=x.extend({},{width:"100%",height:16,start:0,end:100},d),this.data("opts",c)),this.each(function(){var o,s,t=x(this);if(p){if("setValue"===d||"setValueSilent"===d){var e=t.data("opts"),i=t.children("div"),a=i.children("span");s||(o=i.width()-a.width(),s=o/(e.end-e.start));var n=h*s;a.css("left",n),e.onChange&&"setValueSilent"!==d&&e.onChange(h,!0)}return!0}var r=x("<div style='z-index:1; position:relative;border:1px solid #c5c5c5;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius: 3px;'></div>");r.css({width:c.width,height:c.height});var l=x("<span tabindex='0' style='cursor:pointer;background:#fff;position:absolute;z-index:2; border:1px solid #c5c5c5;top:-4px;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius: 3px;width:12px;height:"+(c.height+6)+"px'/>").appendTo(r);r.appendTo(t),l.draggable({axis:"h",onStartDrag:function(t){s||(o=r.width()-l.width(),s=o/(c.end-c.start))},onDrag:function(t){var e=t.state._data;e.left<0?e.left=0:e.left>o&&(e.left=o);var i=parseInt(e.left/s);c.onChange&&c.onChange(i)}}).keydown(function(t){var e,i=t.which,a=x(this);39===i?(e=a.position().left+1,void 0===o&&(o=r.width()-l.width()),e<=o&&(e<0&&(e=0),o<e&&(e=o),a.css("left",e))):37===i&&((e=a.position().left-1)<0&&(e=0),0<=e&&a.css("left",e)),void 0!==e&&(s||(s=o/(c.end-c.start)),(e=parseInt(e/s))<c.start&&(e=c.start),e>c.end&&(e=c.end),c.onChange&&c.onChange(e))})})},x}),function(e,i){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B","plugin"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(t,l){var d={target:void 0,zoomScale:!1,poitStyle:{color:"#FF292E","font-size":"8px"},lineStyle:{"border-color":"#FF292E","border-size":"1px"},onDragReady:void 0,dragStart:void 0,onDrag:void 0,dragEnd:void 0};function h(t){l.extend(this,h);var e=$("body");this.opts=$.extend({},d,t),this.target=this.opts.target;var n,r,j,O,P=this,I={};var i={nameSpace:"dragrezie",which:1,cursor:"move",axis:void 0,onStartDrag:function(t){var e=t.state;n=e.target,r=n.data("resizeData"),j={height:P.target.height(),width:P.target.width()},O=P.target.position(),(I={}).line1={width:P.line1.outerWidth(),position:P.line1.position()},I.line2={height:P.line2.outerHeight(),position:P.line2.position()},I.line3={width:P.line3.outerWidth(),position:P.line3.position()},I.line4={height:P.line4.outerHeight(),position:P.line4.position()},I.point0={position:P.poitArr[0].position()},I.point1={position:P.poitArr[1].position()},I.point2={position:P.poitArr[2].position()},I.point3={position:P.poitArr[3].position()},P.zoomScaleUpdateSet=I,P.opts.dragStart&&P.opts.dragStart.call(n,t)},onDrag:function(t){var e=t.state,i=r.index,a=r._type,o=!0;if(P.opts.onDrag){var s=P.opts.onDrag.call(n,t);void 0!==s&&(o=s)}!function(t,e,i,a){var o=t._data,s=o.leftOffset,n=o.topOffset,r=I.line1,l=I.line2,d=I.line3,h=I.line4,c={};if(i){P.opts.zoomScale&&(n=0===e||2===e?s:-s,o.top=o.startTop+n);var p,f,u={},g={},v={},_={};0===e?(u.top=r.position.top+n,u.left=r.position.left+s,u.width=r.width-s,P.line1.css(u),g.top=l.position.top+n,g.height=l.height-n,P.line2.css(g),v.left=d.position.left+s,v.width=d.width-s,P.line3.css(v),_.height=h.height-n,_.top=h.position.top+n,_.left=h.position.left+s,P.line4.css(_),P._updatePoitPosition(0,n,1),P._updatePoitPosition(s,0,3),c.width=j.width-s,c.height=j.height-n,c.top=O.top+n,c.left=O.left+s):1===e?(u.top=r.position.top+n,u.width=r.width+s,P.line1.css(u),g.top=l.position.top+n,g.height=l.height-n,g.left=l.position.left+s,P.line2.css(g),v.width=d.width+s,P.line3.css(v),_.top=h.position.top+n,_.height=h.height-n,P.line4.css(_),P._updatePoitPosition(0,n,0),P._updatePoitPosition(s,0,2),c.width=j.width+s,c.height=j.height-n,c.top=O.top+n):2===e?(u.width=r.width+s,P.line1.css(u),g.height=l.height+n,g.left=l.position.left+s,P.line2.css(g),v.width=d.width+s,v.top=d.position.top+n,P.line3.css(v),_.height=h.height+n,P.line4.css(_),P._updatePoitPosition(s,0,1),P._updatePoitPosition(0,n,3),c.width=j.width+s,c.height=j.height+n):(u.left=r.position.left+s,u.width=r.width-s,P.line1.css(u),g.height=l.height+n,P.line2.css(g),v.top=d.position.top+n,v.left=d.position.left+s,v.width=d.width-s,P.line3.css(v),_.height=h.height+n,_.left=h.position.left+s,P.line4.css(_),P._updatePoitPosition(s,0,0),P._updatePoitPosition(0,n,2),c.width=j.width-s,c.height=j.height+n,c.left=O.left+s)}else if(0===e){p=n/2,g={top:l.position.top+n},_={top:h.position.top+n},P.line2.outerHeight(l.height-n),P.line4.outerHeight(h.height-n);var m=0,b=0;c.top=O.top+n,c.height=j.height-n,P.opts.zoomScale&&(o.left=r.position.left+p,f=r.width-n,P.line1.outerWidth(f),P.line3.outerWidth(f),P.line3.css("left",d.position.left+p),g.left=l.position.left-p,_.left=h.position.left+p,b=-(m=p),P._updatePoitPosition(-p,0,2),P._updatePoitPosition(p,0,3),c.left=O.left+p,c.width=j.width-n),P._updatePoitPosition(m,n,0),P._updatePoitPosition(b,n,1),P.line2.css(g),P.line4.css(_)}else if(1===e){p=s/2,u={width:f=r.width+s},g={left:r.position.left+s},v={width:f},c.width=j.width+s;var k=n,y=n;P.opts.zoomScale&&(o.top=l.position.top-p,g.height=l.height+s,k=-p,y=p,P._updatePoitPosition(0,k,0),P._updatePoitPosition(0,y,3),P.line4.css({height:g.height,top:h.position.top-p}),P.line1.css({top:r.position.top-p}),P.line3.css({top:d.position.top+p}),c.height=j.height+s,c.top=O.top-p),P._updatePoitPosition(s,k,1),P._updatePoitPosition(s,y,2),P.line1.css(u),P.line2.css(g),P.line3.css(v)}else if(2===e){p=n/2;var x=0,w=0,$=l.height+n;g={height:$},_={height:$},v={top:d.position.top+n},c.height=j.height+n,P.opts.zoomScale&&(o.left=d.position.left-p,v.width=d.width+n,u={width:v.width,left:r.position.left-p},P.line1.css(u),g.left=l.position.left+p,_.left=h.position.left-p,w=-(x=p),P._updatePoitPosition(-p,0,0),P._updatePoitPosition(p,0,1),c.width=j.width+n,c.left=O.left-p),P.line2.css(g),P.line4.css(_),P.line3.css(v),P._updatePoitPosition(x,n,2),P._updatePoitPosition(w,n,3)}else{p=s/2,v={width:(u={width:r.width-s,left:r.position.left+s}).width,left:u.left},_={left:h.left-s},c.width=j.width-s,c.left=O.left+s;var C=0,T=0;P.opts.zoomScale&&(T=-(C=p),P._updatePoitPosition(0,C,1),P._updatePoitPosition(0,T,2),o.top=h.position.top+p,_.height=h.height-s,g={height:l.height-s,top:o.top},P.line2.css(g),u.top=r.position.top+p,v.top=d.position.top-p,c.height=j.height-s,c.top=O.top+p),P.line1.css(u),P.line3.css(v),P.line4.css(_),P._updatePoitPosition(s,C,0),P._updatePoitPosition(s,T,3)}P.target?a&&P.target.css(c):console.log("_this.target.css(targetCss); is null")}(e,i,a,o)},onStopDrag:function(t){P.opts.dragEnd&&P.opts.dragEnd.call(n,t),r=n=void 0}};this.line1=$("<div style='cursor:s-resize;height:3px;position:absolute;border-top:1px solid;display:none;z-index:**********' _id='k_resize_line_0' class='k_resize_element k_box_size k_resize_line_0'></div>").appendTo(e),this.line2=$("<div style='cursor:w-resize;width:3px;position:absolute;border-right:1px solid;display:none;z-index:**********' _id='k_resize_line_1' class='k_resize_element k_box_size k_resize_line_1'></div>").appendTo(e),this.line3=$("<div style='cursor:s-resize;height:3px;position:absolute;border-bottom:1px solid;display:none;z-index:**********' _id='k_resize_line_2' class='k_resize_element k_box_size k_resize_line_2'></div>").appendTo(e),this.line4=$("<div style='cursor:w-resize;width:3px;position:absolute;border-left:1px solid;display:none;z-index:**********'_id='k_resize_line_3'  class='k_resize_element k_box_size k_resize_line_3'></div>").appendTo(e),i.cursor="s-resize",i.axis="v",this.line1.css(this.opts.lineStyle).draggable(i).data("resizeData",{_type:0,index:0}),i.cursor="w-resize",i.axis="h",this.line2.css(this.opts.lineStyle).draggable(i).data("resizeData",{_type:0,index:1}),i.cursor="s-resize",i.axis="v",this.line3.css(this.opts.lineStyle).draggable(i).data("resizeData",{_type:0,index:2}),i.cursor="w-resize",i.axis="h",this.line4.css(this.opts.lineStyle).draggable(i).data("resizeData",{_type:0,index:3}),this._fixLineStyle(),this.poitArr={};var a,o=0;for(i.axis=void 0;o<4;){a=0===o?"se-resize":1===o?"ne-resize":2===o?"se-resize":"ne-resize",i.cursor=a;var s=$("<div style='display:none;position:absolute;z-index:**********;cursor:"+a+"' class='k_resize_element k_resize_element_point k_box_size k_resize_point_"+o+"' _id='k_resize_point_"+o+"'></div>").appendTo(e);s.children().css(this.opts.poitStyle),s.draggable(i).data("resizeData",{_type:1,index:o}),this.poitArr[o]=s,o=++o}this.jqObj=e.children(".k_resize_element"),this.target&&this.bind(this.target)}return h.prototype={_fixLineStyle:function(){this.line1.css({"border-left":"none","border-right":"none","border-bottom":"none"}),this.line2.css({"border-left":"none","border-top":"none","border-bottom":"none"}),this.line3.css({"border-left":"none","border-right":"none","border-top":"none"}),this.line4.css({"border-top":"none","border-right":"none","border-bottom":"none"})},setStyle:function(e,t){this.line1.css(t),this.line2.css(t),this.line3.css(t),this.line4.css(t),this._fixLineStyle();var i=this;Object.keys(this.poitArr).forEach(function(t){i.poitArr[t].css(e)})},zoomScale:function(t){this.opts.zoomScale=t},setTarget:function(t){this.target&&this.target[0]===t[0]||(this.target=t)},bind:function(t){this.setTarget(t),this.show();var e=t.offset(),i={width:t.outerWidth(),height:t.outerHeight()};return this.line1.css({top:e.top-1+"px",left:e.left+"px"}).outerWidth(i.width),this.line2.css({top:e.top+"px",left:e.left+i.width-2+"px",height:i.height}),this.line3.css({top:e.top+i.height-2+"px",left:e.left+"px",width:i.width}),this.line4.css({top:e.top+"px",left:e.left-1+"px"}).outerHeight(i.height),this._initPoitPosition(),this},_updatePoitPosition:function(t,e,i){var a=this.poitArr[i],o=this.zoomScaleUpdateSet["point"+i];a.css({top:o.position.top+e,left:o.position.left+t})},_initPoitPosition:function(){var t=Object.keys(this.poitArr);if(0<t.length){this.line1.outerWidth();var n=this.line1.position(),r=(this.line2.outerHeight(),this.line2.position()),l=(this.line3.outerWidth(),this.line3.position()),d=this;t.forEach(function(t){var e,i=parseInt(t),a=d.poitArr[i],o=a.width()/2,s=a.height()/2;0===i?e={top:n.top-s+"px",left:n.left-3+"px"}:1===i?e={top:n.top-s+"px",left:r.left-2+"px"}:2===i?e={top:l.top-2+"px",left:r.left-2+"px"}:3===i&&(e={top:l.top-2+"px",left:l.left-o+"px"}),a.css(e)})}},_drag:function(t,i){var a=this;"line"===t?(this.line1.draggable(i,"dragrezie"),this.line2.draggable(i,"dragrezie"),this.line3.draggable(i,"dragrezie"),this.line4.draggable(i,"dragrezie")):"point"===t?Object.keys(this.poitArr).forEach(function(t){var e=parseInt(t);a.poitArr[e].draggable(i,"dragrezie")}):"right"===t?(this.line2.draggable(i,"dragrezie"),this.line3.draggable(i,"dragrezie"),this.poitArr[1].draggable(i,"dragrezie"),this.poitArr[2].draggable(i,"dragrezie")):"left"===t?(this.line1.draggable(i,"dragrezie"),this.line4.draggable(i,"dragrezie"),this.poitArr[0].draggable(i,"dragrezie"),this.poitArr[3].draggable(i,"dragrezie")):(this.line1.draggable(i,"dragrezie"),this.line2.draggable(i,"dragrezie"),this.line3.draggable(i,"dragrezie"),this.line4.draggable(i,"dragrezie"),Object.keys(this.poitArr).forEach(function(t){var e=parseInt(t);a.poitArr[e].draggable(i,"dragrezie")}))},disable:function(t){this._drag(t,"disable")},enable:function(t){this._drag(t,"enable")},unbind:function(){return this.target=void 0,this.hide(),this},show:function(t){if(t)this.bind(t);else{var e=this;this.line1.show(),this.line2.show(),this.line3.show(),this.line4.show(),Object.keys(this.poitArr).forEach(function(t){e.poitArr[t].show()})}return this},hide:function(t){if(!t||this.target&&t&&t[0]===this.target[0]){this.line1.hide(),this.line2.hide(),this.line3.hide(),this.line4.hide();var e=this;Object.keys(this.poitArr).forEach(function(t){e.poitArr[t].hide()})}return this},isHide:function(){return"none"===this.line1.css("display")},isShow:function(){return"none"!==this.line1.css("display")},destroy:function(){this.super.destroy.call(this)}},l.Resize=h}),function(e,i){"function"==typeof define&&define.amd?define(["$B","plugin","panel","ctxmenu"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(d,h){var c=!1;d._tab_more_close_fn=function(){c&&$("body").children(".k_tab_more_menu_wrap").hide()},$(d).on("mouseup.tab_more_close",function(){c&&d._tab_more_close_fn(),setTimeout(function(){d.parent!==d.self&&d.parent._tab_more_close_fn&&d.parent._tab_more_close_fn()},10)});var p="<iframe  class='panel_content_ifr' frameborder='0' style='overflow:visible;padding:0;margin:0;display:block;vertical-align:top;' scroll='none'  width='100%' height='100%' src='' ></iframe>",f="<div class='k_box_size' style='position:absolute;z-index:2147483600;width:100%;height:26px;top:2px;left:0;' class='loading'><div class='k_box_size' style='filter: alpha(opacity=50);-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;position:absolute;top:0;left:0;width:100%;height:100%;z-index:2147483600;background:"+h.config.loadingBackground+";'></div><div class='k_box_size' style='width:100%;height:100%;line-height:26px;padding-left:16px;position:absolute;width:100%;height:100%;z-index:2147483611;color:#fff;text-align:center;'><i style='color:#fff;font-size:16px;' class='fa animate-spin fa-spin6'></i><span style='padding-left:5px;font-weight:bold;color:#fff;'>"+h.config.loading+"</span></div></div>";function n(i){var a=this,o=this.data("data");if(o){var e,s="function"==typeof i;a.children().remove(),a.html("");var t=o.url;if(t=0<t.indexOf("?")?t+"&_t_="+h.generateMixed(5):t+"?_t_="+h.generateMixed(5),e=$(f).appendTo(a),"html"===o.dataType)h.htmlLoad({target:a,url:t,onLoaded:function(){e.fadeOut(function(){$(this).remove()}),s&&i.call(a,o.title),h.bindTextClear(a)}});else if("json"===o.dataType)h.request({dataType:"json",url:t,ok:function(t,e){s&&i.call(a,o.title,e)},final:function(t){e.fadeOut(function(){$(this).remove()})}});else{var n=$(p),r=n[0],l=h.getUUID();n.attr({name:l,id:l}),n.on("load",function(){e.fadeOut(function(){$(this).remove()});try{$(this.contentDocument.body).append("<span id='_window_ifr_id_' style='display:none'>"+l+"</span>")}catch(t){}s&&i.call(a,o.title)}),r.src=t,n.appendTo(a)}}}function u(t,e){var c=this,r="line-height:"+e+"px;",l="function"==typeof c.opts.onClick,p="function"==typeof c.opts.onClosed,i=$("<li  title='"+t.title+"' class='k_box_size' style='"+r+"'>"+t.title+"</li>").appendTo(this.$ul).click(function(e){c.$moreWrap.hide();for(var i=$(this),t=0,a=i.prev();0<a.length;)t++,a=a.prev();var o=c.$ul.data("actived");if(o&&o.attr("title")!==i.attr("title")){c.$ul.data("activedItem").css("z-index",-1);var s=c.$ul.parent().parent().css("background-color");o.animate({backgroundColor:s},100,function(){if(o.removeClass("actived"),o.attr("style",r),l&&!e.isTrigger){var t=i.data("itdata");c.opts.onClick.call(i,i.attr("title"),t)}})}c.$ul.data("actived",i.addClass("actived")),i.attr("style",r);var n=c.$body.children(".k_tab_item").eq(t).css("z-index",1);return c.$ul.data("activedItem",n),n.siblings().css("z-index",-1),c.ctxmenu&&c.ctxmenu.hide(),!1});t.data&&i.data("itdata",t.data),t.iconCls&&""!==t.iconCls&&i.prepend('<i class="fa '+t.iconCls+'"></i>&nbsp');var a=$("<div style='z-index:-1;overflow: auto;background:#ffffff;' class='k_tab_item k_box_size'></div>").appendTo(this.$body);t.url&&""!==t.url?(a.data("data",{dataType:t.dataType,url:t.url,onLoaded:t.onLoaded,title:t.title}),n.call(a,c.opts.onLoaded)):t.content&&a.html(t.content),t.actived&&i.trigger("click"),t.closeable&&($("<a style='display:none;' class='close'><i class='small-font smallsize-font fa fa-cancel-2 '></i></a>").appendTo(i).click(function(){var t=$(this).parent();if(c.$moreWrap.hide(),t.hasClass("actived")){var e=t.next();if(0<e.length)e.trigger("click");else{var i=t.prev();0<i.length?i.trigger("click"):(c.removeData("activedItem"),c.removeData("actived"))}}for(var a=t.prev(),o=0;0<a.length;)a=a.prev(),o++;var s=t.attr("title"),n=t.data("itdata");t.remove(),c.$body.children().eq(o).remove();var r=c.$ul.position().left,l=c.$ul.children().last(),d=l.position().left+l.outerWidth()+r;if(r<0){d<=c.$headerUlWrap.width()&&c.rightButton.addClass("k_tab_icon_btn_disabeld");var h=r+(c.$headerUlWrap.width()-d);0<h&&(h=0,c.leftButton.removeClass("k_tab_icon_btn_disabeld")),c.$ul.animate({left:h},150)}else c.leftButton.addClass("k_tab_icon_btn_disabeld"),d<=c.$headerUlWrap.width()&&c.rightButton.removeClass("k_tab_icon_btn_disabeld");p&&setTimeout(function(){c.opts.onClosed(s,n)},1)}),i.on({mouseenter:function(){$(this).children("a.close").show()},mouseleave:function(){$(this).children("a.close").hide()}}));var o=i.outerWidth()+i.position().left-this.$headerUlWrap.outerWidth();if(0<o){this.leftButton.show().removeClass("k_tab_icon_btn_disabeld"),this.rightButton.show().addClass("k_tab_icon_btn_disabeld");var s=o.toString();0<s.indexOf(".")&&(o=parseInt(s.split(".")[0])+2),this.$ul.animate({left:-o},150)}return i}function g(t,e){c=!0,h.extend(this,g),this.jqObj=t.addClass("k_tab_main k_box_size"),this.opts=e,this.$header=$("<div class='k_tab_header_wrap k_box_size k_tab_icon k_tab_icon_header_bg'><div class='k_tab_header_wrap_bottom_border k_box_size'></div></div>").appendTo(this.jqObj).children(),this.$headerUlWrap=$("<div class='k_tab_header_ulwrap'></div>").appendTo(this.$header);var i=this.$header.outerHeight();this.$body=$("<div class='k_tab_item_body k_box_size'></div>").appendTo(this.jqObj).css("border-top",i+"px solid #ffffff"),this.leftButton=$("<div style='left:0' class='k_tab_button_wrap_wrap k_tab_left_btn k_box_size'><div><i class='fa fa-angle-double-left'></i></div></div>").appendTo(this.$header).children(),this.rightButton=$("<div  style='right:0' class='k_tab_button_wrap_wrap k_tab_right_btn k_box_size'><div><i style='' class='fa fa-angle-double-right'></i></div></div>").appendTo(this.$header).children(),this.$ul=$("<ul class='k_box_size'></ul>").appendTo(this.$headerUlWrap);var a=$(d.document.body);this.$moreWrap=$("<div style='width:auto;height:auto;' class='k_tab_more_menu_wrap k_box_shadow'><ul></ul></div>").appendTo(a).hide();for(var o=this.$ul.height(),s=0,n=this.opts.tabs.length;s<n;++s)u.call(this,this.opts.tabs[s],o);var p=this;function l(){for(var t=p.$ul.position().left,e=p.$ul.children().last(),i=e.outerWidth()+e.position().left,a=i+t,o=p.$headerUlWrap.outerWidth();o<a;)a=(i=(e=e.prev()).outerWidth()+e.position().left)+t;var s=e.next();return 0<s.length?s:null}if(this.leftButton.children("i").css("line-height",o+"px"),this.rightButton.children("i").css("line-height",o+"px"),this.rightButton.on({mouseenter:function(){var t=$(this);if(!t.hasClass("k_tab_icon_btn_disabeld")){var e=l();if(null!==e){var i=p.$moreWrap.children();i.children().remove();var a=t.offset();p.$moreWrap.css({top:a.top+t.outerHeight()}).hide();var o=0,s=function(){var t=$(this),e=parseInt(t.attr("idx")),i=t.parent().children().length-e,a=p.$ul.children().last();for(1===i&&p.rightButton.addClass("k_tab_icon_btn_disabeld");1<i;)a=a.prev(),i--;a.trigger("click");var o=p.$headerUlWrap.width(),s=Math.abs(p.$ul.position().left),n=a.position().left+a.outerWidth()-(s+o);return 0<n&&(p.$ul.animate({left:-(s+n)},200,function(){"function"==typeof p.opts.onClick&&p.opts.onClick.call(a,a.attr("title"))}),p.leftButton.show().removeClass("k_tab_icon_btn_disabeld")),p.$moreWrap.hide(),!1},n=function(){for(var t=$(this).parent(),e=parseInt(t.attr("idx")),i=t.parent().children().length-e,a=p.$ul.children().last();1<i;)a=a.prev(),i--;a.find(".close").trigger("click");for(var o=t.next();0<o.length;)o.attr("idx",e--),o=o.next();var s=t.parent();return t.remove(),0===s.children().length&&s.parent().hide(),!1};e.clone().appendTo(i).attr("idx",o).click(s).children(".close").click(n);for(var r=e.next();0<r.length;)o++,r.clone().appendTo(i).attr("idx",o).click(s).children(".close").click(n),r=r.next();p.$moreWrap.css("left",a.left-p.$moreWrap.outerWidth()+16).show()}else p.$moreWrap.hide()}},click:function(){p.$moreWrap.hide();var t=$(this);if(!t.children().hasClass("k_tab_icon_right_disabeld")){var e=l();if(null!==e){p.leftButton.show().removeClass("k_tab_icon_btn_disabeld");var i=p.$ul.position().left,a=p.$headerUlWrap.outerWidth()-(e.position().left+i),o=(i-(e.outerWidth()-a)).toString();0<o.indexOf(".")&&(o=parseInt(o.split(".")[0])-2),p.$ul.animate({left:o},300,function(){0===e.next().length&&p.rightButton.removeClass("k_tab_icon_btn_disabeld"),t.trigger("mouseenter")})}else p.rightButton.addClass("k_tab_icon_btn_disabeld");return!1}}}),this.leftButton.on({mouseenter:function(){var o=$(this);if(!o.hasClass("k_tab_icon_btn_disabeld")){var t=p.$ul.position().left;if(0<=t)o.addClass("k_tab_icon_btn_disabeld");else{var e=p.$ul.children().first(),i=p.$moreWrap.children();i.children().remove();var a=o.offset();p.$moreWrap.css({top:a.top+o.outerHeight()}).hide();var s=0,n=parseInt(Math.abs(t)),r=e.outerWidth(),l=e.position().left+r,d=l-n,h=function(){var t=$(this),e=parseInt(t.attr("idx")),i=p.$ul.children().eq(e).trigger("click"),a=i.position().left;return p.$ul.animate({left:-a},200,function(){"function"==typeof p.opts.onClick&&p.opts.onClick.call(i,i.attr("title"))}),p.$moreWrap.hide(),0===e&&o.addClass("k_tab_icon_btn_disabeld"),p.$ul.children().last().position().left>p.$headerUlWrap.width()&&p.rightButton.removeClass("k_tab_icon_btn_disabeld"),!1},c=function(){var t=$(this).parent(),e=t.parent(),i=parseInt(t.attr("idx"));return p.$ul.children().eq(i).children(".close").trigger("click"),t.remove(),0===e.children().length&&e.parent().hide(),!1};for(e.clone().prependTo(i).attr("idx",s).click(h).children(".close").click(c);d<0;)s++,r=(e=e.next()).outerWidth(),d=(l=e.position().left+r)-n,e.clone().prependTo(i).attr("idx",s).click(h).children(".close").click(c);p.$moreWrap.css("left",a.left).show()}}},click:function(){p.$moreWrap.hide();var t=$(this);if(!t.hasClass("k_tab_icon_btn_disabeld")){var e=p.$ul.position().left;if(!(0<=e)){for(var i=parseInt(Math.abs(e)),a=p.$ul.children().first(),o=a.outerWidth(),s=a.position().left+o,n=s-i;n<0;)o=(a=a.next()).outerWidth(),n=(s=a.position().left+o)-i;var r=parseInt(e+o-n);return r===p.$ul.position().left&&(r=parseInt(r+a.prev().outerWidth())),p.$ul.animate({left:r},200,function(){0===a.prev().length&&t.addClass("k_tab_icon_btn_disabeld"),t.trigger("mouseenter")}),p.rightButton.removeClass("k_tab_icon_btn_disabeld"),!1}t.addClass("k_tab_icon_btn_disabeld")}}}),delete this.opts.tabs,p.opts.cxtmenu){$("body").contextmenu(function(){return!1});var r=this.$ul.children().mousedown(function(){p.ctxtarget=$(this)});"array"==typeof p.opts.cxtmenu?this.ctxmenu=new h.Ctxmenu(r,p.opts.cxtmenu):this.ctxmenu=new h.Ctxmenu(r,[{text:h.config.tabMenu.closeAll,iconCls:"fa-cancel",click:function(){p.$moreWrap.hide();var i=[],a=[],t=p.$ul.children();t.each(function(t){var e=$(this);0<e.children("a.close").length&&(a.push(p.$body.children().eq(t)),i.push(e))});for(var e=!1,o=0,s=i.length;o<s;++o)i[o].hasClass("actived")&&(e=!0),i[o].remove(),a[o].remove();0<(t=p.$ul.children()).length?e&&p.$ul.animate({left:0},200,function(){t.first().trigger("click")}):(p.$ul.removeData("activedItem"),p.$ul.removeData("actived"))}},{text:h.config.tabMenu.closeRight,iconCls:"fa-forward-1",click:function(){p.$moreWrap.hide(),p.rightButton.addClass("k_tab_icon_btn_disabeld");var a=[],o=[],s=!1;p.$ul.children().each(function(t){var e=$(this);if(e.attr("title")===p.ctxtarget.attr("title")){for(var i=e.next();0<i.length;){t++,0<i.children("a.close").length&&(i.hasClass("actived")&&(s=!0),a.push(i),o.push(p.$body.children().eq(t))),i=i.next()}return!1}});for(var t=0,e=a.length;t<e;++t)a[t].remove(),o[t].remove();s&&p.$ul.children().last().trigger("click"),p.$ul.animate({left:0},200,function(){})}},{text:h.config.tabMenu.closeLeft,iconCls:"fa-reply-1",click:function(){p.$moreWrap.hide();var i=[],a=[],o=!1;p.$ul.children().each(function(t){var e=$(this);if(e.attr("title")===p.ctxtarget.attr("title"))return!1;0<e.children("a.close").length&&(e.hasClass("actived")&&(o=!0),i.push(e),a.push(p.$body.children().eq(t)))});for(var t=0,e=i.length;t<e;++t)i[t].remove(),a[t].remove();o&&p.$ul.children().first().trigger("click"),p.$ul.animate({left:0},200,function(){})}},{text:h.config.tabMenu.reload,iconCls:"fa-arrows-ccw",click:function(){p.$moreWrap.hide(),p.reload(p.ctxtarget.attr("title"))}}])}}return g.prototype={_scroll2show:function(t){},active:function(i){this.$moreWrap.hide(),this.$ul.children().each(function(t){var e=$(this);e.attr("title")===i&&(e.hasClass("actived")||e.trigger("click"))})},reload:function(i){this.$moreWrap.hide();var a=-1;if(this.$ul.children().each(function(t){var e=$(this);if(e.attr("title")===i)return a=t,e.trigger("click"),!1}),-1!==a){var t=this.$body.children().eq(a);n.call(t,this.opts.onLoaded)}},close:function(o){var s=this,n=-1;this.$ul.children().each(function(t){var e=$(this);if(e.attr("title")===o){if(n=t,e.hasClass("actived")){var i=e.next();if(0<i.length)i.trigger("click");else{var a=e.prev();0<a.length?a.trigger("click"):(s.removeData("activedItem"),s.removeData("actived"))}}return e.remove(),!1}}),-1!==n&&this.$body.children().eq(n).remove()},add:function(e){this.$moreWrap.hide();var t=this.$ul.children(),i=!1;if(t.each(function(){var t=$(this);if(e.title===t.attr("title"))return i=!0,t.trigger("click"),!1}),!i){if(this.opts.tabCount&&t.length>this.opts.tabCount)return void h.alert(h.config.tabLimit.replace("[x]",this.opts.tabCount),3);e.actived=!0;var a=u.call(this,e,this.$ul.height()),o=this;a.mousedown(function(){o.ctxtarget=$(this)}),this.ctxmenu.bandTarget(a)}},getActived:function(){return this.$ul.children("li.actived")}},h.Tab=g}),function(e,i){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(r,d){var t;function T(){if(!t&&!(t=$(r.document.body).css("position","relative")).attr("_k_d_w_c")){if(t.click(function(){t.children("#k_toolbar_drop_wrap").hide()}),r.top!==r)try{$(r.top.document.body).click(function(){t.children("#k_toolbar_drop_wrap").hide()})}catch(t){}t.attr("_k_d_w_c",1)}return t}var j="<button  style='{bg}' cmd='{cmd}' id='{id}' class='k_toolbar_button k_box_size btn k_toolbar_button_{cls}'>{text}</button>",O="k_toolbar_button_disabled";function P(t){var e=$(this);if(e.hasClass(O))return!1;if(!t.data.priv)return d.alert({message:d.permission}),!1;var i=e,a=t.data._t;a.opts.context&&(i=a.opts.context);var o=e.data("click"),s=e.data("methodsObject");if(s||(s=a.opts.methodsObject),"function"==typeof o)setTimeout(function(){o.call(i,e.data("params"))},1);else{var n=r[s];n&&"function"==typeof n[o]&&setTimeout(function(){n[o].call(i,e.data("params"))},1)}return t.data.wrap&&t.data.wrap.hide(),a.opts.onOperated&&setTimeout(function(){a.opts.onOperated.call(i,e.data("params"))},1),!1}function s(t,e,i){var a,o,w=null,s=this.opts.showText?e:"",n=this;""===(a=t.color?"background-color:"+t.color+";":"")&&this.opts.color&&""!==this.opts.color&&(a="background-color:"+this.opts.color+";"),""===(o=t.fontColor?"color:"+t.fontColor:"")&&this.opts.fontColor&&""!==this.opts.fontColor&&(o="color:"+this.opts.fontColor+";"),""!==o&&(a+=o);var r=j.replace("{cmd}",t.cmd).replace("{id}",t.id).replace("{cls}",this.opts.style).replace("{text}",s).replace("{bg}",a);this.opts.params&&delete this.opts.params.Toolbar;var l=$(r).appendTo(this.buttonWrap).data("params",$.extend({cmd:t.cmd},this.opts.params,t.params)),d=l;if(i&&l.css({"margin-right":0,"border-radius":0,"-moz-border-radius":0,"-webkit-border-radius":0}),t.disabled&&l.addClass(O),""===s&&l.attr("title",t.text),l.data("click",t.click),t.iconCls&&""!==t.iconCls){var h="plain"===n.opts.style;h&&l.attr("title",e).css("background","none");var c="";n.opts.fontSize&&(c="style='font-size:"+n.opts.fontSize+"px'"),t.childrens&&0<t.childrens.length?l.append('<i style="padding-left:4px" '+c+' class="fa '+t.iconCls+'"></i>&nbsp'):l.prepend("<i "+c+' class="fa '+t.iconCls+'"></i>&nbsp');var p="#ffffff";t.iconColor?p=t.iconColor:h&&t.color?p=t.color:this.opts.iconColor&&(p=this.opts.iconColor),l.children("i").css("color",p),h&&l.css("color",p)}var C,f=l.data("params");if(f){var u=f.privilage,g=!0;void 0!==u&&"0"===u&&(g=!1,delete l.data("params").privilage,l.addClass("k_no_privilage_cls").css("color","#D1D1D1"))}(l.on("click",{_t:n,priv:g},P).data("methodsObject",t.methodsObject),t.childrens&&0<t.childrens.length)&&l.mouseenter(function(t){var e=$(this),i=e.data("ins"),a=e.offset(),o=e.data("childrens"),s=e.outerWidth();0===(C=T().children("#k_toolbar_drop_wrap")).length?(C=$("<div id='k_toolbar_drop_wrap' style='width:auto;position:absolute;display:none;top:-1000px;' class='k_box_size k_box_shadow'></div>").appendTo(T())).mouseenter(function(){clearTimeout(w)}).mouseout(function(t){var e=$(this),i=t.pageX,a=t.pageY,o=e.outerWidth(),s=e.outerHeight(),n=e.offset();i>=n.left&&i<=n.left+o&&a>n.top&&a<=n.top+s||(w=setTimeout(function(){C&&C.hide()},1500))}):C.children().remove();var n=a.top+e.outerHeight()-1,r={top:"-1000px",left:a.left,"min-width":s};C.css(r).show();for(var l=0,d=o.length;l<d;++l){var h=o[l],c=i.opts.showText?h.text:"",p=(h.toolMethods,h.color?"background-color:"+h.color:i.opts.color),f=j.replace("{cmd}",h.cmd).replace("{id}",h.id).replace("{cls}",i.opts.style).replace("{text}",c).replace("{bg}",p),u=$(f).appendTo(C).data("params",$.extend({cmd:h.cmd},i.opts.params,h.params));h.disabled&&u.addClass(O),""===c&&u.attr("title",h.text),u.data("click",h.click);var g=u.data("params").privilage,v=!0;if(void 0!==g&&"0"===g&&(v=!1,delete u.data("params").privilage,u.addClass("k_no_privilage_cls").css("color","#D1D1D1")),u.on("click",{_t:i,wrap:C,priv:v},P).data("methodsObject",h.methodsObject),h.iconCls&&""!==h.iconCls){"plain"===i.opts.style&&u.attr("title",h.text);var _="";i.opts.fontSize&&(_="style='font-size:"+i.opts.fontSize+"px'");var m=h.color?h.color:"#666666";u.prepend('<i style="color:'+m+';" '+_+' class="fa '+h.iconCls+'"></i>&nbsp')}}var b=C.outerHeight(),k=b+n,y=T().width(),x=C.outerWidth()+10-(y-a.left);return 0<x&&C.css("left",a.left-x),k>T().height()&&(n=a.top-b),C.css("top",n),C.children().mouseover(function(){clearTimeout(w)}),!1}).data("childrens",t.childrens).data("ins",n).parent().mouseenter(function(){C&&C.hide()});return d}var h=function(t,e){var i={params:null,methodsObject:"methodsObject",align:"left",style:"normal",showText:!0,onOperated:void 0,buttons:[]};d.extend(this,h),this.jqObj=t.addClass("k_toolbar_main clearfix"),this.buttonWrap=$("<div></div>").appendTo(this.jqObj),this.opts=$.extend({},i,e),"center"===this.opts.align?(this.jqObj.css("text-align","center"),this.buttonWrap.css("width","100%")):this.buttonWrap.css("float",this.opts.align);for(var a=0,o=this.opts.buttons.length;a<o;++a){var s=this.opts.buttons[a];if($.isArray(s)){for(var n,r=0,l=s.length;r<l;++r)n=this._createButtonByopt(s[r],!0);a!==o-1&&n.css("border-right","1px solid #C1C1C1")}else this._createButtonByopt(s,!1)}};return h.prototype={constructor:h,_createButtonByopt:function(t,e){var i=!0;if(void 0!==t.visualable&&(i=t.visualable),i)return s.call(this,t,t.text,e)},enableButtons:function(t){for(var e=0,i=t.length;e<i;++e){var a=t[e];this.buttonWrap.children("#"+a).removeClass("k_toolbar_button_disabled")}},disableButtons:function(t){for(var e=0,i=t.length;e<i;++e){var a=t[e];this.buttonWrap.children("#"+a).addClass("k_toolbar_button_disabled")}},delButtons:function(t){for(var e=0,i=t.length;e<i;++e){var a=t[e];this.buttonWrap.children("#"+a).remove()}},addButtons:function(t){for(var e=0,i=t.length;e<i;++e){var a=t[e],o=this.opts.showText?a.text:"";s.call(this,a,o)}},updateParams:function(t){$.extend(this.opts.params,t)}},d?d.Toolbar=h:console.log("exception >>>"),h}),function(e,i){"function"==typeof define&&define.amd?define(["$B","utils","toolbar"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(i,w){var a;var o={data:null,isTreeData:!0,params:null,url:null,textField:"text",idField:"id",showLine:!0,checkSingle:!1,extParamFiled:[],canClickParent:!0,nodeParentIcon:"k_tree_fold_closed",nodeParentOpenIcon:"k_tree_fold_open",leafNodeIcon:"k_tree_file_icon",chkEmptyIcon:"k_tree_check_empty",chkAllIcon:"k_tree_check_all",chkSomeIcon:"k_tree_check_some",fontIconColor:void 0,clickCheck:!1,plainStyle:!1,forecePlainStyle:!1,onlyNodeData:!1,tree2list:!0,checkbox:!1,disChecked:!1,clickItemCls:"k_tree_clicked_cls",toolbar:!1,methodsObject:"methodsObject",onItemCreated:null,onTreeCreated:null,onClick:null,onloaded:null,onOperated:null,onCheck:null,onToggle:null},c=$("<li><div class='k_tree_item_wrap k_box_size'><div class='k_loading'></div></div></li>");function _(t,e,i){var a=this.opts,o=t.id,s=t.text;t.data[a.idField]&&(o=t.data[a.idField]),t.data[a.textField]&&(s=t.data[a.textField]);var n=$("<li id='"+o+"'><div  deep='"+e+"' style='display:inline-block;min-width:100%;white-space:nowrap;' class='k_tree_item_wrap k_box_size'><div class='k_tree_text k_box_size'>"+s+"</div></div></li>");return function(t,e,i,a,o){var s=t.opts,n=o.isLast,r=o.isFirst,l=o.isParent,d=e.children("div");d.data("data",i).data("params",o);var h,c,p,f=d.children(".k_tree_text"),u=i.closed;if(!u&&i.children&&0===i.children.length&&(u=!0),l&&d.attr("closed",u),s.checkbox&&(p=s.chkEmptyIcon,i.checked&&(p=s.chkAllIcon),-1<p.indexOf("fa-")?(c=$('<div class="k_tree_check_box k_tree_font_icon k_tree_font_check"><i class="fa '+p+'"></i></div>').prependTo(d),s.fontIconColor&&c.children("i").css("color",s.fontIconColor)):c=$('<div class="k_tree_check_box k_tree_icon_img '+p+'"></div>').prependTo(d),(i.disChecked||t.opts.disChecked)&&c.addClass("k_tree_check_disabled"),c.on("click",{_this:t},t._checkBoxClick),!l&&i.checked&&t.triggerClickNodes.push(c)),s.plainStyle||(-1<(p=l?u?s.nodeParentIcon:s.nodeParentOpenIcon:s.leafNodeIcon).indexOf("fa-")?(h=$('<div  class="k_tree_font_icon _node_"><i class="fa '+p+'"></i></div>').prependTo(d),s.fontIconColor&&h.children("i").css("color",s.fontIconColor)):h=$('<div class="k_tree_icon_img _node_ '+p+'"></div>').prependTo(d)),s.showLine){l?u?p=n&&r?0===a?"_line_node_ k_tree_line_last_first_closed":"_line_node_ k_tree_line_last_closed":n?"_line_node_ k_tree_line_last_closed":"_line_node_ k_tree_line_closed":n&&r&&0===a?p="_line_node_ k_tree_line_last_first_open":(p="_line_node_ k_tree_line_open",0===i.children.length&&n&&(p="_line_node_ k_tree_line_last_empty_open")):p=n&&r?"k_tree_line_last":r?"k_tree_line_cross":n?"k_tree_line_last":"k_tree_line_cross";var g=$("<div class='k_tree_icon_img  "+p+"'></div>").prependTo(d);l&&(h=g)}if(0<a){var v,_,m=o.endCount,b=d.children().first(),k=a-m;if(s.showLine?(v='<div class="k_tree_icon_img  k_tree_line_last"></div>',_='<div class="k_tree_icon_img  k_tree_line_vertical"></div>'):(v='<div  class="k_tree_blank_div"></div>',_='<div class="k_tree_blank_div"></div>'),n&&m)for(;0<m;)b=$(v).insertBefore(b),m--;for(;0<k;)b=$(_).insertBefore(b),k--}if(h&&h.on("click",{_this:t},t._parentNodeClick),s.toolbar&&i.toolbar){var y=$("<div class='k_tree_tools_wrap' style='width:auto;display:inline-block;height:20px;margin-left:14px;'></div>").insertAfter(f),x={style:"plain",showText:!0,params:i,methodsObject:s.methodsObject,buttons:i.toolbar,onOperated:s.onOperated};s.iconColor&&(x.iconColor=s.iconColor),s.fontColor&&(x.fontColor=s.fontColor),new w.Toolbar(y,x),delete i.toolbar}}(this,n,t,e,i),a.onItemCreated&&a.onItemCreated.call(n,t,e,i),n.on("click",{_this:this},this._onClick),n}function u(t,e,i){if(!e.opts.checkSingle&&"0"!==t.attr("deep")){var a=t.data("data"),o=a.checked?1:0,s=t.parent(),n=s.siblings(),r=e.opts.chkEmptyIcon,l=void 0!==a.children,d=t.children(".k_tree_check_box");i&&(d=d.children("i")),l&&d.hasClass(e.opts.chkSomeIcon)&&(r=e.opts.chkSomeIcon),n.each(function(){var t=$(this).children("div").children(".k_tree_check_box");i&&(t=t.children("i")),t.hasClass(e.opts.chkAllIcon)?o++:t.hasClass("."+e.opts.chkSomeIcon)&&(r=e.opts.chkSomeIcon)}),o===n.length+1?r=e.opts.chkAllIcon:0<o&&(r=e.opts.chkSomeIcon);var h=s.parent().prev();d=h.children(".k_tree_check_box"),i&&(d=d.children("i")),d.removeClass(e.opts.chkSomeIcon+" "+e.opts.chkAllIcon+" "+e.opts.chkEmptyIcon).addClass(r),r===e.opts.chkAllIcon?h.data("data").checked=!0:h.data("data").checked=!1,u(h,e,i)}}function m(t,e,i,a){var p,f,u,g,v;f=t,u=e,g=i,v=a,(p=this).running++,setTimeout(function(){for(var t,e,i,a=0,o=$("<ul />"),s=0,n=u.length;s<n;++s){var r={isFirst:!1,isLast:!0,isParent:!1};e=(t=u[s]).children,a=0;var l=!1,d=t.closed;if(r.isParent=!!e,r.isFirst=0===s,s===u.length-1?(l=r.isLast=!0,r.isParent&&(a=1+v,0<e.length&&!t.closed&&(l=!1))):r.isLast=!1,r.endCount=l?v:0,(i=_.call(p,t,g,r)).appendTo(o),e){p._putParentRecord(i);var h="";d&&(h="style='display:none;'");var c=$("<ul "+h+"/>").addClass("k_tree_ul").appendTo(i);0<e.length&&m.call(p,c,e,g+1,a)}else p._putChildRecord(i)}o.children().appendTo(f),p.running--,v=g=u=f=p=void 0},0)}function p(t,e){this.running=0,this.triggerClickNodes=[];var i,a,o,s,n,r=this.jqObj;$.isPlainObject(t)&&(t=[t]);for(var l=0,d=t.length;l<d;++l){var h={isFirst:!1,isLast:!0,isParent:!1};if(n=(i=t[l]).closed,a=i.children,s=0,h.isParent=!!a,h.isFirst=0===l,l===t.length-1?(h.isLast=!0,h.isParent&&(s=1)):h.isLast=!1,h.endCount=s,o=_.call(this,i,e,h),r.append(o[0]),a){this._putParentRecord(o);var c="";n&&(c="style='display:none;'");var p=$("<ul "+c+"/>").addClass("k_tree_ul").appendTo(o);0<a.length&&m.call(this,p,a,e+1,s)}else this._putChildRecord(o)}}function f(s,t,e){var n,r=this,l=this.opts,d=0,h=s.prev("div.k_tree_item_wrap");0<h.length&&(n=h.data("data"),d=parseInt(h.attr("deep"))+1),c.find(".k_loading").css("margin-left",20*d);var i=c.appendTo(s),a={async:!0,url:l.url,data:t,ok:function(t,e,i){if(console.log("loaded >>> tree"),r.opts.isTreeData||(e=r._formatData(e)),n){var a=h.data("params"),o=a.endCount;r.running=0,r.triggerClickNodes=[],n.children=e,a.isLast&&o++,m.call(r,s,e,d,o),g.call(r)}else l.data=e,p.call(r,e,d);l.onloaded&&setTimeout(function(){l.onloaded(e)},10)},fail:function(t){},final:function(t){i.remove(),e&&setTimeout(function(){e(t)},200),l.requestfinal&&l.requestfinal()}};this.ajax(a)}function g(){var p=this,f=setInterval(function(){if(0===p.running){var t,e,i,a,o,s,n;clearInterval(f),p.setRootUlWidth();for(var r=[],l=0,d=p.triggerClickNodes.length;l<d;++l)if(!(i=(s=(t=p.triggerClickNodes[l]).parent()).parent()).data("skip")){e=t.hasClass("k_tree_font_check"),a=!0;for(var h=0,c=(o=i.siblings()).length;h<c;++h)if(0<(n=(i=$(o[h])).children("ul")).length){0<n.children().length&&(a=!1);break}a&&(o.data("skip",!0),r.push(o),u(s,p,e))}setTimeout(function(){for(var t=0,e=r.length;t<e;++t)r[t].removeData("skip");r=void 0},5),p.opts.onTreeCreated&&p.opts.onTreeCreated()}},10)}var s=function(t,e){if(w.extend(this,s),this.parentNodesArray=[],this.childNodesArray=[],this.jqObj=t.addClass("k_tree_ul k_tree_root k_box_size"),this.jqObj.children().remove(),this.opts=$.extend({},{},o,e),this.opts.showLine||this.opts.forecePlainStyle||(this.opts.plainStyle=!1),this.clickedItem=null,this.opts.data&&0<this.opts.data.length)this.opts.isTreeData||(this.opts.data=this._formatData(this.opts.data)),p.call(this,this.opts.data,0),g.call(this);else if(this.opts.url&&""!==this.opts.url){var i=this;f.call(this,this.jqObj,{pid:""},function(){if(0===i.jqObj.children().length){var t=i.jqObj.offset();t.top=t.top+2,t.left=t.left+12,i._tipNotDate(t)}})}this.jqObj.data("treeIns",this)};return s.prototype={setRootUlWidth:function(){var i=this,t=this.jqObj.parent().width();this.minWidth=t,this.jqObj.children().each(function(){var t=$(this),e=0;t.children("div").children().each(function(){e=$(this).outerWidth()+e}),e>i.minWidth&&(i.minWidth=e),i._loopVisiableUl(t.children("ul"))}),this.jqObj.css("min-width",this.minWidth)},_loopVisiableUl:function(t){if(0<t.length&&"none"!==t.css("display")){var i=this;t.children().each(function(){var t=$(this),e=0;t.children("div").children().each(function(){e=$(this).outerWidth()+e}),e>i.minWidth&&(i.minWidth=e),i._loopVisiableUl(t.children("ul"))})}},_formatData:function(t,e){var i=[];if(e);else for(var a=0,o=t.length;a<o;++a){var s=t[a];i.push({id:s[this.opts.idField],text:s[this.opts.textField],data:s})}return i},_tree2list:function(t,a,o){var s={},n=void 0!==a.children,r=[];if(Object.keys(a).forEach(function(t){var e=a[t],i=!0;o&&(i=o(t,n,a)),"children"===t?r=e:i&&($.isPlainObject(e)&&(e=$.extend(!0,{},e)),s[t]=e)}),$.isEmptyObject(s)||t.push(s),0<r.length)for(var e=0,i=r.length;e<i;++e)this._tree2list(t,r[e],o)},_getNodeData:function(t){var i,e=t.data("params"),a=t.data("data"),o=this;if(e.isParent)if(o.opts.onlyNodeData)i={},Object.keys(a).forEach(function(t){if("children"!==t){var e=a[t];$.isPlainObject(e)&&(e=$.extend(!0,{},e)),i[t]=e}}),o.opts.tree2list&&(i=[i]);else if(o.opts.tree2list){var s=[];o._tree2list(s,a),i=s}else i=$.extend(!0,{},a);else i=$.extend(!0,{},a),o.opts.tree2list&&(i=[i]);return i},_onClick:function(t){var e=t.data._this,i="function"==typeof e.opts.onClick;if(i||e.opts.clickCheck){var a=$(this),o=a.children("div"),s=o.data("params");if(!e.opts.canClickParent&&s.isParent)return!1;if(e.clickedItem&&e.clickedItem.removeClass(e.opts.clickItemCls),e.clickedItem=o.addClass(e.opts.clickItemCls),e.opts.clickCheck){var n=o.children(".k_tree_check_box");n.hasClass("k_tree_check_disabled")||n.trigger("click")}if(i){var r=e._getNodeData(o);e.opts.onClick.call(a,r,s)}}return!1},_checkBoxClick:function(t){var e=t.data._this,i=$(this);if(t.isTrigger||!i.hasClass("k_tree_check_disabled")){var a=i.parent(),o=a.data("params");if(!e.opts.canClickParent&&o.isParent)return!1;var s=a.data("data"),n=0<a.next().length,r=i.children("i"),l=i.hasClass("k_tree_font_icon");if(0<r.length&&(i=r),s.checked?(i.removeClass(e.opts.chkAllIcon).removeClass(e.opts.chkSomeIcon).addClass(e.opts.chkEmptyIcon),s.checked=!1):(i.removeClass(e.opts.chkEmptyIcon).removeClass(e.opts.chkSomeIcon).addClass(e.opts.chkAllIcon),s.checked=!0),e.opts.checkSingle||(n?(u(a,e,l),function i(t,a,o,s){s.opts.checkSingle||t.next().children().each(function(){var t=$(this).children("div"),e=t.children(".k_tree_check_box");a&&(e=e.children("i")),e.removeClass(s.opts.chkSomeIcon+" "+s.opts.chkAllIcon+" "+s.opts.chkEmptyIcon),o?(e.addClass(s.opts.chkAllIcon),t.data("data").checked=!0):(e.addClass(s.opts.chkEmptyIcon),t.data("data").checked=!1),0<t.next().length&&i(t,a,o,s)})}(a,l,s.checked,e)):u(a,e,l)),e.opts.onCheck&&!e._noNotify){var d=e._getNodeData(a);e.opts.onCheck(d,o,s.checked)}return"function"==typeof e.onWatcher&&e.onWatcher.call(e),!1}},_changeNodeUi:function(t){var e,i,a,o,s=t.parent(),n=s.parent(),r=0===n.next().length,l=0,d=(e=t.hasClass("_node_")?t:t.next("._node_")).children("i"),h="none"===s.next().css("display");if(0<d.length&&(e=d),r&&this.opts.showLine)for(i=(a=n.parent()).parent();a[0]!==this.jqObj[0]&&0===i.next().length;)l++,i=(a=i.parent()).parent();if(t.hasClass("_node_"))h?e.removeClass(this.opts.nodeParentOpenIcon).addClass(this.opts.nodeParentIcon):e.removeClass(this.opts.nodeParentIcon).addClass(this.opts.nodeParentOpenIcon);else if(h){if(t.removeClass("k_tree_line_open k_tree_line_last_empty_open"),r)for(t.addClass("k_tree_line_last_closed"),o=t;0<l;)o=o.prev().removeClass("k_tree_line_vertical").addClass("k_tree_line_last"),l--;else t.addClass("k_tree_line_closed");e.removeClass(this.opts.nodeParentOpenIcon).addClass(this.opts.nodeParentIcon)}else for(t.removeClass("k_tree_line_last_closed k_tree_line_closed"),t.addClass("k_tree_line_open"),e.removeClass(this.opts.nodeParentIcon).addClass(this.opts.nodeParentOpenIcon),o=t;0<l;)o=o.prev().removeClass("k_tree_line_last").addClass("k_tree_line_vertical"),l--},_putParentRecord:function(t){this.parentNodesArray.push(t)},_putChildRecord:function(t){this.childNodesArray.push(t)},_parentNodeClick:function(t){var e=$(this);if(!e.data("busy")){var i=e.parent(),a=i.data("data"),o=t.data._this,s="id"===o.opts.idField?a.id:a.data[o.opts.idField],n=i.next(),r=0===n.children().length;e.data("busy",!0);var l=e.offset();if("true"===i.attr("closed"))if(r&&o.opts.url&&""!==o.opts.url){for(var d={pid:s},h=0,c=o.opts.extParamFiled.length;h<c;++h)d[o.opts.extParamFiled[h]]=a.data[o.opts.extParamFiled[h]];n.show(),f.call(o,n,d,function(t){i.removeAttr("closed"),e.removeData("busy"),o._changeNodeUi(e),0===n.children().length&&(e.trigger("click"),o._tipNotDate(l))})}else n.slideDown(150,function(){i.removeAttr("closed"),e.removeData("busy"),o._changeNodeUi(e),o.setRootUlWidth(),r&&(e.trigger("click"),o._tipNotDate(l)),o.opts.onToggle&&o.opts.onToggle("show")});else n.slideUp(150,function(){i.attr("closed",!0),e.removeData("busy"),o._changeNodeUi(e),o.setRootUlWidth(),o.opts.onToggle&&o.opts.onToggle("hide")})}return!1},_tipNotDate:function(t){var e=$("<div style='width:auto;position:absolute;top:-1000px;left:"+t.left+"px' class='k_tree_empty_tip k_box_size'>"+w.config.noData+"</div>").appendTo((a||(a=$(i.document.body).css("position","relative")),a));e.css("top",t.top),setTimeout(function(){e.fadeOut(450,function(){e.remove()})},1200)},reload:function(t,e){var a,i={};1===arguments.length?$.isPlainObject(t)?(a=this.jqObj,i=t):a=t:(a=t,i=e);var o,s=a[0]===this.jqObj[0],n=this;if(s)a.children().remove();else{var r=a.parent().attr("id");i.pid=r}a.children().remove(),f.call(this,a,i,function(t){if(s)0===n.jqObj.children().length&&((o=n.jqObj.offset()).top=o.top+2,o.left=o.left+12,n._tipNotDate(o));else{var e=a.prev();e.removeAttr("closed");var i=e.children("._line_node_");i.removeData("busy"),o=i.offset(),n._changeNodeUi(i),0===a.children().length&&(i.trigger("click"),n._tipNotDate(o))}})},reset:function(){if(this.clickedItem&&(this.clickedItem.removeClass(this.opts.clickItemCls),this.clickedItem=void 0),this.opts.checkbox)for(var t=0,e=this.childNodesArray.length;t<e;++t){var i=this.childNodesArray[t].children("div");i.data("data").checked&&i.children(".k_tree_check_box").trigger("click")}},updateNode:function(t,e){},getClickItem:function(){if(this.clickedItem)return this._getNodeData(this.clickedItem)},getCheckedData:function(t){var o=!1,s=!1;t&&(void 0!==t.onlyId&&(o=t.onlyId),void 0!==t.onlyChild&&(s=t.onlyChild));for(var e=[],n=this,i=this.opts.data,a=function(t,e,i){if(!i.checked)return!1;var a=!0;return o&&t!==n.opts.idField&&(a=!1),s&&e&&(a=!1),a},r=0,l=i.length;r<l;++r)this._tree2list(e,i[r],a);return e},getParentList:function(t){var e=[];e.push(t.data("data"));for(var i=t.parent().parent();0<i.length&&i.hasClass("k_tree_ul")&&!i.hasClass("k_tree_root");)t=i.prev(),e.push(t.data("data")),i=t.parent().parent();return e.reverse()},setCheckDatas:function(t){var e=this;if(0<this.running)setTimeout(function(){e.setCheckDatas(t)},200);else{"string"==typeof t&&(t=""===t?[]:t.split(","));for(var o={},i=0,a=t.length;i<a;++i)o[t[i]]=!0;this._noNotify=!0,this.loopRoot(function(t){var e=t.children("div"),i=e.data("data"),a=t.attr("id");i.checked?o[a]||e.children(".k_tree_check_box").trigger("click"):o[a]&&e.children(".k_tree_check_box").trigger("click")}),this._noNotify=!1}},setClickedItem:function(i){i+="";var a=this;if(0<this.running)setTimeout(function(){a.setClickedItem(i)},200);else{this.clickedItem&&this.clickedItem.removeClass("k_tree_clicked_cls");var o=!1;this.loopRoot(function(t){if(o)return!0;var e=t.children("div");return t.attr("id")===i?(a.clickedItem=e.addClass("k_tree_clicked_cls"),o=!0):void 0})}},loopRoot:function(i){var a=this;this.jqObj.children().each(function(){var t=$(this);if(i(t))return!1;var e=t.children("ul");0<e.length&&a.loopChild(e.children(),i)})},loopChild:function(t,i){var a=this;t.each(function(){var t=$(this);if(i(t))return!1;var e=t.children("ul");0<e.length&&a.loopChild(e.children(),i)})}},w.Tree=s}),function(e,i){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(window,$B){function getDateDiff(t,e){return parseInt(e.getTime()-t.getTime())}var $body;function _getBody(){return $body||($body=$(window.document.body).css("position","relative")),$body}var uploadIconHtml='<i class="fa fa-up-small" style="color:#fff;padding-right:4px;"></i>',formTag='<form enctype="multipart/form-data"   method="post" name="k_fileUpload_from"></form>';function MutilUpload(t){if($B.extend(this,MutilUpload),this.target=t.target.addClass("k_mutilupload_wrap"),this.target.children().remove(),this.onselected=t.onselected,this.immediate=t.immediate,this.success=t.success,this.ondeleted=t.ondeleted,this.isCross=t.isCross,this.crossHelperPage=t.crossHelperPage,this.error=t.error,this.setParams=t.setParams,this.url=t.url,this.form=$(formTag).appendTo(this.target),this.timeout=1e3*(t.timeout?t.timeout:300),this.uploading=!1,this.init=void 0===t.init||t.init,this.init&&$.isArray(t.files))for(var e=0,i=t.files.length;e<i;++e)this._createFileInput(t.files[e]),e<i-1&&this.form.append("<br/>")}return MutilUpload.prototype={_createIframe:function(uploadFileArray){var id="_ifr_"+$B.generateMixed(8),_this=this;_this.isIniting=!0;var ifr=$('<iframe  name="'+id+'" id="'+id+'" style="display:none;"></iframe>').appendTo(_getBody());setTimeout(function(){_this.isIniting=!1},10);var crossHelperPage=_this.crossHelperPage,isCross=_this.isCross;if(isCross&&!crossHelperPage){var links=$("head").children("link");crossHelperPage=1===links.length?links.first().attr("href"):links.eq(2).attr("href")}return ifr.on("load",function(e){if(!_this.isIniting){clearInterval(ifr.data("timeoutchker")),_this.uploading=!1;var i=0,len=uploadFileArray.length,callBakFn,message,json,res,isSuccess=!0;if(isCross){if(!ifr.attr("src")){try{ifr[0].src=crossHelperPage}catch(t){}return}try{ifr.removeAttr("src"),res=ifr[0].contentWindow.name,json=eval("("+res+")")}catch(t){isSuccess=!1,message=$B.config.uploadFail,callBakFn=_this.error}}else try{var ifrDoc=ifr[0].contentWindow.document||ifr[0].contentDocument,body=$(ifrDoc.body);res=body.text(),json=eval("("+res+")")}catch(t){isSuccess=!1,message=$B.config.uploadFail,callBakFn=_this.error,console.log("to json err "+res)}for(json&&(0!==json.code?(isSuccess=!1,callBakFn=_this.error,message=$B.config.uploadFail,json.message&&""!==json.message&&(message=json.message)):callBakFn=_this.success);i<len;){var vInput=uploadFileArray[i],$a=vInput.next().removeClass("k_update_close_disable");isSuccess?(vInput.html(uploadIconHtml+vInput.attr("title")),$a.children("i").attr("class","fa fa-check-1")):vInput.html(uploadIconHtml+message),i++}"function"==typeof callBakFn&&callBakFn.call(uploadFileArray,json),ifr.remove(),uploadFileArray=void 0}}),{id:id,ifr:ifr}},_changeFn:function(t){var e=t.data._this,i=$(this),a=i.val(),o=i.next("div"),s=i.attr("accept").toLowerCase();o.next("a").children("i").attr("class","fa fa-cancel-2").attr("_class","fa fa-cancel-2");var n=i.attr("must"),r=!0;if(n&&""===a)return o.html(uploadIconHtml+n),!1;var l=a.replace(/^.+?\\([^\\]+?)(\.[^\.\\]*?)?$/gi,"$1$2"),d=l,h=i.attr("label"),c=i.attr("name");if(".*"!==s){var p=d.replace(/.+\./,"").toLowerCase();if(s.indexOf(p)<0){i.val("");var f=$B.config.uploadAccept.replace("<accept>",s);return void($B.alert?($B.alert({width:300,height:120,message:f,timeout:2}),o.html(uploadIconHtml+h)):o.html(uploadIconHtml+f))}}if(""===l&&(l=h),"function"==typeof e.onselected&&(e.onselected.call(i,d,c,s)||(i.val(""),o.html(uploadIconHtml+h),r=!1)),r&&(o.attr("title",l).html(uploadIconHtml+l),e.immediate)){var u=o.parent().siblings("div").find("input[type=file]");u.hide().attr("disabled","disabled"),e.submit(),u.show().removeAttr("disabled")}},_createFileInput:function(t){var e=t.must?"must="+t.must:"",i=t.multiple?'multiple="multiple"':"",a=$('<div class="k_upload_file_item_wrap k_box_size" style="width:auto;max-width: 100%; "></div>').appendTo(this.form),o=($("<input  "+i+" "+e+' label="'+t.label+'" id="'+t.name+'" name="'+t.name+'" type="file" accept="'+t.type+'" style="width:100%;">').appendTo(a),$('<div title="'+t.label+'" class="k_visual_file_item_input k_box_size">'+uploadIconHtml+t.label+"</div>").appendTo(a));this._bindFileEvents(o);var n=this;return $("<a class='k_upload_file_item_delete' title='"+$B.config.clearFile+"'><i class='fa fa-cancel-2'></i></a>").appendTo(a).click(function(){var t=$(this);if(t.hasClass("k_update_close_disable"))return!1;var e=t.prev(),i=e.prev(),a=i.attr("id"),o=i.attr("label"),s=t.parent();s.attr("d")?$B.confirm({width:280,height:125,message:$B.config.uploadClearConfirm,okText:$B.config.uploadClear,noText:$B.config.uploadRemove,noIcon:"fa-trash",okIcon:"fa-cancel-circled",okFn:function(){"function"==typeof n.ondeleted&&""!==i.val()&&n.ondeleted(a),e.html(uploadIconHtml+o).attr("title",o),i.val(""),e.next("a").children("i").attr("class","fa fa-cancel-2").attr("_class","fa fa-cancel-2")},noFn:function(){s.prev("br").remove(),s.remove(),"function"==typeof n.ondeleted&&n.ondeleted(a)}}):("function"==typeof n.ondeleted&&""!==i.val()&&n.ondeleted(a),e.html(uploadIconHtml+o).attr("title",o),i.val(""),e.next("a").children("i").attr("class","fa fa-cancel-2").attr("_class","fa fa-cancel-2"))}).mouseover(function(){var t=$(this).children("i"),e=t.attr("class");t.attr("class","fa fa-cancel-2").attr("_class",e)}).mouseout(function(){var t=$(this).children("i"),e=t.attr("_class");t.attr("class",e)}),a},_bindFileEvents:function(t){var i=this;t.on("click",function(){var t=$(this),e=t.prev("input");if(t.siblings("a").hasClass("k_update_close_disable"))return!1;e.unbind("change").val("").on("change",{_this:i},i._changeFn),e.trigger("click")})},submit:function(){if(this.immediate||!this.uploading){this.uploading=!0,clearTimeout(this.tipTimer);var a=[],o=[],s=!1,n=[];if(this.form.children(".k_upload_file_item_wrap").each(function(){var t=$(this).children("input");if(t.attr("disabled"))return!0;var e=t.next(),i=e.next();""===t.val()&&t.attr("must")?(e.html("<span style='color:#FFBCBF'>"+uploadIconHtml+$B.config.uploadNotEmpty+"</span>"),o.push(e)):(e.html("<span style='color:#fff'><span style='padding-right:4px;'><i style='color:#fff' class='fa fa-spin3 animate-spin'></i></span>"+$B.config.uploading+"</span>"),i.addClass("k_update_close_disable"),a.push(e),s=!0,n.push(t.attr("id")))}),s){if(this.form.children("input[type=hidden]").remove(),"function"==typeof this.setParams){var t=this.setParams.call(this,n);if($.isPlainObject(t))for(var e,i=Object.keys(t),r=0,l=i.length;r<l;++r)e=i[r],this.form.append("<input type='hidden' name='"+e+"' id='"+e+"' value='"+t[e]+"'/>")}var d=this,h=this._createIframe(a);d.isIniting=!1;var c=h.id,p=this.url;p=0<p.indexOf("?")?p+"&_t_="+$B.generateMixed(5):p+"?_t_="+$B.generateMixed(5),this.form.attr("target",c),this.form.attr("action",p);var f=new Date;this.form[0].submit();var u=setInterval(function(){var t=new Date;if(getDateDiff(f,t)>d.timeout){d.uploading=!1,clearInterval(h.ifr.data("timeoutchker")),h.ifr.remove(),h=void 0;for(var e=0,i=a.length;e<i;++e)a[e].html(uploadIconHtml+$B.config.uploadTimeout),a[e].siblings("a").removeClass("k_update_close_disable")}},1500);h.ifr.data("timeoutchker",u)}else this.uploading=!1;this.tipTimer=setTimeout(function(){for(var t=0,e=o.length;t<e;++t)o[t].html(uploadIconHtml+o[t].attr("title"))},1e3)}else console.log("is uploading ，donot submit！")},addFile:function(t){0<this.form.children(".k_upload_file_item_wrap").length&&this.form.append("<br/>"),this._createFileInput(t).attr("d",1)},reset:function(){this.form.children(".k_upload_file_item_wrap").each(function(){var t=$(this),e=t.children("input");e.val("");var i=e.attr("label");t.children("div").html(uploadIconHtml+i).attr("title",i),t.children("a").removeClass("k_update_close_disable").children("i").removeClass("fa-check-1").addClass("fa-cancel-2")})},hasFiles:function(){var t=!1;return this.form.children(".k_upload_file_item_wrap").each(function(){if(""!==$(this).children("input").val())return!(t=!0)}),t}},$B.MutilUpload=MutilUpload,$B.downLoad=function(args){args.target.children().remove();var inteVal,ivtTime=1500;args.ivtTime&&(ivtTime=args.ivtTime);var date=new Date,finish_down_key=date.getDay()+""+date.getHours()+date.getMinutes()+date.getSeconds()+date.getMilliseconds()+$B.generateMixed(12),_this=this,ifrId="k_"+$B.generateMixed(12),$ifr=$('<iframe name="'+ifrId+'" id="'+ifrId+'" style="display: none"></iframe>').appendTo(args.target),ifr=$ifr[0],message=args.message?args.message:"正在导出......",_url=args.url;_url=0<_url.indexOf("?")?_url+"&isifr=1&_diff="+$B.generateMixed(4):_url+"?isifr=1&_diff="+$B.generateMixed(4);var $msg=$("<h3 id='k_file_export_xls_msg_'  style='height:20px;line-height:20px;text-align:center;padding-top:12px;'><div class='loading' style='width:110px;margin:0px auto;'><i class='fa-spin3 animate-spin'></i><span style='padding-left:12px;'>"+message+"</span></div></h3>").appendTo(args.target),$form=$('<form action="'+_url+'" target="'+ifrId+'" method="post" ></form>').appendTo(args.target);args.fileName&&$form.append('<input type="hidden" id="fileName" name="fileName" value="'+encodeURIComponent(args.fileName)+'"/>'),args.sheetName&&$form.append('<input type="hidden" id="sheetName" name="sheetName" value="'+encodeURIComponent(args.sheetName)+'"/>'),args.mimeType&&$form.append('<input type="hidden" id="mimeType" name="mimeType" value="'+encodeURIComponent(args.mimeType)+'"/>'),$form.append('<input type="hidden" id="k_finish_down_key_" name="k_finish_down_key_" value="'+finish_down_key+'"/>');var isModel=0;$.isPlainObject(args.model)&&(isModel=1,$form.append('<input type="hidden" id="modelfile" name="modelfile" value="'+encodeURIComponent(args.model.file)+'"/>'),$form.append('<input type="hidden" id="startrow" name="startrow" value="'+args.model.startRow+'"/>')),$form.append('<input type="hidden" id="ismodel" name="ismodel" value="'+isModel+'"/>'),$.isPlainObject(args.params)&&Object.keys(args.params).forEach(function(t){var e=args.params[t];$form.append('<input type="hidden" id="'+t+'" name="'+t+'" value="'+encodeURIComponent(e)+'"/>')});var callReturn="function"==typeof args.onSuccess;$ifr.on("load",function(){clearInterval(inteVal);try{var _$body=$(window.frames[ifrId].document.body),res=_$body.text();if(res&&""!==res){var json=eval("("+res+")");return $msg.html("<h2>"+json.message+"</h2>"),void(0===json.code&&callReturn&&args.onSuccess(json))}}catch(t){return void $msg.html("<h2>"+$B.config.expException+"</h2>")}}),$form[0].submit();for(var regex=/(\/\w+)/g,match,lastChar;match=regex.exec(args.url),null!==match&&(lastChar=match[0]),null!==match;);var url=args.url.replace(lastChar,"/checkresponse");url=0<url.indexOf("?")?url+"&k_finish_down_key_="+finish_down_key:url+"?k_finish_down_key_="+finish_down_key,inteVal=setInterval(function(){try{for(var prt=args.target.parent();0<prt.length&&"BODY"!==prt[0].tagName;)prt=prt.parent();if("BODY"!==prt[0].tagName)return void clearInterval(inteVal);var _$body=$(window.frames[ifrId].document.body),res=_$body.text();if(res&&""!==res){clearInterval(inteVal);var json=eval("("+res+")");return void $msg.html("<h2>"+json.message+"</h2>")}}catch(t){return clearInterval(inteVal),void $msg.html("<h2>"+$B.config.expException+"</h2>")}$B.request({url:url+"&_t="+$B.generateMixed(5),ok:function(t,e,i){"null"!==t&&(clearInterval(inteVal),$msg.html("<h2>"+t+"</h2>"),callReturn&&setTimeout(function(){args.onSuccess(i)},10))}})},ivtTime)},MutilUpload}),function(e,i){"function"==typeof define&&define.amd?define(["$B","config"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(t,x){var e,c=x.config.valid.message,p=x.config.valid.regex;function w(){return e||(e=$(t.document.body)),e}function a(t,e){var i=$._data(t[0],"events");i&&i.click&&i.mouseout&&i.keyup||(t.on({click:function(){var t=$(this);t.siblings(".k_validate_tip_wrap").remove(),t.removeClass("k_input_value_err"),e.onTipFn&&e.onTipFn.call(t,"")},keyup:function(){var t=$(this);o(t,e)&&(t.siblings(".k_validate_tip_wrap").remove(),t.removeClass("k_input_value_err"))}}),t.on("mouseleave.kvalidate",function(t){o($(this),e)}))}function C(o,n,r,l){clearTimeout(n.data("remote_req_timer"));var t=setTimeout(function(){var t=n.val(),e=n.attr("id");if(e&&""!==e||(e=n.attr("name")),""!==t){var i={};i[e]=t;var a={url:o.rule.remote,data:i,ok:function(t){var e;if("string"==typeof t&&""!==t?e=t:$.isPlainObject(t)&&1===t.code&&(e=t.message),e){var i=x.getCharWidth(e)+10,a=n.parent();r.appendTo(a).children(".k_validate_tip_content").css("max_width",i).html(e),n.addClass("k_input_value_err");var o=r.offset();if((l.$winBody?l.$winBody.offset().left+l.$winBody.width()-o.left:w().width()-o.left)<i){var s=r.outerHeight();r.css({left:0,top:-s}),setTimeout(function(){r.remove()},2e3)}}},fail:function(t){}};x.request(a)}},800);n.data("remote_req_timer",t)}function T(t,e,i){var a=[];if($.isPlainObject(t.rule)){var o=typeof t.rule.regex;if("undefined"!==o){var s=null;try{s="string"===o?new RegExp(t.rule.regex,"i"):t.rule.regex}catch(t){}null===s||""===e||s.test(e)||a.push(void 0===t.msg?c.regex:t.msg)}if(void 0!==t.rule.minlength&&""!==e&&e.length<t.rule.minlength&&a.push(void 0===t.msg?c.minlength.replace("{1}",t.rule.minlength):t.msg),void 0!==t.rule.maxlength&&""!==e&&e.length>t.rule.maxlength&&a.push(void 0===t.msg?c.maxlength.replace("{1}",t.rule.maxlength):t.msg),void 0!==t.rule.range){var n=t.rule.range,r=n[0],l=n[1];if(""!==e)if(p.number.test(e)){var d=parseInt(e);(d<r||l<d)&&a.push(void 0===t.msg?c.range.replace("{1}",r).replace("{2}",l):t.msg)}else a.push("只能输入数字文本！")}}else{var h=!0;switch(t.rule){case"url":""!==e&&(p.url.test(e)||(h=!1));break;case"require":""===e&&(h=!1);break;case"email":""!==e&&(p.email.test(e)||(h=!1));break;case"number":""!==e&&(p.number.test(e)||(h=!1));break;case"digits":""!==e&&(p.digits.test(e)||(h=!1));break;case"phone":""!==e&&(p.phone.test(e)||(h=!1));break;case"chchar":""!==e&&p.chchar.test(e)&&(h=!1);break;case"wchar":""!==e&&(p.wchar.test(e)||(h=!1));break;case"telphone":""!==e&&(p.telphone.test(e)||(h=!1));break;case"enchar":""!==e&&(p.enchar.test(e)||(h=!1))}h||a.push(void 0===t.msg?c[t.rule]:t.msg)}return a}function o(n,r){var l=!0,d=n.parent().css("position","relative"),t=n.position(),e=n.outerWidth()+1,i=(n.outerHeight(),{top:t.top+3+"px",left:t.left+e+"px"}),a=n.attr("id");a&&""!==a||(a=n.attr("name"));var o="validate_"+a;d.children("."+o).remove();var h,c=$("<div style='padding:1px 3px;' class='k_validate_tip_wrap "+o+"'><div class='k_validate_tip_top'></div><div style='white-space:nowrap;padding:0' class='k_validate_tip_content'></div></div>").css(i),s=r.rules[a];if($.isPlainObject(s)||$.isArray(s))if(h=(n.tagName,$.trim(n.val())),n.hasClass("k_combox_input")&&(h=n.data("id")),$.isArray(s)){var p,f,u,g=null;$.each(s,function(){if($.isPlainObject(this.rule)&&void 0!==this.rule.remote)g=this;else{var t=T(this,h);if(0<t.length){l=!1;var e=t.join("");if(r.onTipFn&&""!==e)r.onTipFn.call(n,e);else{var i=x.getCharWidth(e)+10;n.hasClass("k_combox_input")&&(d=d.parent().css("position","relative"));var a=d.children(".k_validate_tip_wrap");0<a.length?(c=a.children(".k_validate_tip_content").css("max-width",i).html(e),c=a):c.appendTo(d).children(".k_validate_tip_content").css("max-width",i).html(e);var o=c.offset();if((r.$winBody?r.$winBody.offset().left+r.$winBody.width()-o.left:w().width()-o.left)<i){var s=c.outerHeight();c.css({left:0,top:-s}),setTimeout(function(){c.remove()},2e3)}}}else r.onTipFn&&r.onTipFn.call(n,"")}}),l&&null!==g&&(p="#old_"+a,u=!0,0<(f=r.form.find(p)).length&&f.val()===h&&(u=!1),u&&C(g,n,c,r))}else if(void 0!==s.remote)p="#old_"+a,u=!0,0<(f=r.form.find(p)).length&&f.val()===h&&(u=!1),u&&C(s,n,c,r);else{var v=T(s,h);if(0<v.length){l=!1;var _=v.join("");if(r.onTipFn&&""!==_)r.onTipFn.call(n,_);else{var m=x.getCharWidth(_)+10;c.appendTo(d).children(".k_validate_tip_content").css("max-width",m).html(_);var b=c.offset();if((r.$winBody?r.$winBody.offset().left+r.$winBody.width()-b.left:w().width()-b.left)<m){var k=c.outerHeight();c.css({left:0,top:-k}),setTimeout(function(){c.remove()},2e3)}}}else r.onTipFn&&r.onTipFn.call(n,"")}if(n.attr("valid",l),l){var y=n.parent();n.hasClass("k_combox_input")&&(y=y.parent()),y.children(".k_validate_tip_wrap").remove(),n.removeClass("k_input_value_err")}else n.addClass("k_input_value_err");return l}return x.Validate=function(t,e,i){for(var a,o=e.parent();0<o.length;){if(o.hasClass("k_panel_content")){a=o;break}if("BODY"===(o=o.parent())[0].tagName)break}a&&(this.$winBody=a),this.rules=t,e&&(this.form=e,this.findFormTag(this.form)),i&&(this.onTipFn=i)},x.Validate.prototype.setTipFn=function(t){this.onTipFn=t},x.Validate.prototype.deleteRule=function(t){this.rules&&delete this.rules[t]},x.Validate.prototype.addRule=function(t,e){this.rules||(this.rules={}),this.rules[t]=e},x.Validate.prototype.valid=function(t){if(!this.form&&void 0===t)return alert("验证组件必须传入表单对象！"),!1;this.form||(this.form=t);var e=this,i=this.findFormTag(this.form),a=!0;return $.each(i,function(){o(this,e)||(a=!1)}),a},x.Validate.prototype.findFormTag=function(t){var e=this,i=[];return t.find("input[type=text]").each(function(){var t=$(this);a(t,e),i.push(t)}),t.find("input[type=password]").each(function(){var t=$(this);a(t,e),i.push(t)}),t.find("input[type=file]").each(function(){var t=$(this);a(t,e),i.push(t)}),t.find("textarea").each(function(){var t=$(this);a(t,e),i.push(t)}),t.find("select").each(function(){var t=$(this);a(t,e),i.push(t)}),i},x.Validate}),function(e,i){"function"==typeof define&&define.amd?define(["$B"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(n,r){var l={textField:"text",ellipsis:!1,idField:"id",scrollStyle:{size:"8px",display:"auto",setPadding:!1,hightColor:"#1590FA",slider:{"background-color":"#6CB7FA","border-radius":"8px"},bar:{"background-color":"#E8E8E8","border-radius":"8px",opacity:.5}}};function d(t,e){r.extend(this,d),this.jqObj=t.addClass("k_box_size k_menu_wrap").css({height:"100%",width:"100%",overflow:"visible"}),this.scrollWrap=$("<div style='height:100%;width:100%;padding-right:0px;padding-bottom:2px;' class='k_menu_wrap_scroll k_box_size'></div>").appendTo(this.jqObj),this.treeWrap=$("<div class='k_menu_wrap_ul clearfix' ></div>").appendTo(this.scrollWrap),this.opts=$.extend(!0,{},l,e);var i=this.opts.data,a=$("<div/>");this.firstItem=void 0,this._createList(a,i,1),this.maxWidth=0,a.children().detach().appendTo(this.treeWrap),this.firstItem&&this.firstItem.trigger("click"),this.scrollObj=this.scrollWrap.myscrollbar(this.opts.scrollStyle).getMyScrollIns(),this._setWrapWidth(),this._setMinWidth();var o=this,s=r.getUUID();$(n).on("resize."+s,function(){o._setMinWidth?o._setMinWidth():$(this).off("."+s)})}return d.prototype={_setMinWidth:function(){var t=this.treeWrap.parent().width();this.opts.ellipsis?this.rootUl.css("width",t):this.rootUl.css("min-width",t)},_itClickEnvent:function(t){var e=t.data._this,i=$(this),a=i.data("mdata"),o=void 0!==a.children;if(o)var s=i.next("ul").slideToggle("fast",function(){var t=i.children(".k_menu_parent_icon");"none"===s.css("display")?t.removeClass("fa-angle-down").addClass("fa-angle-left").css("padding-right","6px"):t.removeClass("fa-angle-left").addClass("fa-angle-down").css("padding-right","3px"),e._setWrapWidth(),e.scrollObj.resetSliderPosByTimer()});e.opts.onClick&&setTimeout(function(){e.opts.onClick.call(i,a.data?a.data:a,o)},10),o||(e.activedItem&&e.activedItem.removeClass("k_menu_actived"),e.activedItem=i.addClass("k_menu_actived"))},_setWrapWidth:function(){this.opts.ellipsis?this.treeWrap.width("100%"):this.treeWrap.width(this.rootUl.width())},_createList:function(t,e,i){var a=$("<ul class='k_menu_ul' style='width:auto;'/>").appendTo(t);this.opts.ellipsis||a.css({"min-width":"100%"}),1===i&&(this.rootUl=a.addClass("k_menu_ul_root"));for(var o=0,s=e.length;o<s;++o){var n=e[o],r=n[this.opts.textField],l=$("<li style='white-space: nowrap;width:auto;list-style:none;' ><a style='display:inline-block;white-space:nowrap;padding-right:10px;' class='k_box_size'>"+r+"</a></li>").appendTo(a),d=l.children("a");this.opts.ellipsis?d.css({width:"100%","max-width":"100%","text-overflow":" ellipsis",overflow:"hidden","padding-right":"0px"}):d.css("min-width","100%"),d.attr("title",r);var h=n.data.menuIconCss,c=15;1===i&&(c=12),d.css("padding-left",i*c),h&&""!==h&&d.prepend("<i style='padding-right:6px;' class='fa "+h+"'><i>"),n.children?(d.prepend("<i class='fa fa-angle-down k_menu_parent_icon'></i>"),this._createList(l,n.children,i+1)):this.firstItem||(this.firstItem=d),d.on("click",{_this:this},this._itClickEnvent).data("mdata",n),this.opts.onItemCreated&&this.opts.onItemCreated.call(l,n,void 0!==n.children)}},_loopParent:function(t,e){var i=e.parent().parent();if(!i.hasClass("k_menu_ul_root")){var a=i.prev("a"),o=a.data("mdata").text;t.push(o),this._loopParent(t,a)}},getTreeTxtPath:function(){var t=this.activedItem,e=[],i=t.data("mdata").text;return e.push(i),this._loopParent(e,t),e.reverse()},destroy:function(){this.scrollObj.destroy(),this.super.destroy.call(this)}},r.Menu=d}),function(e,i){"function"==typeof define&&define.amd?define(["$B"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(h,c){var t;function p(){return t||(t=$(h.document.body).css("position","relative")),t}var f={show:!1,fixed:!1,fitWidth:!1,initValue:void 0,shadow:!0,fmt:"yyyy-MM-dd hh:mm:ss",mutil:!1,clickHide:!1,readonly:!0,isSingel:!1,range:{min:void 0,max:void 0},onChange:void 0};function u(t,e){c.extend(this,u),t.addClass("k_calendar_input").attr("autocomplete","off"),this.opts=$.extend({},f,e),this.currentDate=new Date,this.isMonthStyle="yyyy-MM"===this.opts.fmt,this.isDayStyle="yyyy-MM-dd"===this.opts.fmt,this.isSecondStyle="yyyy-MM-dd hh:mm:ss"===this.opts.fmt,this.isTimeStyle="hh:mm:ss"===this.opts.fmt||"hh:mm"===this.opts.fmt,e.fitWidth&&t.addClass("k_box_size").outerWidth(202);var i=t.val();if(""!==i){var a=this._txt2Date(i);a&&(e.initValue=a)}if(e.isSingel){t.data("_calopt",{range:e.range,onChange:e.onChange,initValue:e.initValue}),e.fixed=!1,e.show=!1;var o=h._k_calendar_ins_;if(o)return o.setTarget(t,!0),o;h._k_calendar_ins_=this}var s=c.config.calendar;this.config=s,this.target=t;var n,r,l=this;if(this._initValue(!0),this._bindInputEvents(),this.jqObj=$("<div class='k_calendar_wrap k_box_size clearfix' style='width:202px;z-index:**********;'></div>"),this.opts.fixed&&this.jqObj.css("z-index",1),this.opts.shadow&&this.jqObj.addClass("k_dropdown_shadow"),this.opts.fixed&&(this.opts.show=!0),this.opts.show||this.jqObj.hide(),this.isTimeStyle)this.jqObj.outerHeight(250),this.createTimeOptList();else{for(this.headerPanel=$("<div class='k_calendar_pannel k_box_size clearfix' style='padding-left:2px;padding-top:2px;'><span class='_prev_year _event' style='width:24px'><i class='fa fa-angle-double-left'></i></span><span  style='width:21px' class='_prev_month _event'><i class='fa fa-angle-left'></i></span><span class='_cur_year  _event' style='width:46px;'>"+this.year+"</span><span>"+s.year+"</span><span class='_cur_month _event' style='width:28px;' >"+this.month+"</span><span>"+s.month+"</span><span style='width:25px' class='_next_month _event'><i class='fa fa-angle-right'></i></span><span class='_next_year _event' style='width:24px'><i class='fa fa-angle-double-right'></i></span></div>").appendTo(this.jqObj),this.bindHeaderPanelEvents(),this.weekPanel=$("<div class='k_calendar_week k_box_size' style='padding-left:2px;margin-bottom:5px;'></div>"),n=0,r=s.weekArray.length;n<r;++n)$("<span class='k_calendar_week_day'>"+s.weekArray[n]+"</span>").appendTo(this.weekPanel);this.weekPanel.appendTo(this.jqObj),this.daysPanel=$("<div class='k_calendar_days k_box_size clearfix' style='padding-left:2px;'></div>").appendTo(this.jqObj),this.createDays(),this.timePanel=$("<div class='k_calendar_time k_box_size' style='margin:1px 0px;padding-left:2px;text-align:center;'></div>").appendTo(this.jqObj),this.createTimeTools(),this.toolPanel=$("<div class='k_calendar_bools k_box_size clearfix' style='padding-left:1px;'></div>").appendTo(this.jqObj),this.createTools()}this.setPosition(),this.isDayStyle&&this.timePanel.hide(),this.isMonthStyle&&(this.daysPanel.hide(),this.timePanel.hide(),this.headerPanel.children("._cur_month").trigger("click"),this.toolPanel.css("margin-top","168px")),this.jqObj.appendTo(p());var d=c.getUUID();$(document).on("click."+d,function(t){if(l.hide){if(t.target){var e=$(t.target);if(e.hasClass("k_calendar_hours")||e.hasClass("k_calendar_minutes")||e.hasClass("k_calendar_seconds")||e.hasClass("k_calendar_wrap"))return!0}l.hide()}else $(this).off("."+d)}),$(h).on("resize."+d,function(){l.setPositionByTimer?l.setPositionByTimer():$(this).off("."+d)}),l._setBorderColor(),this.target.data("calender",this),this.nsId=d}return u.prototype={createTimeOptList:function(){var t,e,i;"hh:mm"===this.opts.fmt?(t=$("<div class='k_calendar_hours' style='width:50%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj),e=$("<div  class='k_calendar_minutes' style='width:50%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj)):(t=$("<div class='k_calendar_hours' style='width:33.3333%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj),e=$("<div class='k_calendar_minutes' style='width:33.3333%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj),i=$("<div class='k_calendar_seconds' style='width:33.3333%;height:100%;float:left;text-align:center;'></div>").appendTo(this.jqObj));var a,o,s,n=this;$("<a style='position:absolute;top:1px ;right:1px;cursor:pointer;line-height:14px;'><i class='fa fa-cancel-circled'></i></a>").appendTo(this.jqObj).click(function(){return n.hide(),!1}),t&&(a=c.makeNiceScroll(t)),e&&(o=c.makeNiceScroll(e)),i&&(s=c.makeNiceScroll(i));for(var r,l,d={click:function(){var t=$(this);t.addClass("actived").siblings().removeClass("actived");var e=parseInt(t.text());t.hasClass("_k_hours")?n.hour=e:t.hasClass("_k_minutes")?n.minutes=e:n.seconds=e;var i=n._var2Date();return n._setCurrentDate(i),n.update2target(n.currentDate),!1}},h=0;h<24;)(r=h)<10&&(r="0"+h),l=$("<div class='k_calendar_time_opts _k_hours' style='padding-right:15px'>"+r+"</div>").appendTo(a).on(d),h===n.hour&&l.addClass("actived"),h++;this._createMinuAndSecItems(o,"_k_minutes",d),s&&this._createMinuAndSecItems(s,"_k_seconds",d)},_createMinuAndSecItems:function(t,e,i){for(var a,o,s=0;s<60;)(a=s)<10&&(a="0"+s),o=$("<div class='k_calendar_time_opts "+e+"' style='padding-right:15px'>"+a+"</div>").appendTo(t).on(i),"_k_minutes"===e?this.minutes===s&&o.addClass("actived"):this.seconds===s&&o.addClass("actived"),s++},_time2fullDateTxt:function(t){return this.isTimeStyle&&/^(20|21|22|23|[0-1]\d)?:?([0-5]\d)?:?([0-5]\d)?$/.test(t)?this.currentDate.format("yyyy-MM-dd")+" "+t:t},_initValue:function(t){var e=this.target.val().leftTrim().rightTrim();if(""!==e&&(this.currentDate=this._txt2Date(e)),t){var i=this.opts.initValue;this.opts.isSingel&&(i=this.target.data("_calopt").initValue),i&&(this.currentDate="string"==typeof i?this._txt2Date(i):i,this.target.val(this.currentDate.format(this.opts.fmt)))}this._setVarByDate(this.currentDate)},_callWatcher:function(){this.onWatcher&&(this.target.data("no2update",!0),this.onWatcher.call(this),this.target.removeData("no2update"))},_bindInputEvents:function(){var a=this,t=void 0===this.target.data("k_calr_ins");if(this.opts.readonly)this.target.attr("readonly",!0);else if(t){var e=function(){var t=a.target.val(),e=a._txt2Date(t);if(e)a._beforeChange(e)&&(a._setCurrentDate(e),a._setVarByDate(e),a.rebuildDaysUi(),a.updateYearAndMonthUi(a.currentDate),a.isMonthStyle?a._createMonthOpts():a.isTimeStyle?a.activedTimeUi():a._updateMinAndCec(),a._callWatcher());else{c.error(a.config.error,2);var i=a.currentDate.format(a.opts.fmt);a.target.val(i)}};this.hasInputed=!1,this.target.on("input.calendar",function(){a.hasInputed=!0,clearTimeout(a.userInputTimer),a.userInputTimer=setTimeout(function(){e()},1e3)})}t&&(this.target.on("blur.calendar",function(){a._setBorderColor(),a.hasInputed&&(a.hasInputed=!1,clearTimeout(a.userInputTimer),e())}).on("click.calendar",function(){return a._setBorderColor(),a.opts.fixed||a.slideToggle(),!1}).on("focus.calendar",function(){a.opts.isSingel&&a.setTarget($(this),!1),a._setBorderColor()}),this.target.data("k_calr_ins",this),this.target.css("cursor","pointer"))},activedTimeUi:function(){var e=this;this.jqObj.children(".k_calendar_hours").find("div.k_calendar_time_opts").each(function(){var t=$(this);parseInt(t.text())===e.hour?t.addClass("actived"):t.removeClass("actived")}),this.jqObj.children(".k_calendar_minutes").find("div.k_calendar_time_opts").each(function(){var t=$(this);parseInt(t.text())===e.minutes?t.addClass("actived"):t.removeClass("actived")}),this.jqObj.children(".k_calendar_seconds").find("div.k_calendar_time_opts").each(function(){var t=$(this);parseInt(t.text())===e.seconds?t.addClass("actived"):t.removeClass("actived")})},_setBorderColor:function(){var t=this.target.css("border-color");this.jqObj.css("border-color",t)},_getMinMaxCfg:function(t){var e,i,a=this.opts.range;if(this.opts.isSingel&&(a=this.target.data("_calopt").range),a[t])if(-1<a[t].indexOf("#")){var o=this.target.data(t+"_input");o||(o=$(a[t]),this.target.data(t+"_input",o)),i=o.val(),e=this._txt2Date(i)}else"now"===a[t]?e=new Date:(i=a[t],e=this._txt2Date(i));return e},_legalDate:function(t,e,i){return e&&t.getTime()<e.getTime()?this.config.minTip.replace("x",e.format(this.opts.fmt)):i&&t.getTime()>i.getTime()?this.config.maxTip.replace("x",i.format(this.opts.fmt)):""},_beforeChange:function(t){var e=!0,i=this._getMinMaxCfg("min"),a=this._getMinMaxCfg("max"),o=this._legalDate(t,i,a);""!==o&&(c.alert(o,2.5),e=!1);var s=this.opts.onChange;return this.opts.isSingel&&(s=this.target.data("_calopt").onChange),e&&"function"==typeof s&&void 0!==(o=s(t,t.format(this.opts.fmt)))&&""!==o&&(c.alert(o,2.5),e=!1),e||this._setVarByDate(this.currentDate),e},bindHeaderPanelEvents:function(){var r=this,t=function(){r.hourItsPanel&&r.hourItsPanel.hide(),r.mmItsPanel&&r.mmItsPanel.hide();var t=$(this),e=r._var2Date(),i=e.getDate(),a=!0;if(t.hasClass("_prev_year"))e.setFullYear(e.getFullYear()-1);else if(t.hasClass("_prev_month")){var o=new Date(e.getFullYear(),e.getMonth(),0,r.hour,r.minutes,r.seconds);i<o.getDate()&&o.setDate(i),e=o}else if(t.hasClass("_cur_year"))r._hideMonthPanel(),a=!1,r.yearPanel&&"none"!==r.yearPanel.css("display")?r.yearPanel.hide(100):r._createYearOpts(e.getFullYear());else if(t.hasClass("_cur_month")){if(r.yearPanel){if(r.isMonthStyle)return!1;r.yearPanel.hide()}a=!1,r.monthPanel&&"none"!==r.monthPanel.css("display")?r._hideMonthPanel(100):r._createMonthOpts()}else if(t.hasClass("_next_month")){var s=new Date(e.getFullYear(),e.getMonth()+2,0,r.hour,r.minutes,r.seconds);i<s.getDate()&&s.setDate(i),e=s}else t.hasClass("_next_year")&&e.setFullYear(e.getFullYear()+1);if(a&&(r.yearPanel&&r.yearPanel.hide(),r._hideMonthPanel(),r._beforeChange(e)&&(r._updateVar(e),r.opts.mutil||r.update2target(e),r.updateYearAndMonthUi(e),r.rebuildDaysUi(e),r.isMonthStyle))){var n=e.getMonth()+1;r._activeMonthUi(n)}return!1};this.headerPanel.children().first().click(t).next().click(t).next().click(t).next().next().click(t).next().next().click(t).next().click(t)},_yearOnclick:function(t){var e=t.data._this,i=$(this),a=i.children("i");if(0<a.length){var o;o=a.hasClass("fa-angle-double-left")?parseInt(i.siblings(".k_box_size").first().text())-18:parseInt(i.siblings(".k_box_size").last().text())+1;for(var s=i.parent().children(".k_box_size").first().next();0<s.length&&s.hasClass("_year_num");)s.text(o),o===e.year?s.addClass("actived"):s.removeClass("actived"),o++,s=s.next()}else{var n=parseInt(i.text());e.year=n;var r=e._var2Date();e._beforeChange(r)&&(e._setCurrentDate(r),e.rebuildDaysUi(),e.updateYearAndMonthUi(e.currentDate),e.opts.mutil||e.update2target(e.currentDate),e.yearPanel.hide(120))}return!1},_createYearOpts:function(t){var e=!1;if(this.yearPanel)this.yearPanel.children(".k_box_size").remove(),this.yearPanel.show();else{e=!0;var i=this;this.yearPanel=$("<div class='k_box_size clearfix k_calendar_ym_panel k_dropdown_shadow' style='padding-left:12px;position:absolute;top:30px;left:0;z-index:2110000000;width:100%;background:#fff;'></div>").appendTo(this.jqObj),$("<div style='position:absolute;top:-2px;right:5px;width:12px;height:12px;line-height:12px;text-align:center;cursor:pointer;'><i class='fa fa-cancel-2' style='font-size:14px;color:#AAAAB4'></i></div>").appendTo(this.yearPanel).click(function(){return i.yearPanel.hide(),!1})}for(var a,o,s=t-12,n=22;0<n;)22===n&&(a="<i style='font-size:16px;color:#A0BFDE' class='fa fa-angle-double-left'></i>",(o=$("<div class='k_box_size' style='float:left;line-height:20px;padding:5px 5px;cursor:pointer;width:43px;text-align:center;'>"+a+"</div>").appendTo(this.yearPanel)).on("click",{_this:this},this._yearOnclick)),a=s,o=$("<div class='k_box_size _year_num' style='float:left;line-height:20px;padding:5px 5px;cursor:pointer;width:43px;text-align:center;'>"+a+"</div>").appendTo(this.yearPanel),a===t&&o.addClass("actived"),o.on("click",{_this:this},this._yearOnclick),1===n&&(a="<i style='font-size:16px;color:#A0BFDE' class='fa fa-angle-double-right'></i>",(o=$("<div class='k_box_size' style='float:left;line-height:20px;padding:5px 5px;cursor:pointer;width:43px;text-align:center;'>"+a+"</div>").appendTo(this.yearPanel)).on("click",{_this:this},this._yearOnclick)),n--,s++;e&&this.yearPanel.appendTo(this.jqObj)},_activeMonthUi:function(e){this.monthPanel.children().each(function(){var t=$(this);e===parseInt(t.text())?t.addClass("actived"):t.removeClass("actived")})},_monthOnclick:function(t){var e=t.data._this,i=$(this),a=parseInt(i.text());e.month=a;var o=e._var2Date();return e._beforeChange(o)&&(e.isMonthStyle&&(i.addClass("actived").siblings().removeClass("actived"),e.opts.clickHide&&e.hide()),e._setCurrentDate(o),e.rebuildDaysUi(),e.updateYearAndMonthUi(e.currentDate),e.update2target(e.currentDate),e._hideMonthPanel()),!1},_hideMonthPanel:function(){!this.isMonthStyle&&this.monthPanel&&this.monthPanel.hide(120)},_createMonthOpts:function(){var e=this;if(this.monthPanel)this.monthPanel.show(),this.monthPanel.children().each(function(){var t=$(this);parseInt(t.text())===e.month?t.addClass("actived"):t.removeClass("actived")});else{this.monthPanel=$("<div class='k_box_size clearfix k_calendar_ym_panel' style='padding-bottom:4px; padding-top:8px;padding-left: 18px; position: absolute; top: 30px; left: 0px; z-index: 2100000000; width: 100%; background: rgb(255, 255, 255);'></div>").appendTo(this.jqObj);for(var t,i,a=1;a<13;)(t=a)<10&&(t="0"+a),i=$("<div style='float:left;line-height:32px;padding:5px 5px;cursor:pointer;width:45px;text-align:center;'>"+t+"</div>").appendTo(this.monthPanel),a===this.month&&i.addClass("actived"),i.on("click",{_this:this},this._monthOnclick),a++;if(!this.isMonthStyle)this.monthPanel.addClass("k_dropdown_shadow"),$("<div style='position:absolute;top:-2px;right:5px;width:12px;height:12px;line-height:12px;text-align:center;cursor:pointer;'><i class='fa fa-cancel-2' style='font-size:14px;color:#AAAAB4'></i></div>").appendTo(this.monthPanel).click(function(){return e.monthPanel.hide(),!1})}},createTools:function(){var a=this,t=function(){var t=$(this);if(a.yearPanel&&a.yearPanel.hide(),!a.isMonthStyle&&a.monthPanel&&a.monthPanel.hide(),t.hasClass("002")){var e=new Date;a.year=e.getFullYear(),a.month=e.getMonth()+1,a.day=e.getDate(),a.hour=e.getHours(),a.minutes=e.getMinutes(),a.seconds=e.getSeconds();var i=a._var2Date();a._beforeChange(i)&&(a._setCurrentDate(i),a.rebuildDaysUi(),a.updateYearAndMonthUi(a.currentDate),a.update2target(a.currentDate),a._updateTimeUi(),a.isMonthStyle&&a._activeMonthUi(a.month),a.opts.clickHide&&a.hide())}else t.hasClass("003")?(a.target.val(""),a.daysPanel.children().removeClass("activted")):a.hide();return!1},e=$("<div class='002'>"+this.config.now+"</div>").appendTo(this.toolPanel).click(t),i=$("<div class='003'>"+this.config.clear+"</div>").appendTo(this.toolPanel).click(t);this.opts.fixed?(e.width(99),i.width(99)):$("<div class='004'>"+this.config.close+"</div>").appendTo(this.toolPanel).click(t)},_getStrTime:function(){var t=this.hour,e=this.minutes,i=this.seconds;return t<10&&(t="0"+t),e<10&&(e="0"+e),i<10&&(i="0"+i),{h:t,m:e,s:i}},createTimeTools:function(){var s=this,n=function(){var t=$(this),e=t.text();"_k_cal_hour"===s.userInput.attr("id")?s.hour=parseInt(e):"_k_cal_minu"===s.userInput.attr("id")?s.minutes=parseInt(e):s.seconds=parseInt(e);var i=s._var2Date();return s._beforeChange(i)&&(s.userInput.val(e).removeClass("actived"),s._setCurrentDate(i),s.update2target(i),t.parent().hide()),!1},t=function(){var t=$(this);s.yearPanel&&s.yearPanel.hide(),s.monthPanel&&!s.isMonthStyle&&s.monthPanel.hide();var e,i,a=!0,o=t.val();if("_k_cal_hour"===t.attr("id")){if(s.mmItsPanel&&s.mmItsPanel.hide(),s.hourItsPanel)"none"===s.hourItsPanel.css("display")?s.hourItsPanel.show():(s.hourItsPanel.hide(),a=!1);else{for(s.hourItsPanel=$("<div class='k_box_size clearfix k_calendar_hours_panel k_dropdown_shadow' style='padding-bottom:2px;padding-left: 12px; position: absolute; top: 30px; left: 0px; z-index: 2112000000; width: 100%; background: rgb(255, 255, 255);'></div>"),e=0;e<24;e++)$("<span>"+(e<10?"0"+e:e)+"</span>").appendTo(s.hourItsPanel).click(n);s.hourItsPanel.appendTo(s.jqObj),$("<div style='position:absolute;top:-2px;right:5px;width:12px;height:12px;line-height:12px;text-align:center;cursor:pointer;'><i class='fa fa-cancel-2' style='font-size:14px;color:#AAAAB4'></i></div>").appendTo(s.hourItsPanel).click(function(){return s.hourItsPanel.hide(),s.userInput.removeClass("actived"),!1})}a&&(i=s.hourItsPanel.children("span"))}else{if(s.hourItsPanel&&s.hourItsPanel.hide(),s.mmItsPanel)"none"===s.mmItsPanel.css("display")?s.mmItsPanel.show():s.userInput&&s.userInput[0]===t[0]&&(s.mmItsPanel.hide(),a=!1);else{for(s.mmItsPanel=$("<div class='k_box_size clearfix k_calendar_mm_panel k_dropdown_shadow' style='padding-top:0px;padding-left: 12px; position: absolute; top: 29px; left: 0px; z-index: 2112000000; width: 100%; background: rgb(255, 255, 255);'></div>"),e=0;e<60;e++)$("<span>"+(e<10?"0"+e:e)+"</span>").appendTo(s.mmItsPanel).click(n);s.mmItsPanel.appendTo(s.jqObj),$("<div style='position:absolute;top:-2px;right:5px;width:12px;height:12px;line-height:12px;text-align:center;cursor:pointer;'><i class='fa fa-cancel-2' style='font-size:14px;color:#AAAAB4'></i></div>").appendTo(s.mmItsPanel).click(function(){return s.mmItsPanel.hide(),s.userInput.removeClass("actived"),!1})}a&&(i=s.mmItsPanel.children("span"))}return s.userInput=t,a?(t.addClass("actived").siblings().removeClass("actived"),i.each(function(){var t=$(this);t.text()===o?t.addClass("actived"):t.removeClass("actived")})):t.removeClass("actived"),!1},e=this._getStrTime();this.$hour=$("<input readonly='readonly' style='width:30px;text-align:center;' id='_k_cal_hour' value='"+e.h+"'/>").appendTo(this.timePanel).click(t),this.timePanel.append("<span>：</span>"),this.$minutes=$("<input  readonly='readonly' style='width:30px;text-align:center' id='_k_cal_minu' value='"+e.m+"'/>").appendTo(this.timePanel).click(t),this.isSecondStyle&&(this.timePanel.append("<span>：</span>"),this.$seconds=$("<input  readonly='readonly' style='width:30px;text-align:center' id='_k_cal_secs' value='"+e.s+"'/>").appendTo(this.timePanel).click(t))},_updateTimeUi:function(){var t=this._getStrTime();this.$hour&&this.$hour.val(t.h),this.$minutes&&this.$minutes.val(t.m),this.$seconds&&this.$seconds.val(t.s)},_updateMinAndCec:function(){var t=this._getStrTime();this.$hour&&this.$hour.val(t.h),this.$minutes&&this.$minutes.val(t.m),this.$seconds&&this.$seconds.val(t.s)},rebuildDaysUi:function(t){if(t&&(this.year=t.getFullYear(),this.month=t.getMonth()+1),this.daysPanel)for(var e,i,a,o=this._getStartDate(),s=this.daysPanel.children().first();0<s.length;)e=o.getDate(),i=o.getMonth()+1,a=o.format("yyyy-MM-dd"),i!==this.month?s.addClass("day_dull").removeClass("activted").attr("title",a):(s.removeClass("day_dull").removeAttr("title"),e===this.day?s.addClass("activted"):s.removeClass("activted")),s.text(e).attr("t",a),o.setDate(o.getDate()+1),s=s.next()},updateYearAndMonthUi:function(t){if(this.headerPanel){var e=t.getFullYear(),i=t.getMonth(),a=this.config.monthArray[i];this.headerPanel.children("._cur_year").text(e).next().next().text(a)}},update2target:function(t,e){var i=t.format(this.opts.fmt);this.target.val(i),this._callWatcher()},_getStartDate:function(){for(var t=this.month-1,e=new Date(this.year,t,1),i=e.getDay(),a=new Date(e);0<i;)a.setDate(a.getDate()-1),i--;return a},_dayOnclick:function(t){var e=t.data._this,i=$(this),a=i.attr("t").split("-");e.year=parseInt(a[0]),e.month=parseInt(a[1]),e.day=parseInt(a[2]);var o=e._var2Date();return e._beforeChange(o)&&(e._setCurrentDate(o),e.updateYearAndMonthUi(e.currentDate),e.update2target(e.currentDate),i.addClass("activted").siblings().removeClass("activted"),e.opts.clickHide&&e.hide()),!1},createDays:function(){for(var t,e,i,a,o=this.month-1,s=this._getStartDate(),n=42;0<n;)t=s.format("yyyy-MM-dd"),i=s.getDate(),a=s.getMonth(),e=$("<span t='"+t+"'>"+i+"</span>").appendTo(this.daysPanel),o!==a?e.addClass("day_dull").attr("title",t):i===this.day&&e.addClass("activted"),e.on("click",{_this:this},this._dayOnclick),s.setDate(s.getDate()+1),n--},_txt2Date:function(t){if(""!==t){var e=(t=this._time2fullDateTxt(t)).match(/^(\s*\d{4}\s*)(-|\/)\s*(0?[1-9]|1[0-2]\s*)(-|\/)?(\s*[012]?[0-9]|3[01]\s*)?\s*([01]?[0-9]|2[0-4]\s*)?:?(\s*[012345]?[0-9]|\s*)?:?(\s*[012345]?[0-9]\s*)?$/);if(null!==e&&(e[1]||e[3])){var i=parseInt(e[1]),a=parseInt(e[3])-1,o=1,s=0,n=0,r=0;return e[5]&&(o=parseInt(e[5])),e[6]&&(s=parseInt(e[6])),e[7]&&(n=parseInt(e[7])),e[8]&&(r=parseInt(e[8])),new Date(i,a,o,s,n,r)}}},_setVarByDate:function(t){this.year=t.getFullYear(),this.month=t.getMonth()+1,this.day=t.getDate(),this.hour=t.getHours(),this.minutes=t.getMinutes(),this.seconds=t.getSeconds()},_var2Date:function(){try{var t=new Date(this.year,this.month,0).getDate();this.day>t&&(this.day=t);var e=new Date(this.year,this.month-1,this.day,this.hour,this.minutes,this.seconds);return e}catch(t){return void c.error(this.config.error)}return e},_setCurrentDate:function(t){this.currentDate=t,this.target.hasClass("k_input_value_err")&&(this.target.removeClass("k_input_value_err"),this.target.siblings(".k_validate_tip_wrap").remove())},_updateVar:function(t){this._setCurrentDate(t),this._setVarByDate(t)},setPositionByTimer:function(){var t=this;if(t.t1){var e=new Date;1e3<e.getTime()-t.t1.getTime()&&(t.setPosition(),t.t1=e)}else t.t1=new Date;clearTimeout(this.positionTimer),this.positionTimer=setTimeout(function(){t.setPosition&&t.setPosition()},500)},setPosition:function(){var t,e,i=this.target.offset();this._isTopPosition()?(e=this._getPanelHeight(),t={top:i.top-e,left:i.left}):(e=this.target.outerHeight()-1,t={top:i.top+e,left:i.left}),this.jqObj.css(t)},slideToggle:function(){"none"===this.jqObj.css("display")?this.show():this.hide()},_getPanelHeight:function(){var t=276;return(this.isMonthStyle||this.isDayStyle||this.isTimeStyle)&&(t=250),t},_isTopPosition:function(){var t=p().height();return 0<this.target.offset().top+this.target.outerHeight()+this._getPanelHeight()-t},hide:function(){this.opts.fixed||(this.hourItsPanel&&this.hourItsPanel.hide(),this.mmItsPanel&&this.mmItsPanel.hide(),this._isTopPosition()?this.jqObj.hide():this.jqObj.slideUp(200),this.yearPanel&&this.yearPanel.hide(),this._hideMonthPanel())},show:function(){this.opts.fixed||(this.setPosition(),this._isTopPosition()?this.jqObj.show():this.jqObj.slideDown(200))},setValue:function(t){var e;e="string"==typeof t?this._txt2Date(t):t,this._setCurrentDate(e),this._setVarByDate(e),this.rebuildDaysUi(),this.updateYearAndMonthUi(this.currentDate),this._updateMinAndCec(),this.opts.mutil||this.update2target(this.currentDate)},setTarget:function(t,e){this.target&&this.target[0]!==t[0]&&"none"!==this.jqObj.css("display")&&this.jqObj.hide(),this.target=t,this._bindInputEvents(),this._initValue(e),this.rebuildDaysUi(),this.updateYearAndMonthUi(this.currentDate),this._updateMinAndCec()},destroy:function(){this.target.off(".calendar"),this.target.removeData("k_calr_ins"),this.target.removeData("calender"),this.target.removeData("_calopt"),$(document).off("."+this.nsId),this.super.destroy.call(this)}},c.Calendar=u});