/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(e,i){"function"==typeof define&&define.amd?define(["$B","plugin","panel","ctxmenu"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(d,c){var h=!1;d._tab_more_close_fn=function(){h&&$("body").children(".k_tab_more_menu_wrap").hide()},$(d).on("mouseup.tab_more_close",function(){h&&d._tab_more_close_fn(),setTimeout(function(){d.parent!==d.self&&d.parent._tab_more_close_fn&&d.parent._tab_more_close_fn()},10)});var u="<iframe  class='panel_content_ifr' frameborder='0' style='overflow:visible;padding:0;margin:0;display:block;vertical-align:top;' scroll='none'  width='100%' height='100%' src='' ></iframe>",f="<div class='k_box_size' style='position:absolute;z-index:**********;width:100%;height:26px;top:2px;left:0;' class='loading'><div class='k_box_size' style='filter: alpha(opacity=50);-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;position:absolute;top:0;left:0;width:100%;height:100%;z-index:**********;background:"+c.config.loadingBackground+";'></div><div class='k_box_size' style='width:100%;height:100%;line-height:26px;padding-left:16px;position:absolute;width:100%;height:100%;z-index:2147483611;color:#fff;text-align:center;'><i style='color:#fff;font-size:16px;' class='fa animate-spin fa-spin6'></i><span style='padding-left:5px;font-weight:bold;color:#fff;'>"+c.config.loading+"</span></div></div>";function o(i){var a=this,n=this.data("data");if(n){var e,r="function"==typeof i;a.children().remove(),a.html("");var t=n.url;if(t=0<t.indexOf("?")?t+"&_t_="+c.generateMixed(5):t+"?_t_="+c.generateMixed(5),e=$(f).appendTo(a),"html"===n.dataType)c.htmlLoad({target:a,url:t,onLoaded:function(){e.fadeOut(function(){$(this).remove()}),r&&i.call(a,n.title),c.bindTextClear(a)}});else if("json"===n.dataType)c.request({dataType:"json",url:t,ok:function(t,e){r&&i.call(a,n.title,e)},final:function(t){e.fadeOut(function(){$(this).remove()})}});else{var o=$(u),l=o[0],s=c.getUUID();o.attr({name:s,id:s}),o.on("load",function(){e.fadeOut(function(){$(this).remove()});try{$(this.contentDocument.body).append("<span id='_window_ifr_id_' style='display:none'>"+s+"</span>")}catch(t){}r&&i.call(a,n.title)}),l.src=t,o.appendTo(a)}}}function p(t,e){var h=this,l="line-height:"+e+"px;",s="function"==typeof h.opts.onClick,u="function"==typeof h.opts.onClosed,i=$("<li  title='"+t.title+"' class='k_box_size' style='"+l+"'>"+t.title+"</li>").appendTo(this.$ul).click(function(e){h.$moreWrap.hide();for(var i=$(this),t=0,a=i.prev();0<a.length;)t++,a=a.prev();var n=h.$ul.data("actived");if(n&&n.attr("title")!==i.attr("title")){h.$ul.data("activedItem").css("z-index",-1);var r=h.$ul.parent().parent().css("background-color");n.animate({backgroundColor:r},100,function(){if(n.removeClass("actived"),n.attr("style",l),s&&!e.isTrigger){var t=i.data("itdata");h.opts.onClick.call(i,i.attr("title"),t)}})}h.$ul.data("actived",i.addClass("actived")),i.attr("style",l);var o=h.$body.children(".k_tab_item").eq(t).css("z-index",1);return h.$ul.data("activedItem",o),o.siblings().css("z-index",-1),h.ctxmenu&&h.ctxmenu.hide(),!1});t.data&&i.data("itdata",t.data),t.iconCls&&""!==t.iconCls&&i.prepend('<i class="fa '+t.iconCls+'"></i>&nbsp');var a=$("<div style='z-index:-1;overflow: auto;background:#ffffff;' class='k_tab_item k_box_size'></div>").appendTo(this.$body);t.url&&""!==t.url?(a.data("data",{dataType:t.dataType,url:t.url,onLoaded:t.onLoaded,title:t.title}),o.call(a,h.opts.onLoaded)):t.content&&a.html(t.content),t.actived&&i.trigger("click"),t.closeable&&($("<a style='display:none;' class='close'><i class='small-font smallsize-font fa fa-cancel-2 '></i></a>").appendTo(i).click(function(){var t=$(this).parent();if(h.$moreWrap.hide(),t.hasClass("actived")){var e=t.next();if(0<e.length)e.trigger("click");else{var i=t.prev();0<i.length?i.trigger("click"):(h.removeData("activedItem"),h.removeData("actived"))}}for(var a=t.prev(),n=0;0<a.length;)a=a.prev(),n++;var r=t.attr("title"),o=t.data("itdata");t.remove(),h.$body.children().eq(n).remove();var l=h.$ul.position().left,s=h.$ul.children().last(),d=s.position().left+s.outerWidth()+l;if(l<0){d<=h.$headerUlWrap.width()&&h.rightButton.addClass("k_tab_icon_btn_disabeld");var c=l+(h.$headerUlWrap.width()-d);0<c&&(c=0,h.leftButton.removeClass("k_tab_icon_btn_disabeld")),h.$ul.animate({left:c},150)}else h.leftButton.addClass("k_tab_icon_btn_disabeld"),d<=h.$headerUlWrap.width()&&h.rightButton.removeClass("k_tab_icon_btn_disabeld");u&&setTimeout(function(){h.opts.onClosed(r,o)},1)}),i.on({mouseenter:function(){$(this).children("a.close").show()},mouseleave:function(){$(this).children("a.close").hide()}}));var n=i.outerWidth()+i.position().left-this.$headerUlWrap.outerWidth();if(0<n){this.leftButton.show().removeClass("k_tab_icon_btn_disabeld"),this.rightButton.show().addClass("k_tab_icon_btn_disabeld");var r=n.toString();0<r.indexOf(".")&&(n=parseInt(r.split(".")[0])+2),this.$ul.animate({left:-n},150)}return i}function _(t,e){h=!0,c.extend(this,_),this.jqObj=t.addClass("k_tab_main k_box_size"),this.opts=e,this.$header=$("<div class='k_tab_header_wrap k_box_size k_tab_icon k_tab_icon_header_bg'><div class='k_tab_header_wrap_bottom_border k_box_size'></div></div>").appendTo(this.jqObj).children(),this.$headerUlWrap=$("<div class='k_tab_header_ulwrap'></div>").appendTo(this.$header);var i=this.$header.outerHeight();this.$body=$("<div class='k_tab_item_body k_box_size'></div>").appendTo(this.jqObj).css("border-top",i+"px solid #ffffff"),this.leftButton=$("<div style='left:0' class='k_tab_button_wrap_wrap k_tab_left_btn k_box_size'><div><i class='fa fa-angle-double-left'></i></div></div>").appendTo(this.$header).children(),this.rightButton=$("<div  style='right:0' class='k_tab_button_wrap_wrap k_tab_right_btn k_box_size'><div><i style='' class='fa fa-angle-double-right'></i></div></div>").appendTo(this.$header).children(),this.$ul=$("<ul class='k_box_size'></ul>").appendTo(this.$headerUlWrap);var a=$(d.document.body);this.$moreWrap=$("<div style='width:auto;height:auto;' class='k_tab_more_menu_wrap k_box_shadow'><ul></ul></div>").appendTo(a).hide();for(var n=this.$ul.height(),r=0,o=this.opts.tabs.length;r<o;++r)p.call(this,this.opts.tabs[r],n);var u=this;function s(){for(var t=u.$ul.position().left,e=u.$ul.children().last(),i=e.outerWidth()+e.position().left,a=i+t,n=u.$headerUlWrap.outerWidth();n<a;)a=(i=(e=e.prev()).outerWidth()+e.position().left)+t;var r=e.next();return 0<r.length?r:null}if(this.leftButton.children("i").css("line-height",n+"px"),this.rightButton.children("i").css("line-height",n+"px"),this.rightButton.on({mouseenter:function(){var t=$(this);if(!t.hasClass("k_tab_icon_btn_disabeld")){var e=s();if(null!==e){var i=u.$moreWrap.children();i.children().remove();var a=t.offset();u.$moreWrap.css({top:a.top+t.outerHeight()}).hide();var n=0,r=function(){var t=$(this),e=parseInt(t.attr("idx")),i=t.parent().children().length-e,a=u.$ul.children().last();for(1===i&&u.rightButton.addClass("k_tab_icon_btn_disabeld");1<i;)a=a.prev(),i--;a.trigger("click");var n=u.$headerUlWrap.width(),r=Math.abs(u.$ul.position().left),o=a.position().left+a.outerWidth()-(r+n);return 0<o&&(u.$ul.animate({left:-(r+o)},200,function(){"function"==typeof u.opts.onClick&&u.opts.onClick.call(a,a.attr("title"))}),u.leftButton.show().removeClass("k_tab_icon_btn_disabeld")),u.$moreWrap.hide(),!1},o=function(){for(var t=$(this).parent(),e=parseInt(t.attr("idx")),i=t.parent().children().length-e,a=u.$ul.children().last();1<i;)a=a.prev(),i--;a.find(".close").trigger("click");for(var n=t.next();0<n.length;)n.attr("idx",e--),n=n.next();var r=t.parent();return t.remove(),0===r.children().length&&r.parent().hide(),!1};e.clone().appendTo(i).attr("idx",n).click(r).children(".close").click(o);for(var l=e.next();0<l.length;)n++,l.clone().appendTo(i).attr("idx",n).click(r).children(".close").click(o),l=l.next();u.$moreWrap.css("left",a.left-u.$moreWrap.outerWidth()+16).show()}else u.$moreWrap.hide()}},click:function(){u.$moreWrap.hide();var t=$(this);if(!t.children().hasClass("k_tab_icon_right_disabeld")){var e=s();if(null!==e){u.leftButton.show().removeClass("k_tab_icon_btn_disabeld");var i=u.$ul.position().left,a=u.$headerUlWrap.outerWidth()-(e.position().left+i),n=(i-(e.outerWidth()-a)).toString();0<n.indexOf(".")&&(n=parseInt(n.split(".")[0])-2),u.$ul.animate({left:n},300,function(){0===e.next().length&&u.rightButton.removeClass("k_tab_icon_btn_disabeld"),t.trigger("mouseenter")})}else u.rightButton.addClass("k_tab_icon_btn_disabeld");return!1}}}),this.leftButton.on({mouseenter:function(){var n=$(this);if(!n.hasClass("k_tab_icon_btn_disabeld")){var t=u.$ul.position().left;if(0<=t)n.addClass("k_tab_icon_btn_disabeld");else{var e=u.$ul.children().first(),i=u.$moreWrap.children();i.children().remove();var a=n.offset();u.$moreWrap.css({top:a.top+n.outerHeight()}).hide();var r=0,o=parseInt(Math.abs(t)),l=e.outerWidth(),s=e.position().left+l,d=s-o,c=function(){var t=$(this),e=parseInt(t.attr("idx")),i=u.$ul.children().eq(e).trigger("click"),a=i.position().left;return u.$ul.animate({left:-a},200,function(){"function"==typeof u.opts.onClick&&u.opts.onClick.call(i,i.attr("title"))}),u.$moreWrap.hide(),0===e&&n.addClass("k_tab_icon_btn_disabeld"),u.$ul.children().last().position().left>u.$headerUlWrap.width()&&u.rightButton.removeClass("k_tab_icon_btn_disabeld"),!1},h=function(){var t=$(this).parent(),e=t.parent(),i=parseInt(t.attr("idx"));return u.$ul.children().eq(i).children(".close").trigger("click"),t.remove(),0===e.children().length&&e.parent().hide(),!1};for(e.clone().prependTo(i).attr("idx",r).click(c).children(".close").click(h);d<0;)r++,l=(e=e.next()).outerWidth(),d=(s=e.position().left+l)-o,e.clone().prependTo(i).attr("idx",r).click(c).children(".close").click(h);u.$moreWrap.css("left",a.left).show()}}},click:function(){u.$moreWrap.hide();var t=$(this);if(!t.hasClass("k_tab_icon_btn_disabeld")){var e=u.$ul.position().left;if(!(0<=e)){for(var i=parseInt(Math.abs(e)),a=u.$ul.children().first(),n=a.outerWidth(),r=a.position().left+n,o=r-i;o<0;)n=(a=a.next()).outerWidth(),o=(r=a.position().left+n)-i;var l=parseInt(e+n-o);return l===u.$ul.position().left&&(l=parseInt(l+a.prev().outerWidth())),u.$ul.animate({left:l},200,function(){0===a.prev().length&&t.addClass("k_tab_icon_btn_disabeld"),t.trigger("mouseenter")}),u.rightButton.removeClass("k_tab_icon_btn_disabeld"),!1}t.addClass("k_tab_icon_btn_disabeld")}}}),delete this.opts.tabs,u.opts.cxtmenu){$("body").contextmenu(function(){return!1});var l=this.$ul.children().mousedown(function(){u.ctxtarget=$(this)});"array"==typeof u.opts.cxtmenu?this.ctxmenu=new c.Ctxmenu(l,u.opts.cxtmenu):this.ctxmenu=new c.Ctxmenu(l,[{text:c.config.tabMenu.closeAll,iconCls:"fa-cancel",click:function(){u.$moreWrap.hide();var i=[],a=[],t=u.$ul.children();t.each(function(t){var e=$(this);0<e.children("a.close").length&&(a.push(u.$body.children().eq(t)),i.push(e))});for(var e=!1,n=0,r=i.length;n<r;++n)i[n].hasClass("actived")&&(e=!0),i[n].remove(),a[n].remove();0<(t=u.$ul.children()).length?e&&u.$ul.animate({left:0},200,function(){t.first().trigger("click")}):(u.$ul.removeData("activedItem"),u.$ul.removeData("actived"))}},{text:c.config.tabMenu.closeRight,iconCls:"fa-forward-1",click:function(){u.$moreWrap.hide(),u.rightButton.addClass("k_tab_icon_btn_disabeld");var a=[],n=[],r=!1;u.$ul.children().each(function(t){var e=$(this);if(e.attr("title")===u.ctxtarget.attr("title")){for(var i=e.next();0<i.length;){t++,0<i.children("a.close").length&&(i.hasClass("actived")&&(r=!0),a.push(i),n.push(u.$body.children().eq(t))),i=i.next()}return!1}});for(var t=0,e=a.length;t<e;++t)a[t].remove(),n[t].remove();r&&u.$ul.children().last().trigger("click"),u.$ul.animate({left:0},200,function(){})}},{text:c.config.tabMenu.closeLeft,iconCls:"fa-reply-1",click:function(){u.$moreWrap.hide();var i=[],a=[],n=!1;u.$ul.children().each(function(t){var e=$(this);if(e.attr("title")===u.ctxtarget.attr("title"))return!1;0<e.children("a.close").length&&(e.hasClass("actived")&&(n=!0),i.push(e),a.push(u.$body.children().eq(t)))});for(var t=0,e=i.length;t<e;++t)i[t].remove(),a[t].remove();n&&u.$ul.children().first().trigger("click"),u.$ul.animate({left:0},200,function(){})}},{text:c.config.tabMenu.reload,iconCls:"fa-arrows-ccw",click:function(){u.$moreWrap.hide(),u.reload(u.ctxtarget.attr("title"))}}])}}return _.prototype={_scroll2show:function(t){},active:function(i){this.$moreWrap.hide(),this.$ul.children().each(function(t){var e=$(this);e.attr("title")===i&&(e.hasClass("actived")||e.trigger("click"))})},reload:function(i){this.$moreWrap.hide();var a=-1;if(this.$ul.children().each(function(t){var e=$(this);if(e.attr("title")===i)return a=t,e.trigger("click"),!1}),-1!==a){var t=this.$body.children().eq(a);o.call(t,this.opts.onLoaded)}},close:function(n){var r=this,o=-1;this.$ul.children().each(function(t){var e=$(this);if(e.attr("title")===n){if(o=t,e.hasClass("actived")){var i=e.next();if(0<i.length)i.trigger("click");else{var a=e.prev();0<a.length?a.trigger("click"):(r.removeData("activedItem"),r.removeData("actived"))}}return e.remove(),!1}}),-1!==o&&this.$body.children().eq(o).remove()},add:function(e){this.$moreWrap.hide();var t=this.$ul.children(),i=!1;if(t.each(function(){var t=$(this);if(e.title===t.attr("title"))return i=!0,t.trigger("click"),!1}),!i){if(this.opts.tabCount&&t.length>this.opts.tabCount)return void c.alert(c.config.tabLimit.replace("[x]",this.opts.tabCount),3);e.actived=!0;var a=p.call(this,e,this.$ul.height()),n=this;a.mousedown(function(){n.ctxtarget=$(this)}),this.ctxmenu.bandTarget(a)}},getActived:function(){return this.$ul.children("li.actived")}},c.Tab=_});