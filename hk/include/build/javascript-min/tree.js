/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(t,i){"function"==typeof define&&define.amd?define(["$B","utils","toolbar"],function(e){return i(t,e)}):(t.$B||(t.$B={}),i(t,t.$B))}("undefined"!=typeof window?window:this,function(i,y){var n;var o={data:null,isTreeData:!0,params:null,url:null,textField:"text",idField:"id",showLine:!0,checkSingle:!1,extParamFiled:[],canClickParent:!0,nodeParentIcon:"k_tree_fold_closed",nodeParentOpenIcon:"k_tree_fold_open",leafNodeIcon:"k_tree_file_icon",chkEmptyIcon:"k_tree_check_empty",chkAllIcon:"k_tree_check_all",chkSomeIcon:"k_tree_check_some",fontIconColor:void 0,clickCheck:!1,plainStyle:!1,forecePlainStyle:!1,onlyNodeData:!1,tree2list:!0,checkbox:!1,disChecked:!1,clickItemCls:"k_tree_clicked_cls",toolbar:!1,methodsObject:"methodsObject",onItemCreated:null,onTreeCreated:null,onClick:null,onloaded:null,onOperated:null,onCheck:null,onToggle:null},h=$("<li><div class='k_tree_item_wrap k_box_size'><div class='k_loading'></div></div></li>");function u(e,t,i){var n=this,o=this.opts,s=e.id,a=e.text;e.data[o.idField]&&(s=e.data[o.idField]),e.data[o.textField]&&(a=e.data[o.textField]);var l=$("<li id='"+s+"'><div  deep='"+t+"' style='display:inline-block;min-width:100%;white-space:nowrap;' class='k_tree_item_wrap k_box_size'><div class='k_tree_text k_box_size'>"+a+"</div></div></li>");return function(e,t,i,n,o){var s=e.opts,a=o.isLast,l=o.isFirst,r=o.isParent,c=t.children("div");c.data("data",i).data("params",o);var d,h,_,p=c.children(".k_tree_text"),k=i.closed;if(!k&&i.children&&0===i.children.length&&(k=!0),r&&c.attr("closed",k),s.checkbox&&(_=s.chkEmptyIcon,i.checked&&(_=s.chkAllIcon),-1<_.indexOf("fa-")?(h=$('<div class="k_tree_check_box k_tree_font_icon k_tree_font_check"><i class="fa '+_+'"></i></div>').prependTo(c),s.fontIconColor&&h.children("i").css("color",s.fontIconColor)):h=$('<div class="k_tree_check_box k_tree_icon_img '+_+'"></div>').prependTo(c),(i.disChecked||e.opts.disChecked)&&h.addClass("k_tree_check_disabled"),h.on("click",{_this:e},e._checkBoxClick),!r&&i.checked&&e.triggerClickNodes.push(h)),s.plainStyle||(-1<(_=r?k?s.nodeParentIcon:s.nodeParentOpenIcon:s.leafNodeIcon).indexOf("fa-")?(d=$('<div  class="k_tree_font_icon _node_"><i class="fa '+_+'"></i></div>').prependTo(c),s.fontIconColor&&d.children("i").css("color",s.fontIconColor)):d=$('<div class="k_tree_icon_img _node_ '+_+'"></div>').prependTo(c)),s.showLine){r?k?_=a&&l?0===n?"_line_node_ k_tree_line_last_first_closed":"_line_node_ k_tree_line_last_closed":a?"_line_node_ k_tree_line_last_closed":"_line_node_ k_tree_line_closed":a&&l&&0===n?_="_line_node_ k_tree_line_last_first_open":(_="_line_node_ k_tree_line_open",0===i.children.length&&a&&(_="_line_node_ k_tree_line_last_empty_open")):_=a&&l?"k_tree_line_last":l?"k_tree_line_cross":a?"k_tree_line_last":"k_tree_line_cross";var f=$("<div class='k_tree_icon_img  "+_+"'></div>").prependTo(c);r&&(d=f)}if(0<n){var v,u,m=o.endCount,g=c.children().first(),C=n-m;if(s.showLine?(v='<div class="k_tree_icon_img  k_tree_line_last"></div>',u='<div class="k_tree_icon_img  k_tree_line_vertical"></div>'):(v='<div  class="k_tree_blank_div"></div>',u='<div class="k_tree_blank_div"></div>'),a&&m)for(;0<m;)g=$(v).insertBefore(g),m--;for(;0<C;)g=$(u).insertBefore(g),C--}if(d&&d.on("click",{_this:e},e._parentNodeClick),s.toolbar&&i.toolbar){var I=$("<div class='k_tree_tools_wrap' style='width:auto;display:inline-block;height:20px;margin-left:14px;'></div>").insertAfter(p),b={style:"plain",showText:!0,params:i,methodsObject:s.methodsObject,buttons:i.toolbar,onOperated:s.onOperated};s.iconColor&&(b.iconColor=s.iconColor),s.fontColor&&(b.fontColor=s.fontColor),new y.Toolbar(I,b),delete i.toolbar}}(n,l,e,t,i),o.onItemCreated&&o.onItemCreated.call(l,e,t,i),l.on("click",{_this:n},n._onClick),l}function k(e,t,i){if(!t.opts.checkSingle&&"0"!==e.attr("deep")){var n=e.data("data"),o=n.checked?1:0,s=e.parent(),a=s.siblings(),l=t.opts.chkEmptyIcon,r=void 0!==n.children,c=e.children(".k_tree_check_box");i&&(c=c.children("i")),r&&c.hasClass(t.opts.chkSomeIcon)&&(l=t.opts.chkSomeIcon),a.each(function(){var e=$(this).children("div").children(".k_tree_check_box");i&&(e=e.children("i")),e.hasClass(t.opts.chkAllIcon)?o++:e.hasClass("."+t.opts.chkSomeIcon)&&(l=t.opts.chkSomeIcon)}),o===a.length+1?l=t.opts.chkAllIcon:0<o&&(l=t.opts.chkSomeIcon);var d=s.parent().prev();c=d.children(".k_tree_check_box"),i&&(c=c.children("i")),c.removeClass(t.opts.chkSomeIcon+" "+t.opts.chkAllIcon+" "+t.opts.chkEmptyIcon).addClass(l),l===t.opts.chkAllIcon?d.data("data").checked=!0:d.data("data").checked=!1,k(d,t,i)}}function m(e,t,i,n){var _,p,k,f,v;p=e,k=t,f=i,v=n,(_=this).running++,setTimeout(function(){for(var e,t,i,n=0,o=$("<ul />"),s=0,a=k.length;s<a;++s){var l={isFirst:!1,isLast:!0,isParent:!1};t=(e=k[s]).children,n=0;var r=!1,c=e.closed;if(l.isParent=!!t,l.isFirst=0===s,s===k.length-1?(r=l.isLast=!0,l.isParent&&(n=1+v,0<t.length&&!e.closed&&(r=!1))):l.isLast=!1,l.endCount=r?v:0,(i=u.call(_,e,f,l)).appendTo(o),t){_._putParentRecord(i);var d="";c&&(d="style='display:none;'");var h=$("<ul "+d+"/>").addClass("k_tree_ul").appendTo(i);0<t.length&&m.call(_,h,t,f+1,n)}else _._putChildRecord(i)}o.children().appendTo(p),_.running--,v=f=k=p=_=void 0},0)}function _(e,t){this.running=0,this.triggerClickNodes=[];var i,n,o,s,a,l=this.jqObj;$.isPlainObject(e)&&(e=[e]);for(var r=0,c=e.length;r<c;++r){var d={isFirst:!1,isLast:!0,isParent:!1};if(a=(i=e[r]).closed,n=i.children,s=0,d.isParent=!!n,d.isFirst=0===r,r===e.length-1?(d.isLast=!0,d.isParent&&(s=1)):d.isLast=!1,d.endCount=s,o=u.call(this,i,t,d),l.append(o[0]),n){this._putParentRecord(o);var h="";a&&(h="style='display:none;'");var _=$("<ul "+h+"/>").addClass("k_tree_ul").appendTo(o);0<n.length&&m.call(this,_,n,t+1,s)}else this._putChildRecord(o)}}function p(s,e,t){var a,l=this,r=this.opts,c=0,d=s.prev("div.k_tree_item_wrap");0<d.length&&(a=d.data("data"),c=parseInt(d.attr("deep"))+1),h.find(".k_loading").css("margin-left",20*c);var i=h.appendTo(s),n={async:!0,url:r.url,data:e,ok:function(e,t,i){if(console.log("loaded >>> tree"),l.opts.isTreeData||(t=l._formatData(t)),a){var n=d.data("params"),o=n.endCount;l.running=0,l.triggerClickNodes=[],a.children=t,n.isLast&&o++,m.call(l,s,t,c,o),f.call(l)}else r.data=t,_.call(l,t,c);r.onloaded&&setTimeout(function(){r.onloaded(t)},10)},fail:function(e){},final:function(e){i.remove(),t&&setTimeout(function(){t(e)},200),r.requestfinal&&r.requestfinal()}};this.ajax(n)}function f(){var _=this,p=setInterval(function(){if(0===_.running){var e,t,i,n,o,s,a;clearInterval(p),_.setRootUlWidth();for(var l=[],r=0,c=_.triggerClickNodes.length;r<c;++r)if(!(i=(s=(e=_.triggerClickNodes[r]).parent()).parent()).data("skip")){t=e.hasClass("k_tree_font_check"),n=!0;for(var d=0,h=(o=i.siblings()).length;d<h;++d)if(0<(a=(i=$(o[d])).children("ul")).length){0<a.children().length&&(n=!1);break}n&&(o.data("skip",!0),l.push(o),k(s,_,t))}setTimeout(function(){for(var e=0,t=l.length;e<t;++e)l[e].removeData("skip");l=void 0},5),_.opts.onTreeCreated&&_.opts.onTreeCreated()}},10)}var s=function(e,t){if(y.extend(this,s),this.parentNodesArray=[],this.childNodesArray=[],this.jqObj=e.addClass("k_tree_ul k_tree_root k_box_size"),this.jqObj.children().remove(),this.opts=$.extend({},{},o,t),this.opts.showLine||this.opts.forecePlainStyle||(this.opts.plainStyle=!1),this.clickedItem=null,this.opts.data&&0<this.opts.data.length)this.opts.isTreeData||(this.opts.data=this._formatData(this.opts.data)),_.call(this,this.opts.data,0),f.call(this);else if(this.opts.url&&""!==this.opts.url){var i=this;p.call(this,this.jqObj,{pid:""},function(){if(0===i.jqObj.children().length){var e=i.jqObj.offset();e.top=e.top+2,e.left=e.left+12,i._tipNotDate(e)}})}this.jqObj.data("treeIns",this)};return s.prototype={setRootUlWidth:function(){var i=this,e=this.jqObj.parent().width();this.minWidth=e,this.jqObj.children().each(function(){var e=$(this),t=0;e.children("div").children().each(function(){t=$(this).outerWidth()+t}),t>i.minWidth&&(i.minWidth=t),i._loopVisiableUl(e.children("ul"))}),this.jqObj.css("min-width",this.minWidth)},_loopVisiableUl:function(e){if(0<e.length&&"none"!==e.css("display")){var i=this;e.children().each(function(){var e=$(this),t=0;e.children("div").children().each(function(){t=$(this).outerWidth()+t}),t>i.minWidth&&(i.minWidth=t),i._loopVisiableUl(e.children("ul"))})}},_formatData:function(e,t){var i=[];if(t);else for(var n=0,o=e.length;n<o;++n){var s=e[n];i.push({id:s[this.opts.idField],text:s[this.opts.textField],data:s})}return i},_tree2list:function(e,n,o){var s={},a=void 0!==n.children,l=[];if(Object.keys(n).forEach(function(e){var t=n[e],i=!0;o&&(i=o(e,a,n)),"children"===e?l=t:i&&($.isPlainObject(t)&&(t=$.extend(!0,{},t)),s[e]=t)}),$.isEmptyObject(s)||e.push(s),0<l.length)for(var t=0,i=l.length;t<i;++t)this._tree2list(e,l[t],o)},_getNodeData:function(e){var i,t=e.data("params"),n=e.data("data"),o=this;if(t.isParent)if(o.opts.onlyNodeData)i={},Object.keys(n).forEach(function(e){if("children"!==e){var t=n[e];$.isPlainObject(t)&&(t=$.extend(!0,{},t)),i[e]=t}}),o.opts.tree2list&&(i=[i]);else if(o.opts.tree2list){var s=[];o._tree2list(s,n),i=s}else i=$.extend(!0,{},n);else i=$.extend(!0,{},n),o.opts.tree2list&&(i=[i]);return i},_onClick:function(e){var t=e.data._this,i="function"==typeof t.opts.onClick;if(i||t.opts.clickCheck){var n=$(this),o=n.children("div"),s=o.data("params");if(!t.opts.canClickParent&&s.isParent)return!1;if(t.clickedItem&&t.clickedItem.removeClass(t.opts.clickItemCls),t.clickedItem=o.addClass(t.opts.clickItemCls),t.opts.clickCheck){var a=o.children(".k_tree_check_box");a.hasClass("k_tree_check_disabled")||a.trigger("click")}if(i){var l=t._getNodeData(o);t.opts.onClick.call(n,l,s)}}return!1},_checkBoxClick:function(e){var t=e.data._this,i=$(this);if(e.isTrigger||!i.hasClass("k_tree_check_disabled")){var n=i.parent(),o=n.data("params");if(!t.opts.canClickParent&&o.isParent)return!1;var s=n.data("data"),a=0<n.next().length,l=i.children("i"),r=i.hasClass("k_tree_font_icon");if(0<l.length&&(i=l),s.checked?(i.removeClass(t.opts.chkAllIcon).removeClass(t.opts.chkSomeIcon).addClass(t.opts.chkEmptyIcon),s.checked=!1):(i.removeClass(t.opts.chkEmptyIcon).removeClass(t.opts.chkSomeIcon).addClass(t.opts.chkAllIcon),s.checked=!0),t.opts.checkSingle||(a?(k(n,t,r),function i(e,n,o,s){s.opts.checkSingle||e.next().children().each(function(){var e=$(this).children("div"),t=e.children(".k_tree_check_box");n&&(t=t.children("i")),t.removeClass(s.opts.chkSomeIcon+" "+s.opts.chkAllIcon+" "+s.opts.chkEmptyIcon),o?(t.addClass(s.opts.chkAllIcon),e.data("data").checked=!0):(t.addClass(s.opts.chkEmptyIcon),e.data("data").checked=!1),0<e.next().length&&i(e,n,o,s)})}(n,r,s.checked,t)):k(n,t,r)),t.opts.onCheck&&!t._noNotify){var c=t._getNodeData(n);t.opts.onCheck(c,o,s.checked)}return"function"==typeof t.onWatcher&&t.onWatcher.call(t),!1}},_changeNodeUi:function(e){var t,i,n,o,s=e.parent(),a=s.parent(),l=0===a.next().length,r=0,c=(t=e.hasClass("_node_")?e:e.next("._node_")).children("i"),d="none"===s.next().css("display");if(0<c.length&&(t=c),l&&this.opts.showLine)for(i=(n=a.parent()).parent();n[0]!==this.jqObj[0]&&0===i.next().length;)r++,i=(n=i.parent()).parent();if(e.hasClass("_node_"))d?t.removeClass(this.opts.nodeParentOpenIcon).addClass(this.opts.nodeParentIcon):t.removeClass(this.opts.nodeParentIcon).addClass(this.opts.nodeParentOpenIcon);else if(d){if(e.removeClass("k_tree_line_open k_tree_line_last_empty_open"),l)for(e.addClass("k_tree_line_last_closed"),o=e;0<r;)o=o.prev().removeClass("k_tree_line_vertical").addClass("k_tree_line_last"),r--;else e.addClass("k_tree_line_closed");t.removeClass(this.opts.nodeParentOpenIcon).addClass(this.opts.nodeParentIcon)}else for(e.removeClass("k_tree_line_last_closed k_tree_line_closed"),e.addClass("k_tree_line_open"),t.removeClass(this.opts.nodeParentIcon).addClass(this.opts.nodeParentOpenIcon),o=e;0<r;)o=o.prev().removeClass("k_tree_line_last").addClass("k_tree_line_vertical"),r--},_putParentRecord:function(e){this.parentNodesArray.push(e)},_putChildRecord:function(e){this.childNodesArray.push(e)},_parentNodeClick:function(e){var t=$(this);if(!t.data("busy")){var i=t.parent(),n=i.data("data"),o=e.data._this,s="id"===o.opts.idField?n.id:n.data[o.opts.idField],a=i.next(),l=0===a.children().length;t.data("busy",!0);var r=t.offset();if("true"===i.attr("closed"))if(l&&o.opts.url&&""!==o.opts.url){for(var c={pid:s},d=0,h=o.opts.extParamFiled.length;d<h;++d)c[o.opts.extParamFiled[d]]=n.data[o.opts.extParamFiled[d]];a.show(),p.call(o,a,c,function(e){i.removeAttr("closed"),t.removeData("busy"),o._changeNodeUi(t),0===a.children().length&&(t.trigger("click"),o._tipNotDate(r))})}else a.slideDown(150,function(){i.removeAttr("closed"),t.removeData("busy"),o._changeNodeUi(t),o.setRootUlWidth(),l&&(t.trigger("click"),o._tipNotDate(r)),o.opts.onToggle&&o.opts.onToggle("show")});else a.slideUp(150,function(){i.attr("closed",!0),t.removeData("busy"),o._changeNodeUi(t),o.setRootUlWidth(),o.opts.onToggle&&o.opts.onToggle("hide")})}return!1},_tipNotDate:function(e){var t=$("<div style='width:auto;position:absolute;top:-1000px;left:"+e.left+"px' class='k_tree_empty_tip k_box_size'>"+y.config.noData+"</div>").appendTo((n||(n=$(i.document.body).css("position","relative")),n));t.css("top",e.top),setTimeout(function(){t.fadeOut(450,function(){t.remove()})},1200)},reload:function(e,t){var n,i={};1===arguments.length?$.isPlainObject(e)?(n=this.jqObj,i=e):n=e:(n=e,i=t);var o,s=n[0]===this.jqObj[0],a=this;if(s)n.children().remove();else{var l=n.parent().attr("id");i.pid=l}n.children().remove(),p.call(this,n,i,function(e){if(s)0===a.jqObj.children().length&&((o=a.jqObj.offset()).top=o.top+2,o.left=o.left+12,a._tipNotDate(o));else{var t=n.prev();t.removeAttr("closed");var i=t.children("._line_node_");i.removeData("busy"),o=i.offset(),a._changeNodeUi(i),0===n.children().length&&(i.trigger("click"),a._tipNotDate(o))}})},reset:function(){if(this.clickedItem&&(this.clickedItem.removeClass(this.opts.clickItemCls),this.clickedItem=void 0),this.opts.checkbox)for(var e=0,t=this.childNodesArray.length;e<t;++e){var i=this.childNodesArray[e].children("div");i.data("data").checked&&i.children(".k_tree_check_box").trigger("click")}},updateNode:function(e,t){},getClickItem:function(){if(this.clickedItem)return this._getNodeData(this.clickedItem)},getCheckedData:function(e){var o=!1,s=!1;e&&(void 0!==e.onlyId&&(o=e.onlyId),void 0!==e.onlyChild&&(s=e.onlyChild));for(var t=[],a=this,i=this.opts.data,n=function(e,t,i){if(!i.checked)return!1;var n=!0;return o&&e!==a.opts.idField&&(n=!1),s&&t&&(n=!1),n},l=0,r=i.length;l<r;++l)this._tree2list(t,i[l],n);return t},getParentList:function(e){var t=[];t.push(e.data("data"));for(var i=e.parent().parent();0<i.length&&i.hasClass("k_tree_ul")&&!i.hasClass("k_tree_root");)e=i.prev(),t.push(e.data("data")),i=e.parent().parent();return t.reverse()},setCheckDatas:function(e){var t=this;if(0<this.running)setTimeout(function(){t.setCheckDatas(e)},200);else{"string"==typeof e&&(e=""===e?[]:e.split(","));for(var o={},i=0,n=e.length;i<n;++i)o[e[i]]=!0;this._noNotify=!0,this.loopRoot(function(e){var t=e.children("div"),i=t.data("data"),n=e.attr("id");i.checked?o[n]||t.children(".k_tree_check_box").trigger("click"):o[n]&&t.children(".k_tree_check_box").trigger("click")}),this._noNotify=!1}},setClickedItem:function(i){i+="";var n=this;if(0<this.running)setTimeout(function(){n.setClickedItem(i)},200);else{this.clickedItem&&this.clickedItem.removeClass("k_tree_clicked_cls");var o=!1;this.loopRoot(function(e){if(o)return!0;var t=e.children("div");return e.attr("id")===i?(n.clickedItem=t.addClass("k_tree_clicked_cls"),o=!0):void 0})}},loopRoot:function(i){var n=this;this.jqObj.children().each(function(){var e=$(this);if(i(e))return!1;var t=e.children("ul");0<t.length&&n.loopChild(t.children(),i)})},loopChild:function(e,i){var n=this;e.each(function(){var e=$(this);if(i(e))return!1;var t=e.children("ul");0<t.length&&n.loopChild(t.children(),i)})}},y.Tree=s});