/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(t,i,o){function e(t){this._init(t)}o.extend(e.prototype,{_init:function(t){var i=this;return i.options={scrollDir:"Y",contentSelector:"",barSelector:"",sliderSelector:"",wheelStep:100},o.extend(!0,i.options,t||{}),i._initDomEvent(),i},_initDomEvent:function(){var t=this.options;this.$cont=o(t.contentSelector),this.$slider=o(t.sliderSelector),this.$bar=t.barSelector?o(t.barSelector):self.$slider.parent(),this.$doc=o(i),this._initSliderDragEvent(),this._bindContentScroll(),this._bindMousewheel(),this._initSliderHeight()},_initSliderHeight:function(){var t=this.$cont.height()/this.$cont[0].scrollHeight*this.$bar.height();this.$slider.css("height",t)},_initSliderDragEvent:function(){var i=this,t=this.$slider;if(t[0]){var o,e,n,r=this.$doc;function l(t){t.preventDefault(),null!=o&&i.scrollTo(e+(t.pageY-o)*n)}return t.on("mousedown",function(t){t.preventDefault(),o=t.pageY,e=i.$cont[0].scrollTop,n=i.getMaxScrollPosition()/i.getMaxSliderPosition(),r.on("mousemove.scroll",l).on("mouseup.scroll",function(){r.off(".scroll")})}),i}},getSliderPosition:function(){var t=this,i=t.getMaxSliderPosition();return Math.min(i,i*t.$cont[0].scrollTop/t.getMaxScrollPosition())},getMaxScrollPosition:function(){var t=this;return Math.max(t.$cont.height(),t.$cont[0].scrollHeight)-t.$cont.height()},getMaxSliderPosition:function(){return this.$bar.height()-this.$slider.height()},_bindContentScroll:function(){var i=this;return i.$cont.on("scroll",function(){var t=i.$slider&&i.$slider[0];t&&(t.style.top=i.getSliderPosition()+"px")}),i},_bindMousewheel:function(){var e=this;e.$cont.on("mousewheel DOMMouseScroll",function(t){t.preventDefault();var i=t.originalEvent,o=i.wheelDelta?-i.wheelDelta/120:(i.detail||0)/3;e.scrollTo(e.$cont[0].scrollTop+o*e.options.wheelStep)})},scrollTo:function(t){this.$cont.scrollTop(t)}}),t.CusScrollBar=e}(window,document,jQuery);