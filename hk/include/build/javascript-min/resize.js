/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(t,e){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B","plugin"],function(i){return e(t,i)}):(t.$B||(t.$B={}),e(t,t.$B))}("undefined"!=typeof window?window:this,function(i,l){var p={target:void 0,zoomScale:!1,poitStyle:{color:"#FF292E","font-size":"8px"},lineStyle:{"border-color":"#FF292E","border-size":"1px"},onDragReady:void 0,dragStart:void 0,onDrag:void 0,dragEnd:void 0};function d(i){l.extend(this,d);var t=$("body");this.opts=$.extend({},p,i),this.target=this.opts.target;var h,r,A,D,E=this,O={};var e={nameSpace:"dragrezie",which:1,cursor:"move",axis:void 0,onStartDrag:function(i){var t=i.state;h=t.target,r=h.data("resizeData"),A={height:E.target.height(),width:E.target.width()},D=E.target.position(),(O={}).line1={width:E.line1.outerWidth(),position:E.line1.position()},O.line2={height:E.line2.outerHeight(),position:E.line2.position()},O.line3={width:E.line3.outerWidth(),position:E.line3.position()},O.line4={height:E.line4.outerHeight(),position:E.line4.position()},O.point0={position:E.poitArr[0].position()},O.point1={position:E.poitArr[1].position()},O.point2={position:E.poitArr[2].position()},O.point3={position:E.poitArr[3].position()},E.zoomScaleUpdateSet=O,E.opts.dragStart&&E.opts.dragStart.call(h,i)},onDrag:function(i){var t=i.state,e=r.index,o=r._type,s=!0;if(E.opts.onDrag){var n=E.opts.onDrag.call(h,i);void 0!==n&&(s=n)}!function(i,t,e,o){var s=i._data,n=s.leftOffset,h=s.topOffset,r=O.line1,l=O.line2,p=O.line3,d=O.line4,a={};if(e){E.opts.zoomScale&&(h=0===t||2===t?n:-n,s.top=s.startTop+h);var g,c,f={},_={},u={},z={};0===t?(f.top=r.position.top+h,f.left=r.position.left+n,f.width=r.width-n,E.line1.css(f),_.top=l.position.top+h,_.height=l.height-h,E.line2.css(_),u.left=p.position.left+n,u.width=p.width-n,E.line3.css(u),z.height=d.height-h,z.top=d.position.top+h,z.left=d.position.left+n,E.line4.css(z),E._updatePoitPosition(0,h,1),E._updatePoitPosition(n,0,3),a.width=A.width-n,a.height=A.height-h,a.top=D.top+h,a.left=D.left+n):1===t?(f.top=r.position.top+h,f.width=r.width+n,E.line1.css(f),_.top=l.position.top+h,_.height=l.height-h,_.left=l.position.left+n,E.line2.css(_),u.width=p.width+n,E.line3.css(u),z.top=d.position.top+h,z.height=d.height-h,E.line4.css(z),E._updatePoitPosition(0,h,0),E._updatePoitPosition(n,0,2),a.width=A.width+n,a.height=A.height-h,a.top=D.top+h):2===t?(f.width=r.width+n,E.line1.css(f),_.height=l.height+h,_.left=l.position.left+n,E.line2.css(_),u.width=p.width+n,u.top=p.position.top+h,E.line3.css(u),z.height=d.height+h,E.line4.css(z),E._updatePoitPosition(n,0,1),E._updatePoitPosition(0,h,3),a.width=A.width+n,a.height=A.height+h):(f.left=r.position.left+n,f.width=r.width-n,E.line1.css(f),_.height=l.height+h,E.line2.css(_),u.top=p.position.top+h,u.left=p.position.left+n,u.width=p.width-n,E.line3.css(u),z.height=d.height+h,z.left=d.position.left+n,E.line4.css(z),E._updatePoitPosition(n,0,0),E._updatePoitPosition(0,h,2),a.width=A.width-n,a.height=A.height+h,a.left=D.left+n)}else if(0===t){g=h/2,_={top:l.position.top+h},z={top:d.position.top+h},E.line2.outerHeight(l.height-h),E.line4.outerHeight(d.height-h);var w=0,b=0;a.top=D.top+h,a.height=A.height-h,E.opts.zoomScale&&(s.left=r.position.left+g,c=r.width-h,E.line1.outerWidth(c),E.line3.outerWidth(c),E.line3.css("left",p.position.left+g),_.left=l.position.left-g,z.left=d.position.left+g,b=-(w=g),E._updatePoitPosition(-g,0,2),E._updatePoitPosition(g,0,3),a.left=D.left+g,a.width=A.width-h),E._updatePoitPosition(w,h,0),E._updatePoitPosition(b,h,1),E.line2.css(_),E.line4.css(z)}else if(1===t){g=n/2,f={width:c=r.width+n},_={left:r.position.left+n},u={width:c},a.width=A.width+n;var P=h,x=h;E.opts.zoomScale&&(s.top=l.position.top-g,_.height=l.height+n,P=-g,x=g,E._updatePoitPosition(0,P,0),E._updatePoitPosition(0,x,3),E.line4.css({height:_.height,top:d.position.top-g}),E.line1.css({top:r.position.top-g}),E.line3.css({top:p.position.top+g}),a.height=A.height+n,a.top=D.top-g),E._updatePoitPosition(n,P,1),E._updatePoitPosition(n,x,2),E.line1.css(f),E.line2.css(_),E.line3.css(u)}else if(2===t){g=h/2;var v=0,y=0,S=l.height+h;_={height:S},z={height:S},u={top:p.position.top+h},a.height=A.height+h,E.opts.zoomScale&&(s.left=p.position.left-g,u.width=p.width+h,f={width:u.width,left:r.position.left-g},E.line1.css(f),_.left=l.position.left+g,z.left=d.position.left-g,y=-(v=g),E._updatePoitPosition(-g,0,0),E._updatePoitPosition(g,0,1),a.width=A.width+h,a.left=D.left-g),E.line2.css(_),E.line4.css(z),E.line3.css(u),E._updatePoitPosition(v,h,2),E._updatePoitPosition(y,h,3)}else{g=n/2,u={width:(f={width:r.width-n,left:r.position.left+n}).width,left:f.left},z={left:d.left-n},a.width=A.width-n,a.left=D.left+n;var k=0,m=0;E.opts.zoomScale&&(m=-(k=g),E._updatePoitPosition(0,k,1),E._updatePoitPosition(0,m,2),s.top=d.position.top+g,z.height=d.height-n,_={height:l.height-n,top:s.top},E.line2.css(_),f.top=r.position.top+g,u.top=p.position.top-g,a.height=A.height-n,a.top=D.top+g),E.line1.css(f),E.line3.css(u),E.line4.css(z),E._updatePoitPosition(n,k,0),E._updatePoitPosition(n,m,3)}E.target?o&&E.target.css(a):console.log("_this.target.css(targetCss); is null")}(t,e,o,s)},onStopDrag:function(i){E.opts.dragEnd&&E.opts.dragEnd.call(h,i),r=h=void 0}};this.line1=$("<div style='cursor:s-resize;height:3px;position:absolute;border-top:1px solid;display:none;z-index:2147483647' _id='k_resize_line_0' class='k_resize_element k_box_size k_resize_line_0'></div>").appendTo(t),this.line2=$("<div style='cursor:w-resize;width:3px;position:absolute;border-right:1px solid;display:none;z-index:2147483647' _id='k_resize_line_1' class='k_resize_element k_box_size k_resize_line_1'></div>").appendTo(t),this.line3=$("<div style='cursor:s-resize;height:3px;position:absolute;border-bottom:1px solid;display:none;z-index:2147483647' _id='k_resize_line_2' class='k_resize_element k_box_size k_resize_line_2'></div>").appendTo(t),this.line4=$("<div style='cursor:w-resize;width:3px;position:absolute;border-left:1px solid;display:none;z-index:2147483647'_id='k_resize_line_3'  class='k_resize_element k_box_size k_resize_line_3'></div>").appendTo(t),e.cursor="s-resize",e.axis="v",this.line1.css(this.opts.lineStyle).draggable(e).data("resizeData",{_type:0,index:0}),e.cursor="w-resize",e.axis="h",this.line2.css(this.opts.lineStyle).draggable(e).data("resizeData",{_type:0,index:1}),e.cursor="s-resize",e.axis="v",this.line3.css(this.opts.lineStyle).draggable(e).data("resizeData",{_type:0,index:2}),e.cursor="w-resize",e.axis="h",this.line4.css(this.opts.lineStyle).draggable(e).data("resizeData",{_type:0,index:3}),this._fixLineStyle(),this.poitArr={};var o,s=0;for(e.axis=void 0;s<4;){o=0===s?"se-resize":1===s?"ne-resize":2===s?"se-resize":"ne-resize",e.cursor=o;var n=$("<div style='display:none;position:absolute;z-index:2147483647;cursor:"+o+"' class='k_resize_element k_resize_element_point k_box_size k_resize_point_"+s+"' _id='k_resize_point_"+s+"'></div>").appendTo(t);n.children().css(this.opts.poitStyle),n.draggable(e).data("resizeData",{_type:1,index:s}),this.poitArr[s]=n,s=++s}this.jqObj=t.children(".k_resize_element"),this.target&&this.bind(this.target)}return d.prototype={_fixLineStyle:function(){this.line1.css({"border-left":"none","border-right":"none","border-bottom":"none"}),this.line2.css({"border-left":"none","border-top":"none","border-bottom":"none"}),this.line3.css({"border-left":"none","border-right":"none","border-top":"none"}),this.line4.css({"border-top":"none","border-right":"none","border-bottom":"none"})},setStyle:function(t,i){this.line1.css(i),this.line2.css(i),this.line3.css(i),this.line4.css(i),this._fixLineStyle();var e=this;Object.keys(this.poitArr).forEach(function(i){e.poitArr[i].css(t)})},zoomScale:function(i){this.opts.zoomScale=i},setTarget:function(i){this.target&&this.target[0]===i[0]||(this.target=i)},bind:function(i){this.setTarget(i),this.show();var t=i.offset(),e={width:i.outerWidth(),height:i.outerHeight()};return this.line1.css({top:t.top-1+"px",left:t.left+"px"}).outerWidth(e.width),this.line2.css({top:t.top+"px",left:t.left+e.width-2+"px",height:e.height}),this.line3.css({top:t.top+e.height-2+"px",left:t.left+"px",width:e.width}),this.line4.css({top:t.top+"px",left:t.left-1+"px"}).outerHeight(e.height),this._initPoitPosition(),this},_updatePoitPosition:function(i,t,e){var o=this.poitArr[e],s=this.zoomScaleUpdateSet["point"+e];o.css({top:s.position.top+t,left:s.position.left+i})},_initPoitPosition:function(){var i=Object.keys(this.poitArr);if(0<i.length){this.line1.outerWidth();var h=this.line1.position(),r=(this.line2.outerHeight(),this.line2.position()),l=(this.line3.outerWidth(),this.line3.position()),p=this;i.forEach(function(i){var t,e=parseInt(i),o=p.poitArr[e],s=o.width()/2,n=o.height()/2;0===e?t={top:h.top-n+"px",left:h.left-3+"px"}:1===e?t={top:h.top-n+"px",left:r.left-2+"px"}:2===e?t={top:l.top-2+"px",left:r.left-2+"px"}:3===e&&(t={top:l.top-2+"px",left:l.left-s+"px"}),o.css(t)})}},_drag:function(i,e){var o=this;"line"===i?(this.line1.draggable(e,"dragrezie"),this.line2.draggable(e,"dragrezie"),this.line3.draggable(e,"dragrezie"),this.line4.draggable(e,"dragrezie")):"point"===i?Object.keys(this.poitArr).forEach(function(i){var t=parseInt(i);o.poitArr[t].draggable(e,"dragrezie")}):"right"===i?(this.line2.draggable(e,"dragrezie"),this.line3.draggable(e,"dragrezie"),this.poitArr[1].draggable(e,"dragrezie"),this.poitArr[2].draggable(e,"dragrezie")):"left"===i?(this.line1.draggable(e,"dragrezie"),this.line4.draggable(e,"dragrezie"),this.poitArr[0].draggable(e,"dragrezie"),this.poitArr[3].draggable(e,"dragrezie")):(this.line1.draggable(e,"dragrezie"),this.line2.draggable(e,"dragrezie"),this.line3.draggable(e,"dragrezie"),this.line4.draggable(e,"dragrezie"),Object.keys(this.poitArr).forEach(function(i){var t=parseInt(i);o.poitArr[t].draggable(e,"dragrezie")}))},disable:function(i){this._drag(i,"disable")},enable:function(i){this._drag(i,"enable")},unbind:function(){return this.target=void 0,this.hide(),this},show:function(i){if(i)this.bind(i);else{var t=this;this.line1.show(),this.line2.show(),this.line3.show(),this.line4.show(),Object.keys(this.poitArr).forEach(function(i){t.poitArr[i].show()})}return this},hide:function(i){if(!i||this.target&&i&&i[0]===this.target[0]){this.line1.hide(),this.line2.hide(),this.line3.hide(),this.line4.hide();var t=this;Object.keys(this.poitArr).forEach(function(i){t.poitArr[i].hide()})}return this},isHide:function(){return"none"===this.line1.css("display")},isShow:function(){return"none"!==this.line1.css("display")},destroy:function(){this.super.destroy.call(this)}},l.Resize=d});