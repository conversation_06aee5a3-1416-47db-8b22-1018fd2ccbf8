/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(t,r){"function"==typeof define&&define.amd?define(["jquery"],function(e){return r(t,e)}):r(t,$)}("undefined"!=typeof window?window:this,function(e,w){"use strict";var u={type:!0,name:!0,watcher:!0,id:!0},t=e.$B?e.$B:{};String.prototype.trim=function(){return this.replace(/(^\s*)|(\s*$)/g,"")},String.prototype.leftTrim=function(){return this.replace(/(^\s*)/g,"")},String.prototype.rightTrim=function(){return this.replace(/(\s*$)/g,"")};var s,v=/\{\s*\{(.*)\}\s*\}/,o={};function j(e){if(o[e])return o[e];for(var t=e.match(/\{\{((?!\}\}).)+\}+/g),r=[],a=0,i=t.length;a<i;++a){var n=t[a].replace(/\s*\{\{\s*/,"").replace(/\s*\}\}\s*/,"");r.push(n)}return o[e]=r,clearTimeout(s),s=setTimeout(function(){o={}},1200),r}var r,a={};function _(e){var t=a[e];return t||(t=e.replace(/{\s*\{/g,"").replace(/\}\s*\}/g,"").trim(),a[e]=t,clearTimeout(r),r=setTimeout(function(){a={}},1200),t)}function h(e){for(var t,r,a=Object.keys(e),i={},n=0,s=a.length;n<s;++n)"toJson"!=typeof(r=e[t=a[n]])&&"function"!=typeof r&&"_$watchers"!==t&&"_$path"!==t&&(w.isPlainObject(r)?i[t]=h(r):i[t]=r);return i}function m(e,t,r,a){this.vm=e,this.attrName=a?a.toLowerCase():a,this.el=t,this.$el=w(this.el),this.nodeName=t.nodeName;var i,n=j(this.replaceContent=r);this.methodKeys=[],this.propPathArray=[],this.propObjectArray=[];var s,o,h={};for(s=0,o=n.length;s<o;++s){i=_(i=n[s]),this.methodKeys.push(i);for(var c=i.match(/this\.data\.[^\s*]+/g),p=0,l=c.length;p<l;++p)if(!h[i=c[p]]){h[i]=!0;for(var d=this.vm.extractPropObject(i),f=0,u=d.length;f<u;f++){var v=d[f],m=v.propObject;this.propPathArray.push(v.propPath),m._$watchers||(m._$watchers=[]),m._$watchers.push(this),this.propObjectArray.push(m)}}}var g=this;if("express"===this.attrName){var y=this.$el.attr("watcher");y&&"function"==typeof this.vm[y]&&this.vm[y](this)}else{var k=1===this.el.nodeType,$="TEXTAREA"===this.el.parentNode.nodeName;if($&&(k=!0),k){var x=100;this.userInputTimer;var b,O=this.$el;"value"===this.attrName||$?(b="input",$&&(O=w(this.el.parentNode))):"checked"!==this.attrName&&"selected"!==this.attrName||(x=0,b="change","OPTION"===this.nodeName&&"selected"===this.attrName&&((O=this.$el.parent()).data("hasBindWatcher")?b=void 0:O.data("hasBindWatcher",!0))),b&&O.on(b+".mvvm",function(){if(g.propChangeUpdating)return!1;var t=this,e=w(this),i=e.val(),r=e.attr("type");if(r&&(r=r.toLowerCase()),clearTimeout(g.userInputTimer),"radio"===r){var a=e.attr("name"),n=g.vm.$form.find("input[name="+a+"]");n.each(function(){var e=w(this);this!==t?e.removeAttr("checked"):e.attr("checked","checked").prop("checked",!0)}),n.data("no2update",!0),setTimeout(function(){n.removeData("no2update")},100)}else if("SELECT"===e[0].nodeName){var s=e.children().data("no2update",!0);s.each(function(){var e=w(this);i===e.val()?e.prop("selected",!0).attr("selected","selected"):e.prop("selected",!1).removeAttr("selected")}),setTimeout(function(){s.removeData("no2update")},100)}g.userInputTimer=setTimeout(function(){var e=g.propPathArray[0].split("."),t=e[e.length-1],r=g.propObjectArray[0],a=r[t];w.isPlainObject(a)||("number"==typeof a&&""!==i&&(i=a%1==0?parseInt(i):parseFloat(i)),r[t]=i)},x)})}}}function i(e){this.vm=e,this.el=e.el,this.$el=e.$form,this.data=e.data,this.watchers=[],this.compile(this.el)}function n(e,t){this.data=e,this.vm=t;for(var r,a,i,n=Object.keys(e),s=0,o=n.length;s<o;++s)"_$watchers"!==(a=n[s])&&"_$path"!==a&&"toJson"!==a&&(r=[],i=e[a],w.isPlainObject(i)&&r.push(a),this.getSetBuild(e,a,i,r))}function c(e){this.el="string"==typeof e.el?document.getElementById(e.el):e.el,this.$form=w(this.el),this.options=e,this.data=e.data,this.onChanged=e.onChanged,this.expressMethodCache={},this.pathObjectsCache={},this.initing=!0,this._attachDiyRegiste(this.options.registeWatchers),this._attachDiyRegiste(this.options.registeExpress),this.observer=new n(this.data,this),this.compiler=new i(this);var t=this;setTimeout(function(){t.initing=!0},50),"function"!=typeof this.data.toJson&&(this.data.toJson=function(){for(var e,t,r={},a=Object.keys(this),i=0,n=a.length;i<n;++i)"function"!=typeof(e=this[t=a[i]])&&"toJson"!==t&&"_$watchers"!==t&&"_$path"!==t&&(w.isPlainObject(e)?r[t]=h(e):r[t]=e);return r})}return m.prototype={update:function(e,t,r,a){if(!this.$el.data("no2update")){this.propChangeUpdating=!0;var i=e._$path?e._$path+"."+t:t;if(this.need2Update(i)&&r!==a){"express"===this.attrName?this.expressUpater():3===this.el.nodeType?this.textUpdater():"OPTION"===this.nodeName&&"selected"===this.attrName.toLowerCase()?this.selectUpdater():"INPUT"===this.nodeName&&"radio"===this.$el.attr("type")?this.radioUpdater():this.elUpdater();var n=this;setTimeout(function(){n.propChangeUpdating=!1},20)}else this.propChangeUpdating=!1}},need2Update:function(e){for(var t=!1,r=0,a=this.propPathArray.length;r<a;++r){var i=this.propPathArray[r];if(i===e){t=!0;break}if(0===i.indexOf(e+".")){t=!0;break}}return t},expressUpater:function(){for(var e,t=0,r=this.methodKeys.length;t<r;++t)e=this.methodKeys[t],this.vm.callExpressFuntion(e,this.$el)},radioUpdater:function(){var r=this;r._invokeMethods(function(e,t){t?r.$el.attr("checked","checked").prop("checked",!0):r.$el.removeAttr("checked").prop("checked",!1)})},selectUpdater:function(){var r=this;r._invokeMethods(function(e,t){t?r.$el.prop("selected",!0).attr("selected","selected"):r.$el.prop("selected",!1).removeAttr("selected")})},textUpdater:function(){var a=this.replaceContent,i=!1;this._invokeMethods(function(e,t){if(void 0!==t){e=e.replace(/\+/g,"\\+").replace(/\-/g,"\\-").replace(/\*/g,"\\*");var r=new RegExp("{\\s*{\\s*"+e+"\\s*}\\s*}","g");a=a.replace(r,t),i=!0}}),i&&(this.el.textContent=a,this.el.nodeValue=a,this.el.data=a,"TEXTAREA"===this.el.parentNode.nodeName&&(this.el.parentNode.value=a,this.el=this.el.parentNode.firstChild))},elUpdater:function(){var r=this;this._invokeMethods(function(e,t){r.$el.attr(r.attrName,t),"value"===r.attrName&&r.$el.val(t)})},_invokeMethods:function(e){for(var t,r=0,a=this.methodKeys.length;r<a;++r)e(t=this.methodKeys[r],this.vm.callExpressFuntion(t))},destroy:function(){for(var e in this)this.hasOwnProperty(e)&&delete this[e]}},i.prototype={compile:function(e){3===(this.compileingEl=e).nodeType?this.compileTextNode(e):1===e.nodeType&&this.compileElement(e)},compileElement:function(e){var t=e.nodeName;if("SCRIPT"!==t&&"STYLE"!==t){var r,a,i,n=e.attributes,s=this,o=w(e);for(a=0,i=n.length;a<i;++a)if(r=n[a]){var h=r.name;if(!u[h]){var c,p=r.value;if(v.test(p))if(p=(p=p.replace(/\{\s*\{/g,"{{")).replace(/\}\s*\}/g,"}}"),"INPUT"===t&&"value"===h)c=s.invokeExpressMethod(p),o.attr("value",c),s.watchers.push(new m(s.vm,e,p,h));else if("INPUT"===t&&"checked"===h)c=s.invokeExpressMethod(p),o.removeAttr("checked"),c&&(o.attr("checked","checked"),o.prop("checked",!0)),s.watchers.push(new m(s.vm,e,p,h));else if("OPTION"===t&&"selected"===h)c=s.invokeExpressMethod(p),o.removeAttr("selected"),c&&(o.attr("selected","selected"),o.prop("selected",!0)),s.watchers.push(new m(s.vm,e,p,h));else if("express"===h){var l=_(p);s.makeExpressionFunction(l).call(s.vm,o),s.watchers.push(new m(s.vm,e,p,h))}else c=s.invokeExpressMethod(p),o.attr(r.name,c),s.watchers.push(new m(s.vm,e,p,h))}}var d,f=e.childNodes;for(a=0,i=f.length;a<i;++a)d=f[a],this.compile(d)}},compileTextNode:function(e){var t=e.textContent;if(v.test(t)){t=(t=t.replace(/\{\s*\{/g,"{{")).replace(/\}\s*\}/g,"}}");var r=this.invokeExpressMethod(t);void 0!==r&&(e.textContent=r,this.watchers.push(new m(this.vm,e,t)))}},getExpressMethod:function(e){return this.invokeExpressMethod(e,!0)},invokeExpressMethod:function(e,t){var r=j(e);if(r){for(var a,i,n,s,o,h=e,c=0,p=r.length;c<p;++c){if(i=_(a=r[c]),(n=this.vm.expressMethodCache[i])||(n=this.makeExpressionFunction(i)),t)return n;if("boolean"==typeof(s=n.call(this.vm)))return s;a=(a=(a=a.replace(/\+/g,"\\+").replace(/\-/g,"\\-").replace(/\*/g,"\\*")).replace(/\(/g,"\\(").replace(/\)/g,"\\)")).replace(/\{/g,"\\{").replace(/\}/g,"\\}"),o=new RegExp(a,"g"),h=h.replace(o,s)}return h=h.replace(/\{\{/g,"").replace(/\}\}/g,"")}},makeExpressionFunction:function(e){var t,r=this.vm.expressMethodCache[e];r||(t=0<e.indexOf("return")?e:"return "+e+";",r=new Function("el",t),this.vm.expressMethodCache[e]=r);return r},destroy:function(){for(var e=0,t=this.watchers.length;e<t;++e)this.watchers[e].destroy();for(var r in this)this.hasOwnProperty(r)&&delete this[r]}},n.prototype={forProps:function(e,t){if(e&&w.isPlainObject(e)){e._$path=t.join(".");for(var r,a,i,n=Object.keys(e),s=0,o=n.length;s<o;++s)"_$watchers"!==(r=n[s])&&"_$path"!==r&&"toJson"!==r&&(a=e[r],i=t.slice(),w.isPlainObject(a)&&i.push(r),this.getSetBuild(e,r,a,i))}},getSetBuild:function(e,r,a,i){var n=this;this.forProps(a,i),Object.defineProperty(e,r,{enumerable:!0,configurable:!0,set:function(e){if(a+""!=e+""){var t=a;w.isPlainObject(a)&&w.isPlainObject(e)&&n.copyWatcher(a,e),a=e,n.forProps(a,i),n.onSet(this,r,e,t)}},get:function(){return a}})},copyWatcher:function(e,t){e._$watchers&&(t._$watchers=e._$watchers,e._$watchers=void 0),e._$path&&(t._$path=e._$path,e._$path=void 0);for(var r,a,i=Object.keys(e),n=0,s=i.length;n<s;++n)if("object"==typeof(r=e[a=i[n]])){var o=t[a];"object"==typeof o&&this.copyWatcher(r,o)}},onSet:function(e,t,r,a){if(e._$watchers)for(var i=0,n=e._$watchers.length;i<n;++i){e._$watchers[i].update(e,t,r,a)}"function"==typeof this.vm.onChanged&&this.vm.onChanged.call(this.vm,e,t,r,a)},destroy:function(){for(var e in this)this.hasOwnProperty(e)&&delete this[e]}},c.prototype={destroy:function(){for(var e in function e(t){if(t)for(var r,a,i=Object.keys(t),n=0,s=i.length;n<s;++n)"_$watchers"===(r=i[n])?t[r]=[]:"toJson"!=typeof(a=t[r])&&"function"!=typeof a&&"_$path"!==r&&w.isPlainObject(a)&&e(a)}(this.data),this.observer.destroy(),this.compiler.destroy(),this)this.hasOwnProperty(e)&&delete this[e]},extractPropObject:function(e){var t=e,r=this.pathObjectsCache[t];if(r)return r;var a=e.match(/this\.data\.[^\s+|^,]+/g);r=[];for(var i,n,s=this.data,o=0,h=a.length;o<h;++o){if(i=s,0<(e=a[o].replace("this.data.","")).indexOf("."))for(var c=0,p=(n=e.split(".")).length-1;c<p;++c)c<p&&(i=i[n[c]]);r.push({propObject:i,propPath:e})}return this.pathObjectsCache[t]=r},_attachDiyRegiste:function(e){var t,r,a,i,n;if(w.isPlainObject(e))for(r=0,a=(t=Object.keys(e)).length;r<a;++r)i=e[n=t[r]],"function"!=typeof this[n]?"function"==typeof i&&(this[n]=i):console.log("key["+n+"]已经存在！")},registeWatcher:function(e,t){return"function"==typeof this[e]&&console.log("watcher["+e+"]已经存在！"),this[e]=t,this},registeExpress:function(e,t){return"function"==typeof this[e]&&console.log("express["+e+"]已经存在！"),this[e]=t,this},compile:function(e){return e.css&&(e=e[0]),this.compiler.compile(e),this},callExpressFuntion:function(e,t){var r=this.expressMethodCache[e];if(r)return r.call(this,t)},_forProps:function(e,t){for(var r,a,i=Object.keys(e),n=0,s=i.length;n<s;++n)"function"!=typeof(r=e[a=i[n]])&&"_$watchers"!==a&&"_$path"!==a&&(w.isPlainObject(r)?(t[a]={},this._forProps(r,t[a])):t[a]=r)},getJson:function(){for(var e,t,r={},a=Object.keys(this.data),i=0,n=a.length;i<n;++i)t=a[i],"toJson"!=typeof(e=this.data[t])&&"function"!=typeof e&&"_$watchers"!==t&&"_$path"!==t&&(w.isPlainObject(e)?(r[t]={},this._forProps(e,r[t])):r[t]=e);return this.options.onGetJson&&this.options.onGetJson(r),r},checkboxWather:function(e){var c=e.propObjectArray[0],t=e.propPathArray[0].split("."),p=t[t.length-1];e.$el.on("click",function(){var e=w(this),t=e.val(),r=c[p],a=!1,i=w.isArray(r),n=!1;i?0<r.length&&(a=w.isNumeric(r[0])):(n=0<r.indexOf(";"),a=!1,r=""===r?[]:n?r.split(";"):r.split(",")),a&&(t=parseInt(t));var s=r;if(e.prop("checked"))e.attr("checked","checked"),r.push(t);else{e.removeAttr("checked"),s=[];for(var o=0,h=r.length;o<h;++o)""!==r[o]&&r[o]!==t&&s.push(r[o])}i||(c[p]=n?s.join(";"):s.join(","))})},checkboxExpress:function(e,t){var r=t.val();w.isArray(e)||(0<e.indexOf(",")?e=e.split(","):0<e.indexOf(";")&&(e=e.split(";")));for(var a=!1,i=0,n=e.length;i<n;++i)if(r===e[i]+""){a=!0;break}a?t.prop("checked",!0).attr("checked","checked"):t.prop("checked",!1).removeAttr("checked")},kRadioWatcher:function(e){var t=e.$el,a=e.propObjectArray[0],i=e.propPathArray[0];t.children("input").on("change",function(){var e=w(this).val(),t=a[i],r=w.isNumeric(t);a[i]=r?parseInt(e):e})},kRadioExpress:function(t,e){w(e).find("input[type=radio]").each(function(){var e=w(this);e.val()===t+""?e.attr("checked","checked").prop("checked",!0):e.removeAttr("checked").prop("checked",!1)})},kcheckBoxWatcher:function(e){var t=e.$el,h=e.propObjectArray[0],c=e.propPathArray[0];t.children("input").on("change",function(){var e,t=w(this),r=h[c],a=[];""!==r&&(0<r.indexOf(";")?(a=r.split(";"),e=";"):(a=r.split(","),e=","));var i=t.val();if(t.prop("checked"))a.push(i);else{for(var n=[],s=0,o=a.length;s<o;++s)i!==a[s]&&n.push(a[s]);a=n}h[c]=a.join(e)})},kcheckBoxExpress:function(r,e){w(e).find("input[type=checkbox]").each(function(){var e=w(this),t=e.val();(0<=r.indexOf(";")?new RegExp(";"+t+";|;"+t+"|"+t+";").test(r):0<=r.indexOf(",")?new RegExp(","+t+",|,"+t+"|"+t+",").test(r):r===t)?e.attr("checked","checked").prop("checked",!0):e.removeAttr("checked").prop("checked",!1)})},kcomboxWatcher:function(t){var r,a,e,i,n,s=t.$el.data("combox"),o=t.propObjectArray[0],h=t.propPathArray[0];if(s.opts.checkbox){for(e=s.getCheckedIds(),a=[],i=0,n=e.length;i<n;++i)""!==e[i].id&&a.push(e[i].id);r=a.join(",")}else r=s.jqObj.data("id");o[h]+""!==r&&(t.$el.data("no2update",!0),o[h]=r,t.$el.removeData("no2update")),s.regiterFn("onWatcher",function(){if(this.opts.checkbox){var e=this.getCheckedIds();for(a=[],i=0,n=e.length;i<n;++i)""!==e[i].id&&a.push(e[i].id);r=a.join(",")}else r=this.jqObj.data("id");o[h]+""!==r&&(t.$el.data("no2update",!0),o[h]=r,t.$el.removeData("no2update"))})},kcomboxExpress:function(e,t){t.data("combox").setCheckDatas(e)},kcalendarWatcher:function(e){var t=e.$el.data("calender"),r=e.propObjectArray[0],a=e.propPathArray[0];t.regiterFn("onWatcher",function(){e.$el.data("no2update",!0),r[a]=this.target.val(),e.$el.removeData("no2update")});var i=e.$el.val();""!==i&&i!==r[a]&&(e.$el.data("no2update",!0),r[a]=i,e.$el.removeData("no2update"))},kcalendarExpress:function(e,t){""!==e&&w(t).data("calender").setValue(e)},ktreeExpress:function(e,t){w(t).data("treeIns").setCheckDatas(e)},ktreeWatcher:function(i){var e=i.$el.data("treeIns"),n=i.propObjectArray[0],s=i.propPathArray[0],o=n[s];e.regiterFn("onWatcher",function(){for(var e=this.getCheckedData({onlyId:!0}),t=[],r=0,a=e.length;r<a;++r)t.push(e[r].id);o=t.join(","),n[s]+""!==o&&(i.$el.data("no2update",!0),n[s]=o,i.$el.removeData("no2update"))})},kwindowInputExpress:function(e,t){for(var r,a=e.split(","),i=[],n=0,s=a.length;n<s;++n)r=a[n].split("___"),i.push(r[1]);t.val(i.join(",")).attr("_val",e)},kwindowInputWatcher:function(o){var e=o.$el,h=o.propObjectArray[0],c=o.propPathArray[0];e.on("change",function(){console.log("wather input change");for(var e,t=w(this),r=t.attr("_val"),a=r.split(","),i=[],n=0,s=a.length;n<s;++n)e=a[n].split("___"),i.push(e[1]);t.val(i.join(",")),o.$el.data("no2update",!0),h[c]=r,o.$el.removeData("no2update")})}},t.Mvvm=c,e.$B=t,c});