/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(i,e){"function"==typeof define&&define.amd?define(["$B"],function(t){return e(i,t)}):(i.$B||(i.$B={}),e(i,i.$B))}("undefined"!=typeof window?window:this,function(r,n){var o={textField:"text",ellipsis:!1,idField:"id",scrollStyle:{size:"8px",display:"auto",setPadding:!1,hightColor:"#1590FA",slider:{"background-color":"#6CB7FA","border-radius":"8px"},bar:{"background-color":"#E8E8E8","border-radius":"8px",opacity:.5}}};function l(t,i){n.extend(this,l),this.jqObj=t.addClass("k_box_size k_menu_wrap").css({height:"100%",width:"100%",overflow:"visible"}),this.scrollWrap=$("<div style='height:100%;width:100%;padding-right:0px;padding-bottom:2px;' class='k_menu_wrap_scroll k_box_size'></div>").appendTo(this.jqObj),this.treeWrap=$("<div class='k_menu_wrap_ul clearfix' ></div>").appendTo(this.scrollWrap),this.opts=$.extend(!0,{},o,i);var e=this.opts.data,s=$("<div/>");this.firstItem=void 0,this._createList(s,e,1),this.maxWidth=0,s.children().detach().appendTo(this.treeWrap),this.firstItem&&this.firstItem.trigger("click"),this.scrollObj=this.scrollWrap.myscrollbar(this.opts.scrollStyle).getMyScrollIns(),this._setWrapWidth(),this._setMinWidth();var a=this,d=n.getUUID();$(r).on("resize."+d,function(){a._setMinWidth?a._setMinWidth():$(this).off("."+d)})}return l.prototype={_setMinWidth:function(){var t=this.treeWrap.parent().width();this.opts.ellipsis?this.rootUl.css("width",t):this.rootUl.css("min-width",t)},_itClickEnvent:function(t){var i=t.data._this,e=$(this),s=e.data("mdata"),a=void 0!==s.children;if(a)var d=e.next("ul").slideToggle("fast",function(){var t=e.children(".k_menu_parent_icon");"none"===d.css("display")?t.removeClass("fa-angle-down").addClass("fa-angle-left").css("padding-right","6px"):t.removeClass("fa-angle-left").addClass("fa-angle-down").css("padding-right","3px"),i._setWrapWidth(),i.scrollObj.resetSliderPosByTimer()});i.opts.onClick&&setTimeout(function(){i.opts.onClick.call(e,s.data?s.data:s,a)},10),a||(i.activedItem&&i.activedItem.removeClass("k_menu_actived"),i.activedItem=e.addClass("k_menu_actived"))},_setWrapWidth:function(){this.opts.ellipsis?this.treeWrap.width("100%"):this.treeWrap.width(this.rootUl.width())},_createList:function(t,i,e){var s=$("<ul class='k_menu_ul' style='width:auto;'/>").appendTo(t);this.opts.ellipsis||s.css({"min-width":"100%"}),1===e&&(this.rootUl=s.addClass("k_menu_ul_root"));for(var a=0,d=i.length;a<d;++a){var r=i[a],n=r[this.opts.textField],o=$("<li style='white-space: nowrap;width:auto;list-style:none;' ><a style='display:inline-block;white-space:nowrap;padding-right:10px;' class='k_box_size'>"+n+"</a></li>").appendTo(s),l=o.children("a");this.opts.ellipsis?l.css({width:"100%","max-width":"100%","text-overflow":" ellipsis",overflow:"hidden","padding-right":"0px"}):l.css("min-width","100%"),l.attr("title",n);var h=r.data.menuIconCss,p=15;1===e&&(p=12),l.css("padding-left",e*p),h&&""!==h&&l.prepend("<i style='padding-right:6px;' class='fa "+h+"'><i>"),r.children?(l.prepend("<i class='fa fa-angle-down k_menu_parent_icon'></i>"),this._createList(o,r.children,e+1)):this.firstItem||(this.firstItem=l),l.on("click",{_this:this},this._itClickEnvent).data("mdata",r),this.opts.onItemCreated&&this.opts.onItemCreated.call(o,r,void 0!==r.children)}},_loopParent:function(t,i){var e=i.parent().parent();if(!e.hasClass("k_menu_ul_root")){var s=e.prev("a"),a=s.data("mdata").text;t.push(a),this._loopParent(t,s)}},getTreeTxtPath:function(){var t=this.activedItem,i=[],e=t.data("mdata").text;return i.push(e),this._loopParent(i,t),i.reverse()},destroy:function(){this.scrollObj.destroy(),this.super.destroy.call(this)}},n.Menu=l});