/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(i,n){"function"==typeof define&&define.amd&&!window._all_in_?define(["jquery","config"],function(t,e){return n(i,t,e)}):n(i,$)}("undefined"!=typeof window?window:this,function(window,$,cfg){"use strict";var $B=window.$B?window.$B:{};window.$B=$B;var config=$B.config,document=window.document,charSpan="<span style='position:absolute;white-space:nowrap;top:-10000000px;left:-10000000px' id='{id}'></span>",$body,_char_id_="__getcharwidth__",_ellipsis_char_id="_ellipsis_char_",$spen_CharWidth,$ellipsisCharDiv=null,loadingHtml="<div style='padding-left:16px;' class='loading'>"+config.loading+"</div>",chars=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],srcollArray=[],SCROLLCHECKER,removeClearTimer;function BaseControl(){this._version="_ver_var_",this._release="_ver_date_",this._author="_ver_site_"}function _getBody(){return $body||($body=$(document.body).css("position","relative")),$body}BaseControl.prototype={constructor:BaseControl,version:function(){$B.debug(this._version),$B.debug(this._release),$B.debug(this._author)},ajax:function(t){$B.request(t)},debug:function(t){$B.debug(t)},error:function(t){$B.debug("error:"+t)},clear:function(){this.jqObj&&this.jqObj.children().remove(),this.delProps()},regiterFn:function(t,e){this[t]=e},destroy:function(t){this.jqObj&&this.jqObj.remove(),this.delProps(t),this.__proto__={}},delProps:function(t){for(var e in this)this.hasOwnProperty(e)&&(null!==this[e]&&void 0!==this[e]&&"super"!==e&&"function"==typeof this[e].destroy&&(t&&t===this[e]||this[e].destroy(this)),delete this[e])}};var TextEvents={input:function(){if(""!==$(this).val()){var t=_getBody().children("#k_text_clear_btn");0!==t.length&&t.data("target")===this||TextEvents.mouseover.call(this)}},mouseover:function(){clearTimeout(removeClearTimer);var t=$(this);if(!t.attr("readonly")){var e=_getBody(),i=e.children("#k_text_clear_btn").hide();if(""!==t.val()){var n=t.offset(),o=t.outerWidth()+n.left-15,r=t.outerHeight()/2+n.top-12;t.hasClass("k_combox_input")&&(o-=18),0===i.length?(i=$("<div id='k_text_clear_btn' style='cursor:pointer;position:absolute;top:"+r+"px;left:"+o+"px;width:14px;height:14px;z-index:2147483647;'><i style='color:#C1C1C1' class='fa fa-cancel-2'></i></div>")).appendTo(e).on({click:function(){var t=$(this).data("target");t.val(""),t.trigger("input.mvvm"),$(this).hide(),t.focus()}}).data("target",t):i.data("target",t).css({top:r,left:o}).show()}}},mouseout:function(t){var e=_getBody();removeClearTimer=setTimeout(function(){e.children("#k_text_clear_btn").hide()},800)}},ajaxOpts={timeout:12e4,type:"POST",dataType:"json",async:!0,error:function(xhr,status,errorThrown){var res={message:config.permission+xhr.status};try{res=eval("("+xhr.responseText+")")}catch(t){window.console&&console.log(xhr.responseText)}200===xhr.status?this.success(res):$B.error(config.requestError),this.recoverButton(),this.final(status)},success:function(res){if(this.recoverButton(),this.final(res),void 0!==res.code)if(0===res.code){var data=res.data;res.strConvert&&(data=eval("("+res.data+")")),this.ok(res.message,data,res)}else 99999===res.code?"notlogin"===res.data?($B.error(res.message),setTimeout(function(){window.ctxPath&&(window.top.location=$B.getHttpHost(window.ctxPath))},1600)):$B.error(config.permission):this.fail(res.message,res);else this.ok(res,res)},ok:function(t,e){},recoverButton:function(){this.target&&(this.target.removeAttr("disabled"),"INPUT"===this.target[0].tagName?this.target.val(this.recoverText):this.target.html(this.recoverText),this.target=void 0)},fail:function(t,e){0===_getBody().children("._request_fail_window").length&&$B.alert(t).jqObj.addClass("_request_fail_window")},final:function(t){}},reg=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;function HashTable(){var i,n,o;this.size=0,this.entry={},"function"!=typeof this.add&&(HashTable.prototype.add=function(t,e){this.containsKey(t)||this.size++,this.entry[t]=e}),"function"!=typeof this.getValue&&(HashTable.prototype.getValue=function(t){return this.containsKey(t)?this.entry[t]:null}),"function"!=typeof this.remove&&(HashTable.prototype.remove=function(t){this.containsKey(t)&&delete this.entry[t]&&this.size--}),"function"!=typeof this.containsKey&&(HashTable.prototype.containsKey=function(t){return t in this.entry}),"function"!=typeof this.containsValue&&(HashTable.prototype.containsValue=function(t){var e=Object.keys(this.entry);for(i=0,n=e.length;i<n;++i)if(o=e[i],this.entry[o]===t)return!0;return!1}),"function"!=typeof this.getValues&&(HashTable.prototype.getValues=function(){var t=[],e=Object.keys(this.entry);for(i=0,n=e.length;i<n;++i)o=e[i],t.push(this.entry[o]);return t}),"function"!=typeof this.getKeys&&(HashTable.prototype.getKeys=function(){return Object.keys(this.entry)}),"function"!=typeof this.getSize&&(HashTable.prototype.getSize=function(){return this.size}),"function"!=typeof this.clear&&(HashTable.prototype.clear=function(){this.size=0,this.entry={}}),"function"!=typeof this.joinValue&&(HashTable.prototype.joinValue=function(t,e,i){var n=void 0===i?",":i,o=this.getValue(t);if(null===o)this.add(t,e),this.size++;else{var r=o.split(n);r.push(e),this.add(t,r.join(n))}}),"function"!=typeof this.destroy&&(HashTable.prototype.destroy=function(){this.size=0,this.entry=null}),"function"!=typeof this.each&&(HashTable.prototype.each=function(t){var e=Object.keys(this.entry);for(i=0,n=e.length;i<n;++i)t(o=e[i],this.entry[o])}),"function"!=typeof this.getJson&&(HashTable.prototype.getJson=function(t){return $.extend(!0,{},this.entry)})}function NiceScroll(t,e){var i=$.extend({"border-radius":"6px",position:"absolute",width:"6px",background:"#BCDBF7",opacity:.8},e);this.jqObj=t,this.vbar=$("<div style='top:0;right:1px;display:none;cursor:pointer;'></div>").css(i).insertAfter(t);var n=this;this.fireOnScroll=!0,this.jqObj.on("scroll",function(){n.fireOnScroll&&n.onScroll()}),this.hideTimer=void 0,this.isDraging=!1,this.jqObj.on({mouseenter:function(){n.isDraging||(n.vbar.css("opacity","0.8"),n.lastScrollHeight=0,n.updateUi().show())},mouseleave:function(){n.hide()}}),this.vbar.on("mouseenter",function(){n.show(),n.vbar.css("opacity","1")}).on("mouseleave",function(){n.hide()}).draggable({cursor:"pointer",axis:"v",onStartDrag:function(t){n.maxTop=parseInt(n.clientHeight-n.vbar.height())+1,n.fireOnScroll=!1,n.isDraging=!0},onDrag:function(t){var e=t.state;e._data.top<0?e._data.top=0:e._data.top>n.maxTop&&(e._data.top=n.maxTop);var i=e._data.top/n.scrollRate;n.jqObj.scrollTop(i)},onStopDrag:function(){return n.fireOnScroll=!0,n.isDraging=!1}}),this.vbar.click(function(){return!1})}return String.prototype.toHexColor=function(){var t=this;if(/^(rgb|RGB)/.test(t)){for(var e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(","),i="#",n=0;n<e.length;n++){var o=Number(e[n]).toString(16);1===o.length&&(o="0"+o),"0"===o&&(o+=o),i+=o}return 7!==i.length&&(i=t),i.toUpperCase()}if(!reg.test(t))return t.toUpperCase();var r=t.replace(/#/,"").split("");if(6===r.length)return t.toUpperCase();if(3===r.length){for(var a="#",s=0;s<r.length;s+=1)a+=r[s]+r[s];return a.toUpperCase()}},String.prototype.toRgbColor=function(){var t=this.toLowerCase();if(t&&reg.test(t)){if(4===t.length){for(var e="#",i=1;i<4;i+=1)e+=t.slice(i,i+1).concat(t.slice(i,i+1));t=e}for(var n=[],o=1;o<7;o+=2)n.push(parseInt("0x"+t.slice(o,o+2)));return"RGB("+n.join(",")+")"}return t.toUpperCase()},Array.prototype.unique=function(){this.sort();for(var t=[this[0]],e=1;e<this.length;e++)this[e]!==t[t.length-1]&&t.push(this[e]);return t},Date.prototype.format=function(t){var e={"M+":this.getMonth()+1,"d+":this.getDate(),"h+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),"S+":this.getMilliseconds()};for(var i in/(y+)/i.test(t)&&(t=t.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length))),e)new RegExp("("+i+")").test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?e[i]:("00"+e[i]).substr((""+e[i]).length)));return t},String.prototype.trim=function(){return this.replace(/(^\s*)|(\s*$)/g,"")},String.prototype.leftTrim=function(){return this.replace(/(^\s*)/g,"")},String.prototype.rightTrim=function(){return this.replace(/(\s*$)/g,"")},$B.HashTable=HashTable,$.extend($B,{request:function(){var t,e=arguments[0];for(var i in"function"==typeof(t=void 0!==e?$.extend({},ajaxOpts,e):ajaxOpts).preRequest&&t.preRequest(),t.data)null===t.data[i]?delete t.data[i]:t.data[i]=this.htmlEncode(t.data[i]);var n,o=window._submit_queues;if(o&&0<o.length){var r=o.length-1;if(o[r].date-new Date<=500)n=o[r].btn,o.shift().btn=void 0}if(1<arguments.length||n){var a=1<arguments.length?arguments[1]:n;if((t.target=a).attr("disabled","disabled"),"INPUT"===a[0].tagName)t.recoverText=a.val(),a.val(config.busy);else{t.recoverText=a.html(),a.html(config.busy);var s=a.css("color");a.prepend("<i style='padding:0;margin-right:3px;color:"+s+";' class='fa fa-spin6 animate-spin'></i>")}}$.ajax(t)},lightenDarkenColor:function(t,e){var i=!1;"#"===t[0]&&(t=t.slice(1),i=!0);var n=parseInt(t,16),o=(n>>16)+e;255<o?o=255:o<0&&(o=0);var r=(n>>8&255)+e;255<r?r=255:r<0&&(r=0);var a=(255&n)+e;return 255<a?a=255:a<0&&(a=0),(i?"#":"")+String("000000"+(a|r<<8|o<<16).toString(16)).slice(-6)},isContrastYIQ:function(t){var e=t;-1<t.indexOf("#")&&(e=t.toRgbColor());var i,n=e.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);return i=299*n[1]+587*n[2]+114*n[3],.5<=(i/=255e3)?"light":"dark"},changeButtonStatus:function(t){var e,i=t.children("i");t.attr("disabled")?(t.removeAttr("disabled"),e=t.data("clazz"),t.removeData("clazz").removeClass("k_toolbar_button_disabled"),i.attr("class",e)):(t.prop("disabled",!0),e=i.attr("class"),t.data("clazz",e).addClass("k_toolbar_button_disabled"),i.attr("class","fa fa-spin6 fa-spin"))},getHttpHost:function(t){var e,i=window.location.protocol+"//"+window.location.host;return!t&&window.ctxPath?e=window.ctxPath:t&&(e=t),e&&(i+=e),i},htmlEncode:function(t){if(!t||void 0===t.replace)return t;return 0===t.length?"":t.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/eval\((.*)\)/g,"").replace(/<.*script.*>/,"")},htmlDecode:function(t){if(void 0===t.replace)return t;return 0===t.length?"":t.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#39;/g,"'").replace(/&quot;/g,'"')},getAnglePositionOffset:function(t){var e=t.css("transform"),i={fixTop:0,fixLeft:0};if(e&&"none"!==e){var n=t.position();if(0!==$B.getMatrixAngle(e)){var o=$("<div />"),r=t.attr("style");o.attr("style",r).css({filter:"alpha(opacity=0)","-moz-opacity":"0",opacity:"0",position:"absolute","z-index":-111}),o.css("transform","rotate(0deg)"),o.appendTo(t.parent());var a=o.position();i.fixTop=a.top-n.top,i.fixLeft=-(n.left-a.left),o.remove()}}return i},getMatrixAngle:function(t){if("none"===t)return 0;var e=t.split("(")[1].split(")")[0].split(","),i=e[0],n=e[1];return Math.round(Math.atan2(n,i)*(180/Math.PI))},debug:function(t){console.log("debug:"+t)},extend:function(t,e,i,n){i||(i=BaseControl),i.call(t,n),Object.keys(i.prototype).forEach(function(t){e.prototype[t]||(e.prototype[t]=i.prototype[t])}),e.prototype.constructor=e,t.super=new i},scrollbar:function(t){t.mCustomScrollbar||t.css("overflow","auto")},getHashTable:function(){return new HashTable},isUrl:function(t){return/^((http(s)?|ftp):\/\/)?([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?(:\d+)?/.test(t)},writeCookie:function(t,e,i){this.isNotEmpty(i)||(i=1);try{var n=new Date;n.setDate(n.getDate()+i),document.cookie=t+"="+window.escape(e)+";expires="+n.toGMTString()}catch(t){}},getCookie:function(t){if(0<document.cookie.length){var e=document.cookie.indexOf(t+"=");if(-1!==e){e=e+t.length+1;var i=document.cookie.indexOf(";",e);return-1===i&&(i=document.cookie.length),window.unescape(document.cookie.substring(e,i))}}return""},isIE:function(){var t=navigator.userAgent.toLowerCase();return 0<t.indexOf("rv:11.0")||/msie/.test(t)},isNotEmpty:function(t){return null!==t&&"undefiend"!=typeof t&&""!==t},getScrollWidth:function(){var t,e,i=document.createElement("DIV");return i.style.cssText="position:absolute; top:-1000px; width:100px; height:100px; overflow:hidden;",t=document.body.appendChild(i).clientWidth,i.style.overflowY="scroll",e=i.clientWidth,document.body.removeChild(i),t-e},delayFun:function(n,o,r){var a;return function(){var t=this,e=arguments,i=r&&!a;clearTimeout(a),a=setTimeout(function(){a=null,r||n.apply(t,e)},o),i&&n.apply(t,e)}},tagMessage:function(t,e,i){},getUrlParams:function(t){var e,i={},n=(e=void 0!==t?t:location.search).indexOf("?");if(-1!==n)for(var o=e.substr(n+1).split("&"),r=0;r<o.length;r++)i[o[r].split("=")[0]]=unescape(o[r].split("=")[1]);return i},mouseCoords:function(t){return t.pageX||t.pageY?{x:t.pageX,y:t.pageY}:{x:t.clientX+document.body.scrollLeft-document.body.clientLeft,y:t.clientY+document.body.scrollTop-document.body.clientTop}},getEllipsisChar:function(t,e,i,n){null===$ellipsisCharDiv&&0===($ellipsisCharDiv=_getBody().children("#"+_ellipsis_char_id)).length&&($ellipsisCharDiv=$("<div style='height:"+n+"px;width:"+i+"px;position:absolute;top:0;z-index:0;overflow:auto;top:-1000px;' id='"+_ellipsis_char_id+"'></div>").appendTo(_getBody())),$ellipsisCharDiv.css("font-size",e).html("");for(var o=t.length,r=0,a=$ellipsisCharDiv[0];r<o;r++)if(a.innerHTML=t.substr(0,r),a.scrollHeight>n){a.innerHTML=t.substr(0,r-2);break}return a.innerHTML+"..."},getCharWidth:function(t,e){void 0===e&&(e=$B.config.fontSize),$spen_CharWidth||($spen_CharWidth=$(charSpan.replace(/{id}/,_char_id_)).appendTo(_getBody())),$spen_CharWidth.css({"font-size":e});var i=20;try{$spen_CharWidth.html(this.htmlEncode(t)),i=$spen_CharWidth.outerWidth(),setTimeout(function(){$spen_CharWidth.html("")},1)}catch(t){this.error(t)}return i},getIfrId:function(){return _getBody().children("#_window_ifr_id_").text()},getUUID:function(){return this.generateDateUUID()},generateDateUUID:function(t,e){var i=e||12,n=t||"yyyyMMddhhmmss";return(new Date).format(n)+this.generateMixed(i)},generateMixed:function(t){for(var e=t||6,i=[],n=0;n<e;n++){var o=Math.ceil(35*Math.random());i.push(chars[o])}return i.join("")},htmlLoad:function(){var o=arguments[0];o.target.children().remove(),o.target.html(loadingHtml),"function"==typeof o.preload&&o.preload.call(o.target);var t=o.url;t=0<t.indexOf("?")?t+"&_c_1="+this.generateMixed(5):t+"?_c_1="+this.generateMixed(5),o.target.load(t,o.params,function(t,e,i){if("error"===e)if(t){var n=new RegExp("<body>(.+)</body>","gi").exec(t);2<=n.length?o.target.html(n[1]):o.target.html(t)}else{0<=$B.getHttpHost().indexOf("file:")?o.target.html($B.config.crossError):o.target.html($B.config.htmlLoadError)}else"function"==typeof o.onLoaded&&o.onLoaded.call(o.target)})},getJqObject:function(t){return"string"==typeof t?$(t):t},parseForm:function(t,e){var i=this.getJqObject(t);function r(t,e){var i=t.attr("id");if(i||(i=t.attr("name")),void 0!==i){var n=t.val();return e.add(i,n),i+"="+n}return null}var n=i.find("input[type=text]"),o=i.find("input[type=password]"),a=i.find("textarea"),s=i.find("input[type=radio]"),l=i.find("input[type=checkbox]"),c=i.find("select"),d=i.find("input[type=hidden]"),h=i.find("input[type=file]"),p=this.getHashTable();if($.each(d,function(t,e){r($(e),p)}),$.each(o,function(t,e){r($(e),p)}),$.each(n,function(t,e){r($(e),p)}),$.each(h,function(t,e){r($(e),p)}),$.each(a,function(t,e){r($(e),p)}),$.each(s,function(t,e){p.add($(e).attr("name"),null)}),$.each(s,function(t,e){var i=$(e);i.is(":checked")&&p.joinValue(i.attr("name"),i.val())}),0<l.length){var u={};for(var f in $.each(l,function(t,e){var i=$(e),n=i.attr("name");u[n]||(u[n]=[]),i.is(":checked")&&u[n].push(i.val())}),u)if(this.hasOwnProperty(f)){var g=u[f].join(",");""!==g&&p.add(f,g)}}$.each(c,function(t,e){var i=$(e);if(!0===i.attr("multiple")){var n=[];p.add(i.attr("name"),"");var o=i.children("option[selected]");$.each(o,function(t,e){n.push($(e).value)}),p.joinValue(i.attr("name"),n.join(","))}else r(i,p)});var v=p.getJson();return p.destroy(),e?$.extend(!0,{},e,v):v},bindForm:function(t,a,e){var i=a,r={};if($B.Mvvm){var n=t.find("input[type=text]"),o=function(t,e){var i=t.attr("id");if(i||(i=t.attr("name")),void 0!==a[i]&&e(t,i),0===i.indexOf("old_")){var n=i.replace("old_",""),o=a[n];t.val(o),r[i]=o}};n.each(function(){var t=$(this);o(t,function(t,e){t.hasClass("k_combox_input")?t.attr("watcher","kcomboxWatcher").attr("express","{{this.kcomboxExpress(this.data."+e+",el)}}"):t.hasClass("k_calendar_input")?t.attr("watcher","kcalendarWatcher").attr("express","{{this.kcalendarExpress(this.data."+e+",el)}}"):t.hasClass("k_window_input")?t.attr("watcher","kwindowInputWatcher").attr("express","{{this.kwindowInputExpress(this.data."+e+",el)}}"):t.attr("value","{{this.data."+e+"}}")})}),t.find("input[type=password]").each(function(){var t=$(this);o(t,function(t,e){t.attr("value","{{this.data."+e+"}}")})}),t.find("input[type=hidden]").each(function(){var t=$(this);o(t,function(t,e){t.attr("value","{{this.data."+e+"}}")})}),t.find("textarea").each(function(){var t=$(this);o(t,function(t,e){t.text("{{this.data."+e+"}}")})}),t.find("input[type=radio]").each(function(){var t=$(this),e=t.attr("name"),i=t.parent();i.hasClass("k_radio_label")&&void 0!==a[e]&&i.attr("watcher","kRadioWatcher").attr("express","{{this.kRadioExpress(this.data."+e+",el)}}")}),t.find("input[type=checkbox]").each(function(){var t=$(this),e=t.attr("name"),i=t.parent();i.hasClass("k_checkbox_label")&&void 0!==a[e]&&i.attr("watcher","kcheckBoxWatcher").attr("express","{{this.kcheckBoxExpress(this.data."+e+",el)}}")}),t.find("select").each(function(){var t=$(this),o=t.attr("id");if(a[o]){var r=[];t.children().each(function(){var t=$(this),e=t.val(),i=t.text(),n='<option value="'+e+'" selected="'+("{{this.data."+o+" === '"+e+"' ? true:false}}")+'">'+i+"</option>";r.push(n)}),t.html(r.join(""))}}),t.find("ul.k_tree_root").each(function(){o($(this),function(t,e){t.attr("watcher","ktreeWatcher").attr("express","{{this.ktreeExpress(this.data."+e+",el)}}")})});var s=new $B.Mvvm({el:t[0],data:i,onChanged:e,onGetJson:function(t){$.extend(t,r)}});return window.curMvvm=s}console.log("没有加载mvvm组件")},fillView:function(t,e){for(var i in e)if(this.hasOwnProperty(i)){var n=e[i];null==n&&(n="");var o=t.find("#"+i);0<o.length&&("INPUT"===o[0].tagName?o.value(n):o.text(n))}},resetForm:function(t,e){},simpalSelect:function(r){var a;(a="string"==typeof r.target?$(r.target):r.target).children().remove();var s=null;s=void 0!==r.promptMsg?void 0!==r.promptValue?$("<option  value='"+r.promptValue+"'>"+r.promptMsg+"</option>").appendTo(a):$("<option  value=''>"+r.promptMsg+"</option>").appendTo(a):a,$.isArray(r.data)&&$.each(r.data,function(t,e){var i=e[r.idField],n=!1;$.isArray(r.selected)&&$.each(r.selected,function(t,e){i===e&&(n=!0)});var o=null;(o=n?$("<option value='"+i+"' selected='selected'>"+e[r.textField]+"</option>").appendTo(a):$("<option value='"+i+"' >"+e[r.textField]+"</option>").appendTo(a)).data("data",e),"string"!=typeof r.defaultVal&&"number"!=typeof r.defaultVal||r.defaultVal===i&&(s=o)}),s.attr("selected","selected"),"function"==typeof r.onchange&&a.on("change",function(){var t=$(this).children("option:selected");r.onchange($(t))})},encryptData:function(t,e){if(void 0===window.CryptoJS)return console.log("没有加载 》》 CryptoJS"),t;e||(e=window.SRCUUID);var i=window.CryptoJS.enc.Utf8.parse(e);return window.CryptoJS.DES.encrypt(t,i,{mode:window.CryptoJS.mode.ECB,padding:window.CryptoJS.pad.Pkcs7}).toString()},_toTreeJson:function(h,p,u){var f=this;setTimeout(function(){var t,e,i,n,o,r,a,s,l,c,d=Object.keys(h);for(o=0,r=d.length;o<r;++o){if(t=d[o],e=h[t],i={id:c="_j"+u+o,text:t,data:{}},$.isArray(e))for(i.children=[],0<u&&(i.closed=!0),a=0,s=e.length;a<s;++a)l={id:c+a,text:t+"["+a+"]",data:{}},i.children.push(l),n=e[a],$.isPlainObject(n)?(l.children=[],1<u&&(l.closed=!0),f._toTreeJson(n,l.children,u++)):l.text=l.text+" : "+n;else $.isPlainObject(e)?(i.children=[],1<u&&(i.closed=!0),f._toTreeJson(e,i.children,u++)):i.text=t+" : "+e;p.push(i)}f._createJsonTree()},0)},jsonViewer:function(t,e,i){var n=$("<ul class='k_json_view_root'/>"),o=[];this._toTreeJson(t,o,1),this.$jsonTreeUl=n,this.treeJson=o;var r=this,a=$("<div style='padding:6px 12px;'></div>");a.append("<div><button class='k_icon_fff'><i class='fa fa-docs'></i>"+$B.config.copy+"</button></div>"),this.copyContent=JSON.stringify(t),a.find("button").click(function(t){var e=r.$jsonTreeUl.next("textarea");0<e.length?(e.remove(),r.$jsonTreeUl.show(),$(this).html("<i class='fa fa-docs'></i>"+$B.config.copy)):($(this).html("<i class='fa fa-docs'></i>"+$B.config.recoverCopy),r.$jsonTreeUl.hide(),$("<textarea style='width:100%;height:100%'>"+r.copyContent+"</textarea>").insertAfter(r.$jsonTreeUl))}),a.append(n);var s=$.extend({width:"70%",height:"80%",content:a,onClose:function(){r.$jsonTreeUl.data("treeIns").destroy(),r.$jsonTreeUl=void 0,r.treeJson=void 0,r.copyContent=void 0}},e);this.window(s),this.$jsonTreeUl.html("<li>please waiting.....</li>")},_createJsonTree:function(){clearTimeout(this.jsonViewTimer);var t=this;this.jsonViewTimer=setTimeout(function(){new $B.Tree(t.$jsonTreeUl,{plainStyle:!0,checkbox:!1,data:t.treeJson}),t.treeJson=void 0},300)},decryptData:function(t,e){if(void 0===window.CryptoJS)return t;e||(e=window.SRCUUID);var i=window.CryptoJS.enc.Utf8.parse(e);return window.CryptoJS.DES.decrypt({ciphertext:window.CryptoJS.enc.Base64.parse(t)},i,{mode:window.CryptoJS.mode.ECB,padding:window.CryptoJS.pad.Pkcs7}).toString(window.CryptoJS.enc.Utf8)},getLoadingMask:function(){return $("<div  style='position:absolute;z-index: 2147483645;top:0;left:0;width:100%;height:100%;display:block;'><div id='k_window_mask_bg' style='width:100%;height:100%;position:absolute;top:0;left:0;z-index: 2147483646;display:block'></div><div style='text-align:center;width:100%;height:20px;line-height:20px;position:absolute;top:0;left:0;z-index: 2147483647;background: #EEEEEE;'><i class='fa fa-spin5 animate-spin'></i><span style='padding-left:8px;'>正在处理...</span></div></div>")},_dyFormHclick:function(){var t=$(this),e=t.children("i");e.hasClass("fa-down-open-big")?(e.removeClass("fa-down-open-big").addClass("fa-right-open-big").css("padding-left","5px"),t.next().slideUp(200,function(){$(this).hide()})):(e.removeClass("fa-right-open-big").addClass("fa-down-open-big").css("padding-left","0"),t.next().show().slideDown(200))},_dyFormLoad:function(t,e,i,n,o,s){var r,l,c,d,h,p;r=t,l=e,c=i,d=n,h=o,p=this,$B.request({url:r,ok:function(t,e){delete p.reqQ[d];for(var i=l,n=l.parent();"TABLE"!==n[0].nodeName;)n=n.parent();if(0!==n.parent().length){for(var o=[],r=0,a=e.length;r<a;++r)switch(e[r].checked&&o.push(e[r].id),c){case"select":e[r].selected&&o.push(e[r].id),i.append("<option value='"+e[r].id+"'>"+e[r].text+"</option>");break;case"radio":e[r].checked&&o.push(e[r].id),i.append('<label class="k_radio_label k_radio_anim"><input type="radio" name="'+h+'" value="'+e[r].id+'" /><i class="k_radio_i"></i>'+e[r].text+"</label>");break;case"checkbox":e[r].checked&&o.push(e[r].id),i.append('<label class="k_checkbox_label k_checkbox_anim"><input type="checkbox" name="'+h+'" value="'+e[r].id+'" /><i class="k_checkbox_i"></i>'+e[r].text+"</label>")}0<o.length&&(s.defaultVal=o.join(","))}},final:function(){delete p.reqQ[d]}})},_dyInputWindowClick:function(){var t=$(this).data("params"),e=t.filedName,i=t.url;i=0<i.indexOf("?")?i+"&filed="+e:i+"?filed="+e,window._curDyFiled=e,window._curDyWindow=$B.window({width:"60%",height:"70%",dataType:"html",title:t.title,url:i})},clone:function(t){if($.isArray(t)){for(var e=[],i=0,n=t.length;i<n;++i)e.push($.extend(!0,{},t[i]));return e}return $.extend(!0,{},t)},dyForm:function(t,e,i,n){var o,r,a,s,l,c,d,h,p,u,f,g,v,y,m,b,x,w;clearInterval(this._ivt),t.children().remove(),t.width()<=500&&(w="99%");var _={},k=!1;$.isEmptyObject(i)&&(k=!0);var T=this;window._curDyDataObj=i,this.reqQ={};for(var C=function(t,e,i,n,o){var r={checkbox:t,requestfinal:function(){delete T.reqQ[i]}};"string"==typeof e?(r.url=e,T.reqQ[i]=!0):(delete e.unique,r.data=e),new $B.Tree(n,r)},B=function(t,e,i,n,o,r,a){var s={placeholder:t,mutilchecked:e,checkfather:i,readonly:n,onReqloaded:function(){delete T.reqQ[r]}};"string"==typeof o?(s.url=o,T.reqQ[r]=!0):delete(s.data=o).unique,new $B.Combox(a,s)},j=0,S=e.length;j<S;++j){(o=e[j]).gName&&""!==o.gName&&((s=$(' <h6 style="font-size:14px;padding:4px;font-weight: normal;cursor: pointer;"><i style="padding-right:4px;" class="fa  fa-down-open-big"></i>'+o.gName+"</h6>")).appendTo(t).click(this._dyFormHclick),0<j&&s.css("border-top","1px solid #CCCCCC")),r=o.forms,l=$('<table style="width:100%;" class="form_table k_dy_form_table"></table>');for(var O=0,E=r.length;O<E;++O)switch(u=(a=r[O]).attrs,x=a.ftype,d=a.tip?a.tip:"",h=a.data?a.data:"",b=a.defaultValue,p=a.filedName,a.valid&&""!==a.valid&&(_[p]=a.valid),k&&("label"===x||"hidden"===x||"window"===x||"input"===x||"textarea"===x||"date"===x?i[p]=h:"select"!==x&&"radio"!==x&&"checkbox"!==x&&"combox"!==x&&"tree"!==x||(i[p]=b||"")),(c=$("<tr/>").appendTo(l)).append('<td style="width:60px;text-align: right;padding:2px 2px;">'+a.title+"</td>").width(n),a.ftype){case"label":c.append("<td>"+h+"</td>");break;case"input":c.append('<td><input type="text" id="'+p+'" placeholder="'+d+'" value="'+h+'"/></td>'),g=c.find("input"),u&&g.attr(u),w&&g.width(w);break;case"window":(g=c.append('<td><input class="k_window_input" readonly="readonly" type="text" id="'+p+'"  placeholder="'+d+'" value="'+h+'"/></td>').find("input")).click(this._dyInputWindowClick).data("params",{filedName:p,url:a.url,title:a.title}),w&&g.width(w);break;case"select":if(g=$('<td><select id="'+p+'"  placeholder="'+d+'"/></td>').appendTo(c).children("select"),$.isArray(h))for(v=0,y=h.length;v<y;++v)g.append("<option value='"+h[v].id+"'>"+h[v].text+"</option>");else f=$B.getUUID(),this.reqQ[f]=!0,this._dyFormLoad(h,g,"select",f,p,a);u&&g.attr(u),w&&g.width(w);break;case"textarea":c.append('<td><textarea  id="'+p+'"  placeholder="'+d+'"/>'+h+"</td>"),g=c.find("textarea"),u&&g.attr(u),w&&g.width(w);break;case"radio":if(g=$("<td></td>").appendTo(c),"string"==typeof h)f=$B.getUUID(),this.reqQ[f]=!0,this._dyFormLoad(h,g,"radio",f,p,a);else for(delete h.unique,v=0,y=h.length;v<y;++v)g.append('<label class="k_radio_label k_radio_anim"><input type="radio" name="'+p+'" value="'+h[v].id+'" /><i class="k_radio_i"></i>'+h[v].text+"</label>");break;case"checkbox":if(g=$("<td></td>").appendTo(c),"string"==typeof h)f=$B.getUUID(),this.reqQ[f]=!0,this._dyFormLoad(h,g,"checkbox",f,p,a);else for(delete h.unique,v=0,y=h.length;v<y;++v)g.append('<label class="k_checkbox_label k_checkbox_anim"><input type="checkbox" name="'+p+'" value="'+h[v].id+'" /><i class="k_checkbox_i"></i>'+h[v].text+"</label>");break;case"tree":c.children("td").css("vertical-align","baseline"),g=c.append('<td><ul id="'+p+'"/></td>').find("ul"),f=$B.getUUID(),C(a.mutilChecked,h,f,g);break;case"combox":g=c.append('<td><input type="text" id="'+p+'"  placeholder="'+d+'"/></td>').find("input"),f=$B.getUUID(),B(d,a.mutilchecked,a.checkfather,a.readonly,h,f,g),w&&g.width(w);break;case"date":m={fmt:a.fmt,readonly:!0},""===h&&(m.initValue=new Date),g=c.append('<td><input type="text" id="'+p+'"  placeholder="'+d+'" value="'+h+'"/></td>').find("input"),new $B.Calendar(g,m);break;case"file":console.log("附件上传控件");break;case"time":console.log("待完成....")}l.appendTo(t)}var D,H=1;$.isEmptyObject(T.reqQ)||(D=this.getLoadingMask()).appendTo(t),t.data("min")&&t.children().hide(),T._ivt=setInterval(function(){2e3<H&&(clearInterval(T._ivt),$B.error($B.config.requestError),D&&D.remove()),console.log("waiting for load the datas ....."+JSON.stringify(T.reqQ)),$.isEmptyObject(T.reqQ)&&(clearInterval(T._ivt),D&&D.remove(),$.isEmptyObject(_)||new $B.Validate(_,t),i&&$B.bindForm(t,i,function(t,e,i,n){})),H++},150)},makeNiceScroll:function(t,e){t.css({overflow:"hidden",position:"relative"});var i=$("<div class='k_box_size' style='margin-bottom: -17px; margin-right: -17px;overflow: scroll;height:100%;overflow-x:auto;'></div>").appendTo(t),n=new NiceScroll(i,e);return srcollArray.push(n),setTimeout(function(){n.updateUi()},500),SCROLLCHECKER||(SCROLLCHECKER=setInterval(function(){for(var t=[],e=0,i=srcollArray.length;e<i;++e)srcollArray[e].isLive()&&(srcollArray[e].updateUi(),t.push(srcollArray[e]));srcollArray=t},700)),i}}),$B.bindTextClear=function(t){var e=function(){var t=$(this);t.hasClass("k_combox_input")||(t.on("mouseenter.textbox",TextEvents.mouseover),t.on("input.textbox",TextEvents.input),t.on("mouseleave.textbox",TextEvents.mouseout))};t.find("input[type=text]").each(e),t.find("input[type=password]").each(e)},$(function(){setTimeout(function(){$B.bindTextClear($("body"))},500)}),window.$B=$B,NiceScroll.prototype={onScroll:function(){if(!(this.vscrollSize<=0)){"none"===this.vbar.css("display")&&this.vbar.show();var t=this.jqObj[0].scrollTop;this.posY=t*this.scrollRate,this.vbar.css("top",this.posY)}},show:function(){return clearTimeout(this.hideTimer),this.vbar.show(),this},hide:function(){var t=this;return this.isDraging||(t.hideTimer=setTimeout(function(){t.vbar.hide(50)},800)),this},isLive:function(){for(var t=this.jqObj.parent(),e=!1;0<t.length;){if("BODY"===t[0].tagName){e=!0;break}t=t.parent()}return e},updateUi:function(){var t,e=this.jqObj[0],i=e.scrollHeight,n=e.clientHeight;if(void 0!==this.lastScrollHeight&&this.lastScrollHeight===i&&(t=100*n/i,this.heightPercentage&&t===this.heightPercentage))return this;if(this.lastScrollHeight=i,this.clientHeight=n,0<i){this.heightPercentage=t;var o=n*(this.heightPercentage/100);this.vscrollSize=Math.max(n,i)-n,0<this.vscrollSize&&(this.scrollRate=(n-o)/this.vscrollSize,this.posY=e.scrollTop*this.scrollRate,this.vbar.css({height:this.heightPercentage+"%",top:this.posY}))}else this.vbar.hide();return this}},$B});