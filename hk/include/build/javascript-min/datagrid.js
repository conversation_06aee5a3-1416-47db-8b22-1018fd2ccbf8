/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(e,i){"function"==typeof define&&define.amd?define(["$B","tree","toolbar","pagination"],function(t){return i(e,t)}):(e.$B||(e.$B={}),i(e,e.$B))}("undefined"!=typeof window?window:this,function(A,rt){var t,l;$(document);function h(){return t||(t=$(A.document.body).css("position","relative")),t}var dt={title:"",field:"",width:"auto",rowspan:0,colspan:0,align:"left",sortable:!0,resizable:!1,formatter:null},p={title:"",data:null,url:"",fillParent:!1,loadImd:!0,toolbar:null,oprCol:!0,treeIconColor:void 0,oprColWidth:"auto",cols:[],idField:"id",checkBox:!0,isTree:!1,pgposition:"bottom",pageSize:30,page:1,startPage:1,btnStyle:"plain",showBtnText:!0,pgBtnNum:10,iconCls:void 0,setParams:void 0,splitColLine:"k_datagrid_td_all_line",pageList:[15,20,25,30,35,40,55,70,80,90,100],sortField:void 0,onDbClickRow:null,onClickCell:null,onCheck:null,onRowRender:null,onLoaded:null},nt=25,O={dblclick:function(t){var e=$(this),i=e.data("_this");clearTimeout(i.clickTimer),"function"==typeof i.opts.onDbClickRow&&setTimeout(function(){i.opts.onDbClickRow.call(e)},1)}},lt={mousemove:function(t){var e=$(this);if(e.data("_this").opts.checkBox&&e.position().left<32)return!0;var i=e.offset().left,a=e.width()+i,s=t.pageX;e.css("cursor","default").removeData("splitX"),i-3<=s&&s<=i+3?e.css("cursor","w-resize").data("splitX",s):a-3<=s&&s<=a+3&&e.css("cursor","w-resize").data("splitX",s)},mouseover:function(t){var e=$(this);if(60<e.height()){var i=e.children(".k_datagrid_cell_text");if(!i.data("tipopts")){var a=i.text(),s=rt.getCharWidth(a,i.css("font-size")),o=300;s<o&&(o=s),s>i.width()&&(i.mousetip({maxWidth:h().width()/3,minWidth:o,activedColor:"#A2D8FF"}),i.trigger("mouseenter",{left:t.pageX,top:t.pageY}))}}},mouseout:function(t){},dblclick:function(t){},mousedown:function(t){var e=$(this),n=e.data("_this"),i=e.data("splitX");if(i){var a,s=Object.keys(n.moveXofs),o=n.scrollLeft;i+=o;for(var r=0,d=s.length;r<d;++r){var l=parseInt(s[r]);if(i-6<=l&&l<=i+6){a=n.moveXofs[l];break}}if(a){var h=n.hideTrTdArray.eq(a),p=h.prev(),c=h.data("minWidth"),f=p.data("minWidth"),g={minLeft:p.position().left+f,preTdMinWidth:f,targetTdMinWidth:c,targetTd:h,preTd:p},u=h.position().left;n.splitMovingLine||(n.splitMovingLine=$("<div style='position:absolute;top:0;'  class='k_datagrid_moving_line'></div>").appendTo(n.$scrollWrap),n.splitMovingLine.draggable({axis:"h",cursor:"w-resize",onStartDrag:function(t){var e=t.state,i=e._data,a=e.movingTarget.data("dragParams");i.dragParams=a,n.isDragResize=!0},onDrag:function(t){var e=t.state._data;e.leftOffset<0&&e.left<e.dragParams.minLeft&&(e.left=e.dragParams.minLeft)},onStopDrag:function(t){var e=t.state._data,i=e.leftOffset,a=e.dragParams,s=a.preTd,o=a.targetTd,r=s.outerWidth(),d=o.outerWidth();i<0?(d-=i,(r+=i)<a.preTdMinWidth&&(d=d+(r=a.preTdMinWidth)-f),s.outerWidth(r),o.outerWidth(d)):(r+=i,(d-=i)>=a.targetTdMinWidth&&(s.outerWidth(r),o.outerWidth(d))),e.dragParams=void 0,n._onResize(),n.splitMovingLine.hide(),n.isDragResize=!1},onMouseUp:function(t){n.splitMovingLine.hide()}})),n.splitMovingLine.css("left",u-o).show().trigger("mousedown.draggable",{pageX:t.pageX,pageY:t.pageY,which:t.which}).data("dragParams",g)}}},click:function(t){var e=$(this),i=e.data("_this");i.isDragResize||(clearTimeout(i.clickTimer),i.clickTimer=setTimeout(function(){"function"==typeof i.opts.onClickCell&&i.opts.onClickCell.call(e,e.attr("filed"),e.children("div").text())},200))}};function ht(t){var e,i=$(this),a=i.children(),s=i.parent().data("opt");a.hasClass("fa-down")?(a.removeClass("fa-down").addClass("fa-up"),e="desc"):(a.removeClass("fa-up").addClass("fa-down"),e="asc");var o=t.data.ins;if(""!==o.opts.url){var r="_col_sort_"+s.field,d=$.extend({pageSize:o.pageSize},o.lastParams);d[r]=e,d.page=1,c.call(t.data.ins,function(){_.call(t.data.ins)},d)}return!1}function pt(){$(this).children(".k_datagrid_cell_sort").width(12).slideDown(100)}function ct(t){$(this).children(".k_datagrid_cell_sort").slideUp(100)}function ft(t){var e=$(this),i=e.parent().parent(),a=e.children("i");if(e.hasClass("k_datagrid_chkbox_disabled"))return!1;var s,o=!1;a.hasClass("fa-check-empty")?(s="fa-check",a.removeClass("fa-check-empty fa-ok-squared").addClass(s),o=!0):(s="fa-check-empty",a.removeClass("fa-check fa-ok-squared").addClass(s));var r=e.data("flag"),d=e.data("_this"),n=d.$table.children("tbody").children(),l=[];if(r)n.each(function(){var t=$(this),e=t.children().first().children("div");if(!e.hasClass("k_datagrid_chkbox_disabled")){t.data("isCheck",o);var i=e.children("i");o?i.removeClass("fa-check-empty fa-ok-squared").addClass(s):i.removeClass("fa-check fa-ok-squared").addClass(s),l.push($.extend(!0,{},t.data("data")))}});else{if(i.data("isCheck",o),l.push($.extend(!0,{},i.data("data"))),d.opts.isTree){var h=i.data("treeDeep");if(i.data("isparent"))for(var p=i.next();0<p.length&&p.data("treeDeep")>h;)l.push($.extend(!0,{},i.data("data"))),p.data("isCheck",o),p.children().first().children().children().removeClass("fa-check-empty  fa-check fa-ok-squared").addClass(s),p=p.next()}var c,f=n.length,g=0;n.each(function(){$(this).data("isCheck")&&g++}),c=0===g?"fa-check-empty":g!==f?"fa-ok-squared":"fa-check",d.$header.children().children().last().children().first().children("div").children("i").removeClass("fa-check-empty  fa-check fa-ok-squared").addClass(c)}"function"==typeof d.opts.onCheck&&setTimeout(function(){d.opts.onCheck(o,l)},10)}function B(t){var e,s=t.data._this,i=$(this),o=i.parent().parent().parent(),r=o.data("treeDeep"),a=i.children("i");a.hasClass("fa-folder-open")?(a.removeClass("fa-folder-open").addClass("fa-folder"),e=!0):(a.removeClass("fa-folder").addClass("fa-folder-open"),e=!1),o.data("closed",e);var d,n=o.next(),l=o.data("data");if(!e&&$.isArray(l.children)&&0===l.children.length){var h=i.parent().css("padding-left"),p=$("<tr><td style='padding-left:"+h+"' class='k_box_size' colspan='"+o.children().length+"'><i class='fa fa-cog fa-spin fa-1.6x fa-fw margin-bottom'></i>"+rt.config.loading+"</td></tr>"),c=s.opts;p.insertAfter(o);var f=$.extend({pid:l.data[c.idField]},s.lastParams);"function"==typeof c.setParams&&$.extend(f,s.opts.setParams());var g={async:!0,url:c.url,data:f,ok:function(t,e){0<e.length&&(e.children=e);for(var i=0,a=e.length;i<a;++i)R.call(s,e,o,parseInt(r)+1,!1);c.onLoaded&&setTimeout(function(){c.onLoaded(e)},10)},final:function(t){var e=!1;void 0!==t.data?0===t.data.length&&(e=!0):$.isArray(t)&&0===t.length&&(e=!0),e?(p.children().html("<i style='padding-right:6px;' class='fa fa-info _no_data'></i>"+rt.config.noData),setTimeout(function(){p.remove(),a.removeClass("fa-folder-open").addClass("fa-folder")},1600)):p.remove()}};s.ajax(g)}else{var u,_,v,b=[],k={};for(k[r]=e;0<n.length&&!((u=n.data("treeDeep"))<=r);)e?n.hide():(u===r+1&&n.show(),n.data("isparent")&&(b.push(n),k[u]=n.data("closed"))),n=n.next();for(var m=0,x=b.length;m<x;++m)if(u=(_=b[m]).data("treeDeep"),e=_.data("closed"),v=k[u-1],!e&&!v)for(u+=1,n=_.next();0<n.length&&n.data("treeDeep")===u;)n.show(),n=n.next();s._onResize()}A.getSelection?d=A.getSelection():document.selection&&(d=document.selection.createRange());try{d.removeAllRanges()}catch(t){}return!1}function R(t,e,i,a){for(var s,o,r,d,n=this,l=this.opts.isTree,h=Object.keys(this.titleTDs),p=[],c="TBODY"===e[0].nodeName,f=0,g=t.length;f<g;++f){s=$("<tr/>"),o=t[f],0,s.data("data",o).data("opts",this.opts).data("treeDeep",i),"k_datagrid_td_all_line"!==n.opts.splitColLine&&s.addClass("k_datagrid_old_even_cls");for(var u=0,_=h.length;u<_;++u){var v=n.titleTDs[u],b=v.data("opt"),k=n.colsWidthArray[u],m=k-2;0===f&&p.push(k);var x=b.field?b.field:"",w=$("<td filed='"+x+"' class='k_box_size'></td>");if(w.outerWidth(k),0===u&&w.css("border-left","none"),u===_-1&&w.css("border-right","none"),n.opts.splitColLine&&w.addClass(n.opts.splitColLine),n.opts.checkBox&&0===u)$('<div class="k_box_size k_datagrid_cell_text k_datagrid_cell_chkbox"><i class="fa  fa-check-empty"></i></div>').appendTo(w),d=w.css("text-align","center").data("isCheckBox",!0).children("div").data("data",o).data("_this",n).data("treeDeep",i),l&&o.children&&(d.data("isparent",!0),n.opts.onlyChkChild&&d.addClass("k_datagrid_cell_chkbox_disabled")),d.click(ft).dblclick(function(){return!1});else{var y,T="center",C=v.data("operator");if(C?(y="",w.css("text-align","center")):(y=l?o.data[b.field]:o[b.field],b.align&&(T=b.align),void 0===y&&(y="")),d=$('<div style="text-align:'+T+';" class="k_box_size k_datagrid_cell_text k_mutilline_ellipsis"></div>').appendTo(w),b&&!C&&(r=void 0,"function"==typeof b.formatter?r=b.formatter.call(w,y,o,b.field):"function"==typeof A[b.formatter]&&(r=A[b.formatter].call(w,y,o,b.field)),r&&(y=r)),d.html(y),d.appendTo(w),w.data("txt",d.text()),l)if(n.opts.checkBox&&1===u||!n.opts.checkBox&&0===u){for(var W,z=i,L=0;0<z;)L+=20,z--;if(0===L&&(L=2),d.css({"text-align":"left","padding-left":L}),o.children){var D="fa-folder",P=!0;0<o.children.length&&!o.closed&&(P=!(D="fa-folder-open")),s.data("closed",P),(W=$("<span style='cursor:pointer;'><i style='padding:2px 6px 2px 2px;cursor:pointer;' class='fa "+D+"'></i></span>").prependTo(d)).on("click",{_this:n},B).dblclick(function(){return!1}),W=W.children()}else W=$("<i style='padding:2px 6px 2px 2px;' class='fa fa-doc'></i>").prependTo(d);n.opts.treeIconColor&&W.css("color",n.opts.treeIconColor)}if(C){var S=o.toolbar;S&&(delete o.toolbar,n.rowToolbars.push(new rt.Toolbar(d,{context:s,params:o,align:"center",style:n.opts.btnStyle,showText:n.opts.showBtnText,methodsObject:n.opts.methodsObject,fontSize:"16",iconColor:rt.config.rowBtnColor,buttons:S})),w.data("isOpr",!0),w.data("oprCount",S.length))}else w.on(lt).data("opts",n.opts).data("field",b.field).data("_this",n)}w.children(".k_datagrid_cell_text").outerWidth(m),s.append(w),1}if(c?e.append(s):s.insertAfter(e),s.on(O).data("_this",n),n.opts.onRowRender&&n.opts.onRowRender.call(s,o,f),l&&(a&&s.hide(),o.children)){var j=o.children.length;s.data("isparent",!0).data("childrens",j).data("checked",0),0<j&&(void 0!==a&&a&&(o.closed=a),R.call(this,o.children,c?e:s,i+1,o.closed))}}}function _(){var t,e,s=this;e=this.opts.isTree?this.opts.data:this.opts.data.resultList;for(var i=0,a=this.rowToolbars.length;i<a;++i)this.rowToolbars[i].destroy();if(s.rowToolbars=[],!this.$toolwrap)if(this.$toolwrap=[],t=s.showToolbar?"block":"none",this.$title){var o=$("<div style='display:"+t+"' class='k_datagrid_toolbar_wrap k_box_size clearfix'></div>").insertAfter(this.$title);this.$toolwrap.push(o)}else this.$toolwrap.push($("<div  style='display:"+t+"'  class='k_datagrid_toolbar_wrap k_box_size clearfix'></div>").prependTo(this.jqObj));if(!this.opts.isTree){for(var r=0,d=s.pgList.length;r<d;++r)s.pgList[r].clear();s.pgList=[];var n={height:25,page:this.opts.data.currentPage,total:this.opts.data.totalSize,pageSize:this.opts.pageSize,buttons:this.opts.pgBtnNum?this.opts.pgBtnNum:10,startpg:this.opts.startPage>this.opts.data.currentPage?this.opts.data.currentPage:this.opts.startPage,position:"right",summary:!0,onClick:function(t,e,i){s.opts.startPage=i,s.opts.page=t,s.opts.pageSize=e,s.pageSize=e;var a=$.extend(s.lastParams,{page:t,pageSize:e});c.call(s,function(){_.call(s)},a)}};if("both"===this.opts.pgposition||"top"===this.opts.pgposition){var l=this.$toolwrap[0].children("k_datagrid_pagination_top_wrap");0===l.length&&(l=$("<div class='k_datagrid_pagination_top_wrap' style='float:right'></div>").appendTo(this.$toolwrap[0])),s.pgList.push(new rt.Pagination(l,n))}if("both"===this.opts.pgposition||"bottom"===this.opts.pgposition){if(1===this.$toolwrap.length){var h="";s.opts.fillParent&&(h="k_datagrid_tool_tottom_border"),this.$toolwrap.push($("<div class='k_datagrid_toolbar_wrap k_box_size "+h+"'></div>").appendTo(this.jqObj))}n.position="left",s.pgList.push(new rt.Pagination(this.$toolwrap[1],n))}}if(0<e.length){this.$table.children("tbody").remove();var p=$("<tbody/>");this.updateColsWidthArray(),R.call(this,e,p,0),p.children().first().children().each(function(){$(this).css("border-top","none")}),this.$table.append(p),s._onResize()}}function c(t,i){A.$curTip_&&(A.$curTip_.hide(),A.$curTip_=void 0);var s=this,a=this.opts;if(""!==a.url){this.titleCheckBox&&this.titleCheckBox.removeClass("fa-check fa-ok-squared").addClass("fa-check-empty"),"function"==typeof a.setParams&&$.extend(i,this.opts.setParams());var e=this.$bodyWrap.parent().parent();if(!this.$mask){var o=rt.config.loading,r="<div style='width:"+(rt.getCharWidth(o)+20)+"px;z-index:2147483647;' class='k_datagrid_loading'><i class='fa fa-cog fa-spin fa-1.6x fa-fw margin-bottom'></i>"+o+"</div>";this.$mask=$("<div class='k_datagrid_load_mask'><div style='display:block;' class='k_model_mask'></div>"+r+"</div>").appendTo(e)}var d=0;this.$scrollWrap.prevAll().each(function(){d+=$(this).outerHeight()}),d+=this.$headWrap.outerHeight();var n=this.$bodyWrap.height(),l=!1;if(0===n&&(this.$bodyWrap.css("padding-bottom",40),l=!0),this.$mask.children(".k_datagrid_loading").css("margin-top",d+10),this.$mask.show().fadeIn(100),s.lastParams=i,this.opts.sortField)for(var h,p=Object.keys(this.opts.sortField),c=0,f=p.length;c<f;++c){var g="_col_sort_"+(h=p[c]);i[h]||i[g]||(i[g]=this.opts.sortField[h])}var u={async:!0,url:a.url,data:i,ok:function(t,e){s.opts.data=e,s.opts.data.currentPage=i.page,s.opts.data.totalSize=e.totalSize,s.opts.pageSize=i.pageSize,_.call(s),a.onLoaded&&setTimeout(function(){a.onLoaded(e)},10),rt.isIE()&&s._fixIE()},final:function(t){var e=!1;if(void 0!==t.data?(s.opts.isTree&&0===t.data.length||!t.data.totalSize)&&(e=!0):(s.opts.isTree&&$.isArray(t)&&0===t.length||!t.totalSize)&&(e=!0),e){s.$table.children("tbody").remove();var i=s.$bodyWrap.width()-2,a=$("<tbody><tr><td style='text-align:center;width:"+i+"px' class='k_box_size' colspan='"+s.$header.children().children().first().children().length+"'><i style='padding-right:6px;' class='fa fa-info  _no_data'></i>"+rt.config.noData+"</td></tr></tbody>");s.$table.append(a)}l&&s.$bodyWrap.css("padding-bottom",0),s.$mask.fadeOut(200,function(){s.$mask.hide()})}};this.ajax(u)}}function f(t,e){rt.extend(this,f),this.opts=$.extend({},p,e),$.isArray(this.opts.cols[0])||(this.opts.cols=[this.opts.cols]),l||(l=rt.getScrollWidth()),this.tdTip=h().children("#k_datagrid_td_tip"),0===this.tdTip.length&&(this.tdTip=$("<div id='k_datagrid_td_tip' style='width:auto;min-width:150px;display:none;top:-1000px;' class=' k_box_shadow'><p></p></div>").appendTo(h())),this.showToolbar=!(!$.isArray(this.opts.toolbar)||0===this.opts.toolbar.length),this.showToolbar||(this.showToolbar="top"===this.opts.pgposition||"both"===this.opts.pgposition),this.page=this.opts.page,this.pageSize=this.opts.pageSize,t.addClass("k_datagrid_table_body"),this.jqObj=$('<div class="k_datagrid_main_wrap k_box_size"><div class="k_datagrid_scroll_wrap k_box_size"><div class="k_datagrid_head_wrap k_box_size"></div><div class="k_datagrid_body_wrap k_box_size">'+t[0].outerHTML+"</div></div></div>"),this.$bodyWrap=this.jqObj.find(".k_datagrid_body_wrap"),this.$scrollWrap=this.jqObj.find(".k_datagrid_scroll_wrap"),this.$headWrap=this.jqObj.find(".k_datagrid_head_wrap"),this.$table=this.$bodyWrap.find("table"),this.jqObj.insertAfter(t),t.remove(),this.pgList=[],this.rowToolbars=[],this.$bodyWrap.addClass("k_datagrid_body_border_fix");var i=this;$.isArray(this.opts.toolbar)&&0<this.opts.toolbar.length&&function(){this.$toolwrap||(this.$toolwrap=[],this.$toolwrap.push($("<div class='k_datagrid_toolbar_wrap k_box_size'></div>").prependTo(this.jqObj)));for(var t=0,e=this.$toolwrap.length;t<e;++t)if(this.$toolwrap[t].children().remove(),0<this.opts.toolbar.length){var i={style:this.opts.btnStyle,showText:!0,methodsObject:this.opts.methodsObject,buttons:this.opts.toolbar};rt.config.toolbarOpts&&$.extend(i,rt.config.toolbarOpts),new rt.Toolbar(this.$toolwrap[t],i)}else this.$toolwrap[t].hide()}.call(this),this.opts.title&&""!==this.opts.title&&(this.$title=$("<div class='k_datagrid_head_title k_box_size'></div>").prependTo(this.jqObj),this.$title.append("<h6>"+this.opts.title+"</h6>").appendTo(this.$title),""!==this.opts.iconCls&&this.$title.children().prepend('<i class="fa '+this.opts.iconCls+'"></i>&nbsp'));var a,s=!1;if(""!==this.opts.url){for(var o=0,r=this.opts.pageList.length;o<r;++o)if(this.opts.pageList[o]===this.opts.pageSize){s=!0;break}s||this.opts.pageList.push(this.opts.pageSize)}if(function(){var a=this;this.titleTDs={},this.$header=$("<table></table>");for(var t,e,i,s,o,r,d,n,l,h=$("<tbody></tbody>").appendTo(this.$header),p=[],c={},f=a.opts.cols,g=[],u=[],_=[],v=a.opts.checkBox?1:0,b=f.length-1,k=a.opts.checkBox,m=function(t){var e=0;return t.children().each(function(){var t=$(this).attr("colspan");e+=t?parseInt(t):1}),e},x=""!==a.opts.url,w=0,y=f.length;w<y;++w){t=f[w],e=$("<tr>"),i=0;var T=w===b;T&&(i=t.length-1);for(var C=0,W=t.length;C<W;++C){""===(s=T?$.extend({},dt,t[C]):t[C]).width?s.width="auto":s.width&&!$.isNumeric(s.width)&&"auto"!==s.width&&(s.width=parseFloat(s.width.replace("px",""))),o=s.colspan?'colspan="'+s.colspan+'"':"",r=s.rowspan?'rowspan="'+s.rowspan+'"':"",d=$("<td "+r+" "+o+" class='k_box_size'></td>"),0===w&&d.css("border-top","none"),0===C&&d.css("border-left","none"),C===W-1&&d.css("border-right","none"),$("<div class='k_box_size k_datagrid_cell_text'></div>").appendTo(d).append("<div style='width:100%' class='k_datagrid_title_text k_box_size'>"+s.title+"</div>"),""!==r&&u.push(d),s.sortable&&x&&(n=$("<div  class='k_datagrid_cell_sort k_box_size'><i class='k_datagrid_i_icon fa'></i></div>").appendTo(d).on("click",{ins:a},ht),"desc"===a.opts.sortOrder?n.children("i").addClass("fa-down"):n.children("i").addClass("fa-up"),d.mouseenter(pt),d.mouseleave(ct),_.push(d)),d.outerWidth(s.width).on(lt).data("_this",a);var z=m(e);if(d.data("index",{r:w,c:z}).data("opt",s),w===b){var L=v+C;this.titleTDs[L]=d}var D=c[z];if(D&&D.span&&w<=D.rowIdx&&(d.hide(),g.push(d),w===b)){var P=D.td.data("opt"),S=d.data("opt"),j=$.extend({},S,P);delete j.rowspan,delete j.colspan,D.td.data("opt",j),D.td.outerWidth(j.width),l=v+C,this.titleTDs[l]=D.td}s.rowspan&&(c[z]={span:!0,rowIdx:w+s.rowspan-1,td:d}),e.append(d)}p.push(e)}for(var A=0,O=g.length;A<O;++A)g[A].remove();for(var B,R,I,q=0,F=p.length;q<F;++q){if(B=q===F-1,k&&((R=$("<td style='border-left:none;' class='k_box_size' ><div style='width:"+(nt-2)+"px' class='k_box_size k_datagrid_cell_chkbox'></div></td>")).data("opt",{width:nt}),R.outerWidth(nt),p[q].prepend(R),i+=1),a.opts.oprCol&&0===q){var M="auto";a.opts.oprColWidth&&(M=a.opts.oprColWidth),I=$("<td rowspan='"+F+"' class='k_box_size'  style='border-right:none;'><div  class='k_box_size k_datagrid_cell_text'><div class='k_datagrid_title_text k_box_size'>"+rt.config.oprColName+"</div></div></td>"),p[q].append(I.outerWidth(M)),u.push(I),l=i+1,(this.titleTDs[l]=I).data("operator",!0).data("opt",{width:M,isOpr:!0})}h.append(p[q]),k&&B&&(R=p[q].children().first().children().append("<i class='fa  fa-check-empty'></i>").click(ft).data("flag",!0).data("_this",a),this.titleTDs[0]=R.parent(),R.data("opt",{width:nt}),this.titleCheckBox=R.children("i")),a.opts.onHeadRender&&a.opts.onHeadRender.call(p[q])}for(var X,H,N,E,U,Y,G,J,K,Q,V=this.$headWrap.width(),Z=$("<tr style='height:0;'/>"),tt=[],et=Object.keys(this.titleTDs),it=0,at=0,st=et.length;at<st;++at)X={height:0,"border-top":"none","border-bottom":"none"},H=this.titleTDs[et[at]],0===at&&(X["border-left"]="none"),at===st-1&&(X["border-right"]="none"),N=(J=H.data("opt")).width,E=$("<td class='k_datagrid_header_hide_td'/>").css(X).appendTo(Z).outerWidth(N).data("i",at).data("opt",J),"auto"===N?(Y=H.find(".k_datagrid_title_text").css("font-size"),U=void 0,(G=J.minWidth)&&""!==G?($.isNumeric(G)||(G=parseInt(G.replace("px",""))),U=G):U=rt.getCharWidth(J.title,Y)+16,it+=U,tt.push(E)):(U=rt.getCharWidth(J.title,Y)+16,it+=$.isNumeric(N)?N:parseFloat(N.replace("px",""))),E.data("minWidth",U),H.data("minWidth",U),H.outerWidth("auto");if(V<it)for(K=0,Q=tt.length;K<Q;++K)tt[K].outerWidth(tt[K].data("minWidth"));this.autoTdArray=tt,this.minTableWidth=parseInt(it+1),Z.prependTo(this.$header),this.$header.appendTo(this.$headWrap);var ot=this.$header.outerWidth();if(this.$header.width()<it&&this.$header.width(it),0<V-ot)for(this.$header.outerWidth("100%"),K=0,Q=tt.length;K<Q;++K)(d=tt[K]).outerWidth()<d.data("minWidth")&&d.outerWidth(d.data("minWidth"));this.hideTrTdArray=Z.children(),this.updateColsWidthArray(),Z.children().eq(v),setTimeout(function(){a._setColPosition();var t,e=rt.isIE();for(K=0,Q=_.length;K<Q;++K){var i=(d=_[K]).children(".k_datagrid_cell_sort");t=e?d.outerHeight()-1:d.height(),i.height(t)}},300)}.call(this),i._fillParent(),null!==this.opts.data)setTimeout(function(){_.call(i)},0);else if(""!==this.opts.url&&this.opts.loadImd){var d={page:1,pageSize:this.opts.pageSize};c.call(i,function(){_.call(i)},d)}this.showToolbar||""!==this.opts.title||this.jqObj.css("border-top","none");var n=rt.getUUID();$(A).on("resize."+n,function(){i._onResize?(i._onResize(),clearTimeout(a),a=setTimeout(function(){i._onResize()},200)):$(this).off("."+n)}),this.scrollLeft=0;this.$bodyWrap.on("scroll",function(){i._hideToolBtnList();var t=$(this).scrollLeft();i.$header.css("left",-t),i.scrollLeft=t})}return f.prototype={_fixIE:function(){if(!this.hasFixIe&&(this.hasFixIe=!0,1<this.hideTrTdArray.length)){var t=this.hideTrTdArray.eq(1),e=this.hideTrTdArray.eq(0);e.outerWidth(e.outerWidth()+1),t.outerWidth(t.outerWidth()-1),this._onResize()}},_hideToolBtnList:function(){this.opts.oprCol&&h().children("#k_toolbar_drop_wrap").hide()},_fillParent:function(){if(this.opts.fillParent){var t=this.jqObj.parent().height(),e=this.$headWrap.outerHeight();"bottom"!==this.opts.pgposition&&"both"!==this.opts.pgposition||(e+=40),this.$scrollWrap.siblings().each(function(){var t=$(this);t.hasClass("k_datagrid_load_mask")||(e+=t.outerHeight())});var i=t-e;if(this.$bodyWrap.outerHeight(i).css({"overflow-y":"auto"}),parseInt(i/38)>this.opts.pageSize){for(var a,s=0,o=this.opts.pageList.length;s<o;++s)if(this.opts.pageList[s]===this.opts.pageSize){if(s<o-2){a=this.opts.pageList[s+1];break}a=this.opts.pageList[o-1]}a&&(this.opts.pageSize=a,this.pageSize=a)}}},_setColPosition:function(){var t=this.hideTrTdArray.first().next();this.opts.checkBox&&(t=t.next());var e=function(t,e){for(var i={},a=e.scrollLeft;0<t.length;)i[parseInt(t.offset().left+a)]=t.data("i"),t=t.next();return i}(t,this);this.moveXofs=e},updateColsWidthArray:function(){for(var t=[],e=Object.keys(this.titleTDs),i=0,a=e.length;i<a;++i)t.push(this.titleTDs[e[i]].outerWidth());return this.colsWidthArray=t,this.colsWidthArray},_onResize:function(){var a=this,s=!1;this.$table.outerHeight()>this.$bodyWrap.height()&&(s=!0);var t,e,i,o,r=this.$header.parent().width();if(this.$header.data("isFixWidth")){if(r>this.minTableWidth)for(this.$header.removeData("isFixWidth"),e=0,i=this.autoTdArray.length;e<i;++e)(t=this.autoTdArray[e]).css("width","auto")}else if(r<this.minTableWidth)for(this.$header.data("isFixWidth",!0),e=0,i=this.autoTdArray.length;e<i;++e)o=(t=this.autoTdArray[e]).data("minWidth"),t.css("width",o);this.updateColsWidthArray(),this.$table.children("tbody").children().each(function(){var t=$(this).children(),i=t.length;if(1===i&&0<t.find("._no_data").length)return!0;t.each(function(t){var e=a.colsWidthArray[t];s&&t===i-1&&(e-=l),$(this).outerWidth(e).children().outerWidth(e-2)})}),clearTimeout(this._setColPositionTimer),this._setColPositionTimer=setTimeout(function(){a._setColPosition(),a._hideToolBtnList()},100)},getData:function(){return this.opts.data},openInner:function(t,e){if(!t.next().hasClass("tr_inner")){this.innerTr&&this.innerTr.remove();var i=this,a=t.children().length,s=$("<tr class='tr_inner'><td colspan='"+a+"' style='position:relative;'><div class='tr_inner_content'></div></td></tr>").insertAfter(t),o=s.children(),r=o.children();if($("<a style='position:absolute;top:0;right:0;cursor: pointer;' title='"+rt.config.closeLable+"'><i class='fa fa-cancel-2'></i></a>").appendTo(o).click(function(){s.remove(),i.innerTr=void 0}),this.innerTr=s,"html"===e.type)rt.htmlLoad({target:r,url:e.content,loaded:function(){"function"==typeof e.onLoaded&&e.onLoaded.call(r)}});else if("iframe"===e.type){var d=$("<div style='padding-left:16px;'><i class='fa fa-cog fa-spin fa-1.6x fa-fw margin-bottom'></i>"+rt.config.loading+"</div>").appendTo(r),n=$("<iframe  frameborder='0' style='overflow:visible' scrolling='auto' width='100%' height='100%' src='' ></iframe>").appendTo(r.css("overflow","hidden")),l=n[0];n.on("load",function(){d.remove(),"function"==typeof e.onLoaded&&e.onLoaded.call(r)}),l.src=e.content}else r.html(e.content)}},reload:function(t){var e=this;e.lastParams={};var i=$.extend(!0,{page:1,pageSize:this.opts.pageSize},t);c.call(e,function(){_.call(e)},i)},refresh:function(){var t=this;c.call(t,function(){_.call(t)},$.extend({page:1,pageSize:this.opts.pageSize},t.lastParams))},getCheckedData:function(i){var a=[];return this.$table.children("tbody").children().each(function(){var t=$(this);if(t.data("isCheck")||i){var e=$.extend(!0,{},t.data("data"));delete e.children,delete e.toolbar,a.push(e)}}),a},getCheckedId:function(t){var i=[],a=this;return this.$table.children("tbody").children().each(function(){var t,e=$(this);e.data("isCheck")&&(t=a.opts.isTree?e.data("data").data[a.opts.idField]:e.data("data")[a.opts.idField],i.push(t))}),t?i.join(";"):i},getRowCount:function(){return this.$table.children("tbody").children().length}},rt.Datagrid=f});