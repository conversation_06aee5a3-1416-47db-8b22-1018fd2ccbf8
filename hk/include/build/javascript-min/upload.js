/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(t,a){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B"],function(e){return a(t,e)}):(t.$B||(t.$B={}),a(t,t.$B))}("undefined"!=typeof window?window:this,function(window,$B){function getDateDiff(e,t){return parseInt(t.getTime()-e.getTime())}var $body;function _getBody(){return $body||($body=$(window.document.body).css("position","relative")),$body}var uploadIconHtml='<i class="fa fa-up-small" style="color:#fff;padding-right:4px;"></i>',formTag='<form enctype="multipart/form-data"   method="post" name="k_fileUpload_from"></form>';function MutilUpload(e){if($B.extend(this,MutilUpload),this.target=e.target.addClass("k_mutilupload_wrap"),this.target.children().remove(),this.onselected=e.onselected,this.immediate=e.immediate,this.success=e.success,this.ondeleted=e.ondeleted,this.isCross=e.isCross,this.crossHelperPage=e.crossHelperPage,this.error=e.error,this.setParams=e.setParams,this.url=e.url,this.form=$(formTag).appendTo(this.target),this.timeout=1e3*(e.timeout?e.timeout:300),this.uploading=!1,this.init=void 0===e.init||e.init,this.init&&$.isArray(e.files))for(var t=0,a=e.files.length;t<a;++t)this._createFileInput(e.files[t]),t<a-1&&this.form.append("<br/>")}return MutilUpload.prototype={_createIframe:function(uploadFileArray){var id="_ifr_"+$B.generateMixed(8),_this=this;_this.isIniting=!0;var ifr=$('<iframe  name="'+id+'" id="'+id+'" style="display:none;"></iframe>').appendTo(_getBody());setTimeout(function(){_this.isIniting=!1},10);var crossHelperPage=_this.crossHelperPage,isCross=_this.isCross;if(isCross&&!crossHelperPage){var links=$("head").children("link");crossHelperPage=1===links.length?links.first().attr("href"):links.eq(2).attr("href")}return ifr.on("load",function(e){if(!_this.isIniting){clearInterval(ifr.data("timeoutchker")),_this.uploading=!1;var i=0,len=uploadFileArray.length,callBakFn,message,json,res,isSuccess=!0;if(isCross){if(!ifr.attr("src")){try{ifr[0].src=crossHelperPage}catch(e){}return}try{ifr.removeAttr("src"),res=ifr[0].contentWindow.name,json=eval("("+res+")")}catch(e){isSuccess=!1,message=$B.config.uploadFail,callBakFn=_this.error}}else try{var ifrDoc=ifr[0].contentWindow.document||ifr[0].contentDocument,body=$(ifrDoc.body);res=body.text(),json=eval("("+res+")")}catch(e){isSuccess=!1,message=$B.config.uploadFail,callBakFn=_this.error,console.log("to json err "+res)}for(json&&(0!==json.code?(isSuccess=!1,callBakFn=_this.error,message=$B.config.uploadFail,json.message&&""!==json.message&&(message=json.message)):callBakFn=_this.success);i<len;){var vInput=uploadFileArray[i],$a=vInput.next().removeClass("k_update_close_disable");isSuccess?(vInput.html(uploadIconHtml+vInput.attr("title")),$a.children("i").attr("class","fa fa-check-1")):vInput.html(uploadIconHtml+message),i++}"function"==typeof callBakFn&&callBakFn.call(uploadFileArray,json),ifr.remove(),uploadFileArray=void 0}}),{id:id,ifr:ifr}},_changeFn:function(e){var t=e.data._this,a=$(this),i=a.val(),n=a.next("div"),r=a.attr("accept").toLowerCase();n.next("a").children("i").attr("class","fa fa-cancel-2").attr("_class","fa fa-cancel-2");var s=a.attr("must"),l=!0;if(s&&""===i)return n.html(uploadIconHtml+s),!1;var o=i.replace(/^.+?\\([^\\]+?)(\.[^\.\\]*?)?$/gi,"$1$2"),d=o,c=a.attr("label"),f=a.attr("name");if(".*"!==r){var p=d.replace(/.+\./,"").toLowerCase();if(r.indexOf(p)<0){a.val("");var m=$B.config.uploadAccept.replace("<accept>",r);return void($B.alert?($B.alert({width:300,height:120,message:m,timeout:2}),n.html(uploadIconHtml+c)):n.html(uploadIconHtml+m))}}if(""===o&&(o=c),"function"==typeof t.onselected&&(t.onselected.call(a,d,f,r)||(a.val(""),n.html(uploadIconHtml+c),l=!1)),l&&(n.attr("title",o).html(uploadIconHtml+o),t.immediate)){var u=n.parent().siblings("div").find("input[type=file]");u.hide().attr("disabled","disabled"),t.submit(),u.show().removeAttr("disabled")}},_createFileInput:function(e){var t=e.must?"must="+e.must:"",a=e.multiple?'multiple="multiple"':"",i=$('<div class="k_upload_file_item_wrap k_box_size" style="width:auto;max-width: 100%; "></div>').appendTo(this.form),n=($("<input  "+a+" "+t+' label="'+e.label+'" id="'+e.name+'" name="'+e.name+'" type="file" accept="'+e.type+'" style="width:100%;">').appendTo(i),$('<div title="'+e.label+'" class="k_visual_file_item_input k_box_size">'+uploadIconHtml+e.label+"</div>").appendTo(i));this._bindFileEvents(n);var s=this;return $("<a class='k_upload_file_item_delete' title='"+$B.config.clearFile+"'><i class='fa fa-cancel-2'></i></a>").appendTo(i).click(function(){var e=$(this);if(e.hasClass("k_update_close_disable"))return!1;var t=e.prev(),a=t.prev(),i=a.attr("id"),n=a.attr("label"),r=e.parent();r.attr("d")?$B.confirm({width:280,height:125,message:$B.config.uploadClearConfirm,okText:$B.config.uploadClear,noText:$B.config.uploadRemove,noIcon:"fa-trash",okIcon:"fa-cancel-circled",okFn:function(){"function"==typeof s.ondeleted&&""!==a.val()&&s.ondeleted(i),t.html(uploadIconHtml+n).attr("title",n),a.val(""),t.next("a").children("i").attr("class","fa fa-cancel-2").attr("_class","fa fa-cancel-2")},noFn:function(){r.prev("br").remove(),r.remove(),"function"==typeof s.ondeleted&&s.ondeleted(i)}}):("function"==typeof s.ondeleted&&""!==a.val()&&s.ondeleted(i),t.html(uploadIconHtml+n).attr("title",n),a.val(""),t.next("a").children("i").attr("class","fa fa-cancel-2").attr("_class","fa fa-cancel-2"))}).mouseover(function(){var e=$(this).children("i"),t=e.attr("class");e.attr("class","fa fa-cancel-2").attr("_class",t)}).mouseout(function(){var e=$(this).children("i"),t=e.attr("_class");e.attr("class",t)}),i},_bindFileEvents:function(e){var a=this;e.on("click",function(){var e=$(this),t=e.prev("input");if(e.siblings("a").hasClass("k_update_close_disable"))return!1;t.unbind("change").val("").on("change",{_this:a},a._changeFn),t.trigger("click")})},submit:function(){if(this.immediate||!this.uploading){this.uploading=!0,clearTimeout(this.tipTimer);var i=[],n=[],r=!1,s=[];if(this.form.children(".k_upload_file_item_wrap").each(function(){var e=$(this).children("input");if(e.attr("disabled"))return!0;var t=e.next(),a=t.next();""===e.val()&&e.attr("must")?(t.html("<span style='color:#FFBCBF'>"+uploadIconHtml+$B.config.uploadNotEmpty+"</span>"),n.push(t)):(t.html("<span style='color:#fff'><span style='padding-right:4px;'><i style='color:#fff' class='fa fa-spin3 animate-spin'></i></span>"+$B.config.uploading+"</span>"),a.addClass("k_update_close_disable"),i.push(t),r=!0,s.push(e.attr("id")))}),r){if(this.form.children("input[type=hidden]").remove(),"function"==typeof this.setParams){var e=this.setParams.call(this,s);if($.isPlainObject(e))for(var t,a=Object.keys(e),l=0,o=a.length;l<o;++l)t=a[l],this.form.append("<input type='hidden' name='"+t+"' id='"+t+"' value='"+e[t]+"'/>")}var d=this,c=this._createIframe(i);d.isIniting=!1;var f=c.id,p=this.url;p=0<p.indexOf("?")?p+"&_t_="+$B.generateMixed(5):p+"?_t_="+$B.generateMixed(5),this.form.attr("target",f),this.form.attr("action",p);var m=new Date;this.form[0].submit();var u=setInterval(function(){var e=new Date;if(getDateDiff(m,e)>d.timeout){d.uploading=!1,clearInterval(c.ifr.data("timeoutchker")),c.ifr.remove(),c=void 0;for(var t=0,a=i.length;t<a;++t)i[t].html(uploadIconHtml+$B.config.uploadTimeout),i[t].siblings("a").removeClass("k_update_close_disable")}},1500);c.ifr.data("timeoutchker",u)}else this.uploading=!1;this.tipTimer=setTimeout(function(){for(var e=0,t=n.length;e<t;++e)n[e].html(uploadIconHtml+n[e].attr("title"))},1e3)}else console.log("is uploading ，donot submit！")},addFile:function(e){0<this.form.children(".k_upload_file_item_wrap").length&&this.form.append("<br/>"),this._createFileInput(e).attr("d",1)},reset:function(){this.form.children(".k_upload_file_item_wrap").each(function(){var e=$(this),t=e.children("input");t.val("");var a=t.attr("label");e.children("div").html(uploadIconHtml+a).attr("title",a),e.children("a").removeClass("k_update_close_disable").children("i").removeClass("fa-check-1").addClass("fa-cancel-2")})},hasFiles:function(){var e=!1;return this.form.children(".k_upload_file_item_wrap").each(function(){if(""!==$(this).children("input").val())return!(e=!0)}),e}},$B.MutilUpload=MutilUpload,$B.downLoad=function(args){args.target.children().remove();var inteVal,ivtTime=1500;args.ivtTime&&(ivtTime=args.ivtTime);var date=new Date,finish_down_key=date.getDay()+""+date.getHours()+date.getMinutes()+date.getSeconds()+date.getMilliseconds()+$B.generateMixed(12),_this=this,ifrId="k_"+$B.generateMixed(12),$ifr=$('<iframe name="'+ifrId+'" id="'+ifrId+'" style="display: none"></iframe>').appendTo(args.target),ifr=$ifr[0],message=args.message?args.message:"正在导出......",_url=args.url;_url=0<_url.indexOf("?")?_url+"&isifr=1&_diff="+$B.generateMixed(4):_url+"?isifr=1&_diff="+$B.generateMixed(4);var $msg=$("<h3 id='k_file_export_xls_msg_'  style='height:20px;line-height:20px;text-align:center;padding-top:12px;'><div class='loading' style='width:110px;margin:0px auto;'><i class='fa-spin3 animate-spin'></i><span style='padding-left:12px;'>"+message+"</span></div></h3>").appendTo(args.target),$form=$('<form action="'+_url+'" target="'+ifrId+'" method="post" ></form>').appendTo(args.target);args.fileName&&$form.append('<input type="hidden" id="fileName" name="fileName" value="'+encodeURIComponent(args.fileName)+'"/>'),args.sheetName&&$form.append('<input type="hidden" id="sheetName" name="sheetName" value="'+encodeURIComponent(args.sheetName)+'"/>'),args.mimeType&&$form.append('<input type="hidden" id="mimeType" name="mimeType" value="'+encodeURIComponent(args.mimeType)+'"/>'),$form.append('<input type="hidden" id="k_finish_down_key_" name="k_finish_down_key_" value="'+finish_down_key+'"/>');var isModel=0;$.isPlainObject(args.model)&&(isModel=1,$form.append('<input type="hidden" id="modelfile" name="modelfile" value="'+encodeURIComponent(args.model.file)+'"/>'),$form.append('<input type="hidden" id="startrow" name="startrow" value="'+args.model.startRow+'"/>')),$form.append('<input type="hidden" id="ismodel" name="ismodel" value="'+isModel+'"/>'),$.isPlainObject(args.params)&&Object.keys(args.params).forEach(function(e){var t=args.params[e];$form.append('<input type="hidden" id="'+e+'" name="'+e+'" value="'+encodeURIComponent(t)+'"/>')});var callReturn="function"==typeof args.onSuccess;$ifr.on("load",function(){clearInterval(inteVal);try{var _$body=$(window.frames[ifrId].document.body),res=_$body.text();if(res&&""!==res){var json=eval("("+res+")");return $msg.html("<h2>"+json.message+"</h2>"),void(0===json.code&&callReturn&&args.onSuccess(json))}}catch(e){return void $msg.html("<h2>"+$B.config.expException+"</h2>")}}),$form[0].submit();for(var regex=/(\/\w+)/g,match,lastChar;match=regex.exec(args.url),null!==match&&(lastChar=match[0]),null!==match;);var url=args.url.replace(lastChar,"/checkresponse");url=0<url.indexOf("?")?url+"&k_finish_down_key_="+finish_down_key:url+"?k_finish_down_key_="+finish_down_key,inteVal=setInterval(function(){try{for(var prt=args.target.parent();0<prt.length&&"BODY"!==prt[0].tagName;)prt=prt.parent();if("BODY"!==prt[0].tagName)return void clearInterval(inteVal);var _$body=$(window.frames[ifrId].document.body),res=_$body.text();if(res&&""!==res){clearInterval(inteVal);var json=eval("("+res+")");return void $msg.html("<h2>"+json.message+"</h2>")}}catch(e){return clearInterval(inteVal),void $msg.html("<h2>"+$B.config.expException+"</h2>")}$B.request({url:url+"&_t="+$B.generateMixed(5),ok:function(e,t,a){"null"!==e&&(clearInterval(inteVal),$msg.html("<h2>"+e+"</h2>"),callReturn&&setTimeout(function(){args.onSuccess(a)},10))}})},ivtTime)},MutilUpload});