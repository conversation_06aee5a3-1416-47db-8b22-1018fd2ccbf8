/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(t,o){"function"==typeof define&&define.amd&&!window._all_in_?define(["$B","plugin","panel","config"],function(e){return o(t,e)}):(t.$B||(t.$B={}),o(t,t.$B))}("undefined"!=typeof window?window:this,function(v,b){var y,x=b.config,k=v.document;function o(e,t){var o=$.extend({},b.config.curdDatagridOpts,t);this.opts=b.config.curdOpts,this.id=o.id,this.curWindow=null,this.idField=o.idField;var i=b.getHttpHost();0<=o.url.indexOf(i)?this.url=o.url:this.url=i+o.url;var n=this.url;this.url&&!/gridid=\w+/.test(this.url)&&(n=0<this.url.indexOf("?")?this.url+"&gridid="+this.id:this.url+"?gridid="+this.id),o.url=n,this.dg=new b.Datagrid(e,o)}return $.extend(b,{window:function(t){var e,o=!0;t.isTop?e=$(v.top.document.body).css("position","relative"):(y||(y=$(v.document.body).css("position","relative")),e=y);var i=parseInt(e.outerWidth()),n=parseInt(e.outerHeight());"string"==typeof t.width&&(0<t.width.indexOf("%")?t.width=i*(parseInt(t.width.replace("%",""))/100):t.width=parseInt(t.width.replace("px",""))),"string"==typeof t.height&&(0<t.height.indexOf("%")?t.height=n*(parseInt(t.height.replace("%",""))/100):t.height=parseInt(t.height.replace("px",""))),void 0!==t.mask&&(o=t.mask),t.full&&(t.width=i,t.height=n,o=!1,t.draggable=!1,t.collapseable=!1,t.maxminable=!1),t.width>i&&(t.width=i),t.height>n&&(t.height=n);var a=(i-t.width)/2,s=(n-t.height)/2;s+=v.pageYOffset||k.documentElement.scrollTop||k.body.scrollTop||0;var r=e.children("#k_window_mask_bg"),l=this.generateMixed(6);if(o){var d=parseInt(e[0].scrollWidth-1),c=parseInt(e[0].scrollHeight-1);0===r.length?r=$("<div style='z-index:218000000;position:abosulte;top:-100000px;width:"+d+"px;height:"+c+"px' for='"+l+"' id='k_window_mask_bg'></div>").appendTo(e):r.css({width:d,height:c}),t.opacity&&r.css("opacity",t.opacity),"none"===r.css("display")&&r.css("top",0).show().attr("for",l)}var h,p,u,f,g=$("<div  id='"+l+"' style='position:absolute;z-index:2190000000;' class='k_window_main_wrap'></div>");t.position?(p=$.isPlainObject(t.position))?g.css(t.position).appendTo(e):"bottom"===t.position?(h=e.css("overflow"),e.css("overflow","hidden"),g.css({bottom:-1200,right:0}).appendTo(e)):g.css({top:-1200,left:a}).appendTo(e):g.css({top:s,left:a}).appendTo(e),t.full&&(t.maxminable=!1);var m={width:t.width?t.width:600,height:t.height?t.height:300,zIndex:2147483647,title:t.title,closeType:void 0!==t.closeType?t.closeType:"destroy",iconCls:void 0!==t.iconCls?t.iconCls:"fa-window-restore",iconColor:t.iconColor,shadow:void 0===t.shadow||t.shadow,radius:void 0!==t.radius&&t.radius,header:void 0===t.header||t.header,content:t.content,url:t.url?t.url:"",position:t.position,dataType:void 0!==t.dataType?t.dataType:"iframe",draggableHandler:t.draggableHandler,moveProxy:void 0!==t.moveProxy&&t.moveProxy,draggable:void 0===t.draggable||t.draggable,closeable:void 0===t.closeable||t.closeable,expandable:!1,maxminable:void 0===t.maxminable||t.maxminable,collapseable:void 0===t.collapseable||t.collapseable,onResized:"function"==typeof t.onResized?t.onResized:void 0,onLoaded:"function"==typeof t.onLoaded?t.onLoaded:void 0,onStartDrag:"function"==typeof t.onStartDrag?t.onStartDrag:void 0,onDrag:"function"==typeof t.onDrag?t.onDrag:void 0,onStopDrag:"function"==typeof t.onStopDrag?t.onStopDrag:void 0,toolbar:t.toolbar?t.toolbar:void 0,toolbarAlign:t.toolbarAlign?t.toolbarAlign:"center",onClose:function(){var e=!0;return"function"==typeof t.onClose&&(e=t.onClose()),e&&clearTimeout(u),e},onClosed:function(){r.attr("for")===this.attr("id")&&r.hide(),"function"==typeof t.onClosed&&t.onClosed()}};if(t.createToolsFn&&(m.createToolsFn=t.createToolsFn),t.fixed&&(m.expandable=!1,m.collapseable=!1,m.maxminable=!1),m=$.extend({},x.winDefOpts,m),t.timeout&&t.timeout<5&&(m.closeable=!1),(f=new b.Panel(g,m)).$header&&f.$header.addClass("k_window_header_wrap"),t.timeout&&(u=setTimeout(function(){"destroy"===m.closeType?f.destroy():f.close(!0)},1e3*t.timeout)),t.position&&!p)if("bottom"===t.position)g.show(),g.show().animate({bottom:0},300,function(){e.css("overflow",h)});else{var w=$(k).scrollTop()+1;g.show().animate({top:w},300)}return f},message:function(e){var t,o,i,n="fa-mail-alt";"function"==typeof e.message?"string"!=typeof(o=e.message())&&(t=o.children(".k_window_content_icon")):"string"==typeof e&&(e={message:e},2===arguments.length&&(e.timeout=arguments[1])),o=$('<div class="k_window_content_wrap clearfix"><div class="k_window_content_icon"></div><p class="k_box_size k_window_content_p">'+e.message+"</p></div>");var a=e.contentIcon?e.contentIcon:"fa-chat-empty",s=$("<i class='fa "+a+"'>​</i>").appendTo(o.children(".k_window_content_icon"));e.iconColor&&s.css("color",e.iconColor),delete e.iconColor,t=o.children(".k_window_content_icon"),i=e.position,e.iconCls&&(n=e.iconCls);var r={width:400,height:200,position:i,title:x.messageTitle,iconCls:n,shadow:!0,timeout:e.timeout?e.timeout:0,mask:!0,draggableHandler:"header",radius:!1,header:!0,content:o,draggable:!0,closeable:!0,expandable:!1,maxminable:!1,collapseable:!1};$.extend(r,e);var l=$.extend(r,e),d=o.children("p");t&&(l.onResized=function(){var e=d.height()+"px";t.children("i").css({"line-height":e,height:e,display:"block"})});var c,h=this.window(l),p=o.height(),u=t?t.outerWidth():0,f=d.outerWidth()+u,g=o.width()-f;0<g&&(t&&0<t.length?t.css("margin-left",g/2):d.css("margin-left",g/2));var m=d.outerHeight();if(p<m)c=m-p+20+l.height,h.resize({height:c});else{var w=parseInt(d.css("padding-top").replace("px"));d.css("margin-top",(p-m)/2-w/2),t&&t.children("i").css({"line-height":p+"px",height:p,display:"block"})}return h},success:function(e){var t={width:400,height:180,title:x.successTitle,iconCls:" fa-check",shadow:!0,timeout:0,mask:!0,contentIcon:"fa-ok-circled",iconColor:"#3BB208",draggableHandler:"header"};return"string"==typeof e?(t.message=e,2===arguments.length&&(t.timeout=arguments[1])):$.extend(t,e),this.message(t)},alert:function(e){var t={width:400,height:150,title:x.warnTitle,iconCls:"fa-attention-circled",shadow:!0,timeout:0,mask:!0,contentIcon:"fa-attention-1",iconColor:"#BFBC03",draggableHandler:"header"};return"string"==typeof e?(t.message=e,2===arguments.length&&(t.timeout=arguments[1])):$.extend(t,e),this.message(t)},error:function(e){var t={width:400,height:150,title:x.errorTitle,iconCls:"fa-attention-1",shadow:!0,timeout:0,mask:!0,contentIcon:"fa-cancel-circled",iconColor:"#F7171C",draggableHandler:"header"};return"string"==typeof e?(t.message=e,2===arguments.length&&(t.timeout=arguments[1])):$.extend(t,e),this.message(t)},confirm:function(e){var o,t={width:400,height:150,title:x.confirmTitle,iconCls:"fa-question",shadow:!0,timeout:0,mask:!0,contentIcon:"fa-help-1",draggableHandler:""},i=e.okFn,n=e.noFn;e.buttonColor&&e.buttonColor,delete e.okFn,delete e.noFn,delete e.buttonColor;var a=e.okIcon?e.okIcon:"fa-ok-circled",s=e.okText?e.okText:x.buttonOkText,r=e.noIcon?e.noIcon:"fa-reply-all",l=e.noText?e.noText:x.buttonCancleText;return delete e.okIcon,delete e.okText,delete e.noIcon,delete e.noText,"string"==typeof e?(t.message=e,2===arguments.length&&(t.timeout=arguments[1])):$.extend(t,e),"function"==typeof e.createToolsFn?t.createToolsFn=e.createToolsFn:t.createToolsFn=function(){var e=$("<div class='k_confirm_buttons_wrap'></div>").appendTo(this);$("<button class='yes'><i  class='fa "+a+"'>​</i>"+s+"</button>").appendTo(e).click(function(){var e=!0;if("function"==typeof i){var t=i();void 0!==t&&(e=t)}e&&o.close(!0)}),$("<button class='no'><i  class='fa "+r+"'>​</i>"+l+"</button>").appendTo(e).click(function(){var e=!0;if("function"==typeof n){var t=n();void 0!==t&&(e=t)}e&&o.close(!0)});return e},o=this.message(t)}}),o.prototype={constructor:o,getDataGrid:function(){return this.dg},openInner:function(e){this.dg.openInner(e)},window:function(e,t){if($.isPlainObject(e.params)){for(var o=[],i=Object.keys(e.params),n=0,a=i.length;n<a;++n)o.push(i[n]+"="+e.params[i[n]]);e.params=o.join("&")}if(t){var s;if(e.rowData)s=e.rowData[this.idField];else{var r=this.dg.getCheckedId();if(0===r.length){var l=void 0===e.message?b.config.need2CheckedData:e.message;return void b.alert(l)}if(1<r.length)return void b.alert(b.config.onlyGetOneData);s=r[0]}void 0!==e.params&&""!==e.params?e.params=e.params+"&id="+s:e.params="id="+s,e.iconCls||(e.iconCls="fa-edit")}var d=$.extend({},b.config.curdWinDefOpts,e),c=d.pageName&&""!==d.pageName?d.pageName:"form";return d.url=this.url.replace(/\?\S+/,"").replace(/list/,b.config.curdOpts.page_action)+"/"+c,d.params&&""!==d.params&&(d.url=d.url+"?"+d.params),delete d.pageName,delete d.params,this.curWindow=b.window(d),this.curWindow},setOpenWindow:function(e){this.curWindow=e},close:function(){null!==this.curWindow&&this.curWindow.close&&this.curWindow.close(),this.curWindow=null},closeWindow:function(){this.close()},add:function(e,t){var o=this.url.replace(/\?\S+/,"").replace(/list/,b.config.curdOpts.add_action);this._save(e,o,t)},update:function(e,t){var o=this.url.replace(/\?\S+/,"").replace(/list/,b.config.curdOpts.update_action);this._save(e,o,t)},_save:function(e,t,i){var o,n=this;(o=!e.data||void 0===e.url&&"function"!=typeof e.fail&&"function"!=typeof e.ok&&"final"!=typeof e.ok?{data:e,ok:function(e,t,o){"function"==typeof i&&i(o),n.close(),n.reload({page:1})},fail:function(e,t){"function"==typeof i?i(t):b.error(e)}}:e).url=t,b.request(o)},deleteData:function(e,t){this._del({},e,t)},delChecked:function(e){var t=this.dg.getCheckedId();this._del(e,t)},_del:function(e,a,s){if(0!==a.length){var r=this;b.confirm({message:b.config.confirmDelete,okFn:function(){var o=r.dg.getRowCount()===a.length,e=r.url.replace(/\?\S+/,"").replace(/list/,b.config.curdOpts.del_action),t={idList:a.join(",")};s&&$.extend(t,s);var i=b.window({fixed:!0,position:"top",header:!1,width:220,height:35,content:'<p style="line-height:30px;margin-top:2px;text-align:center;"><i style="color:#1296DB" class="fa fa-spin5 animate-spin"></i><span style="padding-left:12px;color:#1296DB">'+b.config.processing+"</span></p>"}),n={url:e,data:t,ok:function(e,t){r.close(),o?r.reload({page:1}):r.refresh()},final:function(){setTimeout(function(){i.close()},10)}};b.request(n)}})}else{var t=e&&void 0!==e.message?e.message:b.config.need2CheckForDel;b.alert(t)}},getCheckedData:function(){return this.dg.getCheckedData()},getCheckedId:function(){return this.dg.getCheckedId()},getData:function(){return this.dg.getCheckedData(!0)},query:function(e){},get:function(e){},reload:function(e){this.dg.reload(e)},refresh:function(){this.dg.refresh()}},b.CURD=o,b.getCURD=function(e,t){return new o(e,t)},b});