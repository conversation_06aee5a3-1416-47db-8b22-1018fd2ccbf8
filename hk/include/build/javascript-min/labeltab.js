/*! bui - v0.0.1 - 2019-10-24 
Copyright (c): kevin.huang www.vvui.net 
Released under MIT License*/

!function(i,e){"function"==typeof define&&define.amd?define(["$B"],function(t){return e(i,t)}):(i.$B||(i.$B={}),e(i,i.$B))}("undefined"!=typeof window?window:this,function(t,h){var b="<iframe  class='panel_content_ifr' frameborder='0' style='overflow:visible;display:block;vertical-align:top;' scroll='none'  width='100%' height='100%' src='' ></iframe>",u="<div class='k_box_size' style='position:absolute;z-index:**********;width:100%;height:26px;top:2px;left:0;' class='loading'><div class='k_box_size' style='filter: alpha(opacity=50);-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;position:absolute;top:0;left:0;width:100%;height:100%;z-index:**********;background:"+h.config.loadingBackground+";'></div><div class='k_box_size' style='width:100%;height:100%;line-height:26px;padding-left:16px;position:absolute;width:100%;height:100%;z-index:**********;color:#fff;text-align:center;'><i style='color:#fff;font-size:16px;' class='fa animate-spin fa-spin6'></i><span style='padding-left:5px;font-weight:bold;color:#fff;'>"+h.config.loading+"</span></div></div>";function v(t,n){h.extend(this,v),this.jqObj=t.addClass("k_labeltab_main"),this.opts=n,this.wrap=$("<div class='k_labeltab_wrap k_box_size'></div>").appendTo(this.jqObj),this.title=$("<div class='k_labeltab_title k_box_size'></div>").appendTo(this.wrap);var i=this.title.outerHeight();this.body=$("<div class='k_labeltab_body k_box_size'></div>").appendTo(this.wrap).css("border-top",i+"px solid #fff");var e=null,a=n.tabs.length-1,o=this;this.activedItem=null;for(var l=function(){var t=$(this),i=(t.width()-11)/2+t.position().left+5,e=parseInt(t.attr("i")),a=o.body.children(".k_labeltab_body_item").eq(e);if(null!==o.activedItem&&o.activedItem.hide(),o.activedItem=a.show(),o.$actived.animate({left:i},220,function(){0===o.activedItem.children().length&&void 0!==o.activedItem.data("data").dataType&&function(e){var i,a=this,l=this.data("data"),n="function"==typeof e;if(a.children().remove(),i=$(u).appendTo(a),"html"===l.dataType)h.htmlLoad({target:a,url:l.url,onLoaded:function(){i.fadeOut(function(){$(this).remove()}),n&&e.call(a,l.title),h.bindTextClear(a)}});else if("json"===l.dataType)h.request({dataType:"json",url:l.url,ok:function(t,i){n&&e.call(a,l.title,i)},final:function(t){i.fadeOut(function(){$(this).remove()})}});else{var t=$(b),o=t[0],d=h.getUUID();t.attr({name:d,id:d}),t.on("load",function(){i.fadeOut(function(){$(this).remove()});try{$(this.contentDocument.body).append("<span id='_window_ifr_id_' style='display:none'>"+d+"</span>")}catch(t){}n&&e.call(a,l.title)}),o.src=l.url,t.appendTo(a)}}.call(o.activedItem,o.opts.onLoaded)}),n.onclick){var l=t.attr("_title");setTimeout(function(){n.onclick.call(t,l)},10)}},d=0,s=n.tabs.length;d<s;d++){var c=n.tabs[d],p=c.title,r=$("<a i='"+d+"' _title='"+p+"'>"+p+"</a>").appendTo(this.title).click(l);d<a&&this.title.append("|");var f=$("<div class='k_labeltab_body_item'></div>").appendTo(this.body);(c.actived||0===d)&&(e=r),c.url&&""!==c.url?f.data("data",{url:c.url,dataType:c.dataType,title:c.title}):(h.scrollbar(f),f.append(c.content)),c.iconCls&&""!==c.iconCls&&r.addClass("btn").prepend("<i class='fa "+c.iconCls+"'></i>&nbsp")}this.$actived=$('<div style="height: 6px; left:0px;" class="actived"></div>').appendTo(this.title),null!==e&&e.trigger("click")}return h.Labeltab=v});