<div style="position:relative;">
        <div id="k_08105034167LMVRYLC3N" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(237, 243, 250); padding-left: 12px; margin-bottom: 11px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250);" id="k_08105034189841QHS4SW">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250);" id="k_0810504164RVP5ABNEFU">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08105151442SJGEYXCSZ">​<img id="k_08105151459I93YMJ8AH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAkklEQVQ4T2NkIBMwkqmPAaxxFcMVHnaOX1MZGBjj8Bn0/z/DHOaff/N9GUy+gTVuYTur+ZeRKZfp598SkCA2zZsZznD95WCqY/7xr8eXweTNqEakUMIaOODoYP81gYGRMZno6NjAcW4FAwOjIoaG/wzPwGKMDFKYhv2/z7iR43zbfwYGZ1I0Mv5j2EBZkiMnvQIABOd1PA3s9h4AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250);" id="k_0810515146XO1N9AUWD6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-style: normal; font-weight: 700;" id="k_0811010713OKI8N9QPKU">一、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-style: normal; font-weight: 700;" id="k_0811010714CD9S5X7PD4">权限设计说明</span></div><div id="k_08105041664VBANUKYAT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08105928729Q6W3HA9MC">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810592875GVWXWIBUCB">​<img id="k_08105928753JZ57UFTAM" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABRklEQVQ4T42SP0sDQRDF3+wa5BRzib0E/ACChWAhiiDYRAtRG0shRQolCgl+gShJQKs0goUptEgKC9FCKyu/gZVdGhvvgvgPM0+SYAxC7txqdt/7zcwyI+g5NuetU3kMkboaJHEQe+zVe2PpXjL+qInwRoBJAqpqVlCKXvwDpGMj3jkgywBum59cxVHcCwfbDgrSGEZZXvoBP++/rYY5/+gdcI0W440Fo8xDMAHIthbcclAuwe5bwtr3U0Bmu0Zys1mMnwSCNuvVQFwBSEEwRdBXNYsouffBFVvqzuuYsR93IpIg8aC0cyiNPIWD2ed5Q1yKiEPiTIvuBiAMBU3W2xMg35lI+P9aNkGKEet6NYgskWyImDTBpAI1FGLV/guQo2vpXwOYbhcEfaHZahajlaB2BRk6dsCvEpwBUNGvwX0cDtXD9uEb4OF2DwXEIFYAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810592876G4E4EB3SZB">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810592873IQ77P3EUBZ">采用经典的RABC（基于角色的访问控制）设计。</span></div><div id="k_0810521867VBWWGIJON2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810593982S7K41MAD7W">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810593984VGPT139W7Q">​<img id="k_08105939853QVOILVI7D" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABWElEQVQ4T42SsUtCURTGv++pgQgFLU7l3BI0BA1RBEGLNUS2NAZveEbxSpLQ4pFmg0ZC+hKChhxq0KEhaqipqf+gqc2lKaMp9Z6wMiTQ553uvd/vd+65cIi2FY7sLhM4E0pF1dzB06z10p6379k6mKY1WHM17kGMAVAiXMxn9q97EE1vze27ArggwIOn5lrKZq03R/EXoGFYPtu2PjoJrfu/Vp3A//m3GAqFXP7hkVnRVIrgKBQ2ckcJu1sxhrdiAZAXJKdaoACr+XTivKu4FomXhbyFQCcxLpAqhHP5TOKpq9gMjc3YEDU+kgxA8FyHa7qQsV6dxe29GU2pG5BeQC5z6eQKAHEUw5H4DslUE+zlf02Ouq57PP3+Mol5CN6VpgwqLSgiZfsoWeo4AHo0OtCnPHcAJ35elKoGrp+kE8Vu7dI0Te+n21eicFIoxUZdDgvHBxWngfgCtWZ3DzH91gkAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810593986JUUDEHG65Z">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810593982YYPTJ2ZOUW">将系统功能[ </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810525761FQURWLXEWB">functio</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810525762MUIUYDGKK4">n ]与系统菜单[ menu ]实现库表层次的管理，进而达到角色拥有功能，功能追溯到菜单的逻辑关系。</span></div><div id="k_0810525898531V5UZHY3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810594849DTNNWRXF18">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08105948524M8JC2M3BT">​<img id="k_0810594853BBN2PNADBQ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABSElEQVQ4T42SsUtCURTGv+9eHDTCaC6Etp4RRAQNUQRBiwVFPHgKLUFDQ839A/YfBBE0JGq8dHCIGmpqai4fERQtLk0ZRYF6T5hkEviedzr3ft/v3HPgIzqOzlm2CA9BVIyoBFK3T516Z832xbUGVZ2XBCdExBjIKlJeqQdwKKzr0ROAyxC5akQ+1rDy/BoMNh0C4tTqg+29dwN+3/9GDXL+01ugC41afEGBaRDjgOyYZHnfrxeRHY1p6GMQs20jsdFw7o58QZ2NF0GcQ7AJckqAqqFZhOPd+P/YVPNjw8rINcmYAPem/jmH9ceXYDAbn1fkGYGwQPLGKadASCCoctYuodI/xh72a9kOJkO6/6sIcElE3gjZEjBhlBTheIXuAXBHoroWuQAx3coAqhTZbiTLGb9xCbcZtYGCiMwAyJhQbQ/2QyUoD98jLHQPe/elZAAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810594854ASAK85LZGX">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810594850EQKQR3KTWI">系统功能[ function ]采用功能标志码[ funcCode ]标识一个系统功能，该功能也必须归属于一个菜单( 如没有对应的菜单，则创建不需要呈现的菜单进行承载 )。</span></div><div id="k_0810512581LYOZ8XSTON" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081059567725II66QWOK">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810595680C1AQA31WR4">​<img id="k_0810595681DYM88FQY4T" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABUElEQVQ4T42SvUsDQRDF39t83IFYqrWtCCbGxjIpBCEfSiA2KgELK8Va/wBBUQvrIGpnEw5RohZRsI4nCrE0WNhYKAjJJbkbSTQhCN651ey+9xtmmUf0HEM3owLnEMKybWnZNEZee/Xemp2Lgad+aNVjECkRaShbxZLN0K0neILHYFBrbpOyCpGcZQXXMhj99ARbBoHwGs9aFMO1v4DOe3dUL+NvvQ0KRJ0GzIijsE7IlIian6mH8m7NaOBhCHpjC+Bi1yiIp6zwmTuolXYIVXTgZEmmBVLxOzIdr0fKrmBLPEdpoK7DIDgJwZVjITOL8LsnaATMsPikQGAQwGayFtogKJ5gPmAuKJ8ctY3/+F/LxiLE/6Hd7ZJcEcgLhUukxAAUkrXxmz8DcIH7vqpu5wjO/aymAlHLKWvs0m1cfketsQdKgsJ9ZdkHCUy8eQXiC16pfw+/Ifg+AAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08105956839XMC7P5I92">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810595678XRE1J9ILYS">功能标志码</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810545237N6IOORHG1B">[&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810545238KCO7LQL927">funcCode ] 可关联到UI&nbsp;按钮的cmd参数，按钮通过cmd参数匹配是否具有对应的权限，进而设置按钮的可用性。</span></div><div id="k_0810512537PNNDPKVLK8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811000729NOCF6XVK6N">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0811000732938PJP9K9W">​<img id="k_08110007332J5SEMA51H" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABMUlEQVQ4T42SPS+DURiGr6fVDmLRL6F9K8HArInR0Nkk8QNId4OIRGOUELGwSmz+gZD4SBoSm0Vilb5C9EMYpOKjt1SpkrSvM53z3Pd1zvOc3EbzKuSzwCLGPX7/FKH46S+96WCN/UN+kFc7BPoBgeaJJte8wUc3xIv2wVJIFwQCE3T3XnmDNYfk46kQpavnrhXwXf9p1cv5R6+DUpCym6HKHGZJjA0izmy7u4ySO4bYBoYbRrFCzFloDxbdMyCHNInZAKKCaYZocqc9WFOL+RSyPYww6BbrSBPpu/wH6GZAm2BBpBOizjhmVW+w5G4hpusf5T1fzWboppPSWw5sFOkZs1WkNPh2iSWWWwegfJ3gXccYQ18vVvBpnbCz1K5d4zNqHCCNYHaEfFli8XOvPHwAqjdlD8pbQEcAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110007345TQ6P66N4H">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811000730K7HI69V1IV">功能标志码[ funcCode ]是一个可重用的权限标志，可以通过url上的[</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(255, 255, 255);" id="k_0811072007SBT3243V77"> authctl / cmd</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(255, 255, 255);" id="k_0811072008UGJ4O426WK"> </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810581345MV6X31PSLG">]参数复用别的url对应的权限功能（适用于页面内部编写的请求）。</span></div><div id="k_0810512554AVSAOX6FK9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110021209GDD5TJ13U">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0811002123E1J68Z6M69">​<img id="k_0811002123CQ91XA71FJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABOUlEQVQ4T42SP0jDQBjF3yskBUfFTRRUis4Kjg6dnQST66h0dxARLJJJUMRFV8EtbaBTQRT8A0XBzUWw3dzExa1UEtInkVaK0KY33d37/T6+Oz6ib1UqUanT0T6gr0wms+E41lN/3r9n71Aua04K70jOAJDEXWOsk1QxCDQex9ENiWUAr5K1ZgzfU8UE8DxlcjlMFgr8HCT07v9aTQP/579iEMiO46hIakfiNIkz17W3hxWj74crgC5JLvRASUfGZPfSxGdSdQnrJGcBtEltOU7WHyomoe+HyU9ek5iQ9EHaedfl2yhikcQ5AFvCY7NprXoeO6OIFyQ2E3CU9yUcazWNtVpRHcCShG9SxwDzEq+MsQ4HDkC1qqkoCh8AznehNqDTRsM+GNYuu6N2C2CR1L3EkjH2S9pA/AAdgYYPqx5LCQAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110021244YHVD9KHKP">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811002121WH8ELWFTYF">请求过滤器从url中提取出controller及cmd参数(如果没有cmd参数则取action名)，利用controller/cmd对比用户的权限集合是否存在对应的数据，存在则具有访问权限。</span></div><div id="k_08105125882VJM8CDGSK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811003322484533OWDP">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0811003324VCMAEWZ9KQ">​<img id="k_08110033257W5K28TLT1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABRklEQVQ4T4WSMUvDQBiG3zfSJDgqbqKgDjpbsMniUBNwcBL8AUp3BxHB4igo4qKr4OYPEKSp1kKxTQuCS8HVSXFxcGmatvdJrZUgmNx0d+/zHN/BS0SW5+t5CvcBfoDdDdfqVqN5dM/hoVQ1ZntkCcQ0IELBrmO3TxLFQg1jGkwPRBoizR65tmoFL4liHxCBVmpgYiWD9/+E4f3vqEng3/xbbDahv34aOSF3AJmi4My129txj9HzU0sQ7ZLkfAQ8cq1gL1Ys+mZdKBUqrIOcAaQFkS3XDq9ixX5YbKTSUCMFAOMieNOoso4VPieKt76ZU5BzgjqABycTLJNQiaJX0y9IbfMHTPxfn+P1I0b1jlkhsAiRABqPIcgC6sa1wsN/C1CuYzIUo0xwbgBJS8BTNxMcxI3LQdWMOwALIO4pKu/YnaekQnwBHVNyD4AoXrEAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811003326DO7CJKK9LO">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811003323MRNL1FDD3N">采用开发即定义权限的设计，在datagrid.xml中，设置datagrid对应的controller，定义工具按钮对应的权限码（cmd参数），debug模式下，系统会扫描这两个参数形成写入function表的权限数据。</span></div><div id="k_08105126218GKS6WC8M2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810512622OFG1RQUKIJ">​</span></div><div id="k_08105126567XLH5ZFQSZ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(237, 243, 250); padding-left: 12px; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_0810512657PY9POO1KW8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_0811010595DDBEJQJYP7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08110134773PM77F1IGP">​<img id="k_0811013477CDX7M5VREY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAj0lEQVQ4T2NkIBMwkqmPAazxv9Bbvm98X5cwMDD64jXoP8Marh+sCYwvJb6CNX5RfKLP8O9/OfcP1lSQIDbN/8VfcH/j/NXzn5G9gee++MtRjUihhDVwwNHB+20eAyNDMNHR8VXh0c7/DIzyWDS8gIpJoMsxMvx/yPhV/snU/4z/nUnRyMTAsJ6yJEdOegUAa/KBP0TF4ocAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_0811013478H8XNQHKJFL">​</span><span id="k_0811010593HOP9AYV65D" style="color: rgb(3, 166, 44); font-size: 14px; font-style: normal; font-weight: 700; background-color: rgb(237, 243, 250); text-decoration: none solid rgb(3, 166, 44); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">二、​请求权限过滤流程图</span></div><div id="k_0810512689S34AFHLZZK" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 27px; width: 956px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08105126907EHHZE31LF">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811020987LBEPVXW19W">​</span><img tabindex="0" src="/bui/tmp\b331f9a059aa11e99e913c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left; border: 1px solid rgb(75, 250, 238);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811020990UBX6M4QOF3">​</span></div><div id="k_0810512623HONA569C3D" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810512625QU9N8OWGT8">​</span></div><div id="k_0811024290MD8KOSGTFL" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(237, 243, 250); padding-left: 12px; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811024291T1EOCFS5HM">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811025675LAWCUIZ329">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0811041679G8VMH93Z8N">​<img id="k_0811041680ESGFWYZAF7" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAj0lEQVQ4T2NkIBMwkqmPAazxv9Bbvm98X5cwMDD64jXoP8Marh+sCYwvJb6CNX5RfKLP8O9/OfcP1lSQIDbN/8VfcH/j/NXzn5G9gee++MtRjUihhDVwwNHB+20eAyNDMNHR8VXh0c7/DIzyWDS8gIpJoMsxMvx/yPhV/snU/4z/nUnRyMTAsJ6yJEdOegUAa/KBP0TF4ocAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811041681F9NEPLKG79">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_08110329387GA3JKN334">三、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_0811032939B87NC3BH9A">按钮权限过滤说明</span></div><div id="k_0810512669XCR168RY7A" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811052434UQLTNTS7A8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0811052438IOVGAXV5Y7">​<img id="k_0811052439C4FQL4HZZA" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAaElEQVQ4T2NkIBMwkqmPgVGC4b/ZfyYGa0YGBjZ0Q/7/Y7j+i4Hh4HsGxo/ocowSzP93MDAwuOOy+d9fBptXDIxHRzViCSEaBA7T/0UMjAyxWKPjP8PDv/8YAl4zMF7AiA6yUw7dNQIAWxM7D0xSQzAAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110524401S5N4L9GCV">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811053916HAV2C5EHQ5">在"xxx/page/pageNam</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811053917Z2YYX3TZOG">e"页面请求中，如果存在gridid参数则通过DataGridHelper得到列表及工具栏json。controller里hasPrivilage(...)负责对比按钮cmd参数进行权限判断。</span></div><div id="k_0810512669XCR168RY7A" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 25px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811053776I39RZTDJQG">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0811053778E5CWBIZS7H">​<img id="k_0811053779G38M873ZLV" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAZ0lEQVQ4T2NkIBMwkqmPgTFmR0wcw38Gn/8M/9nRDWFkYDz/h+nP3BXuKx5jyEXviH7ByMAojsfm1CUeS+aMasQeQlQOnJjtMTcZGBnUcETH73///0Uv81y2GjOOyUw65Cc5Mi1kAAAbnDcP+N8otgAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811053780JJCHDFS8PE">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110539209FEJAE4OHS">在"xxx/page/list"列表请求中</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811053921585W7OIV3Z">，通过提取请求中的gridid获取行按钮json，同样controller里hasPrivilage(...)负责对比按钮cmd参数进行权限判断。</span><br></div><div id="k_0811031844EYVNX99IXC" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081103184589RWM3J5BX">​</span></div><div id="k_0811031894N2JBLK6PZ5" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(237, 243, 250); padding-left: 12px; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811031895M7SXGXD27J">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110327322NWZ2ZPB6R">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08110425996J8IA9E9NY">​<img id="k_0811042600547UVXREHN" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAj0lEQVQ4T2NkIBMwkqmPAazxv9Bbvm98X5cwMDD64jXoP8Marh+sCYwvJb6CNX5RfKLP8O9/OfcP1lSQIDbN/8VfcH/j/NXzn5G9gee++MtRjUihhDVwwNHB+20eAyNDMNHR8VXh0c7/DIzyWDS8gIpJoMsxMvx/yPhV/snU/4z/nUnRyMTAsJ6yJEdOegUAa/KBP0TF4ocAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811042602TSIVXXULEU">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_0811035459BTYJZYIECR">四、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_081103546071BBEL6OP6">开发注意事项</span></div><div id="k_0811031826XUR1NUP2DS" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 26px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811073641NO1MEDQXSJ">​1、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110807002H2CCGWPF6">一</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110807018EL4EKMF1K">个模块一个菜单（一个controller），该模块下的功能点都应该配置到该controller下，不建议复用controller（通过不同的参数）来区分模块，应该通过新建controller来定义模块。</span></div><div id="k_0811073655LAPYLVZD6Y" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 26px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811074168BNHJZ1SUZE">​2、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811073656DFMOU2E8QT">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811073656831AN92X6M">&nbsp;​需要权限控制的工具栏、按钮应该配置在datagrid.xml中。&nbsp;​</span></div><div id="k_08110741753EZH4UECQG" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 26px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811074667627NKZPTCG">​3、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110741766AFFIU9HL9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081107417711Q4UFDTCT">datagrid.xml中配置的url与按钮cmd参数注意与功能表（function）定义的功能码对应上。</span></div><div id="k_0811074677HMPV5G983Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 26px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08110752425YYWS7CDNC">​4、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811074678F3273VZB74">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811074679DAU626DCE8">&nbsp;每一个请求url都应该带上其对应的cmd参数，不带则与action名称为权限码，复用别的url权限时候，请带上&nbsp;authctl&nbsp;/&nbsp;cmd&nbsp;参数。&nbsp;</span></div><div id="k_0811075251PKFOCAE4KH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 26px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081107525287NTJ92ECY">​5、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081107525332LOR17Z9D">​页面内部编写的不属于当前访问菜单的controller的，应该在请求url上带上antuctl与cmd参数，以便将请求的权限信息修改为当前菜单对应的权限数据。</span></div><div id="k_0811034306FA3OJ2YF8T" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811034308BEN7NF7TPD">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_0811034374GN63FBAHAV" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(237, 243, 250); padding-left: 12px; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_0811043319VSG5V8UEY9">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08110433226O8LC9G1NB">​<img id="k_0811043322NW5CMMF3ZO" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAj0lEQVQ4T2NkIBMwkqmPAazxv9Bbvm98X5cwMDD64jXoP8Marh+sCYwvJb6CNX5RfKLP8O9/OfcP1lSQIDbN/8VfcH/j/NXzn5G9gee++MtRjUihhDVwwNHB+20eAyNDMNHR8VXh0c7/DIzyWDS8gIpJoMsxMvx/yPhV/snU/4z/nUnRyMTAsJ6yJEdOegUAa/KBP0TF4ocAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_0811043323LKSQGWNS7V">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 166, 44); background-color: rgb(237, 243, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(3, 166, 44); vertical-align: baseline;" id="k_08110433206YQ56CKE7M">五、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811034375OVTV76WG6R">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811035226Y341M6ZS8E">​</span><span id="k_0811035225133B2L5849" style="color: rgb(3, 166, 44); font-size: 14px; font-style: normal; font-weight: 700; background-color: rgb(237, 243, 250); text-decoration: none solid rgb(3, 166, 44); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">权限排除配置</span></div><div id="k_08110344259N9OUBSAE7" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 27px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811034426JG7JENQEA2">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811084114QFIY376QK2">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_08111100669S4CWVOA62">通常情</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0811111790S6SVT1JBIC">况下，某些</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0811111791KBQNHX3PX2">请求是不需要登录即可直接访问的，某些请求是登录后可访问，但不需要进行角色功能过滤限制的，对于这些url，可以在except-validation.xml进行配置。</span></div><div id="k_0810512609JPV3HI498Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 56px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 11px; margin-bottom: 11px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08105126108WY9LL8GS3">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811085544EOFJTQ5IH7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(8, 44, 250); background-color: rgb(255, 255, 255); font-style: italic; font-weight: 400;" id="k_0811114217VS5N54L8OB">如"/main"co</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(8, 44, 250); background-color: rgb(255, 255, 255); font-style: italic; font-weight: 400;" id="k_0811121270YRLT5RLE1W">ntroller下</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(8, 44, 250); background-color: rgb(255, 255, 255); font-style: italic; font-weight: 400;" id="k_0811121272Q2ZA3JARV5">的所有请求可以不需要登录直接访问。</span></div><div id="k_0811085618UTCLH4VU6O" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 100px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(8, 44, 250); font-size: 14px; font-style: italic; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0811085619TMY5RR7KP7"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span id="_31222853867RU5ZSXZAD" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&lt;controller</span><span id="_3122330123AEDXAU7OJH" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; color: rgb(247, 5, 21); background: none;">&nbsp;</span><span id="_3122330125J2OMG6BT13" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; color: rgb(247, 5, 21); font-weight: bold; background: none;">access="true"</span><span id="_3122330126OR9W619P1I" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; color: rgb(247, 5, 21); background: none;">&nbsp;name="/main"</span><span id="_3122285389LT33RB71IY" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; background: none;">&gt;&nbsp;</span></div><div id="_31222902277MJCDHLXYN" class="_section_div_" style="padding-left: 60px; line-height: 30.8px; white-space: normal; background: none;"><span id="_3122290853YTGAYAEAMJ" style="font-family: &quot;Microsoft Yahei&quot;; background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&lt;cmd&gt;</span><span id="_3122290855E5C2EXZWJX" style="font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 8, 24); background: none;">*</span><span id="_312229085553O34YUEPG" style="font-family: &quot;Microsoft Yahei&quot;; background: none;">&lt;/cmd&gt;&nbsp;</span></div><div id="_3122290586VAO7GESA49" class="_section_div_" style="padding-left: 60px; line-height: 30.8px; white-space: normal; background: none;"><span id="_3122290587G83P62RQRF" style="font-family: &quot;Microsoft Yahei&quot;; background: none;">&lt;/controller&gt;</span><span style="font-family: &quot;Microsoft Yahei&quot;; background: none;">​</span></div></pre></span></div><div id="k_0811085706P9JVAK5S1R" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(8, 44, 250); font-size: 14px; font-style: italic; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0811085707FDXX3VQ2X2">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811090458XBRMQKL3NA">​</span></div><div id="k_0811103105GIFDBGGLIP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 56px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 11px; margin-bottom: 11px; text-indent: 0px;"><span id="k_0811103107GYWIZRQLVX" style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic; text-decoration: none solid rgb(8, 44, 250); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">如"/org"controller下的query登录后，任意用户均可</span><span id="k_0811103108RAMRUFSXQU" style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic; text-decoration: none solid rgb(8, 44, 250); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">以访</span><span id="k_0811103109O5SCCLH9NR" style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic; text-decoration: none solid rgb(8, 44, 250); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">问&nbsp;</span><span id="k_08111031101VJO3TACCX" style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic; text-decoration: none solid rgb(8, 44, 250); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">。&nbsp;</span></div><div id="k_0811085706P9JVAK5S1R" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 97px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic;"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span id="_3122305436YSXX6PDYW5" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &lt;controller&nbsp;</span><span id="_3122305437ZQLTQZC26X" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 3, 11); background: none;">name="/org"</span><span id="_3122305438CLXA87OHQN" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; background: none;">&gt;&nbsp;</span></div><div id="_3122303864XYO5HVYMRM" class="_section_div_" style="padding-left: 60px; line-height: 30.8px; white-space: normal; background: none;"><span id="_3122304851YSTK4H7RTV" style="font-family: &quot;Microsoft Yahei&quot;; background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&lt;cmd&gt;</span><span id="_3122304852Z8KLTNBIJW" style="font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 8, 16); background: none;">query</span><span id="_3122304854BF5X76UYAA" style="font-family: &quot;Microsoft Yahei&quot;; background: none;">&lt;/cmd&gt;&nbsp;</span></div><div id="_31223041942CYRYXSAL2" class="_section_div_" style="padding-left: 60px; line-height: 30.8px; white-space: normal; background: none;"><span id="_3122304195BX77KCQKYN" style="font-family: &quot;Microsoft Yahei&quot;; background: none;">&lt;/controller&gt;</span></div></pre></span></div><div id="k_0811100409PYJ9XJ1XG9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic;" id="k_0811100410M7YLC5A77C">​</span><span style="color: rgb(8, 44, 250); font-style: italic;">​</span></div><div id="k_0811090560P16JMD83M8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 56px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 11px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic;" id="k_0811090562KPSYKPIWXD">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811092456PJQO2JY8LE">​</span><span id="k_081109245392JF7BBTZU" style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic; text-decoration: none solid rgb(8, 44, 250); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">如登录后</span><span id="k_0811092454OIJM4TEBIT" style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic; text-decoration: none solid rgb(8, 44, 250); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">所有用户、任何地址均可以访问controller基类里面的checkFileCreate</span></div><div id="k_08110905189T68PGZFL2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 99px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(8, 44, 250); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic;" id="k_0811090520K22LIWWT8U"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span id="_3122322494QYW3D5RNAA" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &lt;controller&nbsp;</span><span id="_31223224953QQNEDGM4D" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; color: rgb(250, 5, 13); background: none;">name="*"</span><span id="_3122322497LDNBNCG7N9" style="white-space: normal; font-family: &quot;Microsoft Yahei&quot;; background: none;">&gt;&nbsp;</span></div><div id="_31223213659XH8QJKJVR" class="_section_div_" style="padding-left: 60px; line-height: 30.8px; white-space: normal; background: none;"><span id="_3122322991DCF9LG48NA" style="font-family: &quot;Microsoft Yahei&quot;; background: none;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &lt;cmd&gt;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 13); background: none;" id="k_0811111795BRVKXE1NTW">checkFileCr</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 13); background: none;" id="k_0811111796SI2XI65APR">eate</span><span id="_3122322993GND8GM24R9" style="font-family: &quot;Microsoft Yahei&quot;; background: none;">&lt;/cmd&gt;&nbsp;</span></div><div id="_3122322028FVI3FK75QO" class="_section_div_" style="padding-left: 60px; line-height: 30.8px; white-space: normal; background: none;"><span id="_3122322028CA8KMO4223" style="font-family: &quot;Microsoft Yahei&quot;; background: none;">&lt;/controller&gt;</span></div></pre></span></div><div id="k_0811084445VPNNWK5RCE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811084446CKRNBBYXBB">​</span></div><div id="k_08110844921246DMVDXA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0811084493H1GG3ZWBMH">​</span></div>
</div>