<div style="position:relative;">
<div id="k_0810225990UDK4J97W2Q" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(246, 252, 242); padding-left: 12px; border: 1px solid rgb(224, 251, 252); margin-bottom: 11px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242);" id="k_0810235165UXQB33ZZQ4">一、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242);" id="k_0810234318AQABQL6RXS">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242);" id="k_0810254255COQJRCPUM9">Xls导出</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242);" id="k_0810254256YJYUPXK2WF">导入设计/</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242);" id="k_0810254256M9SXENQJ2G">功能说明&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810251388OCNFZ3WAEU">​<img id="k_0810251389YZ9TGM2CCT" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABRElEQVQ4T5WSv07CUBjFz1erVdrgghQSH8HFwTfQKAuJLC2Tf1YdHBzcXNwcjD6Bm1QmXVBfwAcw0c0NLSwOehsx0mNKoCkoEe56vl9yzrlHMOYLXN8huSHjcMr1twGcgXwcGexBJJ7NLyyPBMYQ0BZqrulla/+CymkWKGFFAIvkgeXlj6N4wk1Oq1Zz1rqwG4N5E1AaZC01kyvJuXx2wA/HPwGwJdDKkYUe3HL8hW9hDZB5AC96G2tGNffQ00W5/hWAIsE3hFrZurRvue5nA4M3gCwCCJMWYzB5FMEaZJfkHkSWukf3qWBiVa7n3pNROuUM2ErqipSS5dl3g/njVvuK6F4R8MyKXRYIh4KRoNzXHUCisiZB1HXoK4aXefprXX3/yAKNIN2oQFAEeWR6+cNhk/w1gKgsNYVTLdT3U9VMfRj4A7oQkkNsrfsAAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242);" id="k_08102513905BMY4LW6IT">​</span></div><div id="k_08102345838JFEBXOOTF" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08103049544UCNTLZ6Z5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810304956GA67QHYLOF">​<img id="k_0810304957UDSK98QXKA" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA2UlEQVQoU5WRsUrDYBSFv9OINE/QRRC6CQUfoi3ddfIBXBWndEn+GzLWwaVbn6AIjm6uruJQ6CZOBUcn0ZDyCy1pJMWc7XLPdy/nXtFQ2vizLDvJ87wPtIFX4MnMfqrzZGYHwAS4AlolwyIIgrM4jpdlSGmaXhdFcQt4sKrnMAxHURR9bhp+wwtwWhPlS9KFc+6hDKyAzp7sl2Y2KwNvwHET4B44rwE+gIGZ+av9ymfoAY/AUQX6lnTjnJvuXMkXZtYF7oAhcAi8SxonSTKXVPwBmjx7++n/Qms37TsNVRlMZgAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810304958NCX8CP8JWS">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810304954E39F3O5OBQ">xls导入导出功能依赖于poi开源库。</span></div><div id="k_0810262749A6WOMLYDG8" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810305922W32EIOMT7X">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810305925CXIFU36RWN">​<img id="k_081030592658K4GU2REB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA8UlEQVQoU5WRPUoDARSEv8nPRvAANoKg2UIQvICNGtdeKw9gq9h5By1s7DyBCJaSKFraSgohsCFiJVhaZTfZEYQNa4yQvO7x5huYN2LGUa4P7sNVk22XYG5otQfV+JFNBuN+4olKNamfCY4klXKB8asqw73+Vq9ThFRrhceGc0Fl3M32czJf3mWj85XfFDTDF4n1SVFs95EPkqh7OwJqrfoHaOG/7Bk+TKP4qgi8gZamBoJmeCOxPxGwP10eNpJGr13IsLImdIe0WIQMqfFJGsWXv770szyEy0HmC2AHEwi9m+w0ibrXCP8FZmh71PS0zDcxNVUNnFloMgAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810305927E9QI8UTTW5">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810305923C5GG8YDZRP">导入导出是一个通用的操作，是xls行数据与java实体bean之间的相互转换。</span></div><div id="k_081027210598TGVDDNTX" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08103108224611HXCN1B">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810310825DMIJZZPKJ8">​<img id="k_0810310825ATP54WBXDT" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA00lEQVQoU5WRLW4CYRCGnxeGgEFh6B3qkdQ0xaEwGBSOhAO0smcg4QCkYm/QhDQ4BAkag0Rgqpv9dqehCd3lL2FHzeSdZ35FQdMx36GJ2SPuVZJkC2wE6Xk9OZQolwdIE6RaLiEijkeCfR6Sm70AEVL9Yro0nZIkY8HPUZNXKjOgf3UV9z0hdATrDDBbIrVu7u7+pBAWeeAT6bkI8Ir0fmOkFSF0BbusAzxg9oHUPoHcv4GeQpifXOkQODQwGwNDDj58Ib0Rxyv9yZn9P+7ehxcGfgEZk0MNzhPIoAAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810310827777FKDHMEG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810310823UTQDZ41W4U">导入功能，可以支持数据验证，并支持错误提示返回给客户端；导出功能，需要支持按条件查询导出数据。</span></div><div id="k_0810275639FR8S6PQXYW" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810311619W5ABT3CQBH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08103116224V3MYIG4MK">​<img id="k_081031162359O9H35VX4" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABAUlEQVQoU2NkIBEwwtS3F1gosPz5Z/WfkZGD8R/jVflXJ86ErWb4i24e46pQBuaH4uZVDAyM9QyMDMwwBf8ZGE79Y/wdWjHp7CNkTYw9eeax//8xzGVgZGTFcN3//zu//voT1DDr7DeYHGNXjsURRkYGaxxe+f6P8b93+aST+xEaci0eMjIwyOHy+////0PKppxcC9fQnWNxnYGRQYNoDV25FrMZGRhSsGn4z/D/CctfJseiacfvwG3oyzRR+svMspOBkUEFTdPP/wz/Essmn1qOEkogTmeOoRQTI1vH//8MwQyMDBwM/xlvMjH8KyyecmoXIwPDfwwNpEQ2PKaJ1QQAdcRXDW+k/R4AAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08103116241WQS55TIMF">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810311620AFV3BI8HFX">导入导出应该设计为接口，当controller实现该接口时候，则说明这个controller具有xls导入导出功能。</span></div><div id="k_0810234732UDX2DR76PM" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810312782HQQ13F51PB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810312784RZTBUDKD87">​<img id="k_0810312785RE6ZW3ZMZX" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA60lEQVQoU5WRMUoDYRCFv7f7r8WuQojoBvQClh7AKqeICGkUPEDQIgewtrEQ1MbcxcoD2Nik2C1CQHdjsbuOWCRuEgPJdMPM95g3T2xYmu4nuX8h804MtiR7cyrvd0OGi3oaTTgsv90Tog2aCYAlwvr7UfVYh5RmwTOyDshbVDN4R5y2wuJlOlOauyHo4H8rVmLcxttl7w/I3AfSzirvBg+tqDifAUnmRpKaawNpHrwCxytOykDXcVTc1TwEl5jdIDXmIatAgzgqunNf+m3SL/+MyrtCdgT4ZoyRDfyw6u+JzyVgk7BrQa2H/QDMWUsNVueP3QAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081031278695VNC11EAU">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810312783418VQYMHLN">导出功能应该支持编程方式自定义复制多表头、支持按文件模板方式导出、支持导出文件名定义。</span></div><div id="k_08102347894FVL1CMCPI" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810313941E7FG9VGGDZ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08103139443YHCHM8LEK">​<img id="k_0810313945YE1BRJ6C7G" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA3UlEQVQoU5WRoU4DURBFz32bCgyiYl/LJ/ANKEwlpgIFKPBVTfgBTBUGDKqOH6ioqUCTYKvAsCn7AyTddwliNy0tSTtuMnNm7swVe4bq/ohzwXGCVoKPEuag9HeewCFmnMvcI9p1g82TEsMCfa1CiviUwFiiu6HOjIrELei7rikGP0jcbDvFZu5Ef4HeGqCTeQL0/rs9VZws0Msq8Az0dwby4EEQo62SYFpVXJTos9mQ46iMR8HZGmTeSVwWaLb2pd/kELcPAteCK8ORxGRZcVfCK8gbwD5mN07vCv0Ajz5EDZtfvnQAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810313946SRORRMJRNF">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08103139424K68X2EHQF">支持数据量大的时候，自动生成多个sheet功能，按60000行分sheet规则。</span></div><div id="k_0810234740JRCM9A7PSV" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810314954TNUJQB3OCJ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810314957PSD5KOZJO2">​<img id="k_0810314958NVN4HI6FKX" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA/0lEQVQoU5WRv0vDcBDF37uY+qPabHVoMRkdRfBPKIiLo5OJzo7i5NJZ8G/QioPO2qHQtUMFJwdBEWMGu4YWgpTwPXFobKuF9rbj3ucdd4+YsTjQ18pJyUi6KTDzYs29vr3nn6qgGfdjFSruWvcIoucE7EygaFqw/f2PfGcYYs3r7irMDYCFcTcFbgHn4DDk12DGCzeuk9j57xSF9ixIxQ+ddgZcevEzgPVJtwu47YdO43eDFz8Q2JoecOMzEicTgBf2c5XgcynKNlyXknJq9+8AbAxDqpqIWntBVLgf+dJPc7XaK5rF9FSBgMplEI80cuxHKy2C+geYJews6WmhbzfbTQ0J38TYAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810314959QEPGZPDVGC">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810314955N8H3UJEX55">导出功能支持根据数据，编程方法控制单元格的样式（字体、颜色等）。</span></div><div id="k_0810234740JRCM9A7PSV" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810320446SGT3VD71V2">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810320448IVFA9WWRG1">​<img id="k_0810320449BTXVEI3RU5" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA/UlEQVQoU5WRMUvDcBDF3/sbRCi4ugiCk0Khg4RukljFXSc/gGulQ1rHriYiLm5+AhEcnUyziKRFkA6Cm3QqOBaEov6fODSt2kJ723Hvd3fvjpgxONBvnjyuO8aWIC0Apm3fe3FS9z//9qNXbzgml4sglEGYoUDPsmavUXNfRiF6YfOI0ClJZ8x2D327uHtfW+sNavSj9IlgYYKVPqCDOCjeZMBWmHZBLk30Lh3G1eLlEIjSV4ArUwN+2LwmsT8OkPBmOLd9F2y0swle1MpT9pbk8igk4ENiJam6F7+u9JOUzlqr+tK5pB2C84I6Ao+TwL0CqX/ALM/OPj0t9A3vXVcNQzSWOwAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08103204506K5YRLRABE">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810320447FUQFQWZMDS">导出功能支持添加总结列、总结行功能。</span></div><div id="k_0810234723VEA4BZZXC7" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810234724LOALN65BWM">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_0810234324ZS88INTSGD" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(246, 252, 242); padding-left: 12px; border: 1px solid rgb(224, 251, 252); margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(53, 0, 158);" id="k_0810234325GER2FK7T6O">二、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(53, 0, 158);" id="k_0810234579HD8ILSI394">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(53, 0, 158);" id="k_0810404972DWZOIH4VJ6">导出</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(53, 0, 158);" id="k_0810404973QDMTTRIIDD">功能开发说明(以FuncController为列)&nbsp;</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(53, 0, 158);" id="k_0810404974JD3TXJTZU7">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810255691HA54BQCUKN">​<img id="k_0810255692LA67HWLPR3" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABRElEQVQ4T5WSv07CUBjFz1erVdrgghQSH8HFwTfQKAuJLC2Tf1YdHBzcXNwcjD6Bm1QmXVBfwAcw0c0NLSwOehsx0mNKoCkoEe56vl9yzrlHMOYLXN8huSHjcMr1twGcgXwcGexBJJ7NLyyPBMYQ0BZqrulla/+CymkWKGFFAIvkgeXlj6N4wk1Oq1Zz1rqwG4N5E1AaZC01kyvJuXx2wA/HPwGwJdDKkYUe3HL8hW9hDZB5AC96G2tGNffQ00W5/hWAIsE3hFrZurRvue5nA4M3gCwCCJMWYzB5FMEaZJfkHkSWukf3qWBiVa7n3pNROuUM2ErqipSS5dl3g/njVvuK6F4R8MyKXRYIh4KRoNzXHUCisiZB1HXoK4aXefprXX3/yAKNIN2oQFAEeWR6+cNhk/w1gKgsNYVTLdT3U9VMfRj4A7oQkkNsrfsAAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242); font-weight: 400; font-style: normal; vertical-align: baseline; text-decoration: none solid rgb(53, 0, 158);" id="k_0810255693LFUVGEFN6W">​</span></div><div id="k_0810234484LYHP2KK8TA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 41px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810234484S7C3F1NXDM">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810322483AQ2P6WJJTN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810384493K7UI61FDOK">FuncC</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810384494RAX4E3VIXT">ontro</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810340683FN5VE3T3PS">ller实现IXlsOperator&lt;Func&gt;接口，并重写以下导出方法。</span></div><div id="k_0810322638LQR2D8PSAK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810391033PMO5EQTK3Z">1.</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810331730BWIER2SKC9">1 设置导出查询参数</span></div><div id="k_0810332964IJ1HHVLERU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 97px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810332965MJODDG6XF3"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">	<span style="background: none; color: rgb(165, 165, 255);">/**</span></span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">         * 导出参数参数设置
         ***/</span>
        @Override
        public Map&lt;String, Object&gt; setXlsExpQueryParams(HttpServletRequest request) throws Exception {
            Map&lt;String, Object&gt; prs = new HashMap&lt;String, Object&gt;();
            String funcDesc = request.getParameter("funcDesc");
            String menuId = request.getParameter("menuId");
            if (StringUtils.isNotEmpty(funcDesc))
                prs.put("funcDesc", URLDecoder.decode(funcDesc, "utf-8"));
            if (StringUtils.isNotEmpty(menuId))
                prs.put("menuId", menuId);	
            return prs;
        }</pre></pre></span></div><div id="k_081033323484COFVWFG8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810333235KXZ3GY6Y97">​</span></div><div id="k_0810341077YV1BBTUKKD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810342806W7A2MXOEBH">​1.</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_081034107897J3ODJAFF">2 ​设置导出的sheet名称​</span></div><div id="k_08103428161WMC2NPZHB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 98px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810342817ORX7WUOYCL"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​          <span style="background: none;">@Override</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">        public String setExportSheetName(HttpServletRequest request) throws Exception {
            return getLang("func_xlsName");
        }</pre></pre></span></div><div id="k_0810341026F4YS3UABZO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_081034102791WUAZMWUI">​</span></div><div id="k_0810341052NRND1YDGYB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(64, 153, 5); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810350559LSMEH4CCUS">​​​1.</span><span id="k_0810350376T4AD4LRRN8" style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);"></span><span id="k_0810350377SM72ZUZQXI" style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">3 设置导出的xls文件名称</span></div><div id="k_0810350565H991FGKX94" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 100px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(64, 153, 5); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_081035056637Y87SD2W2"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">	@Override</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">        public String setExportXlsFileName(HttpServletRequest request) throws Exception {
            return URLDecoder.decode(request.getParameter("fileName"), "utf-8");
        }</pre></pre></span></div><div id="k_08103410874LFY976TU3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810341088L4M2W46BD4">​</span></div><div id="k_0810352297LU39DFINO2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(64, 153, 5); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810353004OVSP8MFJW4">​​​1.</span><span id="k_0810352915S7SIAD35QO" style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">4 设置</span><span id="k_0810352916N8ML6Y1IT2" style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">xls表头（按文件模板导出则</span><span id="k_081035291795LHZSHDJ8" style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">不需要）</span></div><div id="k_081035301461QKW4RXRF" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 102px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(64, 153, 5); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810353015X2ZGMY2MHV"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(165, 165, 255);">	/**</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">         * 创建表头，如果是模板方式导出，则不需要
         * **/</span>
        @Override
        public List&lt;CustomCell[]&gt; setExportSheetTitle(HttpServletRequest request) throws Exception {
            List&lt;CustomCell[]&gt; list = new ArrayList&lt;CustomCell[]&gt;();
            CustomCell[] celssSec = new CustomCell[4];
            CustomCell[] celssFirst = new CustomCell[1];
            celssFirst[0] = new CustomCell("功能列表-" + DataUtils.formatCurrentDateTime("yyyy年MM月dd日"), celssSec.length, 0);
            list.add(celssFirst);
            celssSec[0] = new CustomCell("菜单名称", 0, 1);
            celssSec[1] = new CustomCell("功能描述", 0, 1);
            celssSec[2] = new CustomCell("功能代码", 0, 1);
            celssSec[3] = new CustomCell("是否记录日志", 0, 1);
            list.add(celssSec);
            return list;
        }</pre></pre></span></div><div id="k_0810341020FT2RKY5QRV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810341021SPPRWGNQLC">​</span></div><div id="k_0810360461ARFQFIKZX1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810360462CSX7BPLG51">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810360425DLDSYMM7DS">​</span><span id="k_0810360422U6D67UYMWB" style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">1.5 设置表头单元格样式</span><span id="k_0810360423JL6ZDQZC2O" style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">（返回null，则采用默认样式）</span></div><div id="k_0810360461ARFQFIKZX1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 104px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none; color: rgb(165, 165, 255);">	/**</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">         * 返回null，采用默认样式
         * **/</span>
        @Override
        public CustomCellStyle createHeaderCellStyle(String title, int rowIndex) throws Exception {		
            return null;
        }</pre></pre></span></div><div id="k_0810360613UGSY43ANLC" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810360614GNYHDAEHIK">​</span></div><div id="k_08103606951W2NO6KDQO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810360696OO3MSJKLMB">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810363063FWZ5EHQO3Z">​</span><span id="k_08103630615F8TCD93VH" style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">1.6 设置数据单元格的样式​（返回null，则采用默认样式）</span></div><div id="k_08103645217PUKWTBHKC" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 105px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810364522RUJ6WVVG32"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; color: rgb(165, 165, 255);">​	/**</div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">         * 返回null，采用默认样式
         * **/</span>
        @Override
        public CustomCellStyle createBodyCellStyle(Func bean, XlsCellData cellData, String colTitle) throws Exception {
            return null;
        }</pre></pre></span></div><div id="k_08103606951W2NO6KDQO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);"><br></span></div><div id="k_0810360647HS3IWI5UPD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_08103606471GRM3I61IR">​1.7​ 编写实现实体bean转单元格数据</span></div><div id="k_0810360647HS3IWI5UPD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 106px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​   <span style="background: none; color: rgb(165, 165, 255);">       /**</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">         * 将bean数据转为行的单元格数据
         * **/</span>
        @Override
        public XlsCellData[] xlsExport(Func bean, int row) throws Exception {
            XlsCellData[] datas = new XlsCellData[4];
            datas[0] = XlsCellData.getXlsCellData(bean.getExtFeildsMap().get("menuText"), XlsCellDataTypeEnum.cString);
            datas[1] = XlsCellData.getXlsCellData(bean.getFuncDesc(), XlsCellDataTypeEnum.cString);
            datas[2] = XlsCellData.getXlsCellData(bean.getFuncCode(), XlsCellDataTypeEnum.cString);
            datas[3] = XlsCellData.getXlsCellData(bean.getIsLog().equals("1") ? "是" : "否", XlsCellDataTypeEnum.cString);
            return datas;
        }</pre></pre></span></div><div id="k_0810370592YRE8C1YPWT" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_08103705935GQXUCER96">​</span></div><div id="k_0810370542KAN1SU5PKF" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810370543PKU1XMOWUK">​1.8&nbsp;​按文件模板导出（设置模板文件的输入流）</span></div><div id="k_08103756783S55MUG2EP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 107px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810375679ORT2ZEPYV1"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; color: rgb(165, 165, 255);">​	/***</div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">         * 按模板导出时候，设定导出模板
         ***/</span>
        @Override
        protected FileInputStream setXlsModelFileInputStream(HttpServletRequest request) throws Exception {
            String modelfile = URLDecoder.decode(request.getParameter("modelfile"), "utf-8");
            File f = new File(request.getServletContext().getRealPath("/WEB-INF") + File.separator + modelfile);
            return new FileInputStream(f);
        }</pre></pre></span></div><div id="k_0810370542KAN1SU5PKF" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​</span></div><div id="k_0810375849B3PFGPBLAR" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 71px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810375850NJKUO6A4EP">​1.9&nbsp;​添加实现总结行、总结列功能</span></div><div id="k_0810375849B3PFGPBLAR" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 109px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">	<span style="background: none; color: rgb(165, 165, 255);">/**</span></span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">         * 用于添加总结行，总结列，返回null则无总结 行 /列
         * **/</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none; padding: 3px 5px; margin-right: 3px; margin-left: 3px; border-radius: 3px;" id="k_0810384497OKO5LSYJPU">
        @Override
        public XlsCellData[] onSheetCreateCompleted(Object[] dataArr, int rowCount, int rowNum) throws Exception {
            return null;
        }</span></pre></pre></span></div><div id="k_0810370567PFQWBJAMEU" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810370568RCSVU271RC">​</span></div><div id="k_0810403248B1T2GYMJN4" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(246, 252, 242); padding-left: 12px; border: 1px solid rgb(224, 251, 252); margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_08104032495ZGFOMUAP6">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810404595WV37JA2DEE">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242); font-style: normal; font-weight: 400; text-decoration: none solid rgb(53, 0, 158); vertical-align: baseline;" id="k_0810411675MZVRSE5XAP">三、导入功能开发说明&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08104116778NV1EJSBRA">​<img id="k_0810411678BJX2Z9KXMO" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABM0lEQVQoU42RPy+DURTGf+feDhpCohGhou37dtDPwGCTSEz+DBLfQGwWIUIkvoGIRcRiwWAwYfIlpH3bRlOJSUJIyHuPvFpV6t/Z7snznHt+zxH+U6VUn3Wxw0gqf+oryYR9jh+pknO42d8Ntz0d9qHrRJURDBsuk1/72RCJ7zsPVBgX5Tj0CtMITqgkExCPM5CvNNZTjCn6u8AcUHaqY/jB1RuDDbIXiuacZZJU4TJqmmJ2FccyECK64LzCzvswMYG/V5skN87qjA0lo7At0I7qeRh/mqC/+tgwUAcDRhWqQJtAt6J3TnUKPzhrTrIGXUynjcZOBYaip6IKuuW8YP5r7B8plf1hE3IoSK+ipWbQ1h/qHRNkl0BXEN10mWD9u6N+vkMUZ8lbdPZln8HriKelXgGSVXUaxKHw5QAAAABJRU5ErkJggg==" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(53, 0, 158); background-color: rgb(246, 252, 242); font-style: normal; font-weight: 400; text-decoration: none solid rgb(53, 0, 158); vertical-align: baseline;" id="k_0810411679IPUU36HDUB">​</span></div><div id="k_0810370501QETBRHKEH1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 44px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810370502HLFY275RR9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810413875SIFZDPMBJU">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810445285D5Q7CQDJYE">FuncController</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810445286DZ8TJO8RFY">实现IXlsOperator&lt;Func&gt;接口，并重写以下导入方法。</span></div><div id="k_0810370501QETBRHKEH1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(64, 153, 5); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810450819HAUTOZYDNH">1.</span><span id="k_0810414708GMIFVQUB5T" style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">1​ 设置xls转为bean需要用到的额外数据，如果不需要返回null即可</span></div><div id="k_081041492781MCGVR3WY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 121px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(64, 153, 5); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_08104149282SLKDO6ZL2"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; color: rgb(165, 165, 255);">​           /**</div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">         * 设置行转换bean需要用到的额外数据，如菜单名称转为菜单Id
         * ***/</span>
        @Override
        protected Map&lt;Object, Object&gt; setXlsConvertExtData(HttpServletRequest request) {
            Map&lt;Object, Object&gt; extMap = new HashMap&lt;Object, Object&gt;();
            List&lt;Menu&gt; listMenu = new ArrayList&lt;Menu&gt;();
            try {
                listMenu =	menuService.selectAll();
            } catch (Exception e) {
                logger.error("",e);
            }
            for(Menu m : listMenu) {
                if(!extMap.containsKey(m.getMenuText())) {
                    extMap.put(m.getMenuText(), m.getId());
                }
            }
            return extMap;
        }</pre></pre></span></div><div id="k_0810412270KW8NWMYPFA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810412272Y44BKT5P6W">​</span></div><div id="k_0810412311XCBEGSZQMO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810412312T4OFIT2WYX">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810423748NRC13DDCBG">​</span><span id="k_0810423746E1SLA187N7" style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">1.2 提取请求中需要导入的文件</span></div><div id="k_0810425196BV31HDIOX9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 118px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810425197DFOARJAZ88"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">	@Override</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">        protected MultipartFile getImportXlsfile(Map&lt;String, MultipartFile&gt; fileMap) {
            MultipartFile file = fileMap.get("funcfile");
            return file;
        }</pre></pre></span></div><div id="k_0810412311XCBEGSZQMO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);"><br></span></div><div id="k_0810412333UYYNSBUDQD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(64, 153, 5); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810412334LOYNSHR4FR">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810430178F1KTX3F461">​</span><span id="k_08104301764ZEQAV2BE3" style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">1.3 设置需要导入的sheet名称</span></div><div id="k_0810431563PZB2VURIV6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 119px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810431564KDYQ6C4FUP"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">	@Override</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">        public String getImportSheetName(MultipartHttpServletRequest request) throws Exception {
            String sheetName = request.getParameter("sheetName");
            return sheetName;
        }</pre></pre></span></div><div id="k_0810412333UYYNSBUDQD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​</span></div><div id="k_0810431818KI7P336GR4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810431819CO6TN24N8S">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08104327143HBGOF2UHG">​</span><span id="k_0810432713KVOLUJ7TNR" style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">1.4 设置数据开始行行号</span></div><div id="k_0810434362MJSGIWINC9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 121px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08104343637QGVS5LM2H"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">	@Override</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">        public int getImportStartRow(MultipartHttpServletRequest request) throws Exception {
            String dataStartRow = request.getParameter("dataStartRow");
            if (dataStartRow == null) {
                return 0;
            } else {
                return Integer.parseInt(dataStartRow);
            }
        }</pre></pre></span></div><div id="k_0810431818KI7P336GR4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​</span></div><div id="k_081043463899297DQ3UI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0810434638O71Z8Z3658">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810435392NJMTVE7425">​</span><span id="k_0810435390GCHADIYBU3" style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">1.5 行数据转为实体Bean</span></div><div id="k_0810442151RMS5T74ULQ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 122px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810442152WJJOEY1RTL"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; color: rgb(165, 165, 255);">​           /***</div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">         * 行数据转为实体bean
         * rowData：行数据
         * reqParams：request中的请求参数
         * extDataMap：setXlsConvertExtData设置的扩展数据
         * **/</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none; padding: 3px 5px; margin-right: 3px; margin-left: 3px; border-radius: 3px;" id="k_0810450823SUN5B9VFTL">
        @Override
        public XlsConvertResult&lt;Func&gt; xlsImport(String[] rowData, Map&lt;String, String[]&gt; reqParams,
                Map&lt;Object, Object&gt; extDataMap) throws Exception {
            XlsConvertResult&lt;Func&gt; res = new XlsConvertResult&lt;Func&gt;();
            StringBuffer errBuffer = new StringBuffer();
            String menuText = StringUtils.trim(rowData[0]);
            String funcDesc = StringUtils.trim(rowData[1]);
            String funcCode = StringUtils.trim(rowData[2]);
            String isLog = StringUtils.trim(rowData[3]);
    
            if (StringUtils.isEmpty(menuText))
                errBuffer.append("菜单名称不能为空");
            if (StringUtils.isEmpty(funcDesc))
                errBuffer.append("功能描述不能为空");
    
            if (StringUtils.isEmpty(funcCode))
                errBuffer.append("功能代码不能为空");
    
            if (StringUtils.isEmpty(isLog))
                errBuffer.append("是否记录日志不能为空");
    
            if (errBuffer.length() == 0) {
                String menuId = String.valueOf(extDataMap.get(menuText));
                if (!StringUtils.isEmpty(menuId)) {
                    Func f = new Func();
                    f.setMenu</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none; padding: 3px 5px; margin-right: 3px; margin-left: 3px; border-radius: 3px;" id="k_0810450823NLF1XOU73D">Id(menuId);
                    f.setFuncDesc(rowData[1]);
                    f.setFuncCode(rowData[2]);
                    f.setIsLog("是".e</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none; padding: 3px 5px; margin-right: 3px; margin-left: 3px; border-radius: 3px;" id="k_0810445291HUH8VPCVYR">quals(rowData[3]) ? "1" : "0");
                    f.setCreateTime(new Date());
                    res.bean = f;
                } else {
                    errBuffer.append("[" + menuText + "]菜单在系统中不存在！");
                }
            }
            res.error = errBuffer.toString();
            return res;
        }</span></pre></pre></span></div><div id="k_081043463899297DQ3UI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);"><br></span></div><div id="k_0810431872TFMWOXQPVH" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(64, 153, 5); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_08104318733Q6ZHSGGKO">​</span></div><div id="k_0810322485ST2GDJ6M5N" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810322486BXAOV8G3GF">​</span></div>
</div>