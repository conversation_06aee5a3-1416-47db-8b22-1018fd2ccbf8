<div style="position:relative;">
<div id="k_0515251184PW2YUQFXPT" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 14px; width: 1041px; text-align: left; background-color: rgba(0, 0, 0, 0); border-top: none; border-right: none; border-bottom: 1px dotted rgb(160, 32, 240); border-left: none; border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0515281536YZACJ4MXCP">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0515281539VE14DJP6P6">​<img id="k_0515281540NAHGEBX6B2" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABhklEQVQ4T5WSPUgjYRiEZ1YXNa6ClsIFkpSClYWdguChXjZi5xUi6nWijaUggqWNYucpgmAq/5LjREHwuiuuEiw3gQgpFbyNP6zuSALRROPfV77vPDMfw0u88pz6b900qlbl349Fcr+OKslYaXiGr82eVRsH0QPh0HRvhr7g4Py5tiLsNNijBFeLYkFjkf+JtXfhTMBu8aqwR7C9BP5n3iMWvEpkSw3KkgUwbUUnQGNJwDqEDIgggRHInwy5yWUCKhqUwZmagYhn+nskWym/K+Qm/6StaKdoHEs6NT0jFrzddV7AQld12mqcBTmTXz6HC4A0H3Iv54jju4Km6JKqt9tA7INseQPOQugN5xInj3A+NWU1LpCceizE53A4t7vxonlpMexeTufTC8npumiHqvkbYNNrR/M01wXv1Be6Tv5lFtHAtWWskPheBsqfrCXiN8JQvv3SnYTNOtf/QSdg98PgDgnzE7BnSDYdy94iOfj+d8sVkraZsmIrIMY/C0P4WfG2P2r0ADzJpoVFOtLGAAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0515281541IN6FKHHBEX">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0515281537WIEXCFY699">代码生成器会生成对应的列表页面、表单form页面，datagrid、toolbar配置，下面说明中涉及到的文件/代码并不需要动手实现。</span></div><div id="k_0515274868W93QO1SF5J" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0515274868EVXEMWXIHD">​</span></div><div id="k_0515311235WQ8SY221BE" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_0518020610AIDNL6W4GM">前提：</span></div><div id="k_0515274820HVRP6EN7EV" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(255, 255, 255);" id="k_05180327124HW3AI4SFG">一、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(255, 255, 255);" id="k_0515294861KW1KZXQUFM">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(255, 255, 255);" id="k_0515274821TNDJJ2LKH7">​前端CURD及前后端API接口衔接设计要点</span></div><div id="k_05153155392E4A34XLL5" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 76px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 11px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05190857476IBGZEJ2JW">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0519085749D18OXC8B6R">​<img id="k_0519085750CLGJIINMB4" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABpUlEQVQoU32STUtbQRiFz7l3YqLRbkQoVZOodGuhgpCElkj/QnWnaCoWKbqwLRShG90EFEXdFG2Efv2E7hU1qJBFuxQ1N9GF4EZaNdwkd95yUwxBg7N8Zp73vByGqHGGwietpPi+pAJHte55G8ZiojoL2QWATcoOjK2mWbz95o4U7z19QlWcA1ikI7Of9zp275XclC7bmhdwExoXMDhw7A1MbGywVC1yOJp7ZmgnCrCbRC8EVt5kf53SedPmVxBPRbAPygEcbqpScJvxsDUDok8cvi6g8fTHXvOf6qmD3Wd+059vN8g1iqTNQug9x3rEU6rLfgTYogqet6vpR9fV0nAs4zNtJIR0lB384BZTLsIVHW9uCiJtDefBdyuHtF3eDzEfhLMJQDxuwk2TlfZeRXLPBfrleio0WZ0Uj2QSBLeSqdDPG16RRiKZcReSjd8h11PUovKKC16NF4bW7cndjsU7UjxiLQPoJOQxwCVoXsHQ0wJYBI+SqeAbgFIeXN49fFLfRGcNgl8w/J/Wd1r+uvx/CRwVIlq89I1++/3wqiLV+l/3sX+1vaBIlwIF9AAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05190857514K4LQIG5N5">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_051908574834SHSCKG42">前后端适配API设计，如下：</span></div><div id="k_0517313847KK3UDBZWO6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 104px; width: 946px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 6px; border-top: 1px solid rgb(180, 214, 250); border-right: 1px solid rgb(180, 214, 250); border-bottom: none; border-left: 1px solid rgb(180, 214, 250); border-image: initial;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05174303055P13XRMTH9">​1 . </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519052195YUOHCXRPBQ">page接</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520053194L92FS5CA4H">口</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_052005319568N2A816PU">[</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 240, 255);" id="k_0517382992LURPEF5K8P"> xxx/page/</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(242, 240, 255);" id="k_05173829936XMFLEQ1JE">pageName</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 240, 255);" id="k_051738299483ESHQXVNI"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520053198B65D6UG5PB">]</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520053199IMP9478SYY">，负责</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519063687GZLCETHFZ6">请求名称为[</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(255, 255, 255);" id="k_0519073294X36AHSC7YX"> p</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(255, 255, 255);" id="k_0519073295IRY3BVRAA1">ageName </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517412940JUXEBU77XJ">]的页面，注意controller里[ </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(255, 255, 255);" id="k_0517412941MMHYQTT6CG">setPageModelPath</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517412942XO8BBN6GKE"> ]设置的页面目录。</span></div><div id="k_0517430314C96TOGHOSW" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 104px; width: 946px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 6px; border-top: none; border-right: 1px solid rgb(180, 214, 250); border-bottom: none; border-left: 1px solid rgb(180, 214, 250); border-image: initial;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_051746424144UXPHOKZF">​2 . </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517430315TNEOULQAAQ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05174653537V87KSBNVW">add接口</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05174653546AYZKCFVXT">[ xxx/controller/add ]</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517465355TRNAWYDY5Q">，负责新增数据，AdminControllerHelper.getObjectFromRequest(...)从请求中提取参数返回实体Bean。</span></div><div id="k_05174642505V6XQQSYAF" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 104px; width: 946px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 6px; border-top: none; border-right: 1px solid rgb(180, 214, 250); border-bottom: none; border-left: 1px solid rgb(180, 214, 250); border-image: initial;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05174742042I6MNO6YHM">​3 . </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517464251FSMV8NBTCJ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517475328HDYGN3CP9Y">update接口</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517475329M798V3Y81V">[ xxx/controller/update ]</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_051747533022EBYZB99S">，负责更新数据，同上从请求中提取实体Bean。</span></div><div id="k_0517474213PU71KEU811" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 104px; width: 946px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 6px; border-top: none; border-right: 1px solid rgb(180, 214, 250); border-bottom: none; border-left: 1px solid rgb(180, 214, 250); border-image: initial;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05175049903JFT4WNSEY">​4 . </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517474214X3MOY19YOZ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05174814791AMQTJ5HAV">delete接口[ xxx/controller/</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517481480ZX36YQGEQZ">delete</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05174933494MYDXXDEQJ"> ]，通过</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517500658JDL6BX9UFW">参数[ </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(255, 255, 255);" id="k_05175006591N3F8IWX5S">idList=id,id2....... </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0517500660NDW2NM6GUR">] 负责数据删除。</span></div><div id="k_0517504905ESSRGDYMVD" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 104px; width: 946px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 6px; border-top: none; border-right: 1px solid rgb(180, 214, 250); border-bottom: 1px solid rgb(180, 214, 250); border-left: 1px solid rgb(180, 214, 250); border-image: initial;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519063691IXQDYV7QNC">5 . list接口[ xxx/controller/list ]，负责datagrid</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05190636918ZQ6OKMKPO">的分</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519073298TQEW7ZBM28">页\多条件\</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520034466ZY5BY2C5T5">排序查询。参数有page(页码)、pageSize(页大小)、_col_sort_</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(255, 255, 255);" id="k_0520034467C4PTBAXPJU">fieldName</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05200344686UW3G5SM3Q">(排序字段)。</span></div><div id="k_05173051961MPDVOCVRP" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 76px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 11px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519093193H1QIFL5WAL">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0519093195R2T2C2UNWX">​<img id="k_0519093196O1OLQRFZ41" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABpUlEQVQoU32STUtbQRiFz7l3YqLRbkQoVZOodGuhgpCElkj/QnWnaCoWKbqwLRShG90EFEXdFG2Efv2E7hU1qJBFuxQ1N9GF4EZaNdwkd95yUwxBg7N8Zp73vByGqHGGwietpPi+pAJHte55G8ZiojoL2QWATcoOjK2mWbz95o4U7z19QlWcA1ikI7Of9zp275XclC7bmhdwExoXMDhw7A1MbGywVC1yOJp7ZmgnCrCbRC8EVt5kf53SedPmVxBPRbAPygEcbqpScJvxsDUDok8cvi6g8fTHXvOf6qmD3Wd+059vN8g1iqTNQug9x3rEU6rLfgTYogqet6vpR9fV0nAs4zNtJIR0lB384BZTLsIVHW9uCiJtDefBdyuHtF3eDzEfhLMJQDxuwk2TlfZeRXLPBfrleio0WZ0Uj2QSBLeSqdDPG16RRiKZcReSjd8h11PUovKKC16NF4bW7cndjsU7UjxiLQPoJOQxwCVoXsHQ0wJYBI+SqeAbgFIeXN49fFLfRGcNgl8w/J/Wd1r+uvx/CRwVIlq89I1++/3wqiLV+l/3sX+1vaBIlwIF9AAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519093197952ZMZB3CY">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519123948AS4AIHNCXH">对一个Datag</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519123949YK41UIJ3YE">rid数据列表的增删改查，可以封装为一个通用的前端CURD对象，这个CURD内部根据约定实现与后端的[</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_0517305198AZFFF6OBRQ">add/delete/update/list</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05173051981UD73KAVD4">]接口适配。</span></div><div id="k_0515360812RYBMKUFYF1" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 76px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 1px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_051909437775EPQHENLL">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05190943791LCVUYTW6F">​<img id="k_05190943809RPJJWU6DS" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABpUlEQVQoU32STUtbQRiFz7l3YqLRbkQoVZOodGuhgpCElkj/QnWnaCoWKbqwLRShG90EFEXdFG2Efv2E7hU1qJBFuxQ1N9GF4EZaNdwkd95yUwxBg7N8Zp73vByGqHGGwietpPi+pAJHte55G8ZiojoL2QWATcoOjK2mWbz95o4U7z19QlWcA1ikI7Of9zp275XclC7bmhdwExoXMDhw7A1MbGywVC1yOJp7ZmgnCrCbRC8EVt5kf53SedPmVxBPRbAPygEcbqpScJvxsDUDok8cvi6g8fTHXvOf6qmD3Wd+059vN8g1iqTNQug9x3rEU6rLfgTYogqet6vpR9fV0nAs4zNtJIR0lB384BZTLsIVHW9uCiJtDefBdyuHtF3eDzEfhLMJQDxuwk2TlfZeRXLPBfrleio0WZ0Uj2QSBLeSqdDPG16RRiKZcReSjd8h11PUovKKC16NF4bW7cndjsU7UjxiLQPoJOQxwCVoXsHQ0wJYBI+SqeAbgFIeXN49fFLfRGcNgl8w/J/Wd1r+uvx/CRwVIlq89I1++/3wqiLV+l/3sX+1vaBIlwIF9AAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520042743L68S1L92NS">​通过对CURD的通用封装</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520042496SPSQKNJ9BK">，只需要调用对应的API，传递对应参数即可，无需再重复编写表单读写、请求提交的代码。</span></div><div id="k_0517582096XX9JP54DES" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 76px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 11px; margin-top: 0px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519101641UHN1Z7HTJ8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_051910164479C3EVFJ6B">​<img id="k_0519101645DRGHN4DQ9Z" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABpUlEQVQoU32STUtbQRiFz7l3YqLRbkQoVZOodGuhgpCElkj/QnWnaCoWKbqwLRShG90EFEXdFG2Efv2E7hU1qJBFuxQ1N9GF4EZaNdwkd95yUwxBg7N8Zp73vByGqHGGwietpPi+pAJHte55G8ZiojoL2QWATcoOjK2mWbz95o4U7z19QlWcA1ikI7Of9zp275XclC7bmhdwExoXMDhw7A1MbGywVC1yOJp7ZmgnCrCbRC8EVt5kf53SedPmVxBPRbAPygEcbqpScJvxsDUDok8cvi6g8fTHXvOf6qmD3Wd+059vN8g1iqTNQug9x3rEU6rLfgTYogqet6vpR9fV0nAs4zNtJIR0lB384BZTLsIVHW9uCiJtDefBdyuHtF3eDzEfhLMJQDxuwk2TlfZeRXLPBfrleio0WZ0Uj2QSBLeSqdDPG16RRiKZcReSjd8h11PUovKKC16NF4bW7cndjsU7UjxiLQPoJOQxwCVoXsHQ0wJYBI+SqeAbgFIeXN49fFLfRGcNgl8w/J/Wd1r+uvx/CRwVIlq89I1++/3wqiLV+l/3sX+1vaBIlwIF9AAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519101646BEA2VP5WJW">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519130811H41XKPB4PW">CURD实</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0519130813GCQGSOHXEP">例接收list接</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_05191308166ZAHPE6NCK">口url参数，curd.add、cu</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519130818TYC8M27JYA">rd.delete、curd.update会在内部将[ list ]替换为对应的[ add\delete\update ]。</span><span id="k_0517282056TNOMMBILWO" style="font-family: &quot;Microsoft Yahei&quot;;">​</span><span style="background-color: rgba(0, 0, 0, 0); font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_0515294873CMRGSV6XMZ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(255, 255, 255);" id="k_0515302175GEXEHYSDK3">二、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(255, 255, 255);" id="k_0515315536ZUMIWNRN2I">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(255, 255, 255);" id="k_05152948743LDP2C3PPF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(255, 255, 255);" id="k_05152948754MXSWIY22Q">​datagrid、toolbar可后端配置设计要点</span></div><div id="k_0515302180TBRS446DIG" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 77px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520070459WX7NX3A5JZ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05200704613OJT22KNV7">​<img id="k_0520070462CEBFVQOSZL" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABB0lEQVQoU63SMUvDYBCH8eeSIqJ0lQ6CQ5sO+hUEP0DBoeDkKjGBLiZrkQwuImnFoYlObm4uzsVNcHDTpa8dHAQFFRGcbHOCk2gboXj77467/wkTlExgGIvaprORqbUPXIVVb+V785Fo7zaZs5SuIEugJ4Hjr/+JYpN0BHFRvUPs1cBxb3JRu39Yy4Z6LKKzijRDx2v93FtaJr0GfR0K9ffy40vRlM5FWAbO3ioP9UiiwW/US/qILCh6hHBPJtugT1j2WlhxL0ZdV2KTBoLuABbKh8I0sBtW/ea4OL6uF/fSA1BfRAool1Mzg1pjvvGciyKNCkVT6gKLls3mVtk7zQv9fz8ib9In30tUDmj5PigAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520070463FB3SG11A1V">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520070460ABB2EDHZFW">Bui组件是通过JSON参数进行构建的，通常情况下，datagrid、toolbar的json参数也会在页面进行编写。</span></div><div id="k_0518095188L55ZMKFHGB" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 77px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 1px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520071267PAO6NBCBQD">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0520071269OBF6RR2TFR">​<img id="k_05200712708S9BJKISLI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABB0lEQVQoU63SMUvDYBCH8eeSIqJ0lQ6CQ5sO+hUEP0DBoeDkKjGBLiZrkQwuImnFoYlObm4uzsVNcHDTpa8dHAQFFRGcbHOCk2gboXj77467/wkTlExgGIvaprORqbUPXIVVb+V785Fo7zaZs5SuIEugJ4Hjr/+JYpN0BHFRvUPs1cBxb3JRu39Yy4Z6LKKzijRDx2v93FtaJr0GfR0K9ffy40vRlM5FWAbO3ioP9UiiwW/US/qILCh6hHBPJtugT1j2WlhxL0ZdV2KTBoLuABbKh8I0sBtW/ea4OL6uF/fSA1BfRAool1Mzg1pjvvGciyKNCkVT6gKLls3mVtk7zQv9fz8ib9In30tUDmj5PigAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520071271GSQ748IR9E">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520071268J61BFOBVM7">Bui-ssm为了实现datagrid、toolbar工具栏的动态控制及权限控制toolbar按钮的需求，故将datagrid、toolbar设计为后端xml配置方式。</span></div><div id="k_0520052380HVIY2J8SRS" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 77px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 11px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0520052381AWSHPRAQLS">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0520072101WF9R3TCN5O">​<img id="k_0520072101MOAN2U5M4J" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABB0lEQVQoU63SMUvDYBCH8eeSIqJ0lQ6CQ5sO+hUEP0DBoeDkKjGBLiZrkQwuImnFoYlObm4uzsVNcHDTpa8dHAQFFRGcbHOCk2gboXj77467/wkTlExgGIvaprORqbUPXIVVb+V785Fo7zaZs5SuIEugJ4Hjr/+JYpN0BHFRvUPs1cBxb3JRu39Yy4Z6LKKzijRDx2v93FtaJr0GfR0K9ffy40vRlM5FWAbO3ioP9UiiwW/US/qILCh6hHBPJtugT1j2WlhxL0ZdV2KTBoLuABbKh8I0sBtW/ea4OL6uF/fSA1BfRAool1Mzg1pjvvGciyKNCkVT6gKLls3mVtk7zQv9fz8ib9In30tUDmj5PigAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_052007210363EUT6CT41">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520053436GW6ZPAV3AY">​</span><span id="k_0520053425OEKI2R2I7V" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; text-align: left; background-color: rgb(255, 255, 255);">[</span><span id="k_0520053428N3YGQ4OPRI" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; text-align: left; background-color: rgb(250, 240, 230);">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 240, 230); font-style: normal; font-weight: 400; text-align: left;" id="k_0520053429ZSTCURMV1Q">​xxx/page/</span><span id="k_0520053431BK7XHJ8RHK" style="font-size: 14px; color: rgb(92, 184, 92); font-style: normal; font-weight: 400; text-align: left; background-color: rgb(250, 240, 230);">pageName</span><span id="k_05200534336T8E3JWION" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; text-align: left; background-color: rgb(242, 240, 255);"></span><span id="k_05200534348M587OVJZP" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; text-align: left; background-color: rgb(255, 255, 255);">]页面请求除了负责返回页面，还根据gridid参数加载对应的datagrid、toolbar配置，并设置到返回的页面中。</span></div><div id="k_0515274866D8S1RTN3M7" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05152748671J6N1CEL6D">​开发步骤：</span></div><div id="k_0519151968AUGCLBMRDH" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05193409321G2ZFVS593">​一、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_051925309697DPDAY1RJ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519234864UMPABJGZOV">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519285078HRFLQPXANW">建立列表页面index.html</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(170, 170, 170); background-color: rgb(255, 255, 255);" id="k_0519285080WDRWSA8Q26">（代码生成器会生成）</span></div><div id="k_0519340943TFALKQ8MKG" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255);" id="k_0519445787ZXN9P5VR1N">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0519445789HK8GEFV8SC">​<img id="k_0519445790DDK6K6N5GK" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAs0lEQVQoU2NkIAMwkqGHAaxp3br/wj9//u5lYGAIYWRk4P7/n+ErA8P/vczMbLlhYYyP0A1mXL78vwIDw6/tjIyMGuiS////f8bExBQWHs56FFmOcfnyn6sZGRlDcDnz////O27eZPNuaGD8B1MD0vSAkZFRHo/fXv/7998lKor9EpKmX28YGRmEcdvE8JWJiSkmPJxlA6U2keUnvKH3kJmZMSwsjO0USuiRFU9kpwhSNQIA9VFeDnWpvqAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255);" id="k_0519445791HMDFXK9FFR">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255);" id="k_0519445788LUOUI63FBL">index.html负责显</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255);" id="k_0519363776GXHTWGUO2K">示数据列表及其查询功能，用table标签声明列表标签</span></div><div id="k_0519253014KHONVDD426" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519253015ETN3CYEVHU"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​​<span style="background: none;">		&lt;div class="form_main_wrap"&gt;</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; background: none; border-radius: 3px;">			&lt;div class="form_condition"&gt;&lt;/div&gt;     <span style="color: rgb(120, 120, 210); background: none;">// 此处定义查询表单</span>
			&lt;div class="form_grid"&gt;          
				&lt;<span style="color: rgb(255, 0, 209);">table</span> <span style="color: rgb(255, 0, 209);">id=</span>"<span style="color: rgb(255, 0, 209);">datagrid</span>"&gt;&lt;/<span style="color: rgb(255, 0, 209);">table</span>&gt;     <span style="color: rgb(120, 120, 210); background: none;">// 定义datagrid对应的table 标签</span>
			&lt;/div&gt;
		&lt;/div&gt;</pre></pre></span></div><div id="k_0519301506R141KC66NW" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;"><br></span></div><div id="k_0519383616NOLBBBXYS6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_0519450663MGDPJG8TJX">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0519450665VVE8GJUN89">​<img id="k_0519450666ZLN5K5G6OD" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAs0lEQVQoU2NkIAMwkqGHAaxp3br/wj9//u5lYGAIYWRk4P7/n+ErA8P/vczMbLlhYYyP0A1mXL78vwIDw6/tjIyMGuiS////f8bExBQWHs56FFmOcfnyn6sZGRlDcDnz////O27eZPNuaGD8B1MD0vSAkZFRHo/fXv/7998lKor9EpKmX28YGRmEcdvE8JWJiSkmPJxlA6U2keUnvKH3kJmZMSwsjO0USuiRFU9kpwhSNQIA9VFeDnWpvqAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_0519450667QJ1T2NOBZX">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_0519450663DCZRB79DLF">创建CURD实</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_05194018346CQU5Y8393">例对象，创建实例的参数及相关标题均读取后端配置值</span></div><div id="k_0519360183M9E3RRVNQK" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519360184YSFUTGSX1F"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">		</span>  <span style="background: none;">var	</span> <span style="background: none;">gridOpts = [(${dataGridJson})];                   <span style="background: none; color: rgb(165, 165, 255);">//获取datagrid配置json,此处从服务器得到，也可以静态编码</span>			</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">		var  title = "[(${Lang.dict_windowTitle})]";           <span style="color: rgb(165, 165, 255);">//国际化语言</span>
		gridOpts.title = title;                                           <span style="color: rgb(165, 165, 255);">//datagrid标题</span>
		$(function () {
			var dg = $("#<span style="color: rgb(255, 0, 209);">datagrid</span>");			           <span style="color: rgb(165, 165, 255);"> //获取table对象</span>
			<span style="color: rgb(15, 224, 0);">curdObj = new $B.CURD(dg, gridOpts);</span>    <span style="color: rgb(165, 165, 255);">//创建curd对象，即创建了datagrid列表</span>
		});</pre></pre></span></div><div id="k_0519364640TZ7DSOLQ7W" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519364641HBVZVNNGOS">​</span></div><div id="k_0519394896DED18BC7JU" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519394897Y7MI85J5UT">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05194517856LATRA2ISU">​<img id="k_05194517863PSRQ4C6B5" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAs0lEQVQoU2NkIAMwkqGHAaxp3br/wj9//u5lYGAIYWRk4P7/n+ErA8P/vczMbLlhYYyP0A1mXL78vwIDw6/tjIyMGuiS////f8bExBQWHs56FFmOcfnyn6sZGRlDcDnz////O27eZPNuaGD8B1MD0vSAkZFRHo/fXv/7998lKor9EpKmX28YGRmEcdvE8JWJiSkmPJxlA6U2keUnvKH3kJmZMSwsjO0USuiRFU9kpwhSNQIA9VFeDnWpvqAAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_05194517876ZB4B37CDN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_0519464507OLBZYQFMIU">创建工具栏事件</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_05194645082VMJ8U8DSZ">集合对象，编写事件函数，注意与配置</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_0519464509T5F12KHWBR">中的事件名称一致</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(168, 168, 250); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_0519404331BWHYB97OLT">。</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(170, 170, 170); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_0519404331622VQSAK5I">（</span><span id="_1321513367GOLOP4XGDA" style="font-family: &quot;Microsoft Yahei&quot;; color: rgb(170, 170, 170); font-size: 14px; font-weight: 400; background-color: rgb(255, 255, 255); font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;">将事件规范到一个对象集合中，避免事件函数到处编写的情况）</span></div><div id="k_0519395018BQFPT61WAT" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519395019A3S6PTR4Q5"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">                 <span style="background: none; color: rgb(165, 165, 255);"> ​/** 打开form.html</span></div><div style="background: none;">                  <span style="background: none; color: rgb(165, 165, 255);">*prs:     按钮参数，</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; background: none; border-radius: 3px;">	<span style="color: rgb(165, 165, 255);">	*isUpdate：是否是更新
		*rowData: 行数据对象****/</span>
		function _openForm(prs,isUpdate,rowData){
			var opts = {
				dataType:'html',
				title:title,
				width:700,
				height:height
			};
			if(isUpdate &amp;&amp; rowData){
				opts.rowData = rowData;
			}
			<span style="color: rgb(15, 224, 0);">curdObj.window(opts,isUpdate);</span>        <span style="color: rgb(165, 165, 255);">//调用curd实例的window方法，打开form.html页面</span>
		};
		<span style="color: rgb(165, 165, 255);">/******工具栏事件集合,对应列表工具栏json里面配置的事件名称******/</span>
		window.<span style="color: rgb(255, 0, 5);">toolMethods</span> = {
			<span style="color: rgb(255, 0, 5);">addFn</span>:function(prs){
				_openForm(prs);                <span style="color: rgb(165, 165, 255);">//打开新增窗口</span>
			},
			<span style="color: rgb(255, 0, 5);">updateFn</span>:function(prs){
				_openForm(prs,true);            <span style="color: rgb(165, 165, 255);">//打开更新窗口</span>
			},
			<span style="color: rgb(255, 0, 5);">deleteFn</span>:function(prs){
				<span style="color: rgb(15, 224, 0);">curdObj.delChecked(); </span>          <span style="color: rgb(165, 165, 255);">//删除datagrid里复选的数据</span>
			},
			<span style="color: rgb(255, 0, 5);">lineUpdateFn</span>:function(prs){
				<span style="color: rgb(15, 224, 0);">_openForm(prs,true,prs);</span>        <span style="color: rgb(165, 165, 255);">//行内更新按钮，打开更新窗口</span>
			},
			<span style="color: rgb(255, 0, 5);">lineDeleteFn</span>:function(prs){
				<span style="color: rgb(15, 224, 0);">curdObj.deleteData([prs.id]);</span>   <span style="color: rgb(165, 165, 255);">//行删除 </span>   
			}
		};</pre></pre></span></div><div id="k_0519411984MAICBIX596" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519411985UZIERMM9FV">​</span></div><div id="k_0519234877SEY795I2NZ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 49px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_051930100987CT6S54HE">二、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519340938WOUO3DTQPR">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_051928430025DWF996JF"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_05192843013CESHFVCYY">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0519285901WBWICJI8SS">建立表单页面form.html</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(170, 170, 170); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0519285902GWSCHEXIGN">（代码生成器会生成）</span></div><div id="k_0519301016V8LG6BIJT8" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 77px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(139, 109, 56); background-color: rgb(250, 240, 230); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_05195611375SJTNYAAK2">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(250, 240, 230); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_0519561137SJBNMIYUMK">form.htm</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(250, 240, 230); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_05194756274WGCUIA8UE">l负责新增/更</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(250, 240, 230); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_0519563242RB4R9QR6N1">新表单，通过id隐藏域是否有值区分新增/更新</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(139, 109, 56); background-color: rgb(250, 240, 230); font-weight: 400; font-style: normal; text-decoration: none solid rgb(168, 168, 250); vertical-align: baseline;" id="k_05195632432SUWUTNXLQ">&nbsp;</span></div><div id="k_0519301339MDC44344UH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 76px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0519550520N7SA8GVP5M">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05195505234H4SKCS4ML">​<img id="k_0519550524DM8DUFT66Z" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAs0lEQVQoU2NkIAMwkqGHAayps3Mu7z/WX43/Gf8nMzIw8v1n+P+JgYFxIwPz7+KqvLzX6AYzdndPF/vDzLCDgZHBEIutD/4xMfhW52deQZZjbOubPouRkSEVtzP/r6woyIxkZGT8D1PD2NY37SYjI6MaHk3P/vxhtaktTb2P0NQ//QUjA4M4Lk0g/zExMvlWFGQcotAmcvyEL/T+//9/i5mJwa+8IOsmSuiRFU9kpwhSNQIAjm1WDqHdnHEAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_05195505255WHKYYT4H6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_05195505213FVBRPH9A5">定义表单</span></div><div id="k_05194749786IKBEEWGMN" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 95px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_05194749792XFMJAWT77"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none; color: rgb(0, 137, 255);">​&lt;div id="form_container" class="form_container"&gt;		</div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; background: none; border-radius: 3px; color: rgb(0, 137, 255);" class=""><span style="color: rgb(255, 214, 0);"><input type="hidden" id="id" th:value="*{id}" style="color: rgb(255, 214, 0);"></span>		&lt;<span style="color: rgb(255, 0, 209);">input </span>type="hidden"<span style="color: rgb(255, 0, 209);"> id="id"</span> name="id" th:value="*{id}"/&gt;
		&lt;table class="form_table"&gt;
				&lt;tr&gt;
					&lt;td class="label label30"&gt;[(${Lang.dict_typeid})]&lt;/td&gt;
					&lt;td&gt;&lt;input id="typeid" name="typeid" class="form_input form_input50" type="text"/&gt;&lt;/td&gt;
				&lt;/tr&gt;
                                 ..................................................
				&lt;tr&gt;
					&lt;td class="button_td" colspan="2"&gt;
						&lt;button id="button_submit" class="common_button"&gt;&lt;i class="fa fa-floppy"&gt;&lt;/i&gt;[(${Lang.common_save})]&lt;/button&gt;
						&lt;button id="button_goback"  class="common_button"&gt;&lt;i class="fa fa-reply-all"&gt;&lt;/i&gt;[(${Lang.common_goback})]&lt;/button&gt;
					&lt;/td&gt;
				&lt;/tr&gt;
		&lt;/table&gt;
&lt;/div&gt;</pre></pre></span></div><div id="k_0519474930Z21FCFELAW" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 76px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 5px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0519552416X5Q96NPHYJ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0519552419IBKDQJN49L">​<img id="k_05195524207U67KPCIIE" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAs0lEQVQoU2NkIAMwkqGHAayps3Mu7z/WX43/Gf8nMzIw8v1n+P+JgYFxIwPz7+KqvLzX6AYzdndPF/vDzLCDgZHBEIutD/4xMfhW52deQZZjbOubPouRkSEVtzP/r6woyIxkZGT8D1PD2NY37SYjI6MaHk3P/vxhtaktTb2P0NQ//QUjA4M4Lk0g/zExMvlWFGQcotAmcvyEL/T+//9/i5mJwa+8IOsmSuiRFU9kpwhSNQIAjm1WDqHdnHEAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0519552421IVWK27FXCB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_05195524178PKOYKC719">编写表单脚本</span></div><div id="k_0519501984PNRRECOS2I" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 96px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0519501985I26F8S573L"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​&lt;script type="application/javascript" th:inline="javascript"&gt;
		var $form,
			entityJson,     <span style="background: none; color: rgb(165, 165, 255);">//表单对应的实体数据对象</span>
			vm,               <span style="background: none; color: rgb(165, 165, 255);"> //mvvm双向联动实例</span>
			idValue;        <span style="background: none; color: rgb(165, 165, 255);">//用于区分新增,编辑</span>
		$(function(){
			$form = $("#form_container");
			idValue = $form.find("#id").val();
			<span style="background: none; color: rgb(165, 165, 255);">/**创建mvvm表单，将实体json绑定到表单**/</span>
			if(idValue === ""){
				entityJson = $B.parseForm($form);               <span style="background: none; color: rgb(165, 165, 255);"> //新增场景下，从表单中提取实体JSON</span>
			}else{
				entityJson = [(${entityJson})];                        <span style="background: none; color: rgb(165, 165, 255);"> //更新场景下，取服务器返回的实体JSON</span>
			}
			vm = $B.bindForm($form, entityJson);                   <span style="background: none; color: rgb(165, 165, 255);">//将实体数据绑定为mvvm表单	</span>		
			$form.find("#button_goback").click(function(){     <span style="background: none; color: rgb(165, 165, 255);"> //返回按钮，关闭窗口</span>
				curdObj.closeWindow();
			});
			var validObj = new $B.Validate({  <span style="background: none; color: rgb(165, 165, 255);">//创建校验</span>
				typeid: [{rule:'require'}],
				dkey: [{rule:'require'}],
				dvalue: [{rule:'require'}]
            }, $form);
			$form.find("#button_submit").submit(function(){    <span style="background: none; color: rgb(165, 165, 255);">//提交表单数据</span>
				if(validObj.valid($form)){                                  <span style="background: none; color: rgb(165, 165, 255);">//调用验证</span>
					var json = vm.getJson();                           <span style="background: none; color: rgb(165, 165, 255);">//从双向mvvm实例中获取需要提交的json数据</span>
					if(idValue === ""){
						curdObj.add(json);                           <span style="background: none; color: rgb(165, 165, 255);">//调用curdObj实例的 新增 add api    </span>      
					}else{
						curdObj.update(json);                     <span style="background: none; color: rgb(165, 165, 255);">//调用curdObj实例的更新 update api</span>
					}
				}
			});			
		});
&lt;/script&gt;</div></pre></span></div><div id="k_051527480045UWI8LQTP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05152748016KOSZ485H1">​</span></div><div id="k_0517275111CW52BT5IIO" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519582001PUH8QZK333">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0519582004ULCFYOBEU4">​<img id="k_0519582004QA3CKMSP65" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABPElEQVQoU42Sv0oDQRjEZ273CNiIoGAp+ACCL5AmIMRCbBXjCWqpaCfaidgp2vkHPSPW2hgipPEFBB9AsLQIiIWC3F5GEqPJ3UVwu52d38737X5EjxXZ8gTZOJG8Jd+V7tIWpgXhasBZd00iL+HeOjtNzL52+zKQM+UA1CkIAyGGuGjjUvgnJJwNOd+rQngH+ABoHGDOOn+SmKn/gImk2A9XBB4QWjVRcJjeZyDhfCT2WQM4moYAPZlIBWLhuQm2kgTR2YsdkhvfYjvJhlsit1seade6+U2CakOXY7Ft1EAM9vqClibUjfMKxNwjhSM/9nPHAINOzVo3UbAf++GawL3ORQpN9LlM2XI+RqMCsq8LSjzELyR9GHjFZi+3IIqJsqQ3kS+UhkH2J89QaUI3IKb+7CUzMqpmJuI/8BcFXIw8ifNbsQAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519582005CMSIY2WXX2">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_0519582002OVSCCBYFDJ">注意事项：</span></div><div id="k_05173744555WW3EMJ4R9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0517374457I3D817IUNX">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0520010038VKLV7NJL2K">​<img id="k_0520010038BD6SRIY97E" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAu0lEQVQoU2NkIAMwkqGHgXJNcSefCP/59mspAwPDnmWOSj24XIFiU+j+VzysjJ83MjAwWjIyMDYsdVDswqYRw3kgjWwMn1cwMDI6MzAwti91UGxC18gYtf/eC0ZGBnEcTvnx/z9j5jJHxQXI8oyR++9r/Gdi5IQJMjExMjH+/VP9/z+DFyMDwwJVB6WsBkbGfyiakDkN//8z3Tp4fz4Dw/8IBkbGLjU7xXp0DSD1KH4iK/SIjWjKI5dYmwAvRTkO6KDslQAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520010040ZRN8FCA26W">​</span><span id="_13220550523296SDFQIM" style="font-family: &quot;Microsoft Yahei&quot;;">调用curdObj实例打开新增/更新窗口时候，通过</span><span id="_1322055054XCMQRMG7YE" style="font-family: &quot;Microsoft Yahei&quot;; color: rgb(247, 0, 115);">dataType</span><span id="_1322060098GCGJNAOKH6" style="font-family: &quot;Microsoft Yahei&quot;;">参数指定打开的页面类型，简单表单采用</span><span id="_13220600995LDPU5J5LF" style="font-family: &quot;Microsoft Yahei&quot;; color: rgb(247, 8, 119);">html</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519593688NFNE3HGKHL">类型</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519593689PT36K5VF8K">的</span><span id="_1322060772XGCUGWA1NF" style="font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 10, 119);">页面片段</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519594017XCC1MQJWNZ">，</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05195942323TYQOB3G6L">复杂表单</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519594233KEI7FURS49">采用</span><span id="_1322061231NLMYTB3B6E" style="font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 8, 118);">iframe</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519595379A397KPMZ4R">类型</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519595380MHR3MP6R9E">的</span><span id="_1322061898T2A2YJJFFP" style="font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 5, 117);">完整页面</span></div><div id="k_0520000601QS9NUC3MVD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 5, 117);" id="k_0520000601HWN96HUSFA">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520002314ANYBCRF527">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0520011219MTWGK4P9QP">​<img id="k_05200112209QWC9QGZ76" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAu0lEQVQoU2NkIAMwkqGHgXJNcSefCP/59mspAwPDnmWOSj24XIFiU+j+VzysjJ83MjAwWjIyMDYsdVDswqYRw3kgjWwMn1cwMDI6MzAwti91UGxC18gYtf/eC0ZGBnEcTvnx/z9j5jJHxQXI8oyR++9r/Gdi5IQJMjExMjH+/VP9/z+DFyMDwwJVB6WsBkbGfyiakDkN//8z3Tp4fz4Dw/8IBkbGLjU7xXp0DSD1KH4iK/SIjWjKI5dYmwAvRTkO6KDslQAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520011222VEQPSPD67I">​</span><span id="k_0520002312LO47WXM6SD" style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; text-align: left; background-color: rgb(255, 255, 255);">form.html表单页面，采用$B.bindForm() API实现一个双向表单，解放表单读写开发。</span></div><div id="k_0520002522G2A22JI6J3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; text-align: left; background-color: rgb(255, 255, 255);" id="k_0520002523JQEO47P5B6">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520003467QLL2P5P8X3">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05200119139ZFXVYZKIR">​<img id="k_0520011914I3X826DBLN" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAu0lEQVQoU2NkIAMwkqGHgXJNcSefCP/59mspAwPDnmWOSj24XIFiU+j+VzysjJ83MjAwWjIyMDYsdVDswqYRw3kgjWwMn1cwMDI6MzAwti91UGxC18gYtf/eC0ZGBnEcTvnx/z9j5jJHxQXI8oyR++9r/Gdi5IQJMjExMjH+/VP9/z+DFyMDwwJVB6WsBkbGfyiakDkN//8z3Tp4fz4Dw/8IBkbGLjU7xXp0DSD1KH4iK/SIjWjKI5dYmwAvRTkO6KDslQAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05200119163HZ3611L3E">​</span><span id="k_0520003464ZFSIJPNMEZ" style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; text-align: left; background-color: rgb(255, 255, 255);">form.html表单页面，采用$B.Validate()前端验证组件实现验证。</span></div><div id="k_0520003566FAVHUMJCUR" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; text-align: left; background-color: rgb(255, 255, 255);" id="k_05200035675DE2QHYBNB">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520004423AKR1ZEBNUM">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05200127438CHD4QZEQ2">​<img id="k_0520012744MANWXQ15OB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAu0lEQVQoU2NkIAMwkqGHgXJNcSefCP/59mspAwPDnmWOSj24XIFiU+j+VzysjJ83MjAwWjIyMDYsdVDswqYRw3kgjWwMn1cwMDI6MzAwti91UGxC18gYtf/eC0ZGBnEcTvnx/z9j5jJHxQXI8oyR++9r/Gdi5IQJMjExMjH+/VP9/z+DFyMDwwJVB6WsBkbGfyiakDkN//8z3Tp4fz4Dw/8IBkbGLjU7xXp0DSD1KH4iK/SIjWjKI5dYmwAvRTkO6KDslQAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520012745JDHSU4RD4E">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_0520015390MP5I4AD94P">curdObj.add/update api内部调用</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_0520020469WQ7EYBWDZK">约定的api [ xxxx/add</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_05200204701EF42276RX">（</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_05200204703ZXKGPRTKU">update）]。</span></div><div id="k_05190324516JLSSSVH5P" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0519032452Z6D588UTZ8">​</span></div>
</div>