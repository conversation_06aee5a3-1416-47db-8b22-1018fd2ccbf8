<div style="position:relative;">
<div id="k_28162416575JJBYQKIBV" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(235, 240, 250); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(3, 225, 245); border-image: initial; padding-left: 14px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(8, 23, 240); background-color: rgb(235, 240, 250); font-style: normal; font-weight: bold;" id="k_281624530756REIPBUKG">​一、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(8, 23, 240); background-color: rgb(235, 240, 250); font-style: normal; font-weight: bold;" id="k_2816242254MWBHBLEQ13">​​​</span><span id="k_28162419525B6Y1RC29I" style="font-size: 14px; color: rgb(8, 23, 240); font-style: normal; background-color: rgb(235, 240, 250); font-weight: bold;">源码下载后</span><span id="k_2816241953D94P5DGQ7Y" style="font-size: 14px; color: rgb(8, 23, 240); font-style: normal; background-color: rgb(235, 240, 250); font-weight: bold;">，</span><span id="k_2816241954LGM7RYAXRZ" style="font-size: 14px; color: rgb(8, 23, 240); font-style: normal; background-color: rgb(235, 240, 250); font-weight: bold;">请了解</span><span id="k_28162419557RD1R63F8F" style="font-size: 14px; color: rgb(8, 23, 240); font-style: normal; background-color: rgb(235, 240, 250); font-weight: bold;">以下文件夹（maven工程）</span></div><div id="k_28162453262R9ZDRN9LI" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: bold;" id="k_2816245327T1AU46669V">​</span></div><div id="k_2816250317AJEW1QZRZ1" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 47px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: bold;" id="k_28162503183KKQE75YFO">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2816320309FMNSEJ78LA">​</span><img tabindex="0" src="/bui/tmp\f67efc50513311e98f563c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_28163203106T97Z88JTX">​</span></div><div id="k_2816250382UAZRYOEVFK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: bold;" id="k_2816250383EFLA9VKD4S">​</span></div><div id="k_2816242264FMH9J7GHH1" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(235, 240, 250); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(3, 225, 245); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; color: rgb(8, 23, 240); background-color: rgb(235, 240, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(8, 23, 240); vertical-align: baseline;" id="k_2816245772IB26UGRF5I">二、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: bold;" id="k_2816243555Z7PLH7TN53"></span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; color: rgb(8, 23, 240); background-color: rgb(235, 240, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(8, 23, 240); vertical-align: baseline;" id="k_2816245317K5A1LC335P">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; color: rgb(8, 23, 240); background-color: rgb(235, 240, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(8, 23, 240); vertical-align: baseline;" id="k_2816242265H88BDY6FOU">​​​</span><span style="font-size: 14px; color: rgb(8, 23, 240); font-style: normal; background-color: rgb(235, 240, 250); font-weight: 700; text-decoration: none solid rgb(8, 23, 240); font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; vertical-align: baseline;" id="k_2816242266LM7KRCD11V">​数据库安装</span></div><div id="k_2816245780RWEJSL266T" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: bold;" id="k_28162457815TDY8U2441">​</span></div><div id="k_2816250035ODY5NJ5TDX" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: bold;" id="k_28162500365NTX1EQ5FC">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2816350364G74OJBI4X1">​<img id="k_2816350365MRD4QL8DZ8" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABc0lEQVQ4T52Sv0tcQRSFv3MHIiGtvrepEhBL/4JUYhsQtBFCUokiqWTVdKJYRYipAipWWUiqBAIp0oRU+Scignb7Vi1EJD9g5oZ56+oGU+3tZuZ+cw/3HDFg6YbzoQIeW2DOnUf5XuJ7iux34DPod/+MGmzgIxivEE9xjl0c1KAzhniA0yLRbKOTHqwSvydjC5h1sVJF3oN+5ob85saiYE3Q8sRqhS7rTwt82gIfHOaryH4957r8DmBlhsV2isx00McaLM13EKMx8uwUzrqM/hR4qcBuPqXIhgW2cA6rxFJWpEbwL+4ckXhJ4PVV47oF1gVTDp+ILGG8kHh4GXlygc5ugbm5pzRDHlnowHmju7zRa7A030aMZ6kGKcvrTepCqobx+yHwVs6PdqIJ+pW3OoXRQjTzcgoozHieEm8yBK4yMCfY+3c5fXYgltuRdzdm+93SmAc2b9kxcAD6IzcCExZYwJnsusLXFNk9gW//jdwgOf8LbZ+sD1FtsnMAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: bold;" id="k_2816350366XH5Y2T4Z96">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_28163534914ELT5RRR3V">建立mysql数据库，取</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_28163843242KKS8R1FK4">名bui（任意取名，修改链接信</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_281638432568YC2I8TD4">息即可），数据库配置、spring配置、mybatis配置位于[</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(139, 109, 56); background-color: rgb(255, 255, 255);" id="k_2816373487SSMFV46NEB">&nbsp;web\src\main\resources\configHome </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2816373488K4RLGYPLFH">]</span></div><div id="k_2816330361VCC1FE8NE7" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;" id="k_2816330362YVCMLIKUXD">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2816351549AEHV3AT17B">​<img id="k_2816351550XX8RV8HXDQ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABvklEQVQ4T52SP2hTYRTFz/neS9Km7wldigYKhvpnFhwFt4KgCQYidhZ18B+mOupQcFMErYIFxaVECFRqBsHJgoODs4OaWnVQarFgnk+bl/cdea9Gq3XKt32X87v3cO8h+nzsca9xIOcOuRUZc0bC3qRO4gWtvdn91p3bicdrG2ek4Bvv8IhBPAOyDGhVQCsFgTGAw5DmLZwTO4KHyz2Yy6h6gb92C8IRAbVskLs/isb3RPAJ40OhN1ATeIlU3WvnTo2gEaRN33qloyLrkD1bDJrTBNTrKlSzS/hs5Ps1wFyhNFEMHj1IwZZXngW1KxOjPBrmVtYtNjpL+co2a7r15C+ri3B4G+KrwcAeL6AZctEvP5fQykS83M3Yu4nQsWYyNvYayP2QFtzIHIsymiIxNgB7sNBurmwGE/Fvr1ow1p1Q+GVV/pY7AHf/Ab3SjIg9iVULV6m9X5MSaHs49/F9vlSIHMwTeMn215NFPP3BRa9UFThL2MlkOe/yla3W6V4wsXs1gZQu8NBp0Nz4azn/nsMG0b3esT+gOtjxO+cgTG06R98B2Bg5J58dl9F5gPvW63pGy+tx2Hny38j1k/Ofm7PnD8dEF0AAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2816351552VIBSR21XI3">​</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;">打开data-sql文件夹，分别执行&nbsp;database.sql 、t_sys_base_menu.sql 、&nbsp;t_sys_base_org.sql 即可。</span></div><div id="k_2816332043LV6W9GX6MV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;" id="k_281633204552AOPJGT5T">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2816334005UW1JRGC223">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_28163529943C676WUI29">​<img id="k_2816352995YLS5GTMTED" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABpElEQVQ4T52SP4yMURTFz3kz3vuK3YTCZrNUCord0FCssQVR0NBQKIkKBRGJf40CpS3WbsQEnV5DIVHYNZNIhGQbEpodstnCn1B875nvHvm+7EwWW83t7s395Z6ce4gBi31uHsOZy66AOClopJwTXIHwMLf8Nvbh59obFRhehx3o4gnB7YJ+U/xezkVtJLhB0AfUcSTuie97MLGAkcDwDMQuCM1YxMuYwrdq4Q02hxjuATgK4V1UPIQGVio1WSu7CeCqyR6kyXQahPqSFjGEHLWQQpPkMQC38sn8WgWGVnhLccycHUxD6VMFTeCXX/ATzrmnZWuwi05uRtSXqHgAe/G1vLgsaLlgca6u+uNqkXbByd0BsBVAp8vuiZpqMwRHZWrERvy4Hlgu96pjZodTSEshhRckx9aCbQjbSqkoYKvyqksV1EiLvu13OnPPBXWij/uxGz+YtbMbkq5LelSa41/5cZLTks6XEAT6lm86ulN/mfPPO+5Hi5f6z36JTVk9mxN0/L93DB6Ang3zGPY1f8bBnRW0ZTVynw12NxVpdt3IDZLzP+Ox4Q/7pgKbAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2816352996MFFZMVEK32">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_28163412642UURF9NZ6V">无需插入用户，系统启动</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_28163534969VN6DI5X9R">时候会</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2816353497KGKUCLF15B">检测是否存在</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2816341408B9TU2TW7GJ">admin超级用户，不存</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_281634140963SPW33GPM">在会</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_28163414106DGAAR9IP1">根据configHome下的common.properties的配置插入</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2816340033OGQIN7NVSM">admin超级用户(默认：admin/000000)。</span></div><div id="k_2816250103KGNWAV1DY8" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: bold;" id="k_2816250104RFT3QUFGWQ">​</span></div><div id="k_28162435641CQSA73ZME" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(235, 240, 250); padding-left: 14px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(3, 225, 245); border-image: initial; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; color: rgb(8, 23, 240); background-color: rgb(235, 240, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(8, 23, 240); vertical-align: baseline;" id="k_2816243565OLTIY7FJ84">三、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; color: rgb(8, 23, 240); background-color: rgb(235, 240, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(8, 23, 240); vertical-align: baseline;" id="k_281624532064627GY7PT">​</span><span style="font-size: 14px; color: rgb(8, 23, 240); font-style: normal; background-color: rgb(235, 240, 250); font-weight: 700; text-decoration: none solid rgb(8, 23, 240); font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; vertical-align: baseline;" id="k_2816243566IKXMFHE2NJ">​eclipse工程创建</span></div><div id="k_2816241960RS4KZW6HKS" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2816241961VGXIODHOQI">​</span></div><div id="k_28163804746N9BY7GKPJ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_28163804745X9GD1NRV9">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2816391277WI3IU2VVDX">​<img id="k_2816391278JAG2SWVMX5" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAtklEQVQoU2NkIAMwkqGHAawpweE+B/NPxpT/jAyVjAwMUv8ZGJ4xMDDOYmDk6pt3VPQzusGM0eZv+TiYPi1hYGT0xbD1//9TjP9Yg+eclHmCLMeYbPWwnIHhfwdOZ/5nbJ97XK6agYHxP0wNY7Ll/YMMjIx2uDT9Z2C48v87u9P885Kv4ZqSLB9cZ2Rk0MCj6RnTPwaXOScUrlNmE1l+IhB6h5h/MUbMOqvwHCX0yIonslMEqRoBBpVSDk0WzcQAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2816391282NZQZWZRYPB">​</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; font-weight: 400; background-color: rgb(255, 255, 255); font-style: normal; vertical-align: baseline;">在eclipse开发环境下，将basic （maven java工程）、web（maven java web工程）两个maven导入。</span></div><div id="k_2816381536ZFUYL67RAY" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; vertical-align: baseline;" id="k_2816392958PWRMT4UIWA">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2816392963RPK66T9COE">​<img id="k_28163929641RU25DM95I" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAtklEQVQoU2NkIAMwkqGHAazpNoMnOws3S+R/JqZKBgYGNQYGhlv//zHM4P76fZYEw66v6AYzvmII5fnC+3MqAwNjHIat/xl2Mf1jTlD4tu45shzjXR7/LEZGhqm4nPmfgaFS6fPGTkYGhv8wNYz3ePw2MDAy+uPW9P8o2+effrIMO98hNPH6n2BgYDDHEyC3WH/995b9uekOZTaR5Sf8ofd/4z8G5jSVL+tfoYQeWfFEdoogVSMAS6lTDpP6LawAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; vertical-align: baseline;" id="k_28163929656CIECMS93G">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; vertical-align: baseline;" id="k_281639296081W7UCJY5S">将web工程编译部署至tomcat运行即可，</span><span id="k_2816381702TXNL18D9DD" style="background-color: rgb(255, 255, 255); font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; font-weight: 400; font-style: normal; vertical-align: baseline;">数据库配置文件configHome/jdbc.properties。</span></div><div id="k_28163837039P7X7QV5FV" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 31px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; vertical-align: baseline;" id="k_28163944113RNKVB3U44">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2816394414JNK4UXCLPK">​<img id="k_2816394415Q8PS9W74OD" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAt0lEQVQoU2NkIAMwkqGHAazpSKcIL8Off40MDAzJDIyMfAz//3/6z8i48T/Dz2K7qi+v0Q1mPNrNI/b/N/sOBgYGQ0xb/z9g/P/f17r6/RVkOcbDbUKzGBkYU3E78/9K68p3kYyMDP9hahgPtwnfZGRgUMPjt2esf//YmNd+vA/XdKRV+AUDI4M4Tk3//39i/P/P17rmwyHKbCLLT/hC7z8Dwy3Gv7/8bGo/30QJPbLiiewUQapGAJmDWg4EIbJGAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; vertical-align: baseline;" id="k_28163944166WB57G55Q4">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; vertical-align: baseline;" id="k_28163944122ZULBJK2UP">如不了解eclipse下</span><span id="k_2816383706CO9TSUYAS5" style="background-color: rgb(255, 255, 255); font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; font-weight: 400; font-style: normal; vertical-align: baseline;">如何开发maven j2ee工程，请先自行百度学习</span><span id="k_2816383707WMPP7HN76V" style="background-color: rgb(255, 255, 255); font-family: &quot;Microsoft Yahei&quot;; font-size: 16px; font-weight: 400; font-style: normal; vertical-align: baseline;">。</span></div><div id="k_2816380421XA8TVY7PU4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2816380422PC4FOCTCOH">​</span></div>
</div>