<div style="position:relative;">
<div id="k_0813164140STA645CK9D" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(170, 191, 250); padding-left: 12px; margin-bottom: 11px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250);" id="k_0813164141MOPQDY4Y23">一、​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250);" id="k_08131643441SWAZHPOTJ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-style: normal; font-weight: 700;" id="k_08131929795NQXE99HND">设计说</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-style: normal; font-weight: 700;" id="k_0813192980RFDKMV88VP">明&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08131740863FL7T6CQOD">​<img id="k_08131740877H7TMKQ32J" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA2UlEQVQoU63SoUqEURDF8d8gRkGwWVywadSywWb3CTSIwSBi1RewCqtNUDD4ErtZ3GC3GYxuscvIhfvB58e6bvCGe2GY/5kzhxvqycwN3GAHC7X8heuIOGv6yhvlqsAdtvAaEZu1PsRbRBz9gFrAOsZYmwd6QQ8X2Ea/AxW7i40rTCIzP7HUrIbniOhn5h4GWMV9sZiZ+7gs0DvOI+KhFcohTvGIAzzNhDLzuKhhuZvgr5My87bsGBG77cRqmtPtzQt9YKWjOpo5aYqFv+39F3SFk9b/6+pOvgF9WIfo8UJjjgAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-style: normal; font-weight: 700;" id="k_0813174088XFWSKWM4DE">​</span></div><div id="k_0813164346YN4TSPBBOS" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813164347ZYU516WWCH">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813180618JSILCMGKW2">1、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08131806198LAV936IPQ"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813190572SDI9Y9FYU8">国际化语言</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813190573PCEVVPENCN">采用properties配置文件方式，一个语言采用一个文件夹标志，如中文zh，英文en。</span></div><div id="k_0813180077QVWNH37BQ3" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); margin-left: 36px; line-height: 30.8px;"><span id="k_081318007999MQB6T5B1" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">​2、语言文件夹要求放在configHome/lang下，zh/en文件名称即为语言key，可以在configHome/common.properties中配置lang=zh/en启用系统使用的对应语言。</span></div><div id="k_0813180080DH4253ZYR7" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); margin-left: 36px; line-height: 30.8px;"><span id="k_0813180082342UU9MTPY" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">3、国际化语言采用通用</span><span id="k_08131800833RK1MT7S4R" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">common.properties</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081318401872FYMHG53J">语言配置 + 模块语言配置（如用户模块的user.properties）方式，common.properties已经配置了常用的语言项，如[</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_0813184019SXNKRAIHYO"> 新增/删除/查询/确认/取消....... </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813184019WP5D5Z9GEN">]</span></div><div id="k_0813180086QX4BKT3AWK" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); margin-left: 36px; line-height: 30.8px;"><span id="k_0813180088RKJZV2V8S2" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">​4、代码生成器会根据建表注释说明，生成对应表（模块）的语言文件，开发者只需要在此基础上调整即可。</span></div><div id="k_081318008851SV55Z9LJ" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); margin-left: 36px; line-height: 30.8px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813181387JKDHJZHPRB">5、国际化语言文件，在系统启</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08131856899KOKWPJK4P">动时候，会加载到系统的静态Map中，开发者可以通过登录用户得到语言key，进而从语言map中得到语言的集合</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813185690SQSQ1CNHKN">。</span></div><div id="k_0813174717Y2CEXO7Z7A" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813174718OQ3EEXG8O7">​</span></div><div id="k_08131912959DKRDBFSFZ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(170, 191, 250); padding-left: 12px; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813191297D6CDCXQ431">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813192459FDHRW2R69G">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0813193898QM59Y2RQGD">二、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0813235577OQB7UEZND3">前端开发国际化</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0813235578JM9AALFKN6">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0813194844IH6M69QTSY">​<img id="k_0813194844CIGU6N8BM8" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA2UlEQVQoU63SoUqEURDF8d8gRkGwWVywadSywWb3CTSIwSBi1RewCqtNUDD4ErtZ3GC3GYxuscvIhfvB58e6bvCGe2GY/5kzhxvqycwN3GAHC7X8heuIOGv6yhvlqsAdtvAaEZu1PsRbRBz9gFrAOsZYmwd6QQ8X2Ea/AxW7i40rTCIzP7HUrIbniOhn5h4GWMV9sZiZ+7gs0DvOI+KhFcohTvGIAzzNhDLzuKhhuZvgr5My87bsGBG77cRqmtPtzQt9YKWjOpo5aYqFv+39F3SFk9b/6+pOvgF9WIfo8UJjjgAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0813194845ECSICQ2EXL">​</span></div><div id="k_0813174774DBNRV1OFWA" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813174775XDC49EPY7D">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08132009895YBZPYYS6K">​</span><span id="k_0813200987YPDCDRLDBH" style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">Bui-ssm框架采用thymeleaf视图，直接支持html文件，前端开发可以通过thymeleaf绑定语法进行语言绑定。如下：</span></div><div id="k_0813174721ZMWGHWUXTY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813205426YUZC9D4IRN">特别地：绑定路径为&nbsp;Lang.</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(170, 170, 170); background-color: rgb(255, 255, 255);" id="k_0813202143TMLXGDWZ2C">配置key</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08132048362WU8UYKGB9">&nbsp;“</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background-color: rgb(255, 255, 255);" id="k_0813204837OET8N46E1Q">Lang.</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081320483813LUY2434G">”是必须的前缀</span></div><div id="k_0813232110KULCQRIH5D" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813232110B72EKVDCJB">​</span></div><div id="k_0813174755PKRZ8MBVPW" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255);" id="k_08133038271ROEMJUXF5">Demo：</span><span id="k_0813220507VUF4KRV74T" style="font-family: &quot;Microsoft Yahei&quot;;">​</span><span id="k_0813223966P2CWGG9U6R" style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_0813220506S5B7Z23M2V" class="_section_div_ clearfix" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 73px; width: 1255px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><img tabindex="0" src="/bui/tmp\53a08d8059be11e99e913c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08132239677KORVJ6POZ">​</span></div><div id="k_08132126466MNAZ8KIHC" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 33px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813212647MRZL1XUUIY">​</span></div><div id="k_0813235153A8URCU7AS6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(170, 191, 250); padding-left: 12px; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0813235979VFHNODHQI5">三、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0813235154THFIRL6KYA">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_08132352235T3AN5DO4Y">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0813241212HOG7QH955I">后端开发国际化&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0813241215VKGLIM9Q1G">​<img id="k_0813241215IU3QG56KUJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA2UlEQVQoU63SoUqEURDF8d8gRkGwWVywadSywWb3CTSIwSBi1RewCqtNUDD4ErtZ3GC3GYxuscvIhfvB58e6bvCGe2GY/5kzhxvqycwN3GAHC7X8heuIOGv6yhvlqsAdtvAaEZu1PsRbRBz9gFrAOsZYmwd6QQ8X2Ea/AxW7i40rTCIzP7HUrIbniOhn5h4GWMV9sZiZ+7gs0DvOI+KhFcohTvGIAzzNhDLzuKhhuZvgr5My87bsGBG77cRqmtPtzQt9YKWjOpo5aYqFv+39F3SFk9b/6+pOvgF9WIfo8UJjjgAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(170, 191, 250); font-style: normal; font-weight: 700; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_08132412164GTF4QT3VK">​</span></div><div id="k_0813174798J7LVA3DML9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0813243075NZZRF9HHLP">​​​1、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0813254644JB51D4AAKP">an</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0813254645VHIL85BIPZ">notation验证配置信息国际化</span></div><div id="k_0813243089ZZVN52WDN5" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 61px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0813243167FG1PGHYH1G">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813251352T78FISSOU4">​</span><span id="k_0813251350MWH7VN84BH" style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">在配置实体Bean验证时候，需要通过配置语言key（LangKey）指定验证返回的提示信息，如下：</span></div><div id="k_0813251496KU47STDKRK" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 107px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0813251497ZBIALNRQGK">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813254045R6GHDO74UY">​</span><img tabindex="0" src="/bui/tmp\bf9322a059be11e99e913c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left; border: 1px solid rgb(186, 140, 255);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813254046QC1SGHNV61">​</span></div><div id="k_0813244587TVB9PGLMRV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0813244588SE3CK9TMRZ">​</span></div><div id="k_0813243178CQ1JOAFVAA" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0813243179EMX2C3GNEC">2、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0813273278QDOYL8YUXP">编码方式获取配置语言</span></div><div id="k_0813224472U3U19J9ZCH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813224473T35AZYBHV9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813270425MOS5OOD9X5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_08132756433KYR4WOP4D">开发者编程</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_08132756452GLTW76NED">过程中，如果需要用到返回值，提示语与语言相关的信息，都应该配置在语言文件中，并可以通过以下方式获得语言。</span></div><div id="k_0813224472U3U19J9ZCH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08132714931R4VPP7ALN">​1 .&nbsp;</span><span id="k_08132714905E2CKBW2EW" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">controller基类封装的语言API:&nbsp; &nbsp;&nbsp;</span><span id="k_0813271491ZENGEWB9NX" style="font-size: 14px; color: rgb(3, 118, 240); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">ResetfulBaseController.getLang(String key)</span></div><div id="k_0813224472U3U19J9ZCH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 63px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813272062UDR4JEA2FN">​2 .&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_08132756485A8IAG1BMI">采用编程方式，得到</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0813275649UV7WZ8H9GG">语言，如下：</span></div><div id="k_08132245306YJ2CA4GIM" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 110px; width: 604px; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813224531ZT2A3T7Y7U"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">       <span style="background: none; color: rgb(165, 165, 255);">/*****获取所有的语言配置******/</span></span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">     Map&lt;String, LanguageMap&gt; languageMap = LanguageStore.getLanguageStore();
    
    <span style="color: rgb(165, 165, 255);">/**通过登录用户得到用户使用的语言***/</span>
    LoginUser user = loginUserCtx.getCurrentUser();
    String lang = user.getLang();		
    LanguageMap map = languageMap.get(lang);
    
   <span style="color: rgb(165, 165, 255);"> /****通过key 从语言map中得到需要的配置值*****/</span>
    String resString = map.getLangByKey(key);</pre></pre></span></div><div id="k_0813224572FEK65NDKWX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0813224574I8AIC1E8KC">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div>
</div>