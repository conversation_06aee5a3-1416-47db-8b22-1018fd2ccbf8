<div style="position:relative;">

    <div id="k_2721093316WF3OGP8F1U" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(245, 242, 252); margin-bottom: 14px; padding-left: 15px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(149, 117, 245); border-image: initial;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252);" id="k_2721291548ZSPUQGC6JK">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721291551TZAPWYANCG">​<img id="k_272129155156B8RFMFQX" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABI0lEQVQ4T53Tv0vUcRzH8cf7Su/r4JD/gGNIBc4ujaEOLYKz1JaDixBuLkFIm41RIG4uNUn/gIMuCgpOEoSODSF9vPPuLXpkciJ39l7frye8Xu8foWdlqIwqfhLnN+XRk63yufBeyyuN2O8fHswxD3yWRrTNaMRef3CVo/goTEtfFPPEaQ84a+omhQ/CY+mHlhnN2OmOGGTNoKcaTlBUloS318L0HRtatjUdEI2/vTCUi9KwYsWQd1zau7O++mOO+HWpCFVuYk2xqbIuvLgTzSvdG+L3/8DLimUi7w+3vXQW3/5l7t/2rrZZZ3F4f7grb8d2Z9oPFasqi1gQhm8NLb1W4lPXkWTdgCeajjoryEfqJtRMSc8wji3lakXH/Z1nz4/hAsjIbhDQZTDAAAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252);" id="k_2721291552EZ7XMO27GH">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252);" id="k_280931456263B1IXT9RI">高效的开发特性</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(170, 170, 170); background-color: rgb(245, 242, 252);" id="k_280931456336O68XBJT8">( 3分钟即可完成一个包括页面的单表curd开发 )</span></div><div id="k_2721101623AJ94I6PK57" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2721101624UVEFIEUOIP">​</span></div><div id="k_2721305761TDTUPWO72Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2721305762UZB26SRGGW">​</span></div><div id="k_2721305825GMSDWGJJHF" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_27213058267HDFP24SJ8">​</span></div><div id="k_2721305874QISTQJF7OE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_27213058746JJ9FXU18H">​</span></div><div id="k_2721305809XBMGKP9QI4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2721305810JFD4G7VLAY">​</span></div><div id="k_2721101789B5PH7ROPLL" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_27211017907UVLOY3QPM">​</span></div><div id="k_2721101716OL1UUCJHW6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;"><br></span></div><div id="k_2721102051V2652IZ14B" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(245, 242, 252); padding-left: 15px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(149, 117, 245); border-image: initial; margin-top: 0px; margin-bottom: 14px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_2721293650SDWKAZIZ7L">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721293653K3TJTU88ON">​<img id="k_2721293654CF8WH2XLEZ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABJklEQVQ4T53TMUtcQRTF8d+FEN4jbCkWFvsF/AYB7dIFyxgMpEoMNhaCSNKkFMFgIySQxkIsAylSpssHsLS0UpJa3tuQ3Rve6vKyG5XV6eZy/ndmzj0T7rVyRuFp3J3NOaV9zN4NLrLLEJzHy+nhFlzAG7WjKeGcVfqEJWyp7BJ/poDHwC8qq8SvxqsgOx564rdvRDVuYD5S2sGadCqsqOLHSBPK3MO69F7dCEcN/gEv1ZsqH4h+Cxe5iAOhK71VNwJ9pQ1sXwnHrtvCMhSe46PQkd7hHHvDPWfSC3V8n8zElWH5YOKkVjewq9c0jN4NcFP+741N8djAM704uS6JE6MaGwvptdpnIqeAUeZj6VA4VnlF/Lwp/9eEJENp2cCFXny97eP8BcuoWI79zFEJAAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_27212936553K5TK8ZKWN">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_27212936515JSW8QIOSV">成熟的技术栈</span></div><div id="k_27211024969XN3ZLP3PQ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2721102497ZA1H1K7FLB">​</span></div><div id="k_2721454099ZJMHB6I6TZ" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27215525101CXQFL2KCY">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_27215525128D7AT2YZ4F">​<img id="k_27215525131QK6KN1VUH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABI0lEQVQ4T53STysFURjH8e8ziq6NNHem6J67sLezRLLHTtl4AUqiFLpbibWFFV4BNuouUOzkBVjYzT2hmWEhKeXOo0ma+2focrbnfM7v1zmP8M8luU61G/hAJPnp3nwY2WUcvcU11c6h3vcS1y9QbvDNYucwDMYRTlCx9DBBn3nOw+1V42ATlXXgFWQOr3SaD8PaFrCEUPj1gVUfcWSBojlOz30lhsEYcIjIUA5W0HMozON5D9/7WdUX6/KeHIBMNeA6qjt4ptL6NRlUdYhql4iMNqUq2/hmrbVJBqNgBJUqQj+qZyADCMPANcWuSWTwrRFnMLQboCuIVPBKe6TTE9tdVKeBWfzyVTtMaz7ZVRKO8M1dU604mCHBxS/v5yf+cdg/AdZ/XA+gPz3NAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721552515M1HWW3MU55">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721552510G3JZX3WL4M">前端技术栈：</span></div><div id="k_27215751503DOEL26C14" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721584291XOHRDM4HKQ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721584293WP2PHFQFMH">​<img id="k_2721584294O3HRVMQKFS" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAA5ElEQVQ4T2NkIBMwkqmPgXoaG+bP5+B4+Yu9oiL9Iz7XYNjY1jdtKSMjg8N/BqaQqsKM47g0Y2hs7Z9pxMTwbyNIwz8GJv/qwvRz2DQz9vXNEfrK8lcQWZLpz19zJgaGKQyMjF//MTF4VudnXkHXzNjeP+0pAwOjFC4n/f/PcOM/E4tDdUHqS2Q1jO0Tppoy/GMyRxb8z/hfnvE/QzYDA8OPf0yMkdUFmTsxbEQXaO+bpsTAwLgBpJmJgSGiojBrO1Y/YmqcDtLkiE8TSA9GqDZMncrD+puNG91PBJ1KbBIkO8kBAFYmRQ8ASct5AAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721584295IRCM4OA1JR">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721584292IMU12Z9TPD">jquery、功能丰富全面的bui框架、bui-editor、解放表单开发的双向绑定、基于约定胜于配置的前端CURD封装。</span></div><div id="k_2721492471ML8WUQG86C" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_272155374321JT2QIJSV">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721553746B5KEQV1EXR">​<img id="k_2721553747MPXT4I6UNF" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABSUlEQVQ4T52SzUpCcRDFz1xNItBrEBSC/m0R9BpSQbQqwlWIaUQfT9DOXiDatHCTetcFPUBEi6BFQdCmTS68Km71Rkh+njBQbnoLa3bDzG/OYWYE/wxx4o5BLQVQIPxpriNoqGpE62ilWFnPjw3egm5TWWkR7S5e8Bljg0agHqKneU3gedatx9by0nCCR6waqrpByAWFFbS1pURZf3UEc6q2CSANwcxvCybxRLpiyaL3pdf3pZgJ1IPaRDMDwfIITHQAnDZaemqvIvV+fWA1G+YkUDsSSMoGf5Dc2jb9V8OnGYAEJaesMxEc2lWli2i86L8cdmJTfJ8DWzcQWRDglMC8AFEQ58rU9yOQ9reB/SQbtlbB7gnh2k2avvve96iQtSOCA2l51uOVqeII2LNpBK0VuviQKEzX7A3Z8NsiOvQmSvqjo+Jff/0T0rZ1D28Sl/QAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721553748MIGZHY4KH2">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721563136NLDDK5ACAZ">后端技术栈：</span></div><div id="k_2721575409RJQ3T2YYGH" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 143px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721585333OHXVCKIPT3">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721585336XUPO2VR13J">​<img id="k_2721585337AARSGPKLYZ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAA5ElEQVQ4T62SsU4CURBFz7xHFIRGExuCPILVQkdiaLGkoqGAb+B3/AV6qIxfYGfFbtAC2BAKSwsTi919hG6zD9BsnHJmTu69kxFyluTk+Edw3SiSqEvuV1/n3DiK2jdThF4sDPHC11OwazUwHW2ZH4BYGNAK347Bgl+74UKu00MdqS4JT8B3LEmf1naRhUUHZidI9WQeyzIqSY/m+jO9I7zfPahEddNNBcZaJlh+RMk48jYvjqKj9FFvFiI1s2CwdhS3w+fjGTNd7TdmCI/noAPiXnVxW+GqUs5m+t3qH38w98vtAVpGPA+wJUyAAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721585338ET262KB3Q1">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721585334FQ69J58GI2">spring-mvc[</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_272157541198LHKVIA59">thymeleaf视图]、spring、mybatis，poi、fastjson、memcached、atomikos、druid、</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721575412N2CWOR2E8K">集中/分布式缓存、集中式session。</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;" id="k_272157541381RCF4E5QG">​</span></div><div id="k_2721454883NX57XF6IYU" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_272155539548PP8KFNHL">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721555397L27F6ICMOH">​<img id="k_2721555398BA83R7IRLI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABSUlEQVQ4T53SsUrDUBQG4P/ckIR2yS4Kgj6Aky8gLiIUxcbGRUStkzXpSwgiJAoOOlQc1LQFF1dBgruLiCCCT9BBFCVJe49EaC1tlOpdz/nu+bn3EP55KM3V8lDydUgC+Kd7U6G/pc9Kwc9LbnQ/MDwsQjWyms9MgbUX7g8Ma44+3pK4BvCgiDBnuvhIw31Rq466LJkqxNQQgqdMN7pLhX5JXYEgFyDj9wfmR4CtghffJn1fE/1NjLGinRFoshczWBLTcUYPndwOXtv1TtTLIrJvGXWbSJTaRWaOQVwsePFJ79d0IAPk29opgazuqQy5anlxpTdJB56XMQKp3xDzMIOSxlEiTAN88fIeFTaOEHfjb2hrCwDtCrTWFr3mVbI9ckgvM3hdETRjuuFTH0xiVm1tToujYP4Aje6Gmq1OgNgw3WaQOvGvu/4JOh9zDxljlCIAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27215553995IUSFLQJJS">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721555396G25ZCGUP2X">亮点</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721511343HQ9PJBYGAN">功能</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27214951911EIZ679T9S">：</span></div><div id="k_2721500728IDQTP8M9JX" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722012368UJF6J1NAUF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722012371QFQXDR142A">​<img id="k_2722012372ZSJN8KFS3H" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABNklEQVQ4T7WSwSsEcRTHv98Zy6CUG5va3ZuTksMaKQc2B/wDtj0wmwOFqxwlVzk4bOxuOSnlYIu/wI6tdfAPzKwcHOwJpV3zexpFws5qy7u8eq/P+9b3fYkWiy1yCAQtszIpVKuaF1o5uBq4+yrSEFwacftf21EgMKiARK4YvfwDKLTM221QNqCwr9cj65ky603BdNwZVTrPCVQ9JVN5O+Z+94Kpoftuf3h00/fs92S82mNoj8dCJKAknbVj+d8M5KLplEiG6LXN+QZYY+6yAHuEFGpPncmPgz8ULdPZBbkGkTOP+pYO7wRgl1KYztnR60bv4vx4pdfw1CnJCQFeCBiisJm1IzsApSHoLyzTHRa+Wx+GSEmvdcxkyuGHoHB8/nHBrMxqlBxEUofF2EWzRP1P5IJU3wAmP2sPuZhi4wAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722012373M35ZUEV7F2">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722012369UVLHTOL6HI">动态多数据源实现，多数据源全局事务支持。</span></div><div id="k_27215055732Y7LS2SVYB" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27220133126DX1F5PYVB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722013314UJHINR23JS">​<img id="k_2722013315J6RZCN8KAY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABKElEQVQ4T7WSP0vDABDF30u6iZs4OGhJoB/BSfoHHOqsYkkcRFAcxElwtIO4uIiDOjkIDYIgODiIlKRaP4FLQZqAQz6B4CDtkwyKqE2g4C0Hd/zuwbtHDFkckkMq6PjhNolVGOZaozj1+F1kIOjch9Ps41rQGIAtr2yfZIJ1yXhuRTcAqoLa7xqdu6yMv2aCThCuQzoi+QZDC42i3fzpBWvtl4lkeDEzGSe95kd5k/07gTaEY69ibf5lIN0gjCUYNM35xAAn6J4BXCHQ6eVys58Hfym6QegDKAvokDqAcCgwB2LDK1nng97FpYduwezxlkBeQo+EKeiqULIW62R/IJgsXD9cBnAKYkRATKraKNlPaeH4+qPTinYh7QDY88rWflai/idyaaofirljDy7yz7cAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722013316DKGFQ7FH33">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722013313W257W6AXPZ">通用的xls导出导入封装。</span></div><div id="k_2721514351IG9EOYBOHF" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722014426KNEPH3UBDB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722014429N3B7AEAX5M">​<img id="k_2722014429HBUHRBLBLB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABI0lEQVQ4T7WSsUrDcBDGv6+GShNQENQGH6CDT+GidBEsxAQ66OAmDn0CEVwE30BQB8HETi4Koq9gFx+gW1oVQei/FKz5JIMiahooeMvBHb/7uO+OGDM4JoeRYC+INynsWODGZFR++C6SCQ68p8p7YXgD0qVUtyO3mQsKogm65wR8SNd2qVzjKQe5YN/v1oTkTIShrBUnmr3/6QXlvc6kRTanX9Kstc6cKeKWxCKkfSdyd/8ykCaI2wAmLLGaGtDz40OSDUgtO7GXPwf+UjRB5xLAKqAWxD1RJwSLSLTuXLhXWedi33teSApvdyQrEIYgLADHdji/RVCZYNow/mNVTEICUwDaBWipFLrpCpnxdUcTxNsADwA0nLB8lPdR//Nyo1Q/AJSnYw9elwuiAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27220144306KNCXOMMIO">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722014427Y9EYP91EEV">完善的国际化语言配置方案。</span></div><div id="k_27215250939CT6QHJEEM" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722015967IEZB85Q7WF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722015970K5CULQMKKO">​<img id="k_272201597085ZHK9Q4CC" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABDElEQVQ4T7WSMUvDYBCGnzctiENBm06CS1MnN8FF/AMqiIOD4F53i1PFQV0cXAQXJ7cOgv4AB3cHNyeTtnNIgqtgcpKCImoaKHjLB/fx3AvPnZiwNCHHWDAKOJOxbeKg4XH7PaQQTAI2zLgGZhAnrsdxKWhGNenzAKya8TQFa7UWYSkY9ekq48jgzRF7dY/eTxeKX1jMm+4Cz/n7OmQpTbkD5g16DY/dvwQq8gkFjol2LiAORhK2MAYYm58DfyVGAY+CZWAguMrgEKMqh67b5LxoXYp9VgxuJOaAFKgA9/Um6xLvhWD+EQfsG5wKpg3CirEz2xpZLayvPSY+Fxm0JS5dj07ZRf3PyY1L/QDi2E4PXiczFAAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722015971SY4WL14GQA">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722015968ZMOID8QTRL">可插拔的用户登录实现机制。</span></div><div id="k_2721540114IEVW6FS8QE" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722024480EIJG1H398V">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722024483TPJG2B75HJ">​<img id="k_2722024483Y1HI4N7GDY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABK0lEQVQ4T7WSv0vDcBDF30saLYJSTRuEVP0fXATHLA4inV1ExMFVBwch7de0o4uLzk6COHUQpFuhf0NxcUhrkdQfKE5qcpJBEbUJFLzl4I7PPXj3iCGLQ3JIBM19v6SR24yMjUBNX38XGQhOVnqzGT1skDKHSF8JKnYjHRShVe0cgdwCcBqExXUovqaCZrXr6JC6iDwJdefOta9+esEpdT8RDx+U+Rz3nHrMjWgvFwIsgNjpuzOHfxnIgtdpA8gwMpZiAwpedw+QKohmGI6VPg/+Uix4/gnJNQGaIihrwJlQRin6clC2W4PexXHVy2f190uC8wJ5I2hAcBC4xV2QMhCMF1btZhES1QGYAmkjzDp9Zd0mhePrj/mav0rBMaBt9t3ieVqi/idySaofEHhlDw/tiMwAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27220244856P2HAAPQ5S">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722024481N4LNNH3TE4">精细的可配置的操作日志记录机制。</span></div><div id="k_2721572479VFUISFHSCR" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;" id="k_272157248023HR9ZACPP">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2721572533GRIQY13V3O">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722025815HPQZR21Z48">​<img id="k_2722025816FAUFVR2UMP" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABLklEQVQ4T7WSMUjDYBCF3/utiu1f3BwUpVVwcdLZTXFKjSIORdDFTXAVl4JTNyc3FUQoDkLBhg520t3R0RRFUHRwsDEqNP9JBkXUJlDwloM7vnvw7hFtFtvkEAm6aWuSUKsqSGxm/PL9d5GW4JWe7yOCIxLjEmBmxK9cxIICsJ62NwgUaaSYeXkuEGfNWPA6aU0YpRwAD90B7YHXk9ufXvAOuWQ47Ifjh/0Ri7qh33cBLhCyNOxVjv8ykHU9WwPQpUwiHxpQT9nLQuyBKPU0zNrnwV+Krra3SBQAOYQx20JVIqkT0rSGvOplq3fRxXQvdOqAxJxAGgTTELOe9ZwdAtISDBc3OjcWUJUBjEJQ6/Te8oM4fYoKx9cf3ZQ1RdWxTzErWc85j0vU/0QuSvUDjQ9hDyerWL8AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2722025817SYI3UZTP9O">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722130761GWZ77CSEP8">基于</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722130762H8KTHLYMC2">annotation</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722130764HF7HRPJ66M">的自定义实体bean验证。</span></div><div id="k_27223404652JLOOEVF9N" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722353699TGUSXZBEJZ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722353602AULZCAG9UF">​<img id="k_2722353603E7TO28VC1H" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABHUlEQVQ4T7WSu0oDARBFz91HlVdjZWNA8BOsLSxi7aOy2I0oFmIlWJpCbGzEQm3cJX6AYGEhIjb6BTZpbdJnVQJmsyNbKKImgYDTDMxw5sKdK8YsjckxFEwibwdUx7H1cpA+fhcZCHaa/qwyu8KYENou1XunI0Fr4CRT3rVQDbOHUjFd0AqvI8Ek8jfAjk10XbFUDNK7n17o7ZzJfFhYo533bkz13fxbyaYNO6mE/a2/DFQSe20MB4fF3IAk8iNkAaaWa735z4O/FJPYuwfNAS3IDs10JOGZbLMS9C8GvUtJkxnL/BtBFawPcsEuS8/pshpkA8F80YncVaQzoQJmbUdprRjyNCwcX398ib09g11gvxymB6MS9T+RG6b6AR4dYQ8KmnVLAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722353604GA38ZJCQDJ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722353600JHCYMOPZID">无需引入任何第三方插件的接口开发调试机制，解决后端码农调试接口的痛点需求。</span></div><div id="k_2721572776I1AE7X7Z98" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722033091ZQDTPGBV96">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722033093NT9JP5SZYD">​<img id="k_2722033094QUMFPS4J5Y" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABJ0lEQVQ4T7WSMUvDABCF30sqDq4dmoBQEERpujo7ONRZ3RxKm4qDOAmOdhAXF3GoDmkRf4Dg4CAiLvoLmtDuQtqio+CgyZMMiqhtoOAtB3d89+DdI8YsjslhJJhrV3dIVAjUQqf58F1kKJjzqwsELiFmBWz3i95JOqi6YQWPVwRLgu7fMbn85DReUkHLdzcIHEN6jQysDgrN259eMNut2Mnwea4VJj3XKeeNyLwRMEOwETre1l8G0vbdUIJBaiUxwG67LVFlAN23jJY+D/5StHz3jsCihC6hQxFHFDMxsdl3vPNh72K2U5udiOJrgnlJEUlT0EWvML0G1uOhYLKwAncdsU5JTgEKIzMqDebP2qPC8fVHO3D3EGNXwH6v6B2kJep/IjdK9QPa9WoP46O8ugAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722033095225HC4ASTL">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_27220330925FOJSLC8GN">dao、service、controller基类封装，继承即可完成CURD。</span></div><div id="k_2722364686OY85EUU3UW" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722382221Y3NPPYUIOE">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722382224AJSYA5GTJH">​<img id="k_2722382225PZ5FNOI5PC" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABIklEQVQ4T7WSP0vDABDF30stTTJIQWf9Di6CYxcHkc4uIuLgoiQVtH9AAkojVDBF0NlJEKcOQulW8DMUF2dXHYxpaZ5kUERNKwVvObjjdw/ePWLC4oQcRoJuzS4ilitpMzh5e/wqkgruHphzGYMdAfNivNqsR52/gHSr5gVgbEu6zufCDc9DfyzolM0CyZaAZ2XiQvM4evjuBXc8TCfDcw8vSXc85BFZdyQWAZTO6mHzNwPpVqwegClJy4kBbsWsgjyS0B3mwuLHwR+KTtW+IrAuqEvpEDBuAOQErQR+eJ/2Lu55mB327TaBBUkDkllJp4Ef7gNQKpgsSjVrKY7ZIjEjoJcdvBYaDTyNCsfnH52ytUbyUtBW4Ie34xL1P5EbpfoOjGtrD765KrQAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722382226SX5B2TGJLR">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-align: left;" id="k_2722382222W5CMJMCFU3">datagrid数据列表、toolbar工具栏实现xml配置机制，解决工具栏、数据列表页面硬编码的弊端。</span></div><div id="k_2721545359QLDCNEBUTL" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 144px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;" id="k_272154536036OVLZB9B5">​................................................</span></div><div id="k_2721102461K5OCXFLPQX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2721102462R54ECJTG6A">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_2721102452ZBXQ2PMZJP" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(245, 242, 252); padding-left: 15px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(149, 117, 245); border-image: initial; margin-top: 0px; margin-bottom: 14px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_2721295468GRSA3LVTYE">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721295470JTZTUU67QM">​<img id="k_2721295471F93L2SRA95" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABSklEQVQ4T8XSPWtUQRTG8d/ZeOPdwsaAITaCYGmhLGhjaydpBAtNlVJRIoiVFtoogqLoBxAVBC18+QiCpAixsLcSIUJsBHPNsveIl+XubixcsHCqGZ7nf+bMMye0K3fp6hn4YdtHIse0MOuwVOj7QNS/tWgNs7mo4wm+GTitH2utVmTPjJfYq7ZkO15PwmVeE240QDqnimctXOZZ4elQu66Km5NwN1dwtzHUFv2MNy3czTN4PjxfthX3dt58ULgvbahcJTbH3jyndFuYly6p4tMkPHJOvRsFNjUyMv5PuMgj+jaIL9N3nvsV5kM3X0gLBi7qx/pfCxR51IwH+Bx25yHhodDDLVseE1//LJILSsvCirQmXRgGlnuUrgjnmxFkVe2djk21OR0ncLwZ3fRI5Q7xfUfauU/pFE7imHCgAXgvvVV5Nd7VP33VL4OgYB8QNVgZAAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_2721295472P14ZEUSKMI">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_2721295469RAIQ7ABUVW">完善的通用封装</span></div><div id="k_2721105169G71W4KILOR" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27221207704XORRL518H">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722120772PV8T32BZ2M">​<img id="k_2722120773BQNE1TULDX" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABwUlEQVQ4T5WSP2iTURTFf+d9WCMVFUSQYBs7uPpvaZrBRaQ2KDoJIoXYDG4BHbSoQyepg6gILrURSXVwEIQodRB0aKoOIo5ZkkaKIrgULTYk35Xvi/mSCBl8w4V3Oefdc859yqaqL4C0GauC74beGlqs+4PLj9/vXKPPUTZVvY5xEXgDdhRpe4A1aAorusamy3Mf9pT/5Ss7VjluUlHYqo+Xc9a8hHQkAhrrBrn8ciIPsnZfmdEv+5zXeC00ZMZH8zUpZzclTrRBrenk5kt770fEyf3fBge2/n4a+Awl+lzzZc888Qo03JFo5bo2HyssxWtBT0GZSlVmha60QFZrGuMebhzZnR5vvjLz7xKPImLHJ97fYO6JgVvGxnOhA12S5/KlxIXAazjx/KGvuxTbWJQ43BrKuo+XljVGcHogWg8CL+s/Y2cKn3f/CokhObWSc9jdLmmfmmZnPTELOtWXeG70x7aYW1tAOhlJM4ombgsKgrhBr9Q2MHOwssPbooeI0117XEI8MeMqsoV8aWQ6Cqc7uRnMrYzV0g6bNpHs+LMyqDBUStyYQX7ksSf2PpepZHUCEQ9+0X8Rw50nqxOShv8A4JWp8y6pYv4AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_272212077487IIC4WU49">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27221207719F6U4UOTAY">三大基类封装，解放Dao、Service、controller通用功能编码，继承即拥有，重载/重写即实现。</span></div><div id="k_272207246019YABMM33S" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27220724616XKRJZE156">​</span></div><div id="k_272207214772JRKEMCB5" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 95px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27221214056FA23DDH8O">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722121408Z9BB2B27HD">​<img id="k_2722121409P1QLZOHEMY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAuklEQVQ4T62SMQ4BYRCF39s/HEOj2YsoRL0JpUqikVDvn8k/NQdQatxBqRJO4QA6jWIzQkLYbNj8TP2+l5k3j4gcRnK4g1mWuTRNBwA8ybaZGYC9c24qIrsqc4pIsyiKOckxgORVZGYXkpMQwhLAzew5zPO8D2BFslHlbGYnAF1VPbyB3vsNgM6nW81sqaqjMngE0PoS0jZJkp6InB86eu+jwTqrLlR19p9warxjGEJYlzOIL8BPlYuBr99UWw+nLn1qAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27221214111JDF2OBVPL">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722121406I4DD4JROMN">BaseDao&lt;T,K&gt;</span></div><div id="k_27220828919RQ7TCSQSY" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 95px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 12px; margin-top: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722082892AE5C2FD8TX"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;"><span style="background: none; color: rgb(165, 165, 255);">​/**
 * Generate by Kevin's CodeBuilder
 **/</span>
@Repository(value = "funcDao")
public class<span style="background: none; color: rgb(15, 224, 0);"> FuncDao extends BaseDao<func, style="color: rgb(15, 224, 0);"> </func,></span>implements IFuncDao{</div><div style="background: none;">      ..........................</div><div style="background: none;">}</div></pre></span></div><div id="k_2722061781D5YRBQTF1Q" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 95px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722122089559QJIOAJ2">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722122094PS1XTDW5AQ">​<img id="k_2722122094WCS8NQE1LS" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAuklEQVQ4T62SMQ4BYRCF39s/HEOj2YsoRL0JpUqikVDvn8k/NQdQatxBqRJO4QA6jWIzQkLYbNj8TP2+l5k3j4gcRnK4g1mWuTRNBwA8ybaZGYC9c24qIrsqc4pIsyiKOckxgORVZGYXkpMQwhLAzew5zPO8D2BFslHlbGYnAF1VPbyB3vsNgM6nW81sqaqjMngE0PoS0jZJkp6InB86eu+jwTqrLlR19p9warxjGEJYlzOIL8BPlYuBr99UWw+nLn1qAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722122096PU1PW2DRR4">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722122090WWMHEMW8HN">BaseService&lt;T,K&gt;</span></div><div id="k_2722064008RAZZ2DN4CN" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 95px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722064010WRSQ4SNEDM"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;"><span style="background: none; color: rgb(165, 165, 255);">​/**
*Generate by Kevin's CodeBuilder
**/</span>
@Service
public class <span style="background: none; color: rgb(15, 224, 0);">FuncService extends BaseService<func, style="color: rgb(15, 224, 0);"></func,></span> implements IFuncService {</div><div style="background: none;">  ..........</div><div style="background: none;">}</div></pre></span></div><div id="k_2722083095F5D2LAEQKS" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 95px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722083097SBSX5ARZ4O">​</span></div><div id="k_27220641016FQ55HXKQ9" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 95px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722122860QX6CDJ5RO1">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722122863PQZG5POFCM">​<img id="k_2722122863YBSUDX5BL8" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAuklEQVQ4T62SMQ4BYRCF39s/HEOj2YsoRL0JpUqikVDvn8k/NQdQatxBqRJO4QA6jWIzQkLYbNj8TP2+l5k3j4gcRnK4g1mWuTRNBwA8ybaZGYC9c24qIrsqc4pIsyiKOckxgORVZGYXkpMQwhLAzew5zPO8D2BFslHlbGYnAF1VPbyB3vsNgM6nW81sqaqjMngE0PoS0jZJkp6InB86eu+jwTqrLlR19p9warxjGEJYlzOIL8BPlYuBr99UWw+nLn1qAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27221228646IR2TEUK9N">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722122861EBVLZPL54Z">ResetfulBaseController&lt;T,K&gt;</span></div><div id="k_2722061657Q1WLV636SP" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 95px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 12px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722061658JYPBTDJGCI"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;"><span style="background: none; color: rgb(165, 165, 255);">​/**
 * Generate by Kevin's CodeBuilder
 **/</span>
@Controller
@RequestMapping(value = "/func")
public class <span style="background: none; color: rgb(255, 0, 5);"><span style="background: none; color: rgb(15, 224, 0);">FuncController extends ResetfulBaseController</span><func, style="color: rgb(255, 0, 5);"> </func,></span>implements IXlsOperator&lt;Func&gt; {

    IFuncService funcService;</div><div style="background: none;">    ..................</div><div style="background: none;">}</div></pre></span></div><div id="k_272130462179T1M78LGQ" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2721304622N3QVKJCODD">​</span><span style="font-family: &quot;Microsoft Yahei&quot;; font-size: 16px;">​</span></div><div id="k_2722083279MG95WC1T3B" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722124208EWQ8NHGWG8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722124211IIWYLQZ58V">​<img id="k_2722124212GJG1XRS5JT" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABwUlEQVQ4T5WSP2iTURTFf+d9WCMVFUSQYBs7uPpvaZrBRaQ2KDoJIoXYDG4BHbSoQyepg6gILrURSXVwEIQodRB0aKoOIo5ZkkaKIrgULTYk35Xvi/mSCBl8w4V3Oefdc859yqaqL4C0GauC74beGlqs+4PLj9/vXKPPUTZVvY5xEXgDdhRpe4A1aAorusamy3Mf9pT/5Ss7VjluUlHYqo+Xc9a8hHQkAhrrBrn8ciIPsnZfmdEv+5zXeC00ZMZH8zUpZzclTrRBrenk5kt770fEyf3fBge2/n4a+Awl+lzzZc888Qo03JFo5bo2HyssxWtBT0GZSlVmha60QFZrGuMebhzZnR5vvjLz7xKPImLHJ97fYO6JgVvGxnOhA12S5/KlxIXAazjx/KGvuxTbWJQ43BrKuo+XljVGcHogWg8CL+s/Y2cKn3f/CokhObWSc9jdLmmfmmZnPTELOtWXeG70x7aYW1tAOhlJM4ombgsKgrhBr9Q2MHOwssPbooeI0117XEI8MeMqsoV8aWQ6Cqc7uRnMrYzV0g6bNpHs+LMyqDBUStyYQX7ksSf2PpepZHUCEQ9+0X8Rw50nqxOShv8A4JWp8y6pYv4AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722124213ZR31A48TOS">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27221242094JGLHF3YHO">基于约定胜于配置的原则，前端CURD衔接controller的CURD，将前后端CURD做到规范的最大化通用封装适配。</span></div><div id="k_2722124476TPR1RCZ5KT" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27221327851C5IW2SQ9F">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722132789LXOWAMXK58">​<img id="k_2722132790DPBIXPOTVU" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABwUlEQVQ4T5WSP2iTURTFf+d9WCMVFUSQYBs7uPpvaZrBRaQ2KDoJIoXYDG4BHbSoQyepg6gILrURSXVwEIQodRB0aKoOIo5ZkkaKIrgULTYk35Xvi/mSCBl8w4V3Oefdc859yqaqL4C0GauC74beGlqs+4PLj9/vXKPPUTZVvY5xEXgDdhRpe4A1aAorusamy3Mf9pT/5Ss7VjluUlHYqo+Xc9a8hHQkAhrrBrn8ciIPsnZfmdEv+5zXeC00ZMZH8zUpZzclTrRBrenk5kt770fEyf3fBge2/n4a+Awl+lzzZc888Qo03JFo5bo2HyssxWtBT0GZSlVmha60QFZrGuMebhzZnR5vvjLz7xKPImLHJ97fYO6JgVvGxnOhA12S5/KlxIXAazjx/KGvuxTbWJQ43BrKuo+XljVGcHogWg8CL+s/Y2cKn3f/CokhObWSc9jdLmmfmmZnPTELOtWXeG70x7aYW1tAOhlJM4ombgsKgrhBr9Q2MHOwssPbooeI0117XEI8MeMqsoV8aWQ6Cqc7uRnMrYzV0g6bNpHs+LMyqDBUStyYQX7ksSf2PpepZHUCEQ9+0X8Rw50nqxOShv8A4JWp8y6pYv4AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27221327914RMH7FEYWX">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722132787NCNAWW8IMO">通用的实体bean&nbsp;annotation验证机制，无需引入任何第三方验证插件，无需重复编程，​ResetfulBaseController封装了统一的通用验证逻辑。</span></div><div id="k_2722140061ZM4E1XJGYE" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722145353F38SJWAII4">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722145356IEBQGOFFYF">​<img id="k_2722145356R5HMXANYGN" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABwUlEQVQ4T5WSP2iTURTFf+d9WCMVFUSQYBs7uPpvaZrBRaQ2KDoJIoXYDG4BHbSoQyepg6gILrURSXVwEIQodRB0aKoOIo5ZkkaKIrgULTYk35Xvi/mSCBl8w4V3Oefdc859yqaqL4C0GauC74beGlqs+4PLj9/vXKPPUTZVvY5xEXgDdhRpe4A1aAorusamy3Mf9pT/5Ss7VjluUlHYqo+Xc9a8hHQkAhrrBrn8ciIPsnZfmdEv+5zXeC00ZMZH8zUpZzclTrRBrenk5kt770fEyf3fBge2/n4a+Awl+lzzZc888Qo03JFo5bo2HyssxWtBT0GZSlVmha60QFZrGuMebhzZnR5vvjLz7xKPImLHJ97fYO6JgVvGxnOhA12S5/KlxIXAazjx/KGvuxTbWJQ43BrKuo+XljVGcHogWg8CL+s/Y2cKn3f/CokhObWSc9jdLmmfmmZnPTELOtWXeG70x7aYW1tAOhlJM4ombgsKgrhBr9Q2MHOwssPbooeI0117XEI8MeMqsoV8aWQ6Cqc7uRnMrYzV0g6bNpHs+LMyqDBUStyYQX7ksSf2PpepZHUCEQ9+0X8Rw50nqxOShv8A4JWp8y6pYv4AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722145357Z3MDWRGE4H">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722145354ONMN8U71BI">通用的xls导出导入实现，你只需要实现行数据转实体bean，实体bean转行数据的接口.....</span></div><div id="k_2722210380N1GQEPE137" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27222247348JTUH6QY7P">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_27222247375K9R6JNFVX">​<img id="k_2722224737TUOI3EYQGX" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABwUlEQVQ4T5WSP2iTURTFf+d9WCMVFUSQYBs7uPpvaZrBRaQ2KDoJIoXYDG4BHbSoQyepg6gILrURSXVwEIQodRB0aKoOIo5ZkkaKIrgULTYk35Xvi/mSCBl8w4V3Oefdc859yqaqL4C0GauC74beGlqs+4PLj9/vXKPPUTZVvY5xEXgDdhRpe4A1aAorusamy3Mf9pT/5Ss7VjluUlHYqo+Xc9a8hHQkAhrrBrn8ciIPsnZfmdEv+5zXeC00ZMZH8zUpZzclTrRBrenk5kt770fEyf3fBge2/n4a+Awl+lzzZc888Qo03JFo5bo2HyssxWtBT0GZSlVmha60QFZrGuMebhzZnR5vvjLz7xKPImLHJ97fYO6JgVvGxnOhA12S5/KlxIXAazjx/KGvuxTbWJQ43BrKuo+XljVGcHogWg8CL+s/Y2cKn3f/CokhObWSc9jdLmmfmmZnPTELOtWXeG70x7aYW1tAOhlJM4ombgsKgrhBr9Q2MHOwssPbooeI0117XEI8MeMqsoV8aWQ6Cqc7uRnMrYzV0g6bNpHs+LMyqDBUStyYQX7ksSf2PpepZHUCEQ9+0X8Rw50nqxOShv8A4JWp8y6pYv4AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722224739RERHSQNMK3">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722224735ZGBUB2LDZG">统一的权限拦截，您只需要按标准的RABC配置好对应的权限数据即可（按规范配置好按钮权限系统会扫描按钮形成权限数据，一般无需手工配置）</span></div><div id="k_2722401937S881GWJ127" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722414242DXIAJT19ED">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722414245DXHYDEPVTY">​<img id="k_27224142462DRLR97A84" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABiElEQVQ4T5WSMUtcQRSFvzPTREQkEHeVFJb+hEAWaxsxBNIFA2mUiCJrSBOwsLARVKyMBItlg0oIiGnSbEqJpfgLtHKfpAshTWZumOfu463RwikGZjjf3HPuXFWdfZCYNTgB2jJ+xMix4DRDv7ljqeJsVrAAfBdMIMaS1oxfQEOR9TY6v8mrgtXk+IY4U2AZx2vEdCE0LmJk6QoOQda91wg2Gj17gqcGhxZ45xxvEW+6ory6mMmCDgoQrK/q2Ew5c4swQ6CFpymoFTC0QuDVT3SZ7pS2qrO6xEYHPCYwbZ6ag2Y5mwWeZehrAXZzSgx0GvM+RBresy2YKuVdaUdWUta84iD28IFnV/A8FxkXfyMvPDzG0Sw9uJNF6qA/OZhWxdtLB59KmY4I1HEsScx3nPwPVrF+OdYQcyVr2xZp4NkSPMHotdoVPsIGvGNVygfi2rWxj0gNWcT4nEVtFs3pnQpzwzBuLv+eySIftMz4chX5CIpFxl749tMQNi4YSlN0LzA9l2DvGf4HNAiSflSqGn8AAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722414248G1FZZ4ETTN">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722414243N8MYOKFCT5">借助Bui框架的统一请求封装，框架实现了统一的异常、失败、无权限提示，开发者无需为此编码。</span></div><div id="k_2722094294JZ9U8ZA3RE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 53px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2722094295C7E6SDAJ3R">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​.............................</span></div><div id="k_2721105158OSENLBBI1M" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2721105159O4UMK42AE3">​</span></div><div id="k_2721105288K1OA32G6D9" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(245, 242, 252); padding-left: 15px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(149, 117, 245); border-image: initial; margin-top: 0px; margin-bottom: 14px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_27213016209R24KCG35T">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721301622SGBNH78VHW">​<img id="k_2721301623TR47Q7ULTR" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABvElEQVQ4T6XSwYtNYRgG8N97jbnnmP+AlSxYyFJTFrNgx8aCCAuEhY0hMo2aKNEImZRRmjR/AEkWVlaibKSUkYVmqdhg7jnXzPl07txrJkYWvtXX9/Z8z/O8zxP+48QSNoXMehwWdmAQLyVPMK2I2d95uuDUp+mYcBCTwjMtLblcZbeGQxZc1faQSL1Pgg7jfgwpjBBf/nSS1slNqNxXxuMlcH/aomFEYRifkBFzy+xkKPXbZJUrklOK+FjPQ5YuqbzW9khmFPtU9mjHW820U8OU5JzCtNywyndl3O2Bp1TGtc1oOinslRxRxgd52ia5IxntyM1TvcQDWs4SRc08qagZ4/M/U2umjTiqNEa0avA9yTVlzMhSHdM7rXjx66NaOvPKeLoSc9dzPJCl7cLJ7lJm9afNGq6rnNWON5rphIY1WnFz0fPqtNUqxxVOY05uj2RMqCW+kpxXeIa1crctuNBZZgcs9clcRFthvPZCamAA3xZLkQbkxlXeK030itJrWD28jA2ScYUXxDypX2ZQOIPnWm4R5bKG9a6pITMkOvJ3dV6Tr3howQ0/vFleza7slQJKfV3ZX4nqbxH+BB6bqRA8VQuIAAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_2721301624T1D48V7TIP">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_2721301621DBPQB1VMAU">精细灵活的权限控制</span></div><div id="k_2721104240TVRES77XQW" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27223859149CU891JIS7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722385916UJIV371GZE">​<img id="k_2722385917W3ZUWQMM9O" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAYUlEQVQ4T2NkIBMwkqmPgfE/C4sVAwODAwMDAzuGIf//X2b4+3cPIwPDB3Q5kMYTDIyM5jht/v/fgfHPn4OjGrGFEC0CZwMDI6M/1uj4//89AyOjG+Pv32cwooPslEN3jQAB+kEPv3DorAAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722385918B4TNALQ2XN">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2809595341758VTT869Z">无需引入任何第三方插件，</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2810000193REUW31YIAF">经典的RABC结合工具栏按钮配置机制，实现按钮级别的</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2810001050FITJRY33AH">精细控制，无需在页面上编写</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2810001558Q77DKT7HTS">UI权限逻辑</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_28100027508AY3V46DTL">，</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2810030542FHUNHHH4U9">业务开发无需关心权限</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_281003054517RZU3WKQU">问题</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2810002752IP8MKPIHS6">。</span></div><div id="k_280904261814W9UHJ1OT" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2809072942JSVCYTHIM3">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2809072945JDAKHC9VOS">​<img id="k_2809072946QBQODUG4V8" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAASklEQVQoU2NkIBEwkqiegfFrxIu7DAwMSuga/////46JkdGLa4XESWQ5xi8RL14wMjCIY7Hp6///jEE8K8V3DTsNJIcSyfFAcw0AUs06DSwJYmEAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2809072947WFIIYGCL55">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_28090729434UJCD98Z1W">灵活的权限数据及控制机制，采用可复用的权限标识码，一个权限标准码可以应用于多个url请求，解决冗余的权限配置问题。&nbsp;</span></div><div id="k_27222638017XFD2FZ9Y5" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27223907988BFNDSA5FU">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722390700MV38KUN8ZJ">​<img id="k_2722390701RKD7YLDGD7" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAaElEQVQ4T2NkIBMwkqmPgVGC4b/ZfyYGa0YGBjZ0Q/7/Y7j+i4Hh4HsGxo/ocowSzP93MDAwuOOy+d9fBptXDIxHRzViCSEaBA7T/0UMjAyxWKPjP8PDv/8YAl4zMF7AiA6yUw7dNQIAWxM7D0xSQzAAAAAASUVORK5CYII=" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_2810040346QIKQFXLQPR">全面拦截，但又可特殊配置</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_28100403514NTCUCQS2I">，</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_28100403524CWU9Y7HRW">可</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2810035614SR3D9NFB9A">以配置某个url</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2810035615RDOIJDDQBV">不需要登录即可访</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2810032591J6GY1R8RLD">问，也可配置某个url登录可访问，但不需要校验角色权限</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2810034290KAIVGYLWYN">。</span></div><div id="k_2722280640LSCT38G48E" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722392048L77O65ZRQU">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_27223920513R5JRGD6PX">​<img id="k_2722392052EBRIRI5TB2" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAaUlEQVQ4T2NkIBMwkqmPgXGHeZIj0/9/7gyMDBzohvz/z3j+PzPTZo/jc9+hyzHuNE+4zsjAqIHLZsb/DB6up+bvHNWIJYRoEDhmCQcZGRntsEXH//8M3xj+M7i6n55/DCM6yE45dNcIAHRZPg+x+mRIAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27223920537IGXOWXDT2">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722392049UA3AI9FZ3V">可插拔的请求拦截机制，可插拔的权限数据加载</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722325386CS4LO4FFE8">机制</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722330580FSFXIKY8LA">，轻松应对集成部署时候</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722330581UTMF1JH5S9">4A/门户</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27223305821WFF3WWD7N">统一登录验证等要求。</span></div><div id="k_2722301420CFSUA4CGHL" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 36px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27223935267C3HLABV17">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2722393529HROTX3T5IR">​<img id="k_2722393529NTPAJUPLW8" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAZklEQVQ4T2NkIBMwkqmPgZHhtVoRw39GdwZGBjYMQ/7/v8LA9H8ag8jt6+hyjAyv1N8wMDII47SZ8X8lg8itjlGN2EKI+oHzWv0BAwODPI7o+MPAyJDHIHJzOmZ0kJl0KEhyZNoIAHfRMQ9Vj5fqAAAAAElFTkSuQmCC" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722393530H59YXWAM86">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_27223935279B2WWJ49SH">可插拔的用户登录机制，可配置session机制，jwt机制，集中式session机制，也可以自定义实现登录（只需要实现</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(255, 255, 255);" id="k_2722321792CXD8RPGZRT"> ILoginUserContext </span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);" id="k_2722321794TLDCRBGOX3">接口即可）。</span></div><div id="k_27211042305L2KSB9R8Q" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2721104231A8TMJ9HNY7">​</span><span style="font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_2721104329QG3NNHTOYR" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(245, 242, 252); padding-left: 15px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(149, 117, 245); border-image: initial; margin-top: 0px; margin-bottom: 14px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_27213042894SIPGTXXV5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721304291ZGKSZSQBC8">​<img id="k_27213042921HC2PWMULG" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABYUlEQVQ4T63SvWtUURAF8N88s5s8BJEFKxGxEhXBRixsBG0tVIIRQYR0/gUWWkUxvYWotRIRDCiCjb1dbAyxFLGQoIVC9kt3ZB+72TVZCyG3u3PmzDln7g2yUHdU4QJOCyfRsPWkT/iAd3qWdayGmbwn3JQeSMt61nV93kauOaCwX7gqXJEW++RHmBPu4omm5jbisFAqpWtVb3oc5G6l87iIsxMtj6Z9x1u80PQq/lbJ/n0Ppiao/8IPIofYFvI/DU8EdoBcyxN2WRIOVxLpjZbrxFdyn9JDqqfsYx/9Nqcb7wfK2TBtVmHvoOGLludEm5xSVss8VGE9G9qWiPUdsP1/e9rsHinX87hU07Uy/hyjuRnqjlXGO7Harw8z101bEC7j1ijvkJqlGZdwR3qm7TbRGcucDaUF3Kg2ml4qfJOO4Mzg593Xskj8HFPeVCiUTknzOCccxIr0Ws9THWvjkf4AJ49pShzbTuUAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_27213042931PHHEOUDNQ">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 9, 255); background-color: rgb(245, 242, 252); font-weight: 400; font-style: normal; text-decoration: none solid rgb(0, 9, 255); vertical-align: baseline;" id="k_27213042892NH4G1X8HX">专用的代码生成器</span></div><div id="k_27211017972W96GS2A3J" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809163792YQUAVJMPA5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2809163795MS5I1SU9N1">​<img id="k_2809163796TGICYY8ZUI" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABLElEQVQoU41RvUoDYRCc2cMUQXMHNjY24lMoWNgERARBEBvBQgQrEXJYKFxpjBHESkWClbbaCZJHEDtJY2kl5LtATKHZlYvGhBh/tttlZndnhvhPhTbkWXyWQPknPrK01N0xwElVmfmdEFlK6u4U4CLBlWbBv/iZkIBfXBHgOgwHWvBDgEZElkYDA8gz7rxnlLC2DVgEw72qZFHMPLc0SK56DnJCBVnsBo/J0MvFSwYrAfamxjnsB+X2MkoY77Q2ARUVzMJkREyvCQwbUFLnr+GEr18EfAojuWzAA8wGSY4a7EnhTWMvU+l28kP0RjXwUrgCOJW0BiiMm1rwD3tt77i05cZEcUNg3Ax33UK/X2hPQrcgsBIpq828f9kv1J4cjAjdPBrBLY5Y60d4BxQ0cqxwfP2DAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_28091637983NZ73S3AQR">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809171816YRJ8X4VXY4">增、删、改、查、表单验证、页</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809171818RDW9G3IM1D">面编写....... 这些都是每一个程序员每天必须面对的重复性的工作。</span></div><div id="k_2809121644MFYBPG5KF6" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809164654852TMRS49X">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2809164656782HLVDBLE">​<img id="k_2809164657H7GXKOKYK9" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABSklEQVQoU41RvUvDcBB91yRFO+ikgzi4uQrFZBIJGGvBCuLgUNAI2klxFARdBQXrf2BEK4hjq9AqtIJCG3VRcBM6OOtiKTZtTn7FlKr147Y73ru79x7hH5UMxgJ+2YkLKP2Fz/aZbZVubBJIZ1QnfiXcBGPKs1zZYqYFANMhezf1I+EDvMbAKjHWDdvaIIBJnESgJOsPx6/eewxQRjUXQYgT02V7+W1y6P7wpa4ho5k7YAwr5ET0QuJJDNPq3DiAIwJXiBA2ClbeW0Zp1VwSmwDc+uFMVWtSF0tSCkAPmLeVQHFFz+WqDYInDKBlBttg6iRCPzM/uj6MhPNWsdnJuuhTLdohs3IAoojoGVwjF/Oj15b11faGS1kt2uuwkgTRABgXzUK/XfAG54PmWM1HCR+7M4a9d9Iq1E85CDvPtFmDy+5V6G6/1IrwDjN6fWRFEnzaAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809164658O25LCT9K1G">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809164654C591KHPS52">我们都希望将这些与业务无关的work，能够通过代码生成器来完成。</span></div><div id="k_2809143446EICZMJY7IV" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_280917149029SPO8BF6E">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2809171493R7Y7E2NE1K">​<img id="k_28091714946RYVOWRX9I" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABNUlEQVQoU41RPSxDYRQ958sjL1Kavvd0YrRZdNHWaJAOOpOYhEkikdgtZo2ByWASFj8Tm4H2bSaLWERi69W0nZo235VX2hT1c7d7c86595xL/KPiN/GE4zgnEZR/4YPbYBgGRwpN0TD/O+Earu/6kXIOig3Jyt7PhHfwAYBFKA4lIysglNHKxkBjsD5dl+55CgZhsKPUdSgeAcxKVl7aHvzQvwCQstbmKjOV+2jol/w1EAWCLYUuSUZOO2IMSkGhrQQ8W2vnYTBuYI4JjqjquYzKAibQ6BLwYYxgXqFPVA6BSAIoN9Gcq2aqd71Jtk3HirGkS/cKxFTUK9SS3C6ny1tfY++mlCgmJo0xlwTHVPWh1+i3DZ2BF3qrBmZXqZuSlv1+T/38BwW90FtuoXVWy9Ze+xHeAHiVbtcQGuCoAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_28091714955K23C8B36H">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809171491DKO9BNIT4C">这个代码生成器能够</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255);" id="k_28091549193Y6AUM67QM">生成完整的[ </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255);" id="k_280917182436216BOY76">代</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255);" id="k_2809171826ZKUKCSIMXO">码/sql-map/页面/列表配置/curd工具栏</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255);" id="k_2809155969J9SGF1JHBO"> ]文件</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_28091559706CYMGIID6X">，程序员只需要拷贝这些文件到工程中即可将单表的curd跑起来。</span></div><div id="k_2809174509ALZ4PMYZ4W" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809194251D1YQ7N29P1">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2809194254C7RI1OKDQG">​<img id="k_28091942556GU5XT58OG" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABO0lEQVQoU41RPUsDURCceaKGoLkDmzQ24q9QsLAJiAqCIuopKkjA0kIsPPMSbSwULFVCkJxGsNJOEH+C2EkaSyshd6mEJG/lgglR48d2u8zs7swQ/6j9Vel9qwTZEMq/8HpCosryj0EOm6oa+5WgZ6RLdfunIGdFuJLyrMKPhA/wgZDrFBy6nrVJUBiejETQuXXFoPGeQJhxytugaAgepaYSuhB7rWvIOKUzAYfEIKEv7OdwmHaCOVJygFQJTrp5+76xjGkncMNNBIqmhnEoFVc0NwD6RJCLR61k8oSVJqFF2BKAJ0B6APaLyItCx6jrxYqtTtZF6+WSzSquSY6EvQAGwo2UZx19tb3pkp73B6hwS2JQBA+tQr9daAx2F/xpQ8mRam0nb122C/VTDqGde4v+VM3Yd/qc5XaEd5nGeXhdWfmQAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809194256PSCM8MOEP4">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809194252MK6OISQN81">还有一点：我们希望这个代码生成器是易用的，最好是一个桌面程序，打开后配置数据库连接信息，填写需要生的代码的表名即可一键生成。</span></div><div id="k_27211019841WXH6AEAHK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_27211019852U1W8KD6M8">​</span></div><div id="k_28092014511DUPVJK4R9" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 32px; width: 249px; text-align: left; background-color: rgb(246, 255, 245); padding-left: 14px; border: 1px solid rgb(118, 103, 250);"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(5, 21, 252); background-color: rgb(246, 255, 245);" id="k_2809204326OAF5Y1PL7Z">Bui-SSM专用代码生成器为此而生！</span></div><div id="k_2721101933NP8VBOTSOS" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_27211019358UEZK1KKW1">​</span></div><div id="k_2809215445K7S2PSKFQD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 30px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809351239IN1U4XTO3Q">1）运行[</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(255, 255, 255);" id="k_2809351241D2IVNY2ZNG"> MyCodeBuilder.exe </span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809351242GLHRY8J46G">]打开生成器：</span></div><div id="k_2809243870IJG6A3BCHY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809243871EU22D1FSJS">​</span></div><div id="k_2809244171URXU8XG9V8" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 79px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809244172RDORY2R2AI">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809250702I2TXAK2MH3">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809250704MQOL9PI7M6">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809431113JUSR2WZBKK">​</span><img tabindex="0" src="/bui/tmp\d83f165050fa11e98f563c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809431114CCZ6QYWXNR">​</span></div><div id="k_2809244123AK5FKGJRK2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809244124KC5QHPM597">​</span></div><div id="k_2809244267EAXXTYGEZD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 38px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809244268FO2TNDDGHJ">​2）填写好相关信息，点击【生成代码】即可在根目录下的[&nbsp;javaCode ]文件中生成所需的代码、页面、配置：</span></div><div id="k_2721101943VWAB2LW3WV" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 80px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2721101944WPRJOZ7NYD">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809283670L38CT9XMDS">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809283672KOO9YZI7QM">​</span></div><div id="k_2809432223JQMPOZXRTK" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px; margin-left: 80px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809432224GIKQ7JCFW1">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_28094438306PZDGNF5RA">​</span><img tabindex="0" src="/bui/tmp\0bf4b27050fb11e98f563c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809443853DFBVLNM9RN">​</span></div><div id="k_2809010176UF2OSDHAAL" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_28090101778ALRXOE93W">​</span></div><div id="k_2809424477AZ7UM47R3I" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_28094244795TX64C16AG">​</span></div><div id="k_2809594334MMT8FTAIRO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809594336OD9V9EQGHC">​</span></div><div id="k_2810061050E8WT24KK74" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_28100610515O4VU3RWTT">​</span></div><div id="k_27213102106TYL1RGLUB" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 891px; height: 152px; position: absolute; top: 53px; left: 103px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_2721310212JY13V5X6XJ" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;" class="k_editor_float_input k_box_size"><div id="k_2721310213JALSCRQTMJ" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_27213102136KTQ24EDVZ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2721331792IEMQW4B8GD"><img id="k_27213317931XZR27XD5R" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABAElEQVQ4T2NkoAAwUqCXgSjNC6W+yf1n+93I8P//f66H/BlhDIy/QJbi1QzT9J/hfzTjfwam//8ZIhMfCayGuRarZpCmf6y/yhkYGVIZGRhYQYr/M/w/yPaPMTD6kcB7rJrnyn6VYmL6XY2sCazwP8NfdFvhzsapCWoFNlvBmhfIfWj7z8RQAnMeRuiDbGX8n5L4QHABuhzjfLmPsQxM/4yRJRj/M7gwMDJqQ8VOsf1i8o56xvcGQzO2eF6g8GE6AwNDBtivOGzFGVVwzQwMOG0lrPk/Q0rCQ4G5uFIh1ngG2fz/P4PZ/5/MHkkveF+TqPl9438GhvvYQhglYCnJGAAAB20QA1OcUwAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;"></span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_2809063806MRI6V6B92G">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(252, 5, 161); vertical-align: baseline;" id="k_2721443092N3TH2KDP3Z">借助专用代码生成器，</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_2721442586VR72TVYRSZ">建</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_2721442587FREECWF58W">表后（做好字段注释）</span><span id="k_2721313430195FBEO51Q" style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;">，</span><span id="k_2721313431RBH7P2HN1V" style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;">一句代码也不用写</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_27214400901KXT4EHLWF">，就可以完成单表的增删改查</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_272144009615ARRHVNYU">，</span><span id="k_2721313434YCT63RSA11" style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;">包括页面</span><span id="k_27213134359IJLDRSQDJ" style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;">！</span></div><div id="k_272131343935OPPNSWTP" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2721313440RGPSQ1EFT8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2721323643SY8HVDQMJS">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: normal;" id="k_2721332359WZVKT4SC3M">​<img id="k_27213323602F4I3Z7FEW" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA6UlEQVQ4T6WTsW7CMBCG/4sfhA59AYauFWLpwtzyAmViqtiiRKcbwgIDZOjEztN0ZepMhw5ZkSJf5SpBIXECKB7t+/Sd77cJPRb1YHETzMyP1tqVqmqWZdM0TU9O2glXoAkRudqZiOzKbr1wAYmqvhKRKYq/giB4YeZfLxyG4cAYs6xBrtbWree2O6BS0rD+w1EUfQJ4r7RXD8BZP0RkUz9w8BzAU/WAiJ4BPLg9VT0YY8bM/NOAfTnHcbwH8Fbc1WttjaqEu6xXYQALEVm3vUJvzoV5mOf5KEmS473wFsC3b8IXg+3zMf4AmHBoEMxFO4wAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2721332361TOJGQLZBTN">​</span><span id="k_2721323640ZRKRHHRWQO" style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;">按钮即权限，只需按</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_2810080219U2UE2LKJQ7">规范定义好按钮，系统将自动扫描配置并生成控制到按钮粒度的权限数据</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_2810080220HD9UGO5AYQ">，</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_2810080222UZN8T3LQ87">无需为权限控制而烦恼！</span></div><div id="k_2721323923TWFEAGTZ3Y" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2721323924JJHUV6JUWP">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_27213257302SR8BMQXO5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: normal;" id="k_27213343751O6UMZL786">​<img id="k_27213343765P23ZVCLZR" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA3UlEQVQ4T6WSPw4BQRTGf29P4AJiFCJR0Wqx0Sglm6jdQSUuolKIIyCikCidYTUkDuHJyK6sNeNPTDXJe7/5vnnfE/448gfLV/DqSB9lbIWuEHYNZ3t/C2egmoAoTEPDMHXrhC0kykihLhAkzacA+i3D3gkvj/QCZZKDbK/mVR+230CpyIvqHV7H7BSaGXv5ABSYdwyDfEFWMTNRqk8FoQIUEr+XQIjahu0L7Mp5HXMAGvavPlVvVCmsilf1I4yy6JSJfFvozDlRLqoQhSU2v8J2OCfXhLMPfbXbPuUbkKFKEPu4vkUAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2721334377BV4VTLNLCU">​</span><span id="k_272132572786Y2P6NBVM" style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;">取传统精华，融合现代Web&nbsp; Resetful API，既有前后端分</span><span id="k_2721325728BF83U6DNZ8" style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;">离的优点，又确</span><span id="k_2721325729ZLJOVPUEK6" style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;">保后端对前端权限渲染的控制！</span></div><div id="k_2721335314L7RHXQ5CFD" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 16px; color: rgb(252, 5, 161); font-style: normal; text-align: center; background-color: rgb(255, 255, 255); font-weight: normal;" id="k_2721335315SO2PUYXKYS">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background: none; font-weight: normal;" id="k_2721341300PKL63FSD8X">​<img id="k_27213413017G3UA5BEM9" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA8klEQVQ4T6WSsWoCQRCG///2avuAxuv0ErCRtEGxsLD2XYSAaUQCgUDeIYWP4ItEvUK8A30Gm90buaB4nrtGcdudb75h/iHueLyDxXXwotzwU/9bRMSUTA+V9TaTXoYPEOQ1KxZgkD4ln4dp7fCi3FBGfYDoEvT2xZFWuoPaemOHo6CutHwVIAgkLVqPYzug3DLPrH+wmgUTQPq58U4CyKwUjvRz/F5Mht78ccjUa558UF4IPuyXFBuijTCOz2Bbzuo3mJLoXrI6ozrCcFr/hUEZ6zB5c12hNefMDCA0SlqoJ6sb4eoPwaVtw/lG1922Q70D/INkEDevSawAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;"></span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-weight: normal; font-style: normal; text-decoration: none solid rgb(255, 0, 0); vertical-align: baseline;" id="k_2721364825LYTJZQ4DEU">轻</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal; text-decoration: none solid rgb(255, 0, 0); vertical-align: baseline;" id="k_27213641938RISVEZQEG">便</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_2721364194CA32FL9OWS">的API</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_2721364196UTZ8BTKLFE">接口调试</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_27214331421CIJ3RGPCG">，无需引入任何第三方插件，无需等待前端</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(252, 5, 161); background-color: rgb(255, 255, 255); font-style: normal; text-align: center; font-weight: normal;" id="k_2721433146V7Y7U5LBVP">页面开发即可进行接口调测，适用于前后端分离开发！</span></div></div></div><div id="k_2809330025GVYZ29G8IU" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 332px; height: 34px; position: absolute; top: 2989px; left: 530px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_2809330027EUZ76WIIKZ" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;" spellcheck="false"><div id="k_2809330029ML2SSEASOW" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2809335778EMBVFQ4864">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2809335782Y1DVWUEDZC">​<img id="k_2809335783S9GGFOECB3" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAABGUlEQVQoU42SP0sDQRDF39vZNWLphxDxA/gBFLQI2EVBiBf/FEGSQitBGxvBylICFl4KC00p6WwEewUbESsFG1sL2d0buaAicieZcng/5s28IUpK0XGZq2wYP3JBLL99y1ikVygz122p6q4EmSfqd6VALo7SXQD1TMF7G1y1dIKiMxbs6D6BLUA/oFyyMbn87YJB0jUaOONjP1jpkZgeCBQRxAuAK/F6QDSe8ja9Pb22QdpRsnUYtIt2InRbfOPoC0hvbDCbUWIThs3Co2Wo2Zj0hgT0WbzOEquPwwGKvgQuEivvAyC49FwVxwBnSN37a4mKHQnJ4U8OipNxOJmAxy1gp4LNaqBWqZgE+SrezBH1h3+TLnuXvP8JEU5yDYy4jpsAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2809335784U4FJRRZGI2">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_2809335779ITH1HPBFMW">拷贝这些生成文件到工程中，即可运行curd</span></div></div></div>
</div>