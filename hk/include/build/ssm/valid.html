<div style="position:relative;">
<div id="k_0611111319FW9DRETPE4" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(252, 5, 161); border-image: initial; padding-left: 15px; margin-bottom: 11px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255);" id="k_06111113204NX61LGSP6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255);" id="k_061111266789X2Y2ME4X">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611125645KI6SL6Q3T6">​<img id="k_0611125645K7MFR4ISAO" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA0UlEQVQ4T+2ST0rDYBDF38v3JVTXoiBCziLeQopQwQtI1y5ciztXhdZK6R26KD1LoRQqrm1JJnmSIBhqA8FsneXM/N784REtgi1YlLCw8OaXjwT6IMNaQSkV8OQtfiAu7RuenmU+eXOGeyD6qN8mOck8np1FN8T1pgKnQ2fhbZGsg4ViyE/fP/y3h406WRgMAHQbmGbi0vyO6O1o/nUmMgZAShcgj0sB6RPkGtJ5NSdyVVQpLWlu/KIAV78nakvhXcQpwKP9OnPM23u7wZ0HW74Avq6SnhjnTWEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255);" id="k_06111256471DSTDUY2MO">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 700;" id="k_0611113053TG7V5MLG56">服务器验证实现</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 700;" id="k_0611113054EVE8PXVCO9">思路</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 700;" id="k_06111130565ZZ3LZ92YH">：</span></div><div id="k_06111126682MK24DTJE6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611151752SNXQULB5ZS">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611151755Q3AE5G434K">​<img id="k_0611151756UT4HSMIFYZ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAWElEQVQ4T2NkoAAwUqCXgTLNvbemP2VkZJTC44JHjAyMgYWq6efQ1TD23p7xgZGBgR+X5v8MDB8ZGRjDilTTd41qZmAYwADruz3jIQMDgxxZ8TxwyZMSmwH5yT0QYdud+QAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06111517582SF4RI44IQ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611151753FKMNFFGBHH">采用annotation注解，在实体bean上打上相应的注解即可。</span></div><div id="k_06111126682MK24DTJE6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06111526724K5MY9JOJW">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611152675SW17WJAZ5L">​<img id="k_0611152675GQCHQANG8C" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAW0lEQVQ4T2NkoAAwUqCXgTLNTGUfzjEyMBjidMF/huN/f//3Ypgg+AFdDSNT2fuHjAyMcrg0/2f4/+jfb3Yrhn6up6OaGRgGMMCYSz8cY2BksCQrngcueVJiMwCBelEQ93a58wAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611152677UF1UYI5E75">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611152673QDPT39KZV7">调用验证逻辑由controller的add/update(新增/更新)api&nbsp;统一触发，开发者无需编程触发（可重写实现）。</span></div><div id="k_06111126682MK24DTJE6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06111533677LJGADIM8A">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611153369EUA3CNXRVV">​<img id="k_0611153370JDTOTQ949B" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAW0lEQVQ4T2NkoAAwUqCXgTLNv1kWHmRkZLDD6YL//3cw/2EJZ2SI+YSuhvE364LrjAyMGrg0/2f4f4PlN5sDI0PUy1HNDAwDGGB/WBZsZ2Bk9CArngcueVJiMwAD/UgQQG8R3gAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06111533719E29SALT7E">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611153367EBWHEKP3GZ">annotation注解支持提示语从国际化文件定义（langKey），便于从服务端返回对应的提示语。</span></div><div id="k_06111126682MK24DTJE6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611154454IOCYBOLCVU">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611154457ZPX3RDJIWB">​<img id="k_0611154458YD7EN8J594" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAWElEQVQ4T2NkoAAwUqCXgTLNt14wPWVkYJTC6YL//x8xsPwLVBVlOIeuhvH2C6YPDAyM/Lid//8jA+O/MFVxhl2jmhkYBjLAnjM9ZGBklCMrngcueVJiMwDSwkEQXfT0vgAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611154460QXK18Z8HLE">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_061115445548HATLHOZS">唯一性验证，采用countParam&nbsp;sql，支持通过注解定义count&nbsp;sql的where字段条件。</span></div><div id="k_06111434513O3SK4YDOL" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06111434523SNZTJLNRJ">​</span></div><div id="k_0611143597J2FTHVRZZU" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 15px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(252, 5, 161); border-image: initial; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_0611143598ZNECWX9A4A">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_061116021329XY7OKSO4">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611162402L769AYLRL4">​<img id="k_06111624039NNVVKEVWH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA0UlEQVQ4T+2ST0rDYBDF38v3JVTXoiBCziLeQopQwQtI1y5ciztXhdZK6R26KD1LoRQqrm1JJnmSIBhqA8FsneXM/N784REtgi1YlLCw8OaXjwT6IMNaQSkV8OQtfiAu7RuenmU+eXOGeyD6qN8mOck8np1FN8T1pgKnQ2fhbZGsg4ViyE/fP/y3h406WRgMAHQbmGbi0vyO6O1o/nUmMgZAShcgj0sB6RPkGtJ5NSdyVVQpLWlu/KIAV78nakvhXcQpwKP9OnPM23u7wZ0HW74Avq6SnhjnTWEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-weight: 700; font-style: normal; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_0611162404HI2M4VFBOB">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 700; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_0611184465S7RQ2L6QS6">an</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 700; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_06111844672V763Y5NEZ">notation定义：</span></div><div id="k_0611143546RI1Z62NELP" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-bottom: 10px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_06111435487X9HUQXI87">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255);" id="k_0611163788H92EGUIVD1">​</span><span id="k_0611163786H7JEA4348E" style="color: rgb(24, 3, 255); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​annotation定义为于kevin.framework.basic.common.annotation包中</span></div><div id="k_0611164554RG9JW6IHPE" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 34px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0611164556RSTLSMSE1V">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_061117297961ZDSCV1C5">​</span><img tabindex="0" src="/bui/tmp\827a7c10581a11e9af593c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611172980S6ZI12XASG">​</span></div><div id="k_0611143590A6XMYFOFU9" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611143591344339ZXCL">​</span></div><div id="k_06111435547Q7SXXSKUA" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 15px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(252, 5, 161); border-image: initial; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611143555KR9ZWZRMOS">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611190068GICUVWFM3L">​<img id="k_0611190069WR7648O1B6" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA0UlEQVQ4T+2ST0rDYBDF38v3JVTXoiBCziLeQopQwQtI1y5ciztXhdZK6R26KD1LoRQqrm1JJnmSIBhqA8FsneXM/N784REtgi1YlLCw8OaXjwT6IMNaQSkV8OQtfiAu7RuenmU+eXOGeyD6qN8mOck8np1FN8T1pgKnQ2fhbZGsg4ViyE/fP/y3h406WRgMAHQbmGbi0vyO6O1o/nUmMgZAShcgj0sB6RPkGtJ5NSdyVVQpLWlu/KIAV78nakvhXcQpwKP9OnPM23u7wZ0HW74Avq6SnhjnTWEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611190070BTWLDVVN6Y">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06111833006FBIUEKWGH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 700; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_0611241341OCUUZUAI6P">注</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(99, 58, 224); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 700; text-decoration: none solid rgb(99, 58, 224); vertical-align: baseline;" id="k_0611241342VDU95XJM29">解开发实例（</span><span id="k_06111833979YBKQGGYWL" style="font-size: 14px; color: rgb(99, 58, 224); font-style: normal; font-weight: 700; background-color: rgb(255, 255, 255); text-decoration: none solid rgb(99, 58, 224); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">Org实体Bean orgName字段</span><span id="k_0611183398QNS3P381MD" style="font-size: 14px; color: rgb(99, 58, 224); font-style: normal; background-color: rgb(255, 255, 255); font-weight: 700; text-decoration: none solid rgb(99, 58, 224); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">）：</span></div><div id="k_0611143580PTVFOCTEHC" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 43px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611143582K7XGISNX8R"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">		<span style="background: none; color: rgb(165, 165, 255);">/**</span></span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">                 *机构名称,必填，在同一个pid下的唯一性要求
                 */</span>
                <span style="color: rgb(15, 224, 0);">@Require(langKey="org_Require")</span>
                <span style="color: rgb(15, 224, 0);">@Uniquely(langKey="org_Uniquely",extField="pid")</span>
                <span style="color: rgb(15, 224, 0);">@NewOldCompare</span>
                private String orgName;</pre></pre></span></div><div id="k_06111951307HLL9TMN7G" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;"><br></span></div><div id="k_0611200583BORP49D7SH" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 15px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(252, 5, 161); border-image: initial; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611200584MVE55GVSFK">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_061124100468MLN49TD6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611242691CD7T3F4TCJ">​<img id="k_0611242691KMJZBERIO2" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA0UlEQVQ4T+2ST0rDYBDF38v3JVTXoiBCziLeQopQwQtI1y5ciztXhdZK6R26KD1LoRQqrm1JJnmSIBhqA8FsneXM/N784REtgi1YlLCw8OaXjwT6IMNaQSkV8OQtfiAu7RuenmU+eXOGeyD6qN8mOck8np1FN8T1pgKnQ2fhbZGsg4ViyE/fP/y3h406WRgMAHQbmGbi0vyO6O1o/nUmMgZAShcgj0sB6RPkGtJ5NSdyVVQpLWlu/KIAV78nakvhXcQpwKP9OnPM23u7wZ0HW74Avq6SnhjnTWEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611242693H9GPVILNSH">​</span><span id="k_0611241002NIX66L1JM1" style="color: rgb(99, 58, 224); font-size: 14px; font-style: normal; font-weight: 700; background-color: rgb(255, 255, 255); text-decoration: none solid rgb(99, 58, 224); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">调用逻辑说明：</span></div><div id="k_0611200489NAD7HX77QY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 43px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611254035LQQQLA875K">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611254037OL3JAXF5LY">​<img id="k_0611254038TRVD6T8YTZ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA/0lEQVQ4T6WSu0oDYRBGz/wTi5BKWNnFzsIuna1vIILWgjaCjdgZ8QIKtoKIoLY+hGDlA/gAIliJhdHFFBbCYv4ZMWgqNwnutMM5c+ETKpRUYKkGT+DTBby9I50EnzTQDvI0ykaSBd8EprrGlipzAisxspEjD8MEAl7PAi0gLYztMWU+wNIogt7NKd4gsCMw/mHs1ZVFYCFE1p+Rx7IN+g/7ERwDxaexWwusijArkbU2kv8l6MMJPlNTjogcGNyLcg5cvUQuQbqlcIo3RTn1yKHBXVDOxLlpGxdl4LdM/gv24Cz4fjRuc7jOlGVzklfjZNDE3xOqJWxYEAb1K03+Ag/RWhD9DXIfAAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611254039511M5999DT">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611254036EA7T9RL54M">Resetfu</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611252269KDNFBC6IJM">lBaseController&nbsp;基类中的add(..)&nbsp;/&nbsp;update(..)&nbsp;api内部调用&nbsp;saveFilter(..)&nbsp;。</span></div><div id="k_0611200489NAD7HX77QY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 43px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611254845YOFGQX51O4">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611254848P66GFEWS98">​<img id="k_0611254848354LGMH3L3" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA8klEQVQ4T6WSvyvFYRSHn0/veQdlumVQVmWy2v0FtwyYJCWFMhjupAxKJqOYxGqx+Av8BUqZZCW7es/t6H6Fxffem/esp+c5P/qIilIFSx0cMAe8Cd4DZoAkeBlnI4XZAdIspWyTUhdpC/d1wdMogQImMDtEmqaUXVJaQtoYR9DcHDCJ2RFSh1L2yHmZiBXc1wTPbRv8PKwR5HwBfDQCsx1gEfdVwetfgl845wXgjIh93B8xu0a6oZRzgbfCAfPkfElED/cHzK6AO9xP28CBbPCwf4FfsNkJEff0+7fkvEnEFO7HwyZ+n1CXsFFBGNavmvwJL5lZEKU+RREAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611254850YK44NDWYQX">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_061125484614WP85X9AW">saveFilter内部调用AdminControllerHelper.validEntityBean(......)。</span></div><div id="k_0611200489NAD7HX77QY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 43px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611260705RFOCH7C19W">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611260708ODS4BM6T4S">​<img id="k_0611260709SE5CEM9EW9" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABDklEQVQ4T6WSO07DQBRF7zU2DAUSTMYFomEDrAHR0fBJg9K6pKGjQOnYQArYAg2BkoJfh0BCQqJmC7aTJii2bM9DQUoqnETMa5/OeR9dwqHowMINXk7Do9LDV7EWf6q+2RFrF/NG736ejagS8wxgQwLuscQZgX0Ax8NG3J0lIL7Nusp4CxFtIU0PbJPcnUfwe7Pqr26y8rsCrFQLbPkWbQi2rSdRrpO7ug0mDxsJUAWPoBQS4JAFLynYKmEPCpO+/yWYwEtJeELKOYAOWT3B+jcgXoYaERgPamGVhhEgHUAuQO+BFtdC+cg0W3XgSMb/gmP4VSBvuU5OVc9cATCZZnPaxPEJbgmbFYRpfafJP75FbBBNOBI9AAAAAElFTkSuQmCC" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611260711SU5K7BAEHD">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611260706TY1WWJOBTD">如有需要，子</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611252274861EQVK2WK">类controller可以重写saveFilter(..)实现自己需要的验证。</span></div><div id="k_06112004467BOVFHMBJJ" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611200447PM1AVUMOHA">​</span></div><div id="k_0611200496ASPZSHUEAL" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0611200497I4XW4ZXJC2">​</span></div><div id="k_0611202740NN6NREZGNV" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 601px; height: 116px; position: absolute; top: 542px; left: 490px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_0611202742SHUMLWFSAS" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;"><div id="k_06112027435M3CK4LSUX" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611202744YEY8WRQIOE">​@</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 242, 242);" id="k_0611342298G4OZCLI5GF">Require(langKey="org_Require") ：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(242, 242, 242);" id="k_0611342299SYSGS8X7LA">必填验证，提示语为【org_Requrie】配置的信息</span></div><div id="k_0611212235NQHXYTXG45" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="background-color: rgb(242, 242, 242);" id="k_06112122368WWYACMDS6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 242, 242);" id="k_0611342901PZ3M9SVPTE">@Uniquely(langKey="org_Uniquely",extField="pid")：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(242, 242, 242);" id="k_0611342902SXNRZGDB3N">同一个pid下不能同名</span></div><div id="k_0611214537KA6AGAEYK4" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(252, 247, 247);" id="k_0611230592B874XZK9MU">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(153, 0, 51); background-color: rgb(252, 247, 247);" id="k_061123059357IQHA5WHH"> &nbsp; &nbsp; exField="field1,field2..." : 用于定义其他字段转为where条件</span></div><div id="k_0611220259WBWMW7VCJB" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="background-color: rgb(242, 242, 242);" id="k_06112202603F65VECNQA">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 242, 242);" id="k_0611352876Z9HDHDF5X8">@</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 242, 242);" id="k_06113528772V77C18WD5">NewOldCompare</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(242, 242, 242);" id="k_0611352878GCABHD1SD8">：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(242, 242, 242);" id="k_0611343593N49U847SAX">orgName在更新操作时候，当发生了值变化才触发验证</span></div></div></div><div id="k_0611270647XVSS1312XI" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 320px; height: 37px; position: absolute; top: 176px; left: 496px; transform: rotate(328deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_0611270648I66X5QWSJX" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;"><div id="k_0611270649SADWJZ39TY" class="_section_div_" style="line-height: 28.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: center; background: none;"><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(245, 88, 232); background: none; font-style: italic;" id="k_0611283645KV6EPKZGGR">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611283648VO1JXW3M3R">​<img id="k_06112836484XOJNTOX31" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABYElEQVQ4T41Sv0sCcRx971Qwz8NRqAZPgoYcGoKmBs/a6h+IGiMIIYPaamoJt4Ymh8LBQWiqRURrDRoam+yCCtsKz5TK+4SWpUJ3fqcvn897nx/vfYiep+nlSYGdIRizyWS9YuR684N/9gaCemmfxO5P7LhmKxsw483/CvSRVb28o1DSImiCXLEqxunQnYELr6rLFITPdTNedSK2c32d3cCOOyNaDGktxRDIi3WfuAQo7mOHC6oW8GwKuEfCL0DOaobW8DTz5kj+tYec6wJFkLHuwkkg9u5IVqOlA7akQHIJCrbaYNuWVN2cP3TT4Fuw0etA0P+aIbAMoPZpc6FhGldDkUfGS2Men5yTnBaRm9YHFxsPicfhyJHyrFeRIgANItmaeNadLqtbtDO2qpdWFSL7t6/nSItgombGb12sEmp6OQ1iuwMUnAHis8l8vWKcOHnNAbEggipEUpaZyLsdyRfjlIcQAA//MgAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(245, 88, 232); background: none; font-style: italic;" id="k_0611283649ECDW6VAQBA">​</span><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(245, 88, 232); background: none; font-style: italic;" id="k_0611283646QL6RRGOZ5O">前端验证，请参考Bui里的表单验证</span><span style="font-size: 16px; font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; color: rgb(245, 88, 232); background: none; font-style: italic;" id="k_0611273796GPSW1GNYJ2">说明</span></div></div></div><div id="k_0611351104RO58DVNRS6" class="k_box_shadow k_edit_float_panel" style="box-shadow: rgb(216, 216, 216) 0px 0px 3px 3px; z-index: 20000000; background: none; width: 657px; height: 58px; position: absolute; top: 380px; left: 343px; transform: rotate(0deg);"><div style="width:100%;height:100%;position:absolute;z-index:20000000;background:#fff;top:0;left:0;-moz-opacity: 0.5;-khtml-opacity: 0.5;opacity: 0.5;"></div><div id="k_0611351105M45YVEDTJU" style="padding:4px 5px;width:100%;height:100%;position:absolute;z-index:20000001;background:none;top:0;left:0;"><div id="k_0611351107FSC6WPJD94" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background: none;" id="k_0611375564ZKLYDMA5GQ">特别说明：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0611375565YJQ8Z11EHT">NewOldCompare 需要前端声明一个对应的保存旧值的隐藏域，如：</span></div><div id="k_0611364366ERSSICL25C" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background: none; padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_06113706558PP2DKGGDL">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 88, 232); background: none;" id="k_0611372623TERB89PK82">&lt;input&nbsp;type="hidden"&nbsp;id="</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 222, 36); background: none;" id="k_0611373294VG15B4HVR9">old_</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(222, 13, 79); background: none;" id="k_06113732952IJ85FIICY">orgName</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 88, 232); background: none;" id="k_0611374582UWTHVWC5Z1">"&nbsp;name="</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 222, 36); background: none;" id="k_0611374583DEZ9YPR1Q2">old_</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(245, 88, 232); background: none;" id="k_0611374584ZRV7QREPCR">orgName"&nbsp;value=""&nbsp;/&gt;</span></div><div id="k_0611352925QEBKMWV5E3" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06113529264DMILXUTZ6">​</span></div></div></div>
</div>