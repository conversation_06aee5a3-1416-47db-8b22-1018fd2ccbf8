<div style="position:relative;">
<div id="k_0814422714WOLF3GXDFS" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(136, 139, 247); margin-bottom: 11px; padding-left: 12px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247);" id="k_08144346988UUMQH8SDL">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0814434601LFZZETFZQW">​<img id="k_08144346021M4VAX74Z9" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAARElEQVQoU2NkIBEwkqiegfH///9vGBgYhLFo/MbAwFDHyMjYiywH0vCJgYGBF4uGXwwMDJ2MjIx1w1EDaaFEcjzQXAMAXFwqDVPnP58AAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247);" id="k_0814434603RP7XZP11GV">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247);" id="k_0814434699O1WBJWXRVS">&nbsp;</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247);" id="k_0814425466AP4DWVNSRZ">​</span><span id="k_0814425464EKT5G2GILW" style="color: rgb(255, 255, 255); font-size: 14px; font-style: normal; font-weight: 700; background-color: rgb(136, 139, 247);">登录机制设计说明</span></div><div id="k_08144254672Z859GTAO8" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 24px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081442546892Z28PEUKO">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0814444229MZOMT81LK1">​<img id="k_0814444230F2D54EXHFL" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA/0lEQVQoU5WRvy8DARTHv99rTw1mg3LYjCIRensTsRg7qZ+bUUyWzhL/gMFJLMwYJAZLU23SySBhcDkSdgnS1PuK4S6tVuLe9vK+n/fe9z0iZTDWlwtPeVc2Yw5ybGcevEb+tgLa736sQE40H22Rtg/SjQUSrrItlA+aEy+dENf8aImwEwKDvdvp9GsAq0fXk59xjRt+eAFgsZ8VQW+ybDG4GasnwHohvCMx9Zd3k7MQ1LzLDuCxQXI2BRDukdjpD+i+xVzxuDoSJRM2555HlWmfAZjugoR3A0tBbfy860o/ybL/OuziY5fCisghSk2Zs31Y96oA1QOkeXby6f9C3xXaVw3lpStGAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814444231QL7BCN9Q52">​</span><span id="k_08144406073UI6A1TR4I" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">为适应分布式部署、前后端分离模式，bui-ssm框架对用户登录机制做了抽象与封装，将用户登录抽象出接口</span><span id="k_0814440609D7X5H56CSC" style="font-size: 14px; color: rgb(250, 3, 19); background-color: rgb(255, 255, 255);">ILoginUserContext</span><span id="k_0814440612THPDJDTKU4" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">，该接口定义了各类用户登录机制应该实现的API。</span></div><div id="k_0814440613LQWHVWX8R3" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); line-height: 30.8px; margin-left: 24px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814444978AS4T4RMSRH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0814444981NUK4SJBGIJ">​<img id="k_0814444981XD8KUZP1FB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA9klEQVQoU5WRsUoDYRCEZ/4E7w8+gI0g2AmCD2GCvVY+gK1icbkLaa6RJHciNnY+gQiWdra2kkKwEyvBUrjk0PwTLO5MLgkk2y073yy7Q6xYzPW9XrbjjPYpZwXTz1LvKYr4W/ZjFKlq1weJxFMSJhcIeDWOh0Fg3yYhduPhmaBLEtWym4Bn4+xBEPA7n7GbpC8A9xackgnmuOV7DwXQidNPkhuLbpdw0mrWbv+BZPBOYGt5IE7vSR7NAyR9OVbqbd/rFxsukmy3otEjyM0S9APxPGzam6kv/TWdq+E2R+5aYgPAGoEPQWHo1+5IagZYJewi6WWhMQSHVw1aJ89YAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814444982KZZF7J2TOS">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814444979T4YSTMG3B2">如果采用session用户登录机制，则对应的实现为</span><span id="k_08144406175X6FPPQB5E" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">SessionUserContext</span><span id="k_0814440619HOL2ELSGP3" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">。</span></div><div id="k_0814440620LWSJUFOEIV" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); line-height: 30.8px; margin-left: 24px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08144457141F91E42CCU">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08144457162FDWOV4WYR">​<img id="k_0814445717C2I9MRLYBB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA/0lEQVQoU5WRvy8DARTHv99rTw1mg3LYjCIRensTsRg7qZ+bUUyWzhL/gMFJLMwYJAZLU23SySBhcDkSdgnS1PuK4S6tVuLe9vK+n/fe9z0iZTDWlwtPeVc2Yw5ybGcevEb+tgLa736sQE40H22Rtg/SjQUSrrItlA+aEy+dENf8aImwEwKDvdvp9GsAq0fXk59xjRt+eAFgsZ8VQW+ybDG4GasnwHohvCMx9Zd3k7MQ1LzLDuCxQXI2BRDukdjpD+i+xVzxuDoSJRM2555HlWmfAZjugoR3A0tBbfy860o/ybL/OuziY5fCisghSk2Zs31Y96oA1QOkeXby6f9C3xXaVw3lpStGAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814445718TI4C8H5FVP">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814445715767FWVBNCQ">如果采用jwt用户登录机制，则对应的实现为</span><span id="k_0814440623QF1EZMLLU8" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">JWTLoginUserContext</span><span id="k_081444062435SE9LM8GZ" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">。</span></div><div id="k_0814440625NWG8JY7QCI" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); line-height: 30.8px; margin-left: 24px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814450892DSD7IDON8U">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0814450895LGXFISLUY1">​<img id="k_0814450895V5UP8C73JT" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA9klEQVQoU5WRsUoDYRCEZ/4E7w8+gI0g2AmCD2GCvVY+gK1icbkLaa6RJHciNnY+gQiWdra2kkKwEyvBUrjk0PwTLO5MLgkk2y073yy7Q6xYzPW9XrbjjPYpZwXTz1LvKYr4W/ZjFKlq1weJxFMSJhcIeDWOh0Fg3yYhduPhmaBLEtWym4Bn4+xBEPA7n7GbpC8A9xackgnmuOV7DwXQidNPkhuLbpdw0mrWbv+BZPBOYGt5IE7vSR7NAyR9OVbqbd/rFxsukmy3otEjyM0S9APxPGzam6kv/TWdq+E2R+5aYgPAGoEPQWHo1+5IagZYJewi6WWhMQSHVw1aJ89YAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814450896YCVDNJH2N1">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08144508936O5S7UZC28">如果采用memcached集中式机制，则对应的实现为</span><span id="k_0814440628678N3J9EIZ" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">MemcachedUserContext</span><span id="k_08144406294YJFP5I831" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">（还没有实现完成）。同理可以实现一个redis的用户登录。</span></div><div id="k_081444063052S3A8KMFZ" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); line-height: 30.8px; margin-left: 24px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814452245ZUW7QVN9CT">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08144522478LF4M336ZO">​<img id="k_0814452248R5W2LLF81A" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA/0lEQVQoU5WRvy8DARTHv99rTw1mg3LYjCIRensTsRg7qZ+bUUyWzhL/gMFJLMwYJAZLU23SySBhcDkSdgnS1PuK4S6tVuLe9vK+n/fe9z0iZTDWlwtPeVc2Yw5ybGcevEb+tgLa736sQE40H22Rtg/SjQUSrrItlA+aEy+dENf8aImwEwKDvdvp9GsAq0fXk59xjRt+eAFgsZ8VQW+ybDG4GasnwHohvCMx9Zd3k7MQ1LzLDuCxQXI2BRDukdjpD+i+xVzxuDoSJRM2555HlWmfAZjugoR3A0tBbfy860o/ybL/OuziY5fCisghSk2Zs31Y96oA1QOkeXby6f9C3xXaVw3lpStGAAAAAElFTkSuQmCC" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814452249IR3QYF44CQ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814452246VLVKF5G9IJ">编写好对应的用户登录实现后，在spring-context.xml中配置一个名为【loginUserContext】的bean即可，该bean即为具体的用户登录实现（SessionUserContext /&nbsp;</span><span id="k_0814440634N6CNEJH1DH" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">JWTLoginUserContext</span><span id="k_0814440636H7TH3FKLUE" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">...）</span></div><div id="k_0814440639BKID26GFHD" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08144406406MINJT9CQT">​</span></div><div id="k_081445242398KJ1316QB" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(136, 139, 247); padding-left: 12px; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(136, 139, 247);" id="k_08144524241RRC621U62">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0814465380JS9DSFIR2F">​<img id="k_08144653816L4RACQF8H" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAARElEQVQoU2NkIBEwkqiegfH///9vGBgYhLFo/MbAwFDHyMjYiywH0vCJgYGBF4uGXwwMDJ2MjIx1w1EDaaFEcjzQXAMAXFwqDVPnP58AAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(136, 139, 247);" id="k_08144653833KNF6M1Y7H">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(136, 139, 247);" id="k_0814460149F7A58OIBGN">​&nbsp;</span><span id="k_08144601473OQFXIIBH6" style="color: rgb(255, 255, 255); font-size: 14px; font-style: normal; font-weight: 700; background-color: rgb(136, 139, 247); text-decoration: none solid rgb(255, 255, 255); font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; vertical-align: baseline;">相关配置说明</span></div><div id="k_0814460150REP1VTXHTT" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 23px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814460151DE4QC4FJWE">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814500781HNSBEPEXFQ">相关cookie，jwt的配置在通用系统配置文件 </span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(255, 255, 255);" id="k_0814500782XUNSQF88VW">common.propertie</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(160, 32, 240); background-color: rgb(255, 255, 255);" id="k_08145007869HXDYKC9GW">s</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814500787TNK5EU5OWN">&nbsp;</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814500480AUGFZPAA11">中。</span><span id="k_0814475791EY4YD6M42C" style="font-size: 14px; color: rgb(102, 102, 102);">​</span></div><div id="k_08144757914LKGRT67ZR" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); line-height: 30.8px; margin-left: 23px;"><span id="k_0814475793DW4ML1MJM3" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">1、</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08144947371LF6OHJRVQ">配置为session机制时候，写入的cookie key 为【cookieKey=_jvin$s</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814494739JUU9BC5D9A">0</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814494739UCG3Y5NKK3">s%key_</span><span id="k_0814475797L93G22HSNK" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">】</span></div><div id="k_0814475797S5VBXCGQNR" class="_section_div_" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); line-height: 30.8px; margin-left: 23px;"><span id="k_0814475799OW5N9F1H7Z" style="font-size: 14px; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255);">​2、配置为jwt用户机制时候，其配置如下</span></div><div id="k_0814475801171K1Q1DAK" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 47px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); margin-top: 12px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814475802FNRJDIG3AC"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">      ​<span style="background: none;">jwtKey=k*$%6                                 <span style="background: none; color: rgb(165, 165, 255);"> //HS256 秘钥</span></span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">    jwtExpTime=60                                <span style="color: rgb(165, 165, 255);">// jwt过期时间 分钟</span>
    jwtCookieKey=jwtcookie                 <span style="color: rgb(165, 165, 255);">// jwt 写入的cookie名称</span></pre></pre></span></div><div id="k_0814483251MC3HTKM1I4" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081448325236BI9MC3RG">​</span></div><div id="k_0814484230RV9ZY1B4C5" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(136, 139, 247); padding-left: 12px; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0814484231DB213IRDWU">​</span><span id="k_0814484360KRMMM9YVYD" style="font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; font-size: 14px; font-weight: 700; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247); font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0814490355U5MSDLLTKN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0814490358HN56IKPQB7">​<img id="k_081449035943DALIBXZX" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAARElEQVQoU2NkIBEwkqiegfH///9vGBgYhLFo/MbAwFDHyMjYiywH0vCJgYGBF4uGXwwMDJ2MjIx1w1EDaaFEcjzQXAMAXFwqDVPnP58AAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0814490360R3GYCJRGAF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247); font-weight: 700; font-style: normal; text-decoration: none solid rgb(255, 255, 255); vertical-align: baseline;" id="k_0814490355NXD1K6UPWG">&nbsp;</span><span id="k_08144844712FTGDTBCY9" style="font-weight: 700; font-size: 14px; color: rgb(255, 255, 255); background-color: rgb(136, 139, 247); font-style: normal; text-decoration: none solid rgb(255, 255, 255); font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; vertical-align: baseline;">关于jwt机制说明</span></div><div id="k_0814484475JTC6GC94TZ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 24px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08144844763QP4I4ONF9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814504442MREAQJPZGH">​</span><span id="k_0814504439C26JF9Y93E" style="font-size: 14px; color: rgb(3, 118, 240); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">个人认为:&nbsp;</span></div><div id="k_0814505003IBOM6VB34C" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 79px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0814505004V8AEM2EJAP">jwt适用于(多系统相互调用)微服务接口应用场景，如果是单一系统内的用户使用场景，session机制比较成熟合适，需要分布式无非</span><span id="k_0814505005F27S7WKY41" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">就</span><span id="k_0814505006V6CDA5OR7D" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">部署个集中式session或者利用容器的session同步机制。</span></div><div id="k_081451002027591XI1MW" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 24px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; color: rgb(66, 139, 202); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0814510021OATZ2DLPBC">​jwt机制也可以有多种技术实现场景：</span></div><div id="k_0814512983FJF6MWR8OC" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 80px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0814534043ZINKF55RJJ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0814534045L5AYW9PEU4">​<img id="k_081453404614S9CDKGFH" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAtklEQVQoU5WRMQ6CQBBF/ycRTuFyCCs5jkQLCwtRrEyMhTGxVGMlvfEaRjgGeA5hDJtglBAXp515+9/sEH8W/X56E+ElStQBoJh4Drw0gMgGYs3aQBz1pPO0syWIOYHgfHdPv5JYKmjIybYAxiZIAxWU2+kO4FCASRS7UVPSF1CpoWCoEnVcgUX9E95KufOYihRr0/J66bbDZRp9L1uULxNW2I3VvknjU0sfDuC1zbBOMF223n8BEyxaDWUWJigAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0814534048GXJ8JEMD12">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0814534044EDA3AKNR5C">url&nbsp;token&nbsp;+&nbsp;后端JWTLoginUserContext&nbsp;需要每个url都带上token参数，比较繁琐（缺点无法实现系统用户主动退出功能）。</span></div><div id="k_0814514150NSUHKELURF" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 80px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0814535254OMHK6RXOUH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08145352576R39POK74O">​<img id="k_0814535257T7LAGOVN2M" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAtklEQVQoU5WRMQ6CQBBF/ycRTuFyCCs5jkQLCwtRrEyMhTGxVGMlvfEaRjgGeA5hDJtglBAXp515+9/sEH8W/X56E+ElStQBoJh4Drw0gMgGYs3aQBz1pPO0syWIOYHgfHdPv5JYKmjIybYAxiZIAxWU2+kO4FCASRS7UVPSF1CpoWCoEnVcgUX9E95KufOYihRr0/J66bbDZRp9L1uULxNW2I3VvknjU0sfDuC1zbBOMF223n8BEyxaDWUWJigAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0814535259HD49PP57M6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_08145352557BKWPEMR3F">request&nbsp;head&nbsp;&nbsp;+&nbsp;后端JWTLoginUserContext&nbsp;，该场景框架内置的JWTLoginUserContext还没有实现，开发者可以自行修改实现（缺点无法实现系统用户主动退出功能）。</span></div><div id="k_0814504444LMIOK5CAB8" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 80px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814540107OVRCXZ4731">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08145401109NPWK93UX2">​<img id="k_0814540111J51JSXKCDP" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAtklEQVQoU5WRMQ6CQBBF/ycRTuFyCCs5jkQLCwtRrEyMhTGxVGMlvfEaRjgGeA5hDJtglBAXp515+9/sEH8W/X56E+ElStQBoJh4Drw0gMgGYs3aQBz1pPO0syWIOYHgfHdPv5JYKmjIybYAxiZIAxWU2+kO4FCASRS7UVPSF1CpoWCoEnVcgUX9E95KufOYihRr0/J66bbDZRp9L1uULxNW2I3VvknjU0sfDuC1zbBOMF223n8BEyxaDWUWJigAAAAASUVORK5CYII=" style="width:12px;height:12px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814540113Q618N4OUTN">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814540108GH47G7KVTL">cookie&nbsp;token&nbsp;+&nbsp;后端JWTLoginUserContext&nbsp;，内置的JWTLoginUserContext已经实现，可以实现用户主动退出功能（缺点每次请求都需要解密后重新加载用户权限数据，优点适合前后端分离开发联调，可避免session同步问题的分布式部署）。</span></div><div id="k_0814521842OY1COSWSZE" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0814521844I1MUOKOKQW">​</span></div>
</div>