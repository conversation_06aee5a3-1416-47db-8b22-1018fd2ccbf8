<div style="position:relative;">
<div id="k_0810014282HUFH1VEOL3" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(238, 247, 237); padding-left: 12px; border: 1px solid rgb(242, 237, 250); margin-bottom: 11px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 43, 204); background-color: rgb(238, 247, 237);" id="k_0810014283KIQFMQOI3F">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 43, 204); background-color: rgb(238, 247, 237);" id="k_0810014853C8I5JX9G3S">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810033159TSD4MPNQUF"><img id="k_081003316051WXEWGM2B" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABsUlEQVQ4T42SP2hTcRDHv9/Ls/Vf0EkRFdrFNGmqRROJ4OKimzg4iKaTKDpUcShFHEoHkeIg6qAoTg3SoYPopkMXFWmiVI15jQgWRKSdqtVi9L3fV2IxRd4TvPG4z33ujiNiY8JDZn0WtfkqsC+IK2Esl3q+nQmVFLKI+q5X/wlOeJZJjogqUHzmaguDcdaocck2LOcN0oIRhRyKsxIQ0TG1Dqu5GQo6jOpzwj34+TGky0eMOOTEUdCbwaI+Yqb3M0CR3ZUHkHIgfQKTDiqjET7Cu8IXbHm6Csm2A2YoSNoDsgfAEzWCY0TqZScTP26Ldhlvdj5sdoseQ0T3i/2UG1DYdgL1He+XdsxObqXjLQE3Ucvd/xsWkakcJHBKppOo7v7QRJaP89v884qE0/Bzn1rWdGUTiRsKV5xrmv7kl8GuqW1mwXkXrj2DetdCC0xNJy3x9Zpz3iVM976NgpnKXgMOu+8YQjuO0tAvh+to4K6txLADxlHLPY6C6XIfieMgN0gsASoBLJIqQpqTcAd+fjQOvGDkGmftV1HtmW2Nmn290VzjrJO+wc9fjILx3/7P7C+8R68JC5Nx/AAAAABJRU5ErkJggg==" style="width:14px;height:14px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 43, 204); background-color: rgb(238, 247, 237);" id="k_0810033161PIIYLXRGL7">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 43, 204); background-color: rgb(238, 247, 237); font-style: normal; font-weight: 700;" id="k_0810020034UQFSFAW2QI">一、动态</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 43, 204); background-color: rgb(238, 247, 237); font-style: normal; font-weight: 700;" id="k_0810020035C8XWKG65C2">数</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 43, 204); background-color: rgb(238, 247, 237); font-style: normal; font-weight: 700;" id="k_081001579556H6VYHE3Z">据源实现思想</span></div><div id="k_08100148543JSYTH5BY7" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810070676ART1O4TJUN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08100706784TLZS69L32">​<img id="k_08100706786CN4S878UL" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAUUlEQVQoU2NkIAMwkqGHgZFR6/R2RkZGD+ya/3f9u/qlmoHB8Q+yPCOj1pkTjIwM5lg1/WdY9u8DQyrDM5Nvo5pwBMQGRkYGf5KCnKzIpZsmAK98QA7Lmn/LAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810070678UUF9K9CB4C">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810070677DFRSULIWWO">利用sprin</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810055909XH8RY8QCEN">g&nbsp;bean工厂创建多个由spring管理的SqlSessionFactory，bean的name即为对应数据库的dbkey。每次切换数据时候，通过dbkey从spring中获得对应的SqlSessionFactory。</span></div><div id="k_0810042981M3GYW6Q3AY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810071331EPWH3NIST4">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810071332MHEKV17HBQ">​<img id="k_0810071332S9W3J4JZI9" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAUUlEQVQoU2NkIAMwkqGHgZFR6/R2RkZGD+ya/3f9u/qlmoHB8Q+yPCOj1pkTjIwM5lg1/WdY9u8DQyrDM5Nvo5pwBMQGRkYGf5KCnKzIpZsmAK98QA7Lmn/LAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810071332VUMGRP9GV2">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810071331GNGIFFDKFU">编写一个继承于SqlSessionDaoSupport的DynamicDaoSupport，DynamicDaoSupport&nbsp;重写&nbsp;getSqlSession()，getSqlSession()内部根据线程上的dbkey从spring中获得不同的数据源的sqlSession。</span></div><div id="k_0810044840ZYVOTOHB15" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810072155I9X3D2P24C">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810072155B75BPPJ17T">​<img id="k_08100721558OO7C7L7UB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAUUlEQVQoU2NkIAMwkqGHgZFR6/R2RkZGD+ya/3f9u/qlmoHB8Q+yPCOj1pkTjIwM5lg1/WdY9u8DQyrDM5Nvo5pwBMQGRkYGf5KCnKzIpZsmAK98QA7Lmn/LAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810072156SMXGSDEQEK">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08100721552441N69856">BaseDao及所有的业务Dao均继承于DynamicDaoSupport，以便获得动态getSqlSession()的能力。</span></div><div id="k_0810044840ZYVOTOHB15" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08100730131KYDHWYXQ5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810073014PND9AVJPAA">​<img id="k_08100730149LWR4GOH4A" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAUUlEQVQoU2NkIAMwkqGHgZFR6/R2RkZGD+ya/3f9u/qlmoHB8Q+yPCOj1pkTjIwM5lg1/WdY9u8DQyrDM5Nvo5pwBMQGRkYGf5KCnKzIpZsmAK98QA7Lmn/LAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810073016NU1GH3F5YB">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081007301379H6NDUJ8Z">DynamicDaoSupport提供一个静态的initDataSource(....)&nbsp;api，initDataSource根据数据库连接信息，通过spring的BeanDefinitionRegistry创建由spring管理对应的SqlSessionFactory。</span></div><div id="k_0810044840ZYVOTOHB15" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08100738383LVVRVWTG5">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810073839XO8EY7977K">​<img id="k_0810073839TZMG3XY2EM" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAUUlEQVQoU2NkIAMwkqGHgZFR6/R2RkZGD+ya/3f9u/qlmoHB8Q+yPCOj1pkTjIwM5lg1/WdY9u8DQyrDM5Nvo5pwBMQGRkYGf5KCnKzIpZsmAK98QA7Lmn/LAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810073839WCXT6STD36">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810073838JSMG2KBQNO">系统主数据库配置在spring-context.xml中，其他数据库通过DynamicDaoSupport.initDataSource(List&lt;DataSourceInfo&gt;,&nbsp;ApplicationContext,&nbsp;IOnDataSourceCreated)初始化。</span></div><div id="k_0810044840ZYVOTOHB15" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08100746945G3GE5EKAT">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_08100746961TMYMACFZN">​<img id="k_0810074696AUR3O98894" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAUUlEQVQoU2NkIAMwkqGHgZFR6/R2RkZGD+ya/3f9u/qlmoHB8Q+yPCOj1pkTjIwM5lg1/WdY9u8DQyrDM5Nvo5pwBMQGRkYGf5KCnKzIpZsmAK98QA7Lmn/LAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810074696WNW59H1OB9">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810074694A8PU7SBUJL">当spring容器创建完成后，可以通过访问主数据库或者读取配置文件创建DataSourceInfo，调用DynamicDaoSupport.initDataSource()完成所有数据库初始化。</span></div><div id="k_0810044840ZYVOTOHB15" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 13px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810075494QBI9VMJ86N">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810075495JHYLFW2EF4">​<img id="k_0810075495IEXQN1HVZ9" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAUUlEQVQoU2NkIAMwkqGHgZFR6/R2RkZGD+ya/3f9u/qlmoHB8Q+yPCOj1pkTjIwM5lg1/WdY9u8DQyrDM5Nvo5pwBMQGRkYGf5KCnKzIpZsmAK98QA7Lmn/LAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810075496W9SGS7VUQX">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810075494UXOCPIIG46">DaoDBKeyHolder记录当前线程使用的SqlSessionFactory&nbsp;bean&nbsp;Name，开发者可以通过编程修改DaoDBKeyHolder里面的bean&nbsp;Name实现编程式动态指定数据库。</span></div><div id="k_0810034137TMQKRYZBUS" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810034137RIIAYFPV8N">​</span></div><div id="k_0810034187Y62H25W3TY" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(238, 247, 237); padding-left: 12px; border: 1px solid rgb(242, 237, 250); margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 43, 204); background-color: rgb(238, 247, 237); font-weight: 700; font-style: normal; text-decoration: none solid rgb(2, 43, 204); vertical-align: baseline;" id="k_0810034187S9HTC5ODYO">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 43, 204); background-color: rgb(238, 247, 237); font-weight: 700; font-style: normal; text-decoration: none solid rgb(2, 43, 204); vertical-align: baseline;" id="k_0810081748TR493KPQTW">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0810084365J91U3JNPKW">​<img id="k_0810084365RAVZJ2K9ZE" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAABhklEQVQoU4WST0tUYRSHn3O8M9eUSxAualYJQs0dIdAxI4JwH+5dtApcBwqhJP0RWhUE7mrnB/AbxCytZsZFMFfFSoKoTYvGi1qDc06MJsM4E73L5z0Pv3MOR+j14vcX8SBkc+xLr2/phqVAC9EjnMiSdB6mjs7WdEuj1byaPwaOzHnBZnHjP1Ip0DhaMmQd0z3ta05bLX14Nk2Iy9cRnVD3guM3ED77odxDz//SsL7i7kVB3hp8xHSdrXpZiCvzItz2ZjCH939n+2ra0U6uMsAFLonzUpwNS9JlgVJAHN1XYcjqjSd8vXnYIY3shJqtLxo0SdJnrVb/LqIUkI9mVTxnnHtKMto4Eb2PfHVBhUwr4XS29vbiyqQKd6xWXOpIiquLIO9Ixt6c8raUL989hpnsGs3GLKYBv/0VIbdQyVEbf90laaGyjDPs4lfcWEE4EOGBOJ9Adi0ZbyV6SzxJurzbr4M/nptJjUxmlQ/X9o/5yE5Idm9G8Un7yRzfigdtqecB/hv+AVWJkZTtoqFbAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(2, 43, 204); background-color: rgb(238, 247, 237); font-weight: 700; font-style: normal; text-decoration: none solid rgb(2, 43, 204); vertical-align: baseline;" id="k_081008436597OG66XYIA">​</span><span id="k_081008174775W8UMK9PZ" style="font-size: 16px; color: rgb(2, 43, 204); font-style: normal; background-color: rgb(238, 247, 237); font-weight: 700; text-decoration: none solid rgb(2, 43, 204); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">二、</span><span id="k_0810081747HSPF2GAYQC" style="font-size: 16px; color: rgb(2, 43, 204); font-style: normal; background-color: rgb(238, 247, 237); font-weight: 700; text-decoration: none solid rgb(2, 43, 204); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">开发步骤</span></div><div id="k_0810034128MKUAV6PE4V" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810112589JJXY3VJPT3">1</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810112590D3LE9EXK26">、</span><span id="k_081008549911CMV8U9N9" style="font-size: 14px; color: rgb(0, 42, 196); font-style: italic; font-weight: 400; background-color: rgb(255, 255, 255);">了解配置文件</span><span id="k_0810085499JKSTMTSOTL" style="font-size: 14px; color: rgb(0, 42, 196); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic;">spring-context.xml</span></div><div id="k_0810085542OVHK4RAXI6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 66px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_08100857479LYSZ499PA">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810102091L511U86HB6">​</span><span id="k_0810102090K3AJ1HTLQC" style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​框架默认提供了三个spring配置文件，说明如下：</span></div><div id="k_0810102115WJ96U6U5QY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 100px; width: 671px; text-align: left; background-color: rgb(245, 239, 252); padding-left: 11px; margin-top: 12px; border-top: none; border-right: 1px solid rgb(147, 113, 250); border-bottom: none; border-left: 1px solid rgb(147, 113, 250); border-image: initial;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810120857PUA1RJBOG7">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(245, 239, 252);" id="k_0810120858QZU6HYXQLX"><img id="k_0810120858VQRSJMYI4G" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAuElEQVQoU2NkIAMwkqGHgXJN8xX+czAxfKz//49hT8Ijgb24XIFi00yG/6xs8p86GBn/x/z/xxCT+EhgNzaNGM6DaPxQy8DAmM3IwBiX8JB/K7pGxgUKH84zMDAYYHXKf4a3TP8YPOIeC5xBlmecI/NRiIuVmRVZ8Ne/PykMjAwlDP8ZSuIf8s9jZGD8j6IJmfOf4T/jArlPOQxM/1qZGJhj4x7wbkLXAFKP4ieyQo/YiKY8com1CQAMZjoOvYJxcAAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810120858JNBZYR7YOF">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810133580FCCUOH6QN6">spring-context-o</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810135257X3PSVT82RZ">nlyone-d</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810135258WJFGSB7P4Z">b.xml&nbsp;：</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_08101320096J6BJVV54F">单数据库系统应用配置，sprin</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810132009P6NUR2LF8T">g常用的单库事务管理。</span></div><div id="k_0810102115WJ96U6U5QY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 100px; width: 671px; text-align: left; background-color: rgb(245, 239, 252); padding-left: 11px; border-top: none; border-right: 1px solid rgb(147, 113, 250); border-bottom: none; border-left: 1px solid rgb(147, 113, 250); border-image: initial;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810122276XTNAYTKE93">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(245, 239, 252);" id="k_0810122278VVXG7HN4OO">​<img id="k_0810122278896LRUCA3X" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAn0lEQVQoU62RsQ3CQAxFny+REBWiOraioLoNAjTQU7ARbIBEQ3rEDiSsgGwEVXI6pOgUl7afv/W/kFGSwTAGZBPvWKPUDVL/+yJSsnLh2ANBlW2L3FJg4r0fWBlUqmxeyDUGxRd2FlimLppxFyU8kUd3LnNs9oay25w6Vg52qhxaOIFYD+ormPiCgHFEqRq4xMB3PzYix71hUY8R7jClD6QDLg4Dl37jAAAAAElFTkSuQmCC" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810122278GPZNAFXVB6">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810122276LZAJQF81WY">spring-context-mutildb-local-tx.xml&nbsp;：多数据库，spring常用的单库事务管理（即不是跨库事务）。</span></div><div id="k_0810102115WJ96U6U5QY" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 100px; width: 671px; text-align: left; background-color: rgb(245, 239, 252); padding-left: 11px; border-top: none; border-right: 1px solid rgb(147, 113, 250); border-bottom: none; border-left: 1px solid rgb(147, 113, 250); border-image: initial;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810124014ITJ5EX1M51">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(245, 239, 252);" id="k_0810124016IBOGHOKJCV">​<img id="k_0810124016ZUNCNEYAR1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAvklEQVQoU2NkIAMwkqGHgXJNSUeSeH99+TXn3/9/25d5LluAyxUoNiXsT+D48/PP8v8M/10YGBnyl7ovnYdNI4bzwBp//Jnzn+F/EAMDQ8lSz6XT0DUyRm+PfszIyCiD1Sn/Gb79Z/ofvtR96RZkecbobdEyfxj+sMMEmVmYmZj+MVX+//8/gpGRsX+J+5IaBkaG/yiaUGz4z8AYvTO6j/E/YyYDI0P5Evclk9A1gNSj+Ims0CM2oimPXGJtAgAON0cOApHT+gAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810124016Y8TMOQIP44">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810133582K4ES6NQTLQ">spring-context-mutildb-globel-tx.xml&nbsp;：多数据</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_0810135259T5WUGP6DH9">库</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_08101352593ILVTPBU94">，跨库事务支持，</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(245, 239, 252); font-style: normal; font-weight: 400;" id="k_08101320111UW11R9MN3">依赖于Atomikos。</span></div><div id="k_0810090251DN96UH3Y2G" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 11px; border: none;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810090251QGYLOFJVCH">​</span></div><div id="k_08100857513KWZMU1JSR" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810085768MFE3RNAOFB">2、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_08100857517HTX6JPMPU">​</span><span style="font-size: 14px; color: rgb(0, 42, 196); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic;" id="k_0810085751MQU8LXQ1LY">​编写实现ISpringContextListener接口的实现类，并在配置文件中注册到spring启动通知中。</span></div><div id="k_08100857713AP3MCLAO9" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 66px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08101440275MFH7D1947">​</span><span id="k_0810144027ZJSBAJXFXW" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​</span><span id="k_0810144027769KLKPYKW" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​spring-context.xml配置：</span></div><div id="k_08101453753J4GBDGY9F" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 99px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 11px;"><span style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_08101453758P1FL48NUP"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">	&lt;bean id="SpringContextUtil"</span>   class="kevin.framework.basic.common.utils.SpringContextUtil"&gt;</div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);"><!-- spring就绪后的业务启动，SpringStartedListener内创建其他数据源 --></span>            &lt;!-- <span style="color: rgb(165, 165, 255);">spring就绪后的业务启动，SpringStartedListener内创建其他数据源</span> --&gt;
            &lt;property name="springContextListener"&gt;
                         &lt;bean class="web.commons.SpringStartedListener" /&gt;
            &lt;/property&gt;
        &lt;/bean&gt;</pre></pre></span></div><div id="k_0810092834BUT1WTLNWE" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810092834Y5BAECS278">​</span><span style="color: rgb(0, 42, 196); font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_08100918049WFKFCMRRD" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810093173KMD7Z83DQ7">​3、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810091804QM4JPWE1C7"></span><span style="font-size: 14px; color: rgb(0, 42, 196); font-weight: 400; background-color: rgb(255, 255, 255); font-style: italic;" id="k_0810091804CZ4T9E8EF9">​SpringStartedListener中创建多数据源​</span></div><div id="k_0810093177PKNK2OY2QZ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 99px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 11px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810093515MGTQH19FNN"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​          <span style="background: none; color: rgb(165, 165, 255);"> /***</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">        <span style="color: rgb(165, 165, 255);"> *</span> <span style="color: rgb(165, 165, 255);">设置数据源，返回DataSourceInfo列表，这里从文件读取，可以实现从数据库读取</span>
         <span style="color: rgb(165, 165, 255);">****/</span>
        @Override
        public List&lt;DataSourceInfo&gt; setDataSourceList() {
            List&lt;DataSourceInfo&gt; dbList = new ArrayList&lt;DataSourceInfo&gt;();		
            String file = SysConfigParams.configHome + File.separator + "db-list.json";
                   <span style="color: rgb(165, 165, 255);"> .............</span>
                    return dbList;
          }</pre></pre></span></div><div id="k_08100941184EY55YG57R" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810094118SROOYARRCP">​</span><span style="color: rgb(0, 42, 196); font-family: &quot;Microsoft Yahei&quot;;">​</span></div><div id="k_0810093519KB4PLSBB7N" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0810192762K8EKEYTBOK">4、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(0, 42, 196); background-color: rgb(255, 255, 255); font-style: italic; font-weight: 400;" id="k_08101927636HM8EGHVKN">编程方式动态修改线程中的DaoDBKeyHolder记录</span></div><div id="k_0810034161PRCG4CM3SK" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 100px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 10px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810034162GM9QAPZGY3"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">		<span style="background: none; color: rgb(165, 165, 255);">/***动态跨库 操作</span></span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;"><span style="color: rgb(165, 165, 255);">             *  local 库   remote 库  remote = 默认库***/</span>
            @Override
            public int insert(Dict entity) throws Exception {
                
                  <span style="color: rgb(165, 165, 255);">/**切换到local库**/</span>
                  this.setDbKey("local");
                  int r =super.insert(entity);
                
                  <span style="color: rgb(165, 165, 255);">/***切换到remote**/</span>
                  this.setDbKey("remote");
                  super.insert(entity);
                
               <span style="color: rgb(165, 165, 255);">   /**恢复到默认库**/</span>
                  this.clearDbKey();
                
                  int r = super.insert(entity);			
                  return r;
            }</pre></pre></span></div><div id="k_08100341114H6JEDTUT1" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_081003411258RM16OGLT">​</span></div><div id="k_08100342549LTZ44D681" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810034254FIP3BFPTC7">​</span></div><div id="k_0810034295NGS2EJJWPO" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810034295GC3VFGR1O1">​</span></div><div id="k_0810034237PCTOGBT3ZP" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_08100342374BNBQAYZCP">​</span></div><div id="k_0810034279SEMKAS5C6L" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810034279B66M6WSQJL">​</span></div><div id="k_0810034212J6YX4DCV7O" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0810034212ISXFHJYD3U">​</span></div>
</div>