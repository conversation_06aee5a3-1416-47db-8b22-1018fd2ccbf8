<div style="position:relative;">
     <div id="k_1413463963A52G493YUT" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:0px;width:auto;text-align:left;background-color:rgb(131, 135, 247);padding-left:12px"><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247)" id="k_1413463966BYN48B1QOI">​</span><span style="font-size:14px;color:rgb(102, 102, 102)" id="k_1413463968B77QARWVC6">​<img id="k_1413463969AE4GOFIGBR" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAARElEQVQoU2NkIBEwkqiegfH///9vGBgYhLFo/MbAwFDHyMjYiywH0vCJgYGBF4uGXwwMDJ2MjIx1w1EDaaFEcjzQXAMAXFwqDVPnP58AAAAASUVORK5CYII=" style="width:12px;height:12px">​</span><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247)" id="k_1413463971AVCUEPTC6D">​</span><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247)" id="k_1413463972AXRACYZ5TA">&nbsp;</span><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247)" id="k_14134639743X5AMWHRXE">概要：</span></div><div id="k_1413463975JY7YHH53MP" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:72px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px;margin-top:15px"><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463976976JU2JMUT">​</span><span style="font-size:14px;color:rgb(102, 102, 102)" id="k_1413463978XZPXFIUDAQ">​<img id="k_14134639799PGH5CAL8G" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAtklEQVQoU5XRPQ5BYRCF4XfmikLNxxY07EBrD9iJiIaEWiOE6JSWIAqrkKi50YlCcWdEovKXfFOfJ5OTI0SeROaJBwGvpXAoQk6hlCLHf18lJN4Rp+rGUpU+xuKE7H4hAdeQ0HoiMeaudDHWZ9iC+Dt8dXAJCW116ndjkleGbkxTZP8VBLysyghjlUFBlWZm9C7I9QO8wmMzZkBFlMav8BNLWX2QGZsE7igtjOEZuf0pHTdd9HAPKRI9DduW5iAAAAAASUVORK5CYII=" style="width:12px;height:12px">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463981W2IQE7EUCW">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463982CAOHXECLC6">开发接口调试功能为开发者提供简约版的API调试功能，无需引入swagger或者其他任何第三方组件。</span></div><div id="k_1413463984OKTSTUE4RY" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:72px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463986LEKCJELTV4">​</span><span style="font-size:14px;color:rgb(102, 102, 102)" id="k_141346398757GTVE54Y3">​<img id="k_1413463988ONMMTDXR2S" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA3UlEQVQoU7XRsUrDYBQF4HNu+C2EBAdB0amls4PgI7img2+gfQNH9w4KpcVqV1fBSSwIgoNvIYi2jyCSiJL8OVLdrBEyeOfzcThcouaxZh71wSzsbAVvjYcQqXtpBGvtj8njX618jpOuxE3kwZjOH0vlqJ1N7qoQBdg0TvbmyOU2Kpx6FM6b2dUtAf2EXxsEcBon+xK3rbAjOZ1QZb+VXt//Cmbh7rq34hQqx2aMVDJZzvKDFdy8LoDvsD8jNCSwIWGnKjzHfIo6A/O8kPl3MOhG6dLhKi7TytH//rhPwGBUDbyzXlwAAAAASUVORK5CYII=" style="width:12px;height:12px">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463990IZLYX1C4BD">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463991GBKFKGX6JG">开发者在controller上的接口，[&nbsp;</span><span style="font-size:16px;color:rgb(92, 184, 92);background-color:rgb(255, 255, 255)" id="k_1413463993SNOLXXA3PF">ApiController</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463995AXLLOFCC3N"> ]具有扫描所有controller提取形成接口调用信息的能力&nbsp;</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463996TCCFKTS8HS">。</span></div><div id="k_1413463997T15I8AFTMD" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:72px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463999J2JPSXNRJA">​</span><span style="font-size:14px;color:rgb(102, 102, 102)" id="k_1413463900156JC2SZL8">​<img id="k_1413463902OIOH1RV4KD" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAA0UlEQVQoU7XRv0rDYBQF8HO+GqFLXyCb4FYnX6BL/4zN4PSRl6iri7NSMBBo0alJILQPEF20fZkO7oJLviMfbtUUMvTO58c9l0u0HLbMoz2wlY1Ua+vOXTdAcJmNst2xrbSVTUheQbgHsBSVFJMibUIcfAzOwu9w7pGM7ui4APCSj/MEhA7h7w0C7at9Inktp1sYrAA8FJPi+V8Qv8V9SWtBKYQegKju1NNyVO7/AB92chtJj4bmQtKwKeyxr/LuOwv6IjgzNDfZOPtsPPrkj/sBNTpQDXOgCh4AAAAASUVORK5CYII=" style="width:12px;height:12px">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463903SZ5WUJUBVS">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463906EGX8RWQK9F">通过 [ </span><span style="font-size:16px;color:rgb(92, 184, 92);background-color:rgb(255, 255, 255)" id="k_1413463907HV2M5PDFYF">xxx/api/page/index</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463909BU824VO7ZQ"> ] 打开调试接口列表，框架提供的接口列表具有调用接口的功能。</span></div><div id="k_1413463910PKMS4H16K4" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:0px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463912GHYOSY12QV">​</span></div><div id="k_1413463914QLJZZYIOYH" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:0px;width:auto;text-align:left;background-color:rgb(131, 135, 247);padding-left:12px;margin-top:0px;margin-bottom:0px;text-indent:0px"><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247);font-weight:400;font-style:normal" id="k_1413463916DQAB6TGNF3">​</span><span style="font-size:14px;color:rgb(102, 102, 102)" id="k_1413463917ZURDMDHGJI"><img id="k_1413463919M89DQ55SW7" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAARElEQVQoU2NkIBEwkqiegfH///9vGBgYhLFo/MbAwFDHyMjYiywH0vCJgYGBF4uGXwwMDJ2MjIx1w1EDaaFEcjzQXAMAXFwqDVPnP58AAAAASUVORK5CYII=" style="width:12px;height:12px">​</span><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247);font-weight:400;font-style:normal" id="k_14134639216AT5X9MG2Z">​</span><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247);font-weight:400;font-style:normal" id="k_14134639227M8ZQK855Z">&nbsp;API注解说明：</span><span style="font-size:16px;font-weight:400;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247);font-style:normal" id="k_1413463923GOKHRAHI99">​</span></div><div id="k_1413463925UFH72D8U7J" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:70px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px;margin-top:15px"><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463926SHAK6WZ8TT">​</span><span style="font-size:14px;color:rgb(102, 102, 102)" id="k_1413463927Y12WLW82AE">​<img id="k_14134639297ED7ZKF2GM" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABu0lEQVQ4T52SwUsUcRTHv9/fbDOBCnVQxDp5qENRf0BelGTXkLo0h3VnDi2etEMRQWWXDtZRD2pEyxrsasjcRNrdCLx06FjQpUAvaYgHLTJwbOb35Dc0smGnfcfH9/O+j/e+RIvFlCu+L3aEv8OH1LwFoOtvf1uUzDttzrNyX/lXs0cCejXvPIBlEOcA/IHgRyIiTgE4AcFXANerQ9UvKUy/4XeJljqAywBKOtYPFocXd43AfeN22rRfELwB4BMVc5VsZTuZ6dW9SQCPBFJeyC6MgpB0qrvqtiOCZUd2ieRNAE+ruepECn6EoCdGfDU6Ga2bZtAf7PkN/6LWupaIyHsQzID4HiIcCHLBDgv1whbBrVji2wrqdeKmcBcaUyTPisiGhs5btGYE0h3p6MrStaW1Y6ARp6saSCk1tK/2vzmRsyqUniPQq3sfIOg1q2ZURpv1UicDVbKVz/la/pIF6x2AjTAT9geDwU/j+ITCx0J5ZY7jv/UvaK2nlVJ3DAQBC41CiWDxn+M0v0MgL50O53767JGVkdPM8DmF7rF3tByA5sgd7B2MCWScwjOmL5RNgrN2uz3338i1kvNDcJHaD5oJ170AAAAASUVORK5CYII=" style="width:14px;height:14px">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463931WFOM87FPMN">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463933OPIVWDUHUG">对于add / update 两大接口，框架会提取对应的实体bean，并以此形成调用参数。</span></div><div id="k_141346393587BK5U2S9C" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:70px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463936I2OCYA6Q26">​</span><span style="font-size:14px;color:rgb(102, 102, 102)" id="k_1413463938HGMPIS2ZI2">​<img id="k_1413463939O3V7WLRZOQ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAABc0lEQVQ4T52Sv0tcQRSFv3MHIiGtvrepEhBL/4JUYhsQtBFCUokiqWTVdKJYRYipAipWWUiqBAIp0oRU+Scignb7Vi1EJD9g5oZ56+oGU+3tZuZ+cw/3HDFg6YbzoQIeW2DOnUf5XuJ7iux34DPod/+MGmzgIxivEE9xjl0c1KAzhniA0yLRbKOTHqwSvydjC5h1sVJF3oN+5ob85saiYE3Q8sRqhS7rTwt82gIfHOaryH4957r8DmBlhsV2isx00McaLM13EKMx8uwUzrqM/hR4qcBuPqXIhgW2cA6rxFJWpEbwL+4ckXhJ4PVV47oF1gVTDp+ILGG8kHh4GXlygc5ugbm5pzRDHlnowHmju7zRa7A030aMZ6kGKcvrTepCqobx+yHwVs6PdqIJ+pW3OoXRQjTzcgoozHieEm8yBK4yMCfY+3c5fXYgltuRdzdm+93SmAc2b9kxcAD6IzcCExZYwJnsusLXFNk9gW//jdwgOf8LbZ+sD1FtsnMAAAAASUVORK5CYII=" style="width:14px;height:14px">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463941A2248W8KUJ">​</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_14134639422VI9RJ92I1">为方便开发者为接口调用者提供更详细的接口信息，框架提供两个API注解：</span></div><div id="k_14134639442BQET3IXF5" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:116px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:16px;color:rgb(222, 13, 79);background-color:rgb(255, 255, 255)" id="k_1413463945MX4BBRWQ3M">ApiDescript</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463947YKHCAY5G3M"> :&nbsp; API描述</span></div><div id="k_14134639488YHB48X6PK" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:116px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:16px;color:rgb(222, 13, 79);background-color:rgb(255, 255, 255)" id="k_1413463949ZYSN8XU3PV">ApiParams</span><span style="font-size:16px;color:rgb(102, 102, 102);background-color:rgb(255, 255, 255)" id="k_1413463951KKFXMME4OF">： API参数说明 JSON格式字符串</span></div><div id="k_1413463952D7F39KU7GN" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:116px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px;margin-bottom:12px"><span style="font-size:16px;color:rgb(92, 184, 92);background-color:rgb(255, 255, 255)" id="k_14134639558MYY83UAGY">列如</span><span style="font-size:16px;color:rgb(92, 184, 92);background-color:rgb(255, 255, 255)" id="k_1413463956UXV1B6ZAIU">：</span></div><div id="k_1413463958KEFO6U1IHU" class="_section_div_" style="line-height:25.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:116px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:14px;font-family:微软雅黑;color:#666666;background-color:#ffffff" id="k_1413463959TYS5V98MU1"><pre style="background-color:#F5F5F5;font-weight:normal;font-size:14px;color:#666666" id="k_1413463961W2G6IHSQRM"><div style="" id="k_1413463964SWEP74D1FC">​   <span style="color:rgb(255, 0, 5)" id="k_1413463965V4MI63S8WJ"> @ApiDescript(<span style="color:rgb(15, 224, 0)" id="k_1413463967BE6PUTT8TJ">"<span style="color:rgb(0, 137, 255)" id="k_1413463969GK22ETKXUM">分页查询</span>"</span>)</span>
    <span style="color:rgb(255, 0, 5)" id="k_14134639711CV4NYL75L">@ApiParams("<span style="color:rgb(0, 137, 255)" id="k_1413463973D17UESE6C5">{'page':1,'pageSize':15,'_col_sort_fieldName':'asc'}</span>")</span>
    @RequestMapping(value = "/list")
    public @ResponseBody AjaxResult list(HttpServletRequest request) throws Exception {</div><div style="" id="k_1413463975T8VIJ3HP1X">                      ............................................</div><div style="" id="k_1413463976DMHVTBOQ54"><span style="font-size:14px;color:rgb(102, 102, 102)" id="k_1413463977OHP16GZ44N">    }</span></div></pre></span></div><div id="k_14134639799TUHS5DNL8" class="_section_div_" style="line-height:25.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:0px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:14px;font-family:微软雅黑;color:#666666;background-color:#ffffff" id="k_1413463980L3VHOLNLQK">​</span></div><div id="k_1413463982NXHUQEGSY8" class="_section_div_" style="line-height:25.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:0px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:14px;font-family:微软雅黑;color:#666666;background-color:#ffffff" id="k_141346398485J7NVT2TV">​</span></div><div id="k_1413463985TTOR4AQJSK" class="_section_div_" style="line-height:35.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:0px;width:auto;text-align:left;background-color:rgb(131, 135, 247);padding-left:12px;margin-top:0px;margin-bottom:12px;text-indent:0px"><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247);font-weight:400;font-style:normal" id="k_1413463987F2KBJV85D4">​</span><span style="font-size:14px;color:rgb(102, 102, 102)" id="k_1413463988RSTFZBTBDI">​<img id="k_1413463990HRJHJQOJ77" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAARElEQVQoU2NkIBEwkqiegfH///9vGBgYhLFo/MbAwFDHyMjYiywH0vCJgYGBF4uGXwwMDJ2MjIx1w1EDaaFEcjzQXAMAXFwqDVPnP58AAAAASUVORK5CYII=" style="width:12px;height:12px">​</span><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247);font-weight:400;font-style:normal" id="k_1413463991817IRNASE7">​</span><span style="font-size:16px;color:rgb(255, 255, 255);background-color:rgb(131, 135, 247);font-weight:400;font-style:normal" id="k_1413463993KP1S24YSFG">&nbsp;调试图例：</span></div><div id="k_1413463994YDX4ZXK78W" class="_section_div_ clearfix" style="line-height:25.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:65px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0)"><span style="font-size:14px;font-family:微软雅黑;color:#666666;background-color:#ffffff" id="k_1413463996S4BVUMZ74I">​</span><span style="font-size:14px;font-family:微软雅黑;color:#666666;background-color:#ffffff" id="k_1413463997VHSSLGYTYB">​</span><img tabindex="0" src="/bui/tmp\e127caa05e6511e999cd3c970e751c70.png" style="text-align:left" id="k_14134639991OYXD1L7Q7"><span style="font-size:14px;font-family:微软雅黑;color:#666666;background-color:#ffffff" id="k_1413463901A99BXYBFPN">​</span></div><div id="k_1413463902KEJ5721E75" class="_section_div_" style="line-height:25.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:0px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0);padding-left:0px"><span style="font-size:14px;font-family:微软雅黑;color:#666666;background-color:#ffffff" id="k_14134639057B5RGEZT59">​</span></div><div id="k_1413463906Z8P2R9ZFZA" class="_section_div_" style="line-height:25.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:0px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0)"><span style="font-size:14px;font-family:微软雅黑;color:#666666;background-color:#ffffff" id="k_1413463908J58JG57W19">​</span></div><div id="k_1413463909O5GUNGIDRB" class="_section_div_" style="line-height:25.2px;font-size:14px;padding-top:0px;padding-bottom:0px;margin-left:0px;width:auto;text-align:left;background-color:rgba(0, 0, 0, 0)"><span style="font-size:14px;font-family:微软雅黑;color:#666666;background-color:#ffffff" id="k_1413463911X48L81DQMP">​</span></div>
</div>