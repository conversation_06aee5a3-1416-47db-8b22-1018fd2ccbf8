<div style="position:relative;">
<div id="k_0609200102UZZC18EWHY" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(185, 194, 247); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(20, 144, 252); border-image: initial; margin-bottom: 11px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(185, 194, 247);" id="k_0609200102GP1LAQVBHM">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_06092148671YP64QKNAF">​<img id="k_0609214867YOF1A1S2FJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA/UlEQVQoU43SMUqdURCG4eeTgDaCVcANWFikcgFBC1O5AQslYrDVLlY2CoJ2gdSp7BRBsJAkG9DWDaikFjtFRo78N1xFvZ72nHdmvndOvPNU1Sj2MJH3MB2wj884HAj1ATM4w/SbUN9IX/AP35IcD4J+YR532E6y0eKkqjYxhdUk572MVfUDSxjGSZLZ3l2DrjCOv1hIctkVWsMILrCY5E8/1Dq1B63ibzRLWxjDLXaTrPdbfsxUVb3Zh3CPD92j00dbyc1LUFtc69C09uRc43uSn893+d9eVU12G//UmuMoydxLy3+ivKq+YqdTvJLkYCDU5WtiPiZZfu2LPQBlB1SYkJG+5wAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(185, 194, 247);" id="k_060921486852LV66HG91">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(185, 194, 247);" id="k_06092012437YC8Y5N7UJ">​</span><span id="k_0609201242MEE72Z9C7H" style="color: rgb(255, 255, 255); font-size: 16px; font-style: normal; font-weight: 700; background-color: rgb(185, 194, 247);">工程说明：</span></div><div id="k_0609201244WP5SAXH9XC" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609201244SOIM1BVOCS">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_06092325737AWA3RRY9R">​<img id="k_06092325741UEPX9N124" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA7ElEQVQoU2NkIAMwkqGHAa+mBIf7HMw/GYP+M/wvYPrPGD/nhMJ1kCVYNcEUMzD8b2NgZJT/z8C4gOWnXNqss4y/MTQhTGboZWRkkAAp+M/A8IHp73/POScVT8C8ArYJrPgHY8x/RoZmmGKYAnRbwDYlWT5IZmBgaEFXDLOFgZHJdd5RuTPIAcaYbPUwluH/P2O4ICODAgMDoz9Y03+GuffY5TMOHGD8g6IJPcgTLR+5MzH+2wHyCzZbsIYekqbJ99jki9Btwadpyf8/LC7zTslcxBb5GPGUbPnQmYHhv8dddvlKbLbgjFxCSQsAxGlaDuSsQ5AAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609232574PHUOQ4V3BI">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06092202032GL31MGA5W">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_060927455862J3H69UHS">bui-ssm采用maven web</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_06092745591MVG5864OP">工程结构，其结构即说明如下图：</span></div><div id="k_0609220421HP2UE1NCTK" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 90px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609220421Z4BCWK3YTX">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_060922340738GHLS3A89">​</span><img tabindex="0" src="/bui/tmp\74c3b150580a11e9af593c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609223409PVB33B8I2X">​</span></div><div id="k_06092017104AJ9YHIIXG" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609201711YEMSRIRSJI">​</span></div><div id="k_0609201758CX2997654A" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(185, 194, 247); padding-left: 12px; border-top: none; border-right: none; border-bottom: none; border-left: 3px solid rgb(20, 144, 252); border-image: initial; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609201758P3P553WAVG">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609234735CENST891LX">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0609240445LISC3ZOBAE">​<img id="k_0609240445N2JXNYUR77" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA/UlEQVQoU43SMUqdURCG4eeTgDaCVcANWFikcgFBC1O5AQslYrDVLlY2CoJ2gdSp7BRBsJAkG9DWDaikFjtFRo78N1xFvZ72nHdmvndOvPNU1Sj2MJH3MB2wj884HAj1ATM4w/SbUN9IX/AP35IcD4J+YR532E6y0eKkqjYxhdUk572MVfUDSxjGSZLZ3l2DrjCOv1hIctkVWsMILrCY5E8/1Dq1B63ibzRLWxjDLXaTrPdbfsxUVb3Zh3CPD92j00dbyc1LUFtc69C09uRc43uSn893+d9eVU12G//UmuMoydxLy3+ivKq+YqdTvJLkYCDU5WtiPiZZfu2LPQBlB1SYkJG+5wAAAABJRU5ErkJggg==" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609240445BG3S8VFQOW">​</span><span id="k_0609234735P27YTYN43G" style="font-size: 16px; color: rgb(255, 255, 255); font-style: normal; background-color: rgb(185, 194, 247); font-weight: 700; text-decoration: none solid rgb(255, 255, 255); font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; vertical-align: baseline;">配置文件说明</span><span id="k_0609234735OFYSMG7ENO" style="font-size: 16px; color: rgb(255, 255, 255); font-style: normal; background-color: rgb(185, 194, 247); font-weight: 700; text-decoration: none solid rgb(255, 255, 255); font-family: &quot;Microsoft Yahei&quot;, Helvetica, Arial, sans-serif; vertical-align: baseline;">：</span></div><div id="k_0609201776PVY11RR73U" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 17px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06092017766YZX1TPRAU">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609243799I7BT7F4G55">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0609271260KTTE7S5WJ1">​<img id="k_0609271260ROSKW22XE2" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAtklEQVQoU83SMQrCUAwG4P9vQXAWB/UOgruggkcoL/QA3sDNC4guDq7OpdAeQHQS9AC6eQAXwROURAqO0qdOZk2+hJAQPwTTNJ2SXAAggHsQBOMois5VvUo0IbkGUDOzq6qO4ji++VBMcgOg/ucIwAXA0Dn3qNwpz/NWURRLkh0AW+fc3HcFJknSDsNwBaBpZicRmXlRlmU9Vd0BaJjZUUT6n6Cuqu5fkw4iMvAiX8G7fPk6X8cTnM1iDqKnefsAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609271260ENYDHTZEOJ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0609272695O44GYYO998">统</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_06092726964QOHM3NLHG">一的</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_06092639156AM2ISAYMR">配置文件根目录，默认class/configHome下，可通过jvm/系统环境变量【KEVIN_WEB_CONFIG_HOME】指定到外部任意目录。</span></div><div id="k_0609243997BAO6EID953" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609243997EO9Q1DLZWJ">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06092455859TG8MXEDBA">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0609280631UTYA3HIQ8V">​<img id="k_0609280631KMNCIBI1VB" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA7ElEQVQoU2NkIAMwkqGHAa+mBIf7HMw/GYP+M/wvYPrPGD/nhMJ1kCVYNcEUMzD8b2NgZJT/z8C4gOWnXNqss4y/MTQhTGboZWRkkAAp+M/A8IHp73/POScVT8C8ArYJrPgHY8x/RoZmmGKYAnRbwDYlWT5IZmBgaEFXDLOFgZHJdd5RuTPIAcaYbPUwluH/P2O4ICODAgMDoz9Y03+GuffY5TMOHGD8g6IJPcgTLR+5MzH+2wHyCzZbsIYekqbJ99jki9Btwadpyf8/LC7zTslcxBb5GPGUbPnQmYHhv8dddvlKbLbgjFxCSQsAxGlaDuSsQ5AAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609280632F5A8UE1CN7">​</span><span id="k_0609245585ZQPW9ZSSH9" style="color: rgb(24, 3, 255); font-size: 16px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); text-decoration: none solid rgb(24, 3, 255); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">Datagrid配置说明:</span></div><div id="k_06092457024GYRJGAPJP" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 52px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 700; background-color: rgb(255, 255, 255);" id="k_0609245702TB2K76XIDR">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609251595AR1S4MAMCE">​</span><span id="k_0609251594MOC2NDBCQM" style="color: rgb(2, 39, 173); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">特别说明，支持一个datagrid文件配置多个datagrid、支持下拉列表按钮配置方式，支持多行复杂表头配置</span></div><div id="k_06092457024GYRJGAPJP" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 52px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(2, 39, 173); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06092525538GLHRIX6ZJ">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0609263917ZNCJ1VIV3C">datagrid配置经过后端处</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0609263917LBB6IK1BB5">理后，形成前端CURD实例需要用到的json参数：</span></div><div id="k_0609252854WHGNLQCKIW" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 55px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609252854A61KD2NWDE">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609263399FSSMS3E738">​</span><img tabindex="0" src="/bui/tmp\03365e10580b11e9af593c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609263300OR16QXFU6X">​</span></div><div id="k_0609244177QBNAW3NCKW" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609244177EZDUJ6X1NI">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06092911486I848WVQMA">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0609293105VPWNVL5HWJ">​<img id="k_0609293105STZ1MKGXEO" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA7ElEQVQoU2NkIAMwkqGHAa+mBIf7HMw/GYP+M/wvYPrPGD/nhMJ1kCVYNcEUMzD8b2NgZJT/z8C4gOWnXNqss4y/MTQhTGboZWRkkAAp+M/A8IHp73/POScVT8C8ArYJrPgHY8x/RoZmmGKYAnRbwDYlWT5IZmBgaEFXDLOFgZHJdd5RuTPIAcaYbPUwluH/P2O4ICODAgMDoz9Y03+GuffY5TMOHGD8g6IJPcgTLR+5MzH+2wHyCzZbsIYekqbJ99jki9Btwadpyf8/LC7zTslcxBb5GPGUbPnQmYHhv8dddvlKbLbgjFxCSQsAxGlaDuSsQ5AAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06092931061A6RX6IB99">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_0609362109CXJVM12239">s</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(24, 3, 255); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-decoration: none solid rgb(24, 3, 255); vertical-align: baseline;" id="k_0609362109UWRZCHLVYJ">ql配置说明</span></div><div id="k_0609293718UF7FPZIGKA" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 40px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="color: rgb(24, 3, 255); font-size: 16px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); text-decoration: none solid rgb(24, 3, 255); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;" id="k_0609293718UPAJB75MG3">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_060929573663V41SIBGV">​</span><span id="k_0609295734A2VIA6MOGQ" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; background-color: rgb(255, 255, 255); font-weight: bold;">这里说明仅多表关联注意事项</span><span id="k_0609295734HQGFUB5529" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">（</span><span id="k_0609295735D6XXPVWNIG" style="font-size: 14px; color: rgb(252, 5, 63); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">多表关联，无需修改原表对应的实体bean</span><span id="k_0609295735ZWKCIJKDMF" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">）</span></div><div id="k_0609244124UKZYIYXRKW" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 39px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609244124R9YF6LZCKD">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06093026019X291QBQWD">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0609324322YYWPCY2D9T">通常情况下</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0609324323GTS9PFZQDI">，sql查询的字段对应</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0609324323C3ODYU98V9">一个实体bean字段，而多表关联后，原有的实体bean并不存在关联表的字段，这个场景下，你只需要修改sql配置文件，无需修改bean</span></div><div id="k_06092441583O5DQGA2XA" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 39px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0609324740G88MSVRRE5">修</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(92, 184, 92); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0609321395K4G778F311">改resultMap：</span></div><div id="k_0609325315VP6MP3T7TN" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 56px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0609325315KWHTOPBB3Q">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609333094QXLGWSVJ77">​</span><img tabindex="0" src="/bui/tmp\fba2b620580b11e9af593c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left; border: 1px solid rgb(250, 203, 203);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609333094TYZ6C5844P">​</span></div><div id="k_0609315048PDQIECJ3K6" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 56px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609315049HO3PSS9Q44">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609341004UPZE79FSHU">​</span><img tabindex="0" src="/bui/tmp\138c6290580c11e9af593c970e751c70.jpg" style="margin: 4px 2px; float: left; text-align: left; border: 1px solid rgb(250, 203, 203);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609341005U51IXY62VX">​</span></div><div id="k_06093151731HHBYK9QMY" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_06093151742ZHUYQWQMY">​</span></div><div id="k_0609315152TK5TBH6CB3" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609315152MND9CSSOS5">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609361550EX5QLA193X">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_06093643687KQ5KI2NIK">​<img id="k_0609364368B5SA2JC4FC" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA7ElEQVQoU2NkIAMwkqGHAa+mBIf7HMw/GYP+M/wvYPrPGD/nhMJ1kCVYNcEUMzD8b2NgZJT/z8C4gOWnXNqss4y/MTQhTGboZWRkkAAp+M/A8IHp73/POScVT8C8ArYJrPgHY8x/RoZmmGKYAnRbwDYlWT5IZmBgaEFXDLOFgZHJdd5RuTPIAcaYbPUwluH/P2O4ICODAgMDoz9Y03+GuffY5TMOHGD8g6IJPcgTLR+5MzH+2wHyCzZbsIYekqbJ99jki9Btwadpyf8/LC7zTslcxBb5GPGUbPnQmYHhv8dddvlKbLbgjFxCSQsAxGlaDuSsQ5AAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609364369TFINFO58DX">​</span><span id="k_0609361550X3KU37WP6E" style="color: rgb(24, 3, 255); font-size: 16px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); text-decoration: none solid rgb(24, 3, 255); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">特殊权限过滤排除配置【except-validation.xml 】</span></div><div id="k_0609244111AER6JTH45H" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 55px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_06092441118KN6J6KFD2">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609372168GNO6X8EPNO">​</span><img tabindex="0" src="/bui/tmp\85bbf3d0580c11e9af593c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left; border: 1px solid rgb(250, 203, 203);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06093721694B9M7AU3G9">​</span></div><div id="k_060936165831TMSZDO5F" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609361658L81EWLFVTT">​</span></div><div id="k_0609374538MR35MSGGBH" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 16px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 0px; margin-bottom: 0px; text-indent: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609374538W3I7ISGUH5">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609380505QV1C3DKIS8">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0609382291L2NK59VET3">​<img id="k_0609382291UZP1WF1F72" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA7ElEQVQoU2NkIAMwkqGHAa+mBIf7HMw/GYP+M/wvYPrPGD/nhMJ1kCVYNcEUMzD8b2NgZJT/z8C4gOWnXNqss4y/MTQhTGboZWRkkAAp+M/A8IHp73/POScVT8C8ArYJrPgHY8x/RoZmmGKYAnRbwDYlWT5IZmBgaEFXDLOFgZHJdd5RuTPIAcaYbPUwluH/P2O4ICODAgMDoz9Y03+GuffY5TMOHGD8g6IJPcgTLR+5MzH+2wHyCzZbsIYekqbJ99jki9Btwadpyf8/LC7zTslcxBb5GPGUbPnQmYHhv8dddvlKbLbgjFxCSQsAxGlaDuSsQ5AAAAAASUVORK5CYII=" style="width:13px;height:13px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609382292WE4V9NFBJ6">​</span><span id="k_0609380505R5D1BZZYAX" style="font-size: 16px; color: rgb(24, 3, 255); font-style: normal; background-color: rgb(255, 255, 255); font-weight: 400; text-decoration: none solid rgb(24, 3, 255); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">其</span><span id="k_0609380505IZWRRVJ12M" style="font-size: 16px; color: rgb(24, 3, 255); font-style: normal; background-color: rgb(255, 255, 255); font-weight: 400; text-decoration: none solid rgb(24, 3, 255); font-family: &quot;Microsoft Yahei&quot;; vertical-align: baseline;">他配置说明【common.properties】</span></div><div id="k_0609361689N7MJY9PMB8" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 55px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="color: rgb(102, 102, 102); font-size: 14px; font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);" id="k_0609361690FRD7VNTZJY">​</span>​<span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609385179VS4RJBHFHG">​</span><img tabindex="0" src="/bui/tmp\bb229b50580c11e9af593c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left; border: 1px solid rgb(250, 203, 203);"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0609385179JTFICV72DM">​</span></div><div id="k_0609201718CDZ9K5Q4GG" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_06092017189BV3IS7FJY">​</span></div>
</div>