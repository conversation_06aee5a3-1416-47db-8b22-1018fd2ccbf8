<div style="position:relative;">
<div id="k_0520365722HTKEM2CJ68" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 251, 255); padding-left: 12px; border-top: 1px dotted rgb(24, 3, 255); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-bottom: 11px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255);" id="k_0520380945MWK4T9KZBN">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none rgb(250, 251, 255);" id="k_05203809494RBDE7GAYC">​<img id="k_0520380950TRUTSX534C" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABWklEQVQ4T6WTwUoCURSG/98hssTRQhAiTaptz2CbNtEiMGlTkrjoAVrZql4hEFpUmkVQSxcR9Ai1bNPO0SgKKScNimnmxEiKqYyEd3v5/nPu+c4lBjgcgIUjfDkrw5Va3Zd49r70KuII54PisUb0A7FwlCz5rzsD+sKmW78AZB50pTaK6jlBaYZ0wQLhcUifFgVxCEKkLAEMC2DA4pZWUjM7oGUH/IFzoeqMKHJIMNpzkAIT4J5Q3U4W+dmCs+HqAokzEAEnAyLyCMu1nCz7bhpwdrI+B8W4IjnhrE5KLpOxRNl/22g7GxE3oOcIRAnuQ6yCYbq01IP6dhLEqD0wEosC3CumspIoe+9aA8tHalFLvuPmhz+dqrDWXtlW9QsHBLKaLI4V2++Zm6rGvjRfYRM0OltueHbraQVDmXXN89Tl+XRc1LVXvvd6q61tF2BTzb+WpN/eD/QxfgB0TIMQfHE0ugAAAABJRU5ErkJggg==" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(250, 251, 255);" id="k_0520380952GEG6SJSGW6">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(250, 251, 255);" id="k_0520380946VBO8LV8ZUD">后端CURD设计思想概要</span></div><div id="k_05203727517AWNYFFZKT" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520481148DL6IFLFACD">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05204811517PN7BS8KON">​<img id="k_0520481152WLOBMNCMEJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA1ElEQVQ4T82TvQ4BARCEZ7lwaChwWp1EIfEefhLiovQKIgqJV5AoNGpCKBVeQ0lCydHQ3EUiuRFKziEa2+58k53NruCHkh9Y/BHM6ili2edS0KNOZRg+usV6Gtuq7MoUGQBcAEo+NIpuXxk4ZjZ1owagS2KjeFFQh9rGyUDMyr4NYdahmQaQJLlSbCWnTqLLR42YurG+iVyyXQRoBkda5wl2glg0YpaPM4hkhGgExvGuQPgWvoN+zgGkQNRD40Tv44VZ+qFk0+4L2HIDb4Z/dGHfPMoVnm9DEJz5abEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520481153DCFAVP3F14">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520481149JT7ACTY3E6">采用mybatis外置sql配置方式，通过代码生成器解决sql-map编写的工作量问题。</span></div><div id="k_0520432368KCQL3GRXI1" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05204821332RAYGPRNM9">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0520482135XFKES3KKQ2">​<img id="k_0520482136WMO9QRVTVY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA1ElEQVQ4T82TvQ4BARCEZ7lwaChwWp1EIfEefhLiovQKIgqJV5AoNGpCKBVeQ0lCydHQ3EUiuRFKziEa2+58k53NruCHkh9Y/BHM6ili2edS0KNOZRg+usV6Gtuq7MoUGQBcAEo+NIpuXxk4ZjZ1owagS2KjeFFQh9rGyUDMyr4NYdahmQaQJLlSbCWnTqLLR42YurG+iVyyXQRoBkda5wl2glg0YpaPM4hkhGgExvGuQPgWvoN+zgGkQNRD40Tv44VZ+qFk0+4L2HIDb4Z/dGHfPMoVnm9DEJz5abEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520482137OQDV5EY48R">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520482134YU1LLSWJ8Y">采用通用封装思想、将dao、service、controller分别做基类封装，只需要继承这三个基类即可完成通用的curd功能。</span></div><div id="k_052043554092ILTNUFRQ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520482916TPNDZ1TV8Y">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05204829181QX86WOG5P">​<img id="k_0520482919DS1MZO3XPP" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA1ElEQVQ4T82TvQ4BARCEZ7lwaChwWp1EIfEefhLiovQKIgqJV5AoNGpCKBVeQ0lCydHQ3EUiuRFKziEa2+58k53NruCHkh9Y/BHM6ili2edS0KNOZRg+usV6Gtuq7MoUGQBcAEo+NIpuXxk4ZjZ1owagS2KjeFFQh9rGyUDMyr4NYdahmQaQJLlSbCWnTqLLR42YurG+iVyyXQRoBkda5wl2glg0YpaPM4hkhGgExvGuQPgWvoN+zgGkQNRD40Tv44VZ+qFk0+4L2HIDb4Z/dGHfPMoVnm9DEJz5abEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520482920JJ9PHAZAI7">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05204829175BLXYAIL6V">采用自定义的annotation实体Bean验证方案，将服务器验证简化为在实体Bean字段上声明验证注解。</span></div><div id="k_052043554092ILTNUFRQ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520483643Q9SB3OU91A">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0520483645TBFBNN357N">​<img id="k_052048364688M1TBS3VY" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA1ElEQVQ4T82TvQ4BARCEZ7lwaChwWp1EIfEefhLiovQKIgqJV5AoNGpCKBVeQ0lCydHQ3EUiuRFKziEa2+58k53NruCHkh9Y/BHM6ili2edS0KNOZRg+usV6Gtuq7MoUGQBcAEo+NIpuXxk4ZjZ1owagS2KjeFFQh9rGyUDMyr4NYdahmQaQJLlSbCWnTqLLR42YurG+iVyyXQRoBkda5wl2glg0YpaPM4hkhGgExvGuQPgWvoN+zgGkQNRD40Tv44VZ+qFk0+4L2HIDb4Z/dGHfPMoVnm9DEJz5abEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520483647B5R4WRO4Y6">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520483644BWJ5VKCCCY">采用spring&nbsp;mvc&nbsp;resetfull风格，将服务器返回数据统一为&nbsp;【{code:&nbsp;0/1&nbsp;,&nbsp;message:&nbsp;"信息"&nbsp;,&nbsp;data&nbsp;:&nbsp;{...}&nbsp;}】，便于对数据返回做统一的处理。</span></div><div id="k_052043554092ILTNUFRQ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520484259Z87PU2POBH">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05204842615L1P1IE6O5">​<img id="k_0520484262JBDSPAZ7SJ" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA1ElEQVQ4T82TvQ4BARCEZ7lwaChwWp1EIfEefhLiovQKIgqJV5AoNGpCKBVeQ0lCydHQ3EUiuRFKziEa2+58k53NruCHkh9Y/BHM6ili2edS0KNOZRg+usV6Gtuq7MoUGQBcAEo+NIpuXxk4ZjZ1owagS2KjeFFQh9rGyUDMyr4NYdahmQaQJLlSbCWnTqLLR42YurG+iVyyXQRoBkda5wl2glg0YpaPM4hkhGgExvGuQPgWvoN+zgGkQNRD40Tv44VZ+qFk0+4L2HIDb4Z/dGHfPMoVnm9DEJz5abEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520484263DVCKBCBEND">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520484260W1F8SVTYCD">保留服务器处理html页面请求的做法，统一由page接口（conctroller/xxx/page/[pageName]）负责页面请求及datagrid、toolbar参数加载。</span></div><div id="k_052043554092ILTNUFRQ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520485477GA5L7Q7JIM">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_0520485479UFJP8P9N4I">​<img id="k_0520485480BUR2QWBKNU" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA1ElEQVQ4T82TvQ4BARCEZ7lwaChwWp1EIfEefhLiovQKIgqJV5AoNGpCKBVeQ0lCydHQ3EUiuRFKziEa2+58k53NruCHkh9Y/BHM6ili2edS0KNOZRg+usV6Gtuq7MoUGQBcAEo+NIpuXxk4ZjZ1owagS2KjeFFQh9rGyUDMyr4NYdahmQaQJLlSbCWnTqLLR42YurG+iVyyXQRoBkda5wl2glg0YpaPM4hkhGgExvGuQPgWvoN+zgGkQNRD40Tv44VZ+qFk0+4L2HIDb4Z/dGHfPMoVnm9DEJz5abEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520485481LLCSX92XQY">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520485478ML7BEF871D">采用当下前后端分离开发主流下的thymeleaf视图引擎，直接支持html，便于前后端分离开发工作模式。</span></div><div id="k_052043554092ILTNUFRQ" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 29px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520491224QHBE6QHLCS">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05204912265K9CCUYI5Z">​<img id="k_0520491227ILCCQKWMW1" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAA1ElEQVQ4T82TvQ4BARCEZ7lwaChwWp1EIfEefhLiovQKIgqJV5AoNGpCKBVeQ0lCydHQ3EUiuRFKziEa2+58k53NruCHkh9Y/BHM6ili2edS0KNOZRg+usV6Gtuq7MoUGQBcAEo+NIpuXxk4ZjZ1owagS2KjeFFQh9rGyUDMyr4NYdahmQaQJLlSbCWnTqLLR42YurG+iVyyXQRoBkda5wl2glg0YpaPM4hkhGgExvGuQPgWvoN+zgGkQNRD40Tv44VZ+qFk0+4L2HIDb4Z/dGHfPMoVnm9DEJz5abEAAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520491228E8L1SGGVRY">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520491225EH2O9IOFSS">将前端datagrid列表、工具栏按钮（json参数）通过服务端controller控制</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05204634214BCET6E369">生成</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520463422LO2WYJOUN3">，进而达到服务端控制到按钮级别的ui权限需求。</span></div><div id="k_05203727155FPB9CIU8N" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520372716CQXB9GZB3I">​</span></div><div id="k_0520372757D31PDX7KUF" class="_section_div_" style="line-height: 35.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgb(250, 251, 255); padding-left: 12px; border-top: 1px dotted rgb(24, 3, 255); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-top: 0px; margin-bottom: 11px; text-indent: 0px;"><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(250, 251, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 118, 240); vertical-align: baseline;" id="k_0520505084KUR7X2VZL3">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background: none;" id="k_05205050887FD5TELDRP">​<img id="k_05205050897CUODSTK45" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABJUlEQVQ4T6WTMUuCURSGn/eTLEEaJM2gxuaWICJoq/wVToIENfQXwuZqamxocWqOlloi2mtqLtSPDKyGqHtPKCqZH36Ed7087znnfc8RYzyNwRIDW3Ia0i3UjCoSB6fyAfvec95At38FYuHZgENBwXm2Q7gAWU8kAjZlYH4iwSbGHKIgWMNomtitO6og3xYYgDPYQjKggihGzWjGm6BS8xyDPvtwDltVwInE0qgEDO5wlOrovgNnscUgwZlgJQa8kaNcQw/dtm0yH3CAWPfGqfNce3h+hRYw1TFMlA0uvWMnRI99w2aw5UTAFp6jOvoYrGypLpz5cuy9oKff/8phGw24An0Pt9yBS/JUaygcyjmLpUP0Hj2rtT1RL5p/LsnozR/rMH4A++hcEEVePd8AAAAASUVORK5CYII=" style="width:15px;height:15px;margin-left:1px ;margin-right:1px;">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(250, 251, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 118, 240); vertical-align: baseline;" id="k_0520505090KEPCXOYT1I">​</span><span style="font-size: 16px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(3, 118, 240); background-color: rgb(250, 251, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(3, 118, 240); vertical-align: baseline;" id="k_05205050853LDG8XPS3I">开发步骤</span></div><div id="k_0520372790V9S8YXII8O" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520372791PU3BNVDEC6">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05205131776YFOOLHBKA">​</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0520590619QEXLU343C6">1</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0520590620S7YHWUMG2Q">、第一步：创</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_05205136954Y6Q955E8Q">建</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0520513696X5R3HKCU9L">数据库表，建表</span><span id="k_0520513173NXVXXVOG37" style="font-size: 14px; color: rgb(255, 10, 59); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">请用上字段注释</span><span id="k_0520513174EITE1WQTGL" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">，以便代码生成器使用。</span><span id="k_0520513175QMFDEJUMJP" style="font-size: 14px; color: rgb(194, 2, 41); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">特别要求主键名称必须为"ID"，字段名称单词之间应该用下划线 _ 链接，如 user_name</span><span id="k_052051317649S2OY2CC3" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">。</span></div><div id="k_0520372824GYJKMY4SH2" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 55px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05203728254QXIJJTZ8T"><pre style="line-height:1.6em;background-color:#F5F5F5;border-radius: 3px;padding: 3px 5px;margin: 0 3px;font-weight:normal;font-size:14px;color:#666666;"><div style="background: none;">​<span style="background: none;">CREATE TABLE t_sys_base_dict (</span></div><pre style="padding: 3px 5px; margin-right: 3px; margin-left: 3px; line-height: 1.6em; background: none; border-radius: 3px;">	ID  varchar(32)  NOT NULL comment 'ID',
	TYPEID  varchar(32)  NOT NULL comment '类型ID' ,
	DKEY  varchar(32)  NOT NULL comment 'key',
	DVALUE  varchar(64)  NOT NULL comment 'value',
	DESP  varchar(256)  comment '描述',
	EXT_FILED  text  comment '扩展字段',
	PRIMARY KEY (ID)
)DEFAULT CHARSET=utf8; </pre></pre></span></div><div id="k_0520372865GSIA7VFQX6" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0520572157QWSJCLVBFO">2、</span><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_0520572158QCRU8YIOLD"></span><span id="k_0520534029SEISFE6EVW" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">第二步：启动代码生成器，链接上数据库，并填写好包名、生成表，点击生成</span><span id="k_0520534031Y1UKE2T7GH" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">代码</span><span id="k_0520534032F2J13CUTCM" style="font-size: 14px; color: rgb(102, 102, 102); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255);">。</span></div><div id="k_0520534310M9Q5Q8R9HO" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 57px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-style: normal; font-weight: 400;" id="k_05205343113YADZ32PCL">​​​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520550222OGHKEA6F6D">​</span><img tabindex="0" src="/bui/tmp\06debd5057a211e9bd913c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520550224WAABT9FHER">​</span></div><div id="k_0520372955X94L2TXZQC" class="_section_div_" style="line-height: 30.8px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px; margin-top: 12px; margin-bottom: 0px; text-indent: 0px;"><span style="font-size: 14px; font-family: &quot;Microsoft Yahei&quot;; color: rgb(102, 102, 102); background-color: rgb(255, 255, 255); font-weight: 400; font-style: normal; text-decoration: none solid rgb(102, 102, 102); vertical-align: baseline;" id="k_0520372956KWC8BU8LGB">3、将生成的代码拷贝至工程中</span></div><div id="k_0520372998ZIYQLIEUN9" class="_section_div_ clearfix" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 57px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;">​</span><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05205834995POSTC2CR1">​</span><img tabindex="0" src="/bui/tmp\8570791057a211e9bd913c970e751c70.png" style="margin: 4px 2px; float: left; text-align: left;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_05205834001Y5K2RT6TC">​</span></div><div id="k_05205447149CY1HOL4LK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 28px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_0520544715TUAP2D8OAW">​</span></div><div id="k_0520372916S6WY3DOPFK" class="_section_div_" style="line-height: 25.2px; font-size: 14px; padding-top: 0px; padding-bottom: 0px;  margin-left: 0px; width: auto; text-align: left; background-color: rgba(0, 0, 0, 0); padding-left: 0px;"><span style="font-size:14px;font-family:Microsoft Yahei;color:#666666;background-color:#ffffff;" id="k_052037291789X7ITINI7">​</span></div>
</div>