!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),d=function(e){return!1!==e.settings.image_dimensions},a=function(e){return!0===e.settings.image_advtab},f=function(e){return e.getParam("image_prepend_url","")},n=function(e){return e.getParam("image_class_list")},r=function(e){return!1!==e.settings.image_description},i=function(e){return!0===e.settings.image_title},o=function(e){return!0===e.settings.image_caption},l=function(e){return e.getParam("image_list",!1)},u=function(e){return e.getParam("images_upload_url",!1)},c=function(e){return e.getParam("images_upload_handler",!1)},s=function(e){return e.getParam("images_upload_url")},g=function(e){return e.getParam("images_upload_handler")},m=function(e){return e.getParam("images_upload_base_path")},p=function(e){return e.getParam("images_upload_credentials")},h="undefined"!=typeof window?window:Function("return this;")(),v=function(e,t){for(var n=t!==undefined&&null!==t?t:h,r=0;r<e.length&&n!==undefined&&null!==n;++r)n=n[e[r]];return n},b=function(e,t){var n=e.split(".");return v(n,t)},t={getOrDie:function(e,t){var n=b(e,t);if(n===undefined||null===n)throw e+" not available on this browser";return n}};function y(){return new(t.getOrDie("FileReader"))}var x=tinymce.util.Tools.resolve("tinymce.util.Promise"),w=tinymce.util.Tools.resolve("tinymce.util.Tools"),S=tinymce.util.Tools.resolve("tinymce.util.XHR"),N=function(e,t){return Math.max(parseInt(e,10),parseInt(t,10))},C=function(e,n){var r=document.createElement("img");function t(e,t){r.parentNode&&r.parentNode.removeChild(r),n({width:e,height:t})}r.onload=function(){t(N(r.width,r.clientWidth),N(r.height,r.clientHeight))},r.onerror=function(){t(0,0)};var i=r.style;i.visibility="hidden",i.position="fixed",i.bottom=i.left="0px",i.width=i.height="auto",document.body.appendChild(r),r.src=e},A=function(e,i,t){return function n(e,r){return r=r||[],w.each(e,function(e){var t={text:e.text||e.title};e.menu?t.menu=n(e.menu):(t.value=e.value,i(t)),r.push(t)}),r}(e,t||[])},_=function(e){return e&&(e=e.replace(/px$/,"")),e},T=function(e){return 0<e.length&&/^[0-9]+$/.test(e)&&(e+="px"),e},O=function(e){if(e.margin){var t=e.margin.split(" ");switch(t.length){case 1:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[0],e["margin-bottom"]=e["margin-bottom"]||t[0],e["margin-left"]=e["margin-left"]||t[0];break;case 2:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[1],e["margin-bottom"]=e["margin-bottom"]||t[0],e["margin-left"]=e["margin-left"]||t[1];break;case 3:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[1],e["margin-bottom"]=e["margin-bottom"]||t[2],e["margin-left"]=e["margin-left"]||t[1];break;case 4:e["margin-top"]=e["margin-top"]||t[0],e["margin-right"]=e["margin-right"]||t[1],e["margin-bottom"]=e["margin-bottom"]||t[2],e["margin-left"]=e["margin-left"]||t[3]}delete e.margin}return e},R=function(e,t){var n=l(e);"string"==typeof n?S.send({url:n,success:function(e){t(JSON.parse(e))}}):"function"==typeof n?n(t):t(n)},P=function(e,t,n){function r(){n.onload=n.onerror=null,e.selection&&(e.selection.select(n),e.nodeChanged())}n.onload=function(){t.width||t.height||!d(e)||e.dom.setAttribs(n,{width:n.clientWidth,height:n.clientHeight}),r()},n.onerror=r},I=function(r){return new x(function(e,t){var n=new y;n.onload=function(){e(n.result)},n.onerror=function(){t(y.error.message)},n.readAsDataURL(r)})},L=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),U=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===t}},E={isString:U("string"),isObject:U("object"),isArray:U("array"),isNull:U("null"),isBoolean:U("boolean"),isUndefined:U("undefined"),isFunction:U("function"),isNumber:U("number")},M=function(a){return function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var n={},r=0;r<e.length;r++){var i=e[r];for(var o in i)i.hasOwnProperty(o)&&(n[o]=a(n[o],i[o]))}return n}},j=M(function(e,t){return E.isObject(e)&&E.isObject(t)?j(e,t):t}),k=M(function(e,t){return t}),D={deepMerge:j,merge:k},z=L.DOM,B=function(e){return e.style.marginLeft&&e.style.marginRight&&e.style.marginLeft===e.style.marginRight?_(e.style.marginLeft):""},H=function(e){return e.style.marginTop&&e.style.marginBottom&&e.style.marginTop===e.style.marginBottom?_(e.style.marginTop):""},F=function(e){return e.style.borderWidth?_(e.style.borderWidth):""},W=function(e,t){return e.hasAttribute(t)?e.getAttribute(t):""},J=function(e,t){return e.style[t]?e.style[t]:""},V=function(e){return null!==e.parentNode&&"FIGURE"===e.parentNode.nodeName},G=function(e,t,n){e.setAttribute(t,n)},$=function(e){var t,n,r,i;V(e)?(i=(r=e).parentNode,z.insertAfter(r,i),z.remove(i)):(t=e,n=z.create("figure",{"class":"image"}),z.insertAfter(n,t),n.appendChild(t),n.appendChild(z.create("figcaption",{contentEditable:!0},"Caption")),n.contentEditable="false")},X=function(e,t){var n=e.getAttribute("style"),r=t(null!==n?n:"");0<r.length?(e.setAttribute("style",r),e.setAttribute("data-mce-style",r)):e.removeAttribute("style")},q=function(e,r){return function(e,t,n){e.style[t]?(e.style[t]=T(n),X(e,r)):G(e,t,n)}},K=function(e,t){return e.style[t]?_(e.style[t]):W(e,t)},Q=function(e,t){var n=T(t);e.style.marginLeft=n,e.style.marginRight=n},Y=function(e,t){var n=T(t);e.style.marginTop=n,e.style.marginBottom=n},Z=function(e,t){var n=T(t);e.style.borderWidth=n},ee=function(e,t){e.style.borderStyle=t},te=function(e){return"FIGURE"===e.nodeName},ne=function(e,t){var n=document.createElement("img");return G(n,"style",t.style),(B(n)||""!==t.hspace)&&Q(n,t.hspace),(H(n)||""!==t.vspace)&&Y(n,t.vspace),(F(n)||""!==t.border)&&Z(n,t.border),(J(n,"borderStyle")||""!==t.borderStyle)&&ee(n,t.borderStyle),e(n.getAttribute("style"))},re=function(e,t){return{src:W(t,"src"),alt:W(t,"alt"),title:W(t,"title"),width:K(t,"width"),height:K(t,"height"),"class":W(t,"class"),style:e(W(t,"style")),caption:V(t),hspace:B(t),vspace:H(t),border:F(t),borderStyle:J(t,"borderStyle")}},ie=function(e,t,n,r,i){n[r]!==t[r]&&i(e,r,n[r])},oe=function(r,i){return function(e,t,n){r(e,n),X(e,i)}},ae=function(e,t,n){var r=re(e,n);ie(n,r,t,"caption",function(e,t,n){return $(e)}),ie(n,r,t,"src",G),ie(n,r,t,"alt",G),ie(n,r,t,"title",G),ie(n,r,t,"width",q(0,e)),ie(n,r,t,"height",q(0,e)),ie(n,r,t,"class",G),ie(n,r,t,"style",oe(function(e,t){return G(e,"style",t)},e)),ie(n,r,t,"hspace",oe(Q,e)),ie(n,r,t,"vspace",oe(Y,e)),ie(n,r,t,"border",oe(Z,e)),ie(n,r,t,"borderStyle",oe(ee,e))},le=function(e,t){var n=e.dom.styles.parse(t),r=O(n),i=e.dom.styles.parse(e.dom.styles.serialize(r));return e.dom.styles.serialize(i)},ue=function(e){var t=e.selection.getNode(),n=e.dom.getParent(t,"figure.image");return n?e.dom.select("img",n)[0]:t&&("IMG"!==t.nodeName||t.getAttribute("data-mce-object")||t.getAttribute("data-mce-placeholder"))?null:t},ce=function(t,e){var n=t.dom,r=n.getParent(e.parentNode,function(e){return t.schema.getTextBlockElements()[e.nodeName]});return r?n.split(r,e):e},se=function(t){var e=ue(t);return e?re(function(e){return le(t,e)},e):{src:"",alt:"",title:"",width:"",height:"","class":"",style:"",caption:!1,hspace:"",vspace:"",border:"",borderStyle:""}},de=function(t,e){var n=function(e,t){var n=document.createElement("img");if(ae(e,D.merge(t,{caption:!1}),n),G(n,"alt",t.alt),t.caption){var r=z.create("figure",{"class":"image"});return r.appendChild(n),r.appendChild(z.create("figcaption",{contentEditable:!0},"Caption")),r.contentEditable="false",r}return n}(function(e){return le(t,e)},e);t.dom.setAttrib(n,"data-mce-id","__mcenew"),t.focus(),t.selection.setContent(n.outerHTML);var r=t.dom.select('*[data-mce-id="__mcenew"]')[0];if(t.dom.setAttrib(r,"data-mce-id",null),te(r)){var i=ce(t,r);t.selection.select(i)}else t.selection.select(r)},fe=function(e,t){var n=ue(e);n?t.src?function(t,e){var n,r=ue(t);if(ae(function(e){return le(t,e)},e,r),n=r,t.dom.setAttrib(n,"src",n.getAttribute("src")),te(r.parentNode)){var i=r.parentNode;ce(t,i),t.selection.select(r.parentNode)}else t.selection.select(r),P(t,e,r)}(e,t):function(e,t){if(t){var n=e.dom.is(t.parentNode,"figure.image")?t.parentNode:t;e.dom.remove(n),e.focus(),e.nodeChanged(),e.dom.isEmpty(e.getBody())&&(e.setContent(""),e.selection.setCursorLocation())}}(e,n):t.src&&de(e,t)},ge=function(n,r){r.find("#style").each(function(e){var t=ne(function(e){return le(n,e)},D.merge({src:"",alt:"",title:"",width:"",height:"","class":"",style:"",caption:!1,hspace:"",vspace:"",border:"",borderStyle:""},r.toJSON()));e.value(t)})},me=function(t){return{title:"Advanced",type:"form",pack:"start",items:[{label:"Style",name:"style",type:"textbox",onchange:(o=t,function(e){var t=o.dom,n=e.control.rootControl;if(a(o)){var r=n.toJSON(),i=t.parseStyle(r.style);n.find("#vspace").value(""),n.find("#hspace").value(""),((i=O(i))["margin-top"]&&i["margin-bottom"]||i["margin-right"]&&i["margin-left"])&&(i["margin-top"]===i["margin-bottom"]?n.find("#vspace").value(_(i["margin-top"])):n.find("#vspace").value(""),i["margin-right"]===i["margin-left"]?n.find("#hspace").value(_(i["margin-right"])):n.find("#hspace").value("")),i["border-width"]?n.find("#border").value(_(i["border-width"])):n.find("#border").value(""),i["border-style"]?n.find("#borderStyle").value(i["border-style"]):n.find("#borderStyle").value(""),n.find("#style").value(t.serializeStyle(t.parseStyle(t.serializeStyle(i))))}})},{type:"form",layout:"grid",packV:"start",columns:2,padding:0,defaults:{type:"textbox",maxWidth:50,onchange:function(e){ge(t,e.control.rootControl)}},items:[{label:"Vertical space",name:"vspace"},{label:"Border width",name:"border"},{label:"Horizontal space",name:"hspace"},{label:"Border style",type:"listbox",name:"borderStyle",width:90,maxWidth:90,onselect:function(e){ge(t,e.control.rootControl)},values:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]}]}]};var o},pe=function(e,t){e.state.set("oldVal",e.value()),t.state.set("oldVal",t.value())},he=function(e,t){var n=e.find("#width")[0],r=e.find("#height")[0],i=e.find("#constrain")[0];n&&r&&i&&t(n,r,i.checked())},ve=function(e,t,n){var r=e.state.get("oldVal"),i=t.state.get("oldVal"),o=e.value(),a=t.value();n&&r&&i&&o&&a&&(o!==r?(a=Math.round(o/r*a),isNaN(a)||t.value(a)):(o=Math.round(a/i*o),isNaN(o)||e.value(o))),pe(e,t)},be=function(e){he(e,ve)},ye=function(){var e=function(e){be(e.control.rootControl)};return{type:"container",label:"Dimensions",layout:"flex",align:"center",spacing:5,items:[{name:"width",type:"textbox",maxLength:5,size:5,onchange:e,ariaLabel:"Width"},{type:"label",text:"x"},{name:"height",type:"textbox",maxLength:5,size:5,onchange:e,ariaLabel:"Height"},{name:"constrain",type:"checkbox",checked:!0,text:"Constrain proportions"}]}},xe=function(e){he(e,pe)},we=be,Se=function(e){e.meta=e.control.rootControl.toJSON()},Ne=function(s,e){var t=[{name:"src",type:"filepicker",filetype:"image",label:"Source",autofocus:!0,onchange:function(e){var t,n,r,i,o,a,l,u,c;n=s,a=(t=e).meta||{},l=t.control,u=l.rootControl,(c=u.find("#image-list")[0])&&c.value(n.convertURL(l.value(),"src")),w.each(a,function(e,t){u.find("#"+t).value(e)}),a.width||a.height||(r=n.convertURL(l.value(),"src"),i=f(n),o=new RegExp("^(?:[a-z]+:)?//","i"),i&&!o.test(r)&&r.substring(0,i.length)!==i&&(r=i+r),l.value(r),C(n.documentBaseURI.toAbsolute(l.value()),function(e){e.width&&e.height&&d(n)&&(u.find("#width").value(e.width),u.find("#height").value(e.height),xe(u))}))},onbeforecall:Se},e];return r(s)&&t.push({name:"alt",type:"textbox",label:"Image description"}),i(s)&&t.push({name:"title",type:"textbox",label:"Image Title"}),d(s)&&t.push(ye()),n(s)&&t.push({name:"class",type:"listbox",label:"Class",values:A(n(s),function(e){e.value&&(e.textStyle=function(){return s.formatter.getCssText({inline:"img",classes:[e.value]})})})}),o(s)&&t.push({name:"caption",type:"checkbox",label:"Caption"}),t},Ce=function(e,t){return{title:"General",type:"form",items:Ne(e,t)}},Ae=Ne,_e=function(){return t.getOrDie("URL")},Te=function(e){return _e().createObjectURL(e)},Oe=function(e){_e().revokeObjectURL(e)},Re=tinymce.util.Tools.resolve("tinymce.ui.Factory");function Pe(){return new(t.getOrDie("XMLHttpRequest"))}var Ie=function(){};function Le(a){var t=function(e,r,i,t){var o,n;(o=new Pe).open("POST",a.url),o.withCredentials=a.credentials,o.upload.onprogress=function(e){t(e.loaded/e.total*100)},o.onerror=function(){i("Image upload failed due to a XHR Transport error. Code: "+o.status)},o.onload=function(){var e,t,n;o.status<200||300<=o.status?i("HTTP Error: "+o.status):(e=JSON.parse(o.responseText))&&"string"==typeof e.location?r((t=a.basePath,n=e.location,t?t.replace(/\/$/,"")+"/"+n.replace(/^\//,""):n)):i("Invalid JSON: "+o.responseText)},(n=new FormData).append("file",e.blob(),e.filename()),o.send(n)};return a=w.extend({credentials:!1,handler:t},a),{upload:function(e){return a.url||a.handler!==t?(r=e,i=a.handler,new x(function(e,t){try{i(r,e,t,Ie)}catch(n){t(n.message)}})):x.reject("Upload url missing from the settings.");var r,i}}}var Ue=function(u){return function(e){var t=Re.get("Throbber"),n=e.control.rootControl,r=new t(n.getEl()),i=e.control.value(),o=Te(i),a=Le({url:s(u),basePath:m(u),credentials:p(u),handler:g(u)}),l=function(){r.hide(),Oe(o)};return r.show(),I(i).then(function(e){var t=u.editorUpload.blobCache.create({blob:i,blobUri:o,name:i.name?i.name.replace(/\.[^\.]+$/,""):null,base64:e.split(",")[1]});return a.upload(t).then(function(e){var t=n.find("#src");return t.value(e),n.find("tabpanel")[0].activateTab(0),t.fire("change"),l(),e})})["catch"](function(e){u.windowManager.alert(e),l()})}},Ee=".jpg,.jpeg,.png,.gif",Me=function(e){return{title:"Upload",type:"form",layout:"flex",direction:"column",align:"stretch",padding:"20 20 20 20",items:[{type:"container",layout:"flex",direction:"column",align:"center",spacing:10,items:[{text:"Browse for an image",type:"browsebutton",accept:Ee,onchange:Ue(e)},{text:"OR",type:"label"}]},{text:"Drop an image here",type:"dropzone",accept:Ee,height:100,onchange:Ue(e)}]}},je=function(e){return function(){return e}},ke=(je(!1),je(!0),function(o){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];for(var a=new Array(arguments.length-1),n=1;n<arguments.length;n++)a[n-1]=arguments[n];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];var i=a.concat(n);return o.apply(null,i)}}),De=function(t,e){var n=e.control.getRoot();we(n),t.undoManager.transact(function(){var e=D.merge(se(t),n.toJSON());fe(t,e)}),t.editorUpload.uploadImagesAuto()};function ze(o){function e(e){var n,t,r=se(o);if(e&&(t={type:"listbox",label:"Image list",name:"image-list",values:A(e,function(e){e.value=o.convertURL(e.value||e.url,"src")},[{text:"None",value:""}]),value:r.src&&o.convertURL(r.src,"src"),onselect:function(e){var t=n.find("#alt");(!t.value()||e.lastControl&&t.value()===e.lastControl.text())&&t.value(e.control.text()),n.find("#src").value(e.control.value()).fire("change")},onPostRender:function(){t=this}}),a(o)||u(o)||c(o)){var i=[Ce(o,t)];a(o)&&i.push(me(o)),(u(o)||c(o))&&i.push(Me(o)),n=o.windowManager.open({title:"Insert/edit image",data:r,bodyType:"tabpanel",body:i,onSubmit:ke(De,o)})}else n=o.windowManager.open({title:"Insert/edit image",data:r,body:Ae(o,t),onSubmit:ke(De,o)});xe(n)}return{open:function(){R(o,e)}}}var Be=function(e){e.addCommand("mceImage",ze(e).open)},He=function(o){return function(e){for(var t,n,r=e.length,i=function(e){e.attr("contenteditable",o?"true":null)};r--;)t=e[r],(n=t.attr("class"))&&/\bimage\b/.test(n)&&(t.attr("contenteditable",o?"false":null),w.each(t.getAll("figcaption"),i))}},Fe=function(e){e.on("preInit",function(){e.parser.addNodeFilter("figure",He(!0)),e.serializer.addNodeFilter("figure",He(!1))})},We=function(e){e.addButton("image",{icon:"image",tooltip:"Insert/edit image",onclick:ze(e).open,stateSelector:"img:not([data-mce-object],[data-mce-placeholder]),figure.image"}),e.addMenuItem("image",{icon:"image",text:"Image",onclick:ze(e).open,context:"insert",prependToContext:!0})};e.add("image",function(e){Fe(e),We(e),Be(e)})}();