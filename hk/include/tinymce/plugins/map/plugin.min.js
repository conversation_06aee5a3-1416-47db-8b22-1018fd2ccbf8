tinymce.PluginManager.add("map",
function(a, b) {
    var douPlugin = b;
    a.addButton("map", {
        tooltip: "百度地图",
        image: b + '/images/map.png',
        onclick: function() {
            a.windowManager.open({
                title: "百度地图",
                url: b + "/dialog.html",
                width: 600,
                height: 440,
                buttons: [{
                    text: "Insert",
                    onclick: function() {
                        var b = a.windowManager.getWindows()[0];
                        var txtlatitude = b.getContentWindow().document.getElementById("txtlatitude").value; // 维度
                        var txtLongitude = b.getContentWindow().document.getElementById("txtLongitude").value; // 经度
                        var txtZoom = b.getContentWindow().document.getElementById("txtZoom").value; // 缩放
                        if(b.getContentWindow().document.getElementById("txtDynamic").checked) {
                            a.insertContent("<iframe class=\"ueditor_baidumap\" src=\"" + douPlugin + "/show.html#center=" + txtLongitude + "," + txtlatitude + "&zoom=" + txtZoom +"&width=600%&height=400&markers=" + txtLongitude + "," + txtlatitude + "&title=豆壳网络&markerStyles=l,A\" frameborder=\"0\" width=\"600%\" height=\"400\"></iframe>"),
                            b.close()
                        } else {
                            a.insertContent("<img width=\"600\" height=\"400\" src=\"http://api.map.baidu.com/staticimage?center=" + txtLongitude + "," + txtlatitude + "&zoom=" + txtZoom +"&width=600&height=400&markers=" + txtLongitude + "," + txtlatitude + "&title=豆壳网络\">"),
                            b.close()
                        }
                    }
                },
                {
                    text: "Close",
                    onclick: "close"
                }]
            })
        }
    }),
    a.addMenuItem("map", {
        text: "map plugin",
        context: "tools",
        onclick: function() {
            a.windowManager.open({
                title: "TinyMCE site",
                url: b + "/dialog.html",
                width: 600,
                height: 400,
                buttons: [{
                    text: "Insert",
                    onclick: function() {
                        var b = a.windowManager.getWindows()[0];
                        var txtlatitude = b.getContentWindow().document.getElementById("txtlatitude").value; // 维度
                        var txtLongitude = b.getContentWindow().document.getElementById("txtLongitude").value; // 经度
                        var txtZoom = b.getContentWindow().document.getElementById("txtZoom").value; // 缩放
                        if(b.getContentWindow().document.getElementById("txtDynamic").checked) {
                            a.insertContent("<iframe class=\"ueditor_baidumap\" src=\"" + douPlugin + "/show.html#center=" + txtLongitude + "," + txtlatitude + "&zoom=" + txtZoom +"&width=100%&height=400&markers=" + txtLongitude + "," + txtlatitude + "&title=豆壳网络&markerStyles=l,A\" frameborder=\"0\" width=\"100%\" height=\"400\"></iframe>"),
                            b.close()
                        } else {
                            a.insertContent("<img width=\"600\" height=\"400\" src=\"http://api.map.baidu.com/staticimage?center=" + txtLongitude + "," + txtlatitude + "&zoom=" + txtZoom +"&width=600&height=400&markers=" + txtLongitude + "," + txtlatitude + "&title=豆壳网络\">"),
                            b.close()
                        }
                    }
                },
                {
                    text: "Close",
                    onclick: "close"
                }]
            })
        }
    })
});