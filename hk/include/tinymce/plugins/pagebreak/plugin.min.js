!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),a=tinymce.util.Tools.resolve("tinymce.Env"),n=function(e){return e.getParam("pagebreak_separator","\x3c!-- pagebreak --\x3e")},i=function(e){return e.getParam("pagebreak_split_block",!1)},t=function(){return"mce-pagebreak"},r=function(){return'<img src="'+a.transparentSrc+'" class="mce-pagebreak" data-mce-resize="false" data-mce-placeholder />'},c=function(c){var o=n(c),a=new RegExp(o.replace(/[\?\.\*\[\]\(\)\{\}\+\^\$\:]/g,function(e){return"\\"+e}),"gi");c.on("BeforeSetContent",function(e){e.content=e.content.replace(a,r())}),c.on("PreInit",function(){c.serializer.addNodeFilter("img",function(e){for(var a,n,t=e.length;t--;)if((n=(a=e[t]).attr("class"))&&-1!==n.indexOf("mce-pagebreak")){var r=a.parent;if(c.schema.getBlockElements()[r.name]&&i(c)){r.type=3,r.value=o,r.raw=!0,a.remove();continue}a.type=3,a.value=o,a.raw=!0}})})},o=r,g=t,u=function(e){e.addCommand("mcePageBreak",function(){e.settings.pagebreak_split_block?e.insertContent("<p>"+o()+"</p>"):e.insertContent(o())})},m=function(a){a.on("ResolveName",function(e){"IMG"===e.target.nodeName&&a.dom.hasClass(e.target,g())&&(e.name="pagebreak")})},s=function(e){e.addButton("pagebreak",{title:"Page break",cmd:"mcePageBreak"}),e.addMenuItem("pagebreak",{text:"Page break",icon:"pagebreak",cmd:"mcePageBreak",context:"insert"})};e.add("pagebreak",function(e){u(e),s(e),c(e),m(e)})}();