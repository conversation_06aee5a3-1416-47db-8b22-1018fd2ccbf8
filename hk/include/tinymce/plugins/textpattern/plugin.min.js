!function(){"use strict";var r=function(t){var e=t,n=function(){return e};return{get:n,set:function(t){e=t},clone:function(){return r(n())}}},t=tinymce.util.Tools.resolve("tinymce.PluginManager"),n=function(e){return{setPatterns:function(t){e.set(t)},getPatterns:function(){return e.get()}}},e=[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"***",end:"***",format:["bold","italic"]},{start:"#",format:"h1"},{start:"##",format:"h2"},{start:"###",format:"h3"},{start:"####",format:"h4"},{start:"#####",format:"h5"},{start:"######",format:"h6"},{start:"1. ",cmd:"InsertOrderedList"},{start:"* ",cmd:"InsertUnorderedList"},{start:"- ",cmd:"InsertUnorderedList"}],a=function(t){return t.textpattern_patterns!==undefined?t.textpattern_patterns:e},o=tinymce.util.Tools.resolve("tinymce.util.Delay"),i=tinymce.util.Tools.resolve("tinymce.util.VK"),g=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),h=tinymce.util.Tools.resolve("tinymce.util.Tools"),m=function(t,e){for(var n=0;n<t.length;n++)if(0===e.indexOf(t[n].start)&&(!t[n].end||e.lastIndexOf(t[n].end)===e.length-t[n].end.length))return t[n]},c=function(t,e,n,r){var a,o,i,s,d,f,l=t.sort(function(t,e){return t.start.length>e.start.length?-1:t.start.length<e.start.length?1:0});for(o=0;o<l.length;o++)if((a=l[o]).end!==undefined&&(s=a,d=n,f=r,e.substr(d-s.end.length-f,s.end.length)===s.end)&&0<n-r-(i=a).end.length-i.start.length)return a},s=function(t,e,n){if(!1!==e.collapsed){var r=e.startContainer,a=r.data,o=!0===n?1:0;if(3===r.nodeType){var i=c(t,a,e.startOffset,o);if(i!==undefined){var s=a.lastIndexOf(i.end,e.startOffset-o),d=a.lastIndexOf(i.start,s-i.end.length);if(s=a.indexOf(i.end,d+i.start.length),-1!==d){var f=document.createRange();f.setStart(r,d),f.setEnd(r,s+i.end.length);var l=m(t,f.toString());if(!(i===undefined||l!==i||r.data.length<=i.start.length+i.end.length))return{pattern:i,startOffset:d,endOffset:s}}}}}},d=function(t,e,n){var r=t.selection.getRng(!0),a=s(e,r,n);if(a)return function(a,o,i,t){var s=h.isArray(i.pattern.format)?i.pattern.format:[i.pattern.format];if(0!==h.grep(s,function(t){var e=a.formatter.get(t);return e&&e[0].inline}).length)return a.undoManager.transact(function(){var t,e,n,r;t=o,e=i.pattern,n=i.endOffset,r=i.startOffset,(t=0<r?t.splitText(r):t).splitText(n-r+e.end.length),t.deleteData(0,e.start.length),t.deleteData(t.data.length-e.end.length,e.end.length),o=t,s.forEach(function(t){a.formatter.apply(t,{},o)})}),o}(t,r.startContainer,a)},f=function(t,e){return d(t,e,!0)},l=function(t,e){return d(t,e,!1)},u=function(t,e){var n,r,a,o,i,s,d,f,l,c,u;if(n=t.selection,r=t.dom,n.isCollapsed()&&(d=r.getParent(n.getStart(),"p"))){for(l=new g(d,d);i=l.next();)if(3===i.nodeType){o=i;break}if(o){if(!(f=m(e,o.data)))return;if(a=(c=n.getRng(!0)).startContainer,u=c.startOffset,o===a&&(u=Math.max(0,u-f.start.length)),h.trim(o.data).length===f.start.length)return;f.format&&(s=t.formatter.get(f.format))&&s[0].block&&(o.deleteData(0,f.start.length),t.formatter.apply(f.format,{},o),c.setStart(a,u),c.collapse(!0),n.setRng(c)),f.cmd&&t.undoManager.transact(function(){o.deleteData(0,f.start.length),t.execCommand(f.cmd)})}}},p=function(t,e,n){for(var r=0;r<t.length;r++)if(n(t[r],e))return!0},y={handleEnter:function(t,e){var n,r;(n=l(t,e))&&((r=t.dom.createRng()).setStart(n,n.data.length),r.setEnd(n,n.data.length),t.selection.setRng(r)),u(t,e)},handleInlineKey:function(t,e){var n,r,a,o,i;(n=f(t,e))&&(i=t.dom,r=n.data.slice(-1),/[\u00a0 ]/.test(r)&&(n.deleteData(n.data.length-1,1),a=i.doc.createTextNode(r),i.insertAfter(a,n.parentNode),(o=i.createRng()).setStart(a,1),o.setEnd(a,1),t.selection.setRng(o)))},checkCharCode:function(t,e){return p(t,e,function(t,e){return t.charCodeAt(0)===e.charCode})},checkKeyCode:function(t,e){return p(t,e,function(t,e){return t===e.keyCode&&!1===i.modifierPressed(e)})}},v=function(e,n){var r=[",",".",";",":","!","?"],a=[32];e.on("keydown",function(t){13!==t.keyCode||i.modifierPressed(t)||y.handleEnter(e,n.get())},!0),e.on("keyup",function(t){y.checkKeyCode(a,t)&&y.handleInlineKey(e,n.get())}),e.on("keypress",function(t){y.checkCharCode(r,t)&&o.setEditorTimeout(e,function(){y.handleInlineKey(e,n.get())})})};t.add("textpattern",function(t){var e=r(a(t.settings));return v(t,e),n(e)})}();