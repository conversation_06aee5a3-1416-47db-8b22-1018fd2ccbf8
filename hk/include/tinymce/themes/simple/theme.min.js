!function(){"use strict";var e,t,n,i,r,o=tinymce.util.Tools.resolve("tinymce.ThemeManager"),l=tinymce.util.Tools.resolve("tinymce.EditorManager"),w=tinymce.util.Tools.resolve("tinymce.util.Tools"),d=function(e){return!1!==c(e)},c=function(e){return e.getParam("menubar")},f=function(e){return e.getParam("toolbar_items_size")},h=function(e){return e.getParam("menu")},m=function(e){return!1===e.settings.skin},g=function(e){var t=e.getParam("resize","vertical");return!1===t?"none":"both"===t?"both":"vertical"},p=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),v=tinymce.util.Tools.resolve("tinymce.ui.Factory"),b=tinymce.util.Tools.resolve("tinymce.util.I18n"),s=function(e){return e.fire("SkinLoaded")},y=function(e){return e.fire("ResizeEditor")},x=function(e){return e.fire("BeforeRenderUI")},a=function(t,n){return function(){var e=t.find(n)[0];e&&e.focus(!0)}},_=function(e,t){e.shortcuts.add("Alt+F9","",a(t,"menubar")),e.shortcuts.add("Alt+F10,F10","",a(t,"toolbar")),e.shortcuts.add("Alt+F11","",a(t,"elementpath")),t.on("cancel",function(){e.focus()})},R=tinymce.util.Tools.resolve("tinymce.geom.Rect"),u=tinymce.util.Tools.resolve("tinymce.util.Delay"),C=function(e){return function(){return e}},k=C(!1),E=C(!0),H={noop:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},noarg:function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n()}},compose:function(n,i){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(i.apply(null,arguments))}},constant:C,identity:function(e){return e},tripleEquals:function(e,t){return e===t},curry:function(o){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];for(var s=new Array(arguments.length-1),n=1;n<arguments.length;n++)s[n-1]=arguments[n];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var r=s.concat(n);return o.apply(null,r)}},not:function(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return!n.apply(null,arguments)}},die:function(e){return function(){throw new Error(e)}},apply:function(e){return e()},call:function(e){e()},never:k,always:E},S=H.never,M=H.always,T=function(){return P},P=(i={fold:function(e,t){return e()},is:S,isSome:S,isNone:M,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},or:n,orThunk:t,map:T,ap:T,each:function(){},bind:T,flatten:T,exists:S,forall:M,filter:T,equals:e=function(e){return e.isNone()},equals_:e,toArray:function(){return[]},toString:H.constant("none()")},Object.freeze&&Object.freeze(i),i),W=function(n){var e=function(){return n},t=function(){return r},i=function(e){return e(n)},r={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:M,isNone:S,getOr:e,getOrThunk:e,getOrDie:e,or:t,orThunk:t,map:function(e){return W(e(n))},ap:function(e){return e.fold(T,function(e){return W(e(n))})},each:function(e){e(n)},bind:i,flatten:e,exists:i,forall:i,filter:function(e){return e(n)?r:P},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(S,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return r},N={some:W,none:T,from:function(e){return null===e||e===undefined?P:W(e)}},D=function(e){return e?e.getRoot().uiContainer:null},A={getUiContainerDelta:function(e){var t=D(e);if(t&&"static"!==p.DOM.getStyle(t,"position",!0)){var n=p.DOM.getPos(t),i=t.scrollLeft-n.x,r=t.scrollTop-n.y;return N.some({x:i,y:r})}return N.none()},setUiContainer:function(e,t){var n=p.DOM.select(e.settings.ui_container)[0];t.getRoot().uiContainer=n},getUiContainer:D,inheritUiContainer:function(e,t){return t.uiContainer=D(e)}},B=function(i,e,r){var o,s=[];if(e)return w.each(e.split(/[ ,]/),function(t){var e,n=function(){var e=i.selection;t.settings.stateSelector&&e.selectorChanged(t.settings.stateSelector,function(e){t.active(e)},!0),t.settings.disabledStateSelector&&e.selectorChanged(t.settings.disabledStateSelector,function(e){t.disabled(e)})};"|"===t?o=null:(o||(o={type:"buttongroup",items:[]},s.push(o)),i.buttons[t]&&(e=t,"function"==typeof(t=i.buttons[e])&&(t=t()),t.type=t.type||"button",t.size=r,t=v.create(t),o.items.push(t),i.initialized?n():i.on("init",n)))}),{type:"toolbar",layout:"flow",items:s}},O=B,z=function(n,i){var e,t,r=[];if(w.each(!1===(t=(e=n).getParam("toolbar"))?[]:w.isArray(t)?w.grep(t,function(e){return 0<e.length}):function(e,t){for(var n=[],i=1;i<10;i++){var r=e["toolbar"+i];if(!r)break;n.push(r)}var o=e.toolbar?[e.toolbar]:[t];return 0<n.length?n:o}(e.settings,"undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image"),function(e){var t;(t=e)&&r.push(B(n,t,i))}),r.length)return{type:"panel",layout:"stack",classes:"toolbar-grp",ariaRoot:!0,ariaRemember:!0,items:r}},L=p.DOM,I=function(e){return{left:e.x,top:e.y,width:e.w,height:e.h,right:e.x+e.w,bottom:e.y+e.h}},F=function(e,t){e.moveTo(t.left,t.top)},U=function(e,t,n,i,r,o){return o=I({x:t,y:n,w:o.w,h:o.h}),e&&(o=e({elementRect:I(i),contentAreaRect:I(r),panelRect:o})),o},V=function(x){var i,o=function(){return x.contextToolbars||[]},n=function(e,t){var n,i,r,o,s,a,l,u=x.getParam("inline_toolbar_position_handler");if(!x.removed){if(!e||!e.toolbar.panel)return c=x,void w.each(c.contextToolbars,function(e){e.panel&&e.panel.hide()});var c,d,f,h,m;l=["bc-tc","tc-bc","tl-bl","bl-tl","tr-br","br-tr"],s=e.toolbar.panel,t&&s.show(),d=e.element,f=L.getPos(x.getContentAreaContainer()),h=x.dom.getRect(d),"BODY"===(m=x.dom.getRoot()).nodeName&&(h.x-=m.ownerDocument.documentElement.scrollLeft||m.scrollLeft,h.y-=m.ownerDocument.documentElement.scrollTop||m.scrollTop),h.x+=f.x,h.y+=f.y,r=h,i=L.getRect(s.getEl()),o=L.getRect(x.getContentAreaContainer()||x.getBody());var g,p,v,b=A.getUiContainerDelta(s).getOr({x:0,y:0});if(r.x+=b.x,r.y+=b.y,i.x+=b.x,i.y+=b.y,o.x+=b.x,o.y+=b.y,"inline"!==L.getStyle(e.element,"display",!0)){var y=e.element.getBoundingClientRect();r.w=y.width,r.h=y.height}x.inline||(o.w=x.getDoc().documentElement.offsetWidth),x.selection.controlSelection.isResizable(e.element)&&r.w<25&&(r=R.inflate(r,0,8)),n=R.findBestRelativePosition(i,r,o,l),r=R.clamp(r,o),n?(a=R.relativePosition(i,r,n),F(s,U(u,a.x,a.y,r,o,i))):(o.h+=i.h,(r=R.intersect(o,r))?(n=R.findBestRelativePosition(i,r,o,["bc-tc","bl-tl","br-tr"]))?(a=R.relativePosition(i,r,n),F(s,U(u,a.x,a.y,r,o,i))):F(s,U(u,r.x,r.y,r,o,i)):s.hide()),g=s,v=function(e,t){return e===t},p=(p=n)?p.substr(0,2):"",w.each({t:"down",b:"up"},function(e,t){g.classes.toggle("arrow-"+e,v(t,p.substr(0,1)))}),w.each({l:"left",r:"right"},function(e,t){g.classes.toggle("arrow-"+e,v(t,p.substr(1,1)))})}},r=function(e){return function(){u.requestAnimationFrame(function(){x.selection&&n(a(x.selection.getNode()),e)})}},t=function(e){var t;if(e.toolbar.panel)return e.toolbar.panel.show(),void n(e);t=v.create({type:"floatpanel",role:"dialog",classes:"tinymce tinymce-inline arrow",ariaLabel:"Inline toolbar",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:!0,fixed:!0,border:1,items:O(x,e.toolbar.items),oncancel:function(){x.focus()}}),A.setUiContainer(x,t),function(e){if(!i){var t=r(!0),n=A.getUiContainer(e);i=x.selection.getScrollContainer()||x.getWin(),L.bind(i,"scroll",t),L.bind(n,"scroll",t),x.on("remove",function(){L.unbind(i,"scroll",t),L.unbind(n,"scroll",t)})}}(t),(e.toolbar.panel=t).renderTo().reflow(),n(e)},s=function(){w.each(o(),function(e){e.panel&&e.panel.hide()})},a=function(e){var t,n,i,r=o();for(t=(i=x.$(e).parents().add(e)).length-1;0<=t;t--)for(n=r.length-1;0<=n;n--)if(r[n].predicate(i[t]))return{toolbar:r[n],element:i[t]};return null};x.on("click keyup setContent ObjectResized",function(e){("setcontent"!==e.type||e.selection)&&u.setEditorTimeout(x,function(){var e;(e=a(x.selection.getNode()))?(s(),t(e)):s()})}),x.on("blur hide contextmenu",s),x.on("ObjectResizeStart",function(){var e=a(x.selection.getNode());e&&e.toolbar.panel&&e.toolbar.panel.hide()}),x.on("ResizeEditor ResizeWindow",r(!0)),x.on("nodeChange",r(!1)),x.on("remove",function(){w.each(o(),function(e){e.panel&&e.panel.remove()}),x.contextToolbars={}}),x.shortcuts.add("ctrl+shift+e > ctrl+shift+p","",function(){var e=a(x.selection.getNode());e&&e.toolbar.panel&&e.toolbar.panel.items()[0].focus()})},j=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===t}},q={isString:j("string"),isObject:j("object"),isArray:j("array"),isNull:j("null"),isBoolean:j("boolean"),isUndefined:j("undefined"),isFunction:j("function"),isNumber:j("number")},Y=(r=Array.prototype.indexOf)===undefined?function(e,t){return Q(e,t)}:function(e,t){return r.call(e,t)},$=function(e,t){return-1<Y(e,t)},X=function(e,t){for(var n=e.length,i=new Array(n),r=0;r<n;r++){var o=e[r];i[r]=t(o,r,e)}return i},J=function(e,t){for(var n=0,i=e.length;n<i;n++)t(e[n],n,e)},G=function(e,t){for(var n=e.length-1;0<=n;n--)t(e[n],n,e)},K=function(e,t){for(var n=[],i=0,r=e.length;i<r;i++){var o=e[i];t(o,i,e)&&n.push(o)}return n},Z=function(e,t){for(var n=0,i=e.length;n<i;n++)if(t(e[n],n,e))return N.some(n);return N.none()},Q=function(e,t){for(var n=0,i=e.length;n<i;++n)if(e[n]===t)return n;return-1},ee=Array.prototype.push,te=function(e){for(var t=[],n=0,i=e.length;n<i;++n){if(!Array.prototype.isPrototypeOf(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);ee.apply(t,e[n])}return t},ne=function(e,t){for(var n=0,i=e.length;n<i;++n)if(!0!==t(e[n],n,e))return!1;return!0},ie=Array.prototype.slice,re=q.isFunction(Array.from)?Array.from:function(e){return ie.call(e)},oe={map:X,each:J,eachr:G,partition:function(e,t){for(var n=[],i=[],r=0,o=e.length;r<o;r++){var s=e[r];(t(s,r,e)?n:i).push(s)}return{pass:n,fail:i}},filter:K,groupBy:function(e,t){if(0===e.length)return[];for(var n=t(e[0]),i=[],r=[],o=0,s=e.length;o<s;o++){var a=e[o],l=t(a);l!==n&&(i.push(r),r=[]),n=l,r.push(a)}return 0!==r.length&&i.push(r),i},indexOf:function(e,t){var n=Y(e,t);return-1===n?N.none():N.some(n)},foldr:function(e,t,n){return G(e,function(e){n=t(n,e)}),n},foldl:function(e,t,n){return J(e,function(e){n=t(n,e)}),n},find:function(e,t){for(var n=0,i=e.length;n<i;n++){var r=e[n];if(t(r,n,e))return N.some(r)}return N.none()},findIndex:Z,flatten:te,bind:function(e,t){var n=X(e,t);return te(n)},forall:ne,exists:function(e,t){return Z(e,t).isSome()},contains:$,equal:function(e,n){return e.length===n.length&&ne(e,function(e,t){return e===n[t]})},reverse:function(e){var t=ie.call(e,0);return t.reverse(),t},chunk:function(e,t){for(var n=[],i=0;i<e.length;i+=t){var r=e.slice(i,i+t);n.push(r)}return n},difference:function(e,t){return K(e,function(e){return!$(t,e)})},mapToObject:function(e,t){for(var n={},i=0,r=e.length;i<r;i++){var o=e[i];n[String(o)]=t(o,i)}return n},pure:function(e){return[e]},sort:function(e,t){var n=ie.call(e,0);return n.sort(t),n},range:function(e,t){for(var n=[],i=0;i<e;i++)n.push(t(i));return n},head:function(e){return 0===e.length?N.none():N.some(e[0])},last:function(e){return 0===e.length?N.none():N.some(e[e.length-1])},from:re},se={file:{title:"File",items:"newdocument restoredraft | preview | print"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen"},insert:{title:"Insert",items:"image link media template codesample inserttable | charmap hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | blockformats align | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code"},table:{title:"Table"},help:{title:"Help"}},ae=function(e,t){return"|"===e?{name:"|",item:{text:"|"}}:t?{name:e,item:t}:null},le=function(e){return e&&"|"===e.item.text},ue=function(n,e,t,r){var i,o,s,a,l,u,c,d;return e?(o=e[r],a=!0):o=se[r],o&&(i={text:o.title},s=[],w.each((o.items||"").split(/[ ,]/),function(e){var t=ae(e,n[e]);t&&s.push(t)}),a||w.each(n,function(e,t){var n,i;e.context!==r||(n=s,i=t,oe.findIndex(n,function(e){return e.name===i}).isSome())||("before"===e.separator&&s.push({name:"|",item:{text:"|"}}),e.prependToContext?s.unshift(ae(t,e)):s.push(ae(t,e)),"after"===e.separator&&s.push({name:"|",item:{text:"|"}}))}),i.menu=oe.map((l=s,u=t,c=oe.filter(l,function(e){return!1===u.hasOwnProperty(e.name)}),d=oe.filter(c,function(e,t,n){return!le(e)||!le(n[t-1])}),oe.filter(d,function(e,t,n){return!le(e)||0<t&&t<n.length-1})),function(e){return e.item}),!i.menu.length)?null:i},ce=function(e){for(var t,n=[],i=function(e){var t,n=[],i=h(e);if(i)for(t in i)n.push(t);else for(t in se)n.push(t);return n}(e),r=w.makeMap((t=e,t.getParam("removed_menuitems","")).split(/[ ,]/)),o=c(e),s="string"==typeof o?o.split(/[ ,]/):i,a=0;a<s.length;a++){var l=s[a],u=ue(e.menuItems,h(e),r,l);u&&n.push(u)}return n},de=p.DOM,fe=function(e){return{width:e.clientWidth,height:e.clientHeight}},he=function(e,t,n){var i,r,o,s;i=e.getContainer(),r=e.getContentAreaContainer().firstChild,o=fe(i),s=fe(r),null!==t&&(t=Math.max(e.getParam("min_width",100,"number"),t),t=Math.min(e.getParam("max_width",65535,"number"),t),de.setStyle(i,"width",t+(o.width-s.width)),de.setStyle(r,"width",t)),n=Math.max(e.getParam("min_height",100,"number"),n),n=Math.min(e.getParam("max_height",65535,"number"),n),de.setStyle(r,"height",n),y(e)},me=he,ge=function(e,t,n){var i=e.getContentAreaContainer();he(e,i.clientWidth+t,i.clientHeight+n)},pe=tinymce.util.Tools.resolve("tinymce.Env"),ve=function(e,t,n){var i,r=e.settings[n];r&&r((i=t.getEl("body"),{element:function(){return i}}))},be=function(c,d,f){return function(e){var t,n,i,r,o,s=e.control,a=s.parents().filter("panel")[0],l=a.find("#"+d)[0],u=(t=f,n=d,w.grep(t,function(e){return e.name===n})[0]);i=d,r=a,o=f,w.each(o,function(e){var t=r.items().filter("#"+e.name)[0];t&&t.visible()&&e.name!==i&&(ve(e,t,"onhide"),t.visible(!1))}),s.parent().items().each(function(e){e.active(!1)}),l&&l.visible()?(ve(u,l,"onhide"),l.hide(),s.active(!1)):(l?l.show():(l=v.create({type:"container",name:d,layout:"stack",classes:"sidebar-panel",html:""}),a.prepend(l),ve(u,l,"onrender")),ve(u,l,"onshow"),s.active(!0)),y(c)}},ye=function(e){return!(pe.ie&&!(11<=pe.ie)||!e.sidebars)&&0<e.sidebars.length},xe=function(n){return{type:"panel",name:"sidebar",layout:"stack",classes:"sidebar",items:[{type:"toolbar",layout:"stack",classes:"sidebar-toolbar",items:w.map(n.sidebars,function(e){var t=e.settings;return{type:"button",icon:t.icon,image:t.image,tooltip:t.tooltip,onclick:be(n,e.name,n.sidebars)}})}]}},we=function(e){var t=function(){e._skinLoaded=!0,s(e)};return function(){e.initialized?t():e.on("init",t)}},_e=p.DOM,Re=function(e){return{type:"panel",name:"iframe",layout:"stack",classes:"edit-area",border:e,html:""}},Ce=function(t,e,n){var i,r,o,s,a;if(!1===m(t)&&n.skinUiCss?_e.styleSheetLoader.load(n.skinUiCss,we(t)):we(t)(),i=e.panel=v.create({type:"panel",role:"application",classes:"tinymce",style:"visibility: hidden",layout:"stack",border:1,items:[{type:"container",classes:"top-part",items:[!1===d(t)?null:{type:"menubar",border:"0 0 1 0",items:ce(t)},z(t,f(t))]},ye(t)?(s=t,{type:"panel",layout:"stack",classes:"edit-aria-container",border:"1 0 0 0",items:[Re("0"),xe(s)]}):Re("1 0 0 0")]}),A.setUiContainer(t,i),"none"!==g(t)&&(r={type:"resizehandle",direction:g(t),onResizeStart:function(){var e=t.getContentAreaContainer().firstChild;o={width:e.clientWidth,height:e.clientHeight}},onResize:function(e){"both"===g(t)?me(t,o.width+e.deltaX,o.height+e.deltaY):me(t,null,o.height+e.deltaY)}}),t.getParam("statusbar",!0,"boolean")){var l=b.translate(["Powered by {0}",'<a href="https://www.tinymce.com/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce" rel="noopener" target="_blank" role="presentation" tabindex="-1">tinymce</a>']),u=t.getParam("branding",!0,"boolean")?{type:"label",classes:"branding",html:" "+l}:null;i.add({type:"panel",name:"statusbar",classes:"statusbar",layout:"flow",border:"1 0 0 0",ariaRoot:!0,items:[{type:"elementpath",editor:t},r,u]})}return x(t),t.on("SwitchMode",(a=i,function(e){a.find("*").disabled("readonly"===e.mode)})),i.renderBefore(n.targetNode).reflow(),t.getParam("readonly",!1,"boolean")&&t.setMode("readonly"),n.width&&_e.setStyle(i.getEl(),"width",n.width),t.on("remove",function(){i.remove(),i=null}),_(t,i),V(t),{iframeContainer:i.find("#iframe")[0].getEl(),editorContainer:i.getEl()}},ke=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),Ee=0,He={id:function(){return"mceu_"+Ee++},create:function(e,t,n){var i=document.createElement(e);return p.DOM.setAttribs(i,t),"string"==typeof n?i.innerHTML=n:w.each(n,function(e){e.nodeType&&i.appendChild(e)}),i},createFragment:function(e){return p.DOM.createFragment(e)},getWindowSize:function(){return p.DOM.getViewPort()},getSize:function(e){var t,n;if(e.getBoundingClientRect){var i=e.getBoundingClientRect();t=Math.max(i.width||i.right-i.left,e.offsetWidth),n=Math.max(i.height||i.bottom-i.bottom,e.offsetHeight)}else t=e.offsetWidth,n=e.offsetHeight;return{width:t,height:n}},getPos:function(e,t){return p.DOM.getPos(e,t||He.getContainer())},getContainer:function(){return pe.container?pe.container:document.body},getViewPort:function(e){return p.DOM.getViewPort(e)},get:function(e){return document.getElementById(e)},addClass:function(e,t){return p.DOM.addClass(e,t)},removeClass:function(e,t){return p.DOM.removeClass(e,t)},hasClass:function(e,t){return p.DOM.hasClass(e,t)},toggleClass:function(e,t,n){return p.DOM.toggleClass(e,t,n)},css:function(e,t,n){return p.DOM.setStyle(e,t,n)},getRuntimeStyle:function(e,t){return p.DOM.getStyle(e,t,!0)},on:function(e,t,n,i){return p.DOM.bind(e,t,n,i)},off:function(e,t,n){return p.DOM.unbind(e,t,n)},fire:function(e,t,n){return p.DOM.fire(e,t,n)},innerHtml:function(e,t){p.DOM.setHTML(e,t)}},Se=function(e){return"static"===He.getRuntimeStyle(e,"position")},Me=function(e){return e.state.get("fixed")};function Te(e,t,n){var i,r,o,s,a,l,u,c,d,f;return d=Pe(),o=(r=He.getPos(t,A.getUiContainer(e))).x,s=r.y,Me(e)&&Se(document.body)&&(o-=d.x,s-=d.y),i=e.getEl(),a=(f=He.getSize(i)).width,l=f.height,u=(f=He.getSize(t)).width,c=f.height,"b"===(n=(n||"").split(""))[0]&&(s+=c),"r"===n[1]&&(o+=u),"c"===n[0]&&(s+=Math.round(c/2)),"c"===n[1]&&(o+=Math.round(u/2)),"b"===n[3]&&(s-=l),"r"===n[4]&&(o-=a),"c"===n[3]&&(s-=Math.round(l/2)),"c"===n[4]&&(o-=Math.round(a/2)),{x:o,y:s,w:a,h:l}}var Pe=function(){var e=window,t=Math.max(e.pageXOffset,document.body.scrollLeft,document.documentElement.scrollLeft),n=Math.max(e.pageYOffset,document.body.scrollTop,document.documentElement.scrollTop);return{x:t,y:n,w:t+(e.innerWidth||document.documentElement.clientWidth),h:n+(e.innerHeight||document.documentElement.clientHeight)}},We=function(e){var t,n=A.getUiContainer(e);return n&&!Me(e)?{x:0,y:0,w:(t=n).scrollWidth-1,h:t.scrollHeight-1}:Pe()},Ne={testMoveRel:function(e,t){for(var n=We(this),i=0;i<t.length;i++){var r=Te(this,e,t[i]);if(Me(this)){if(0<r.x&&r.x+r.w<n.w&&0<r.y&&r.y+r.h<n.h)return t[i]}else if(r.x>n.x&&r.x+r.w<n.w&&r.y>n.y&&r.y+r.h<n.h)return t[i]}return t[0]},moveRel:function(e,t){"string"!=typeof t&&(t=this.testMoveRel(e,t));var n=Te(this,e,t);return this.moveTo(n.x,n.y)},moveBy:function(e,t){var n=this.layoutRect();return this.moveTo(n.x+e,n.y+t),this},moveTo:function(e,t){var n=this;function i(e,t,n){return e<0?0:t<e+n&&(e=t-n)<0?0:e}if(n.settings.constrainToViewport){var r=We(this),o=n.layoutRect();e=i(e,r.w,o.w),t=i(t,r.h,o.h)}var s=A.getUiContainer(n);return s&&Se(s)&&!Me(n)&&(e-=s.scrollLeft,t-=s.scrollTop),s&&(e+=1,t+=1),n.state.get("rendered")?n.layoutRect({x:e,y:t}).repaint():(n.settings.x=e,n.settings.y=t),n.fire("move",{x:e,y:t}),n}},De=tinymce.util.Tools.resolve("tinymce.util.Class"),Ae=tinymce.util.Tools.resolve("tinymce.util.EventDispatcher"),Be=function(e){var t;if(e)return"number"==typeof e?{top:e=e||0,left:e,bottom:e,right:e}:(1===(t=(e=e.split(" ")).length)?e[1]=e[2]=e[3]=e[0]:2===t?(e[2]=e[0],e[3]=e[1]):3===t&&(e[3]=e[1]),{top:parseInt(e[0],10)||0,right:parseInt(e[1],10)||0,bottom:parseInt(e[2],10)||0,left:parseInt(e[3],10)||0})},Oe=function(i,e){function t(e){var t=parseFloat(function(e){var t=i.ownerDocument.defaultView;if(t){var n=t.getComputedStyle(i,null);return n?(e=e.replace(/[A-Z]/g,function(e){return"-"+e}),n.getPropertyValue(e)):null}return i.currentStyle[e]}(e));return isNaN(t)?0:t}return{top:t(e+"TopWidth"),right:t(e+"RightWidth"),bottom:t(e+"BottomWidth"),left:t(e+"LeftWidth")}};function ze(){}function Le(e){this.cls=[],this.cls._map={},this.onchange=e||ze,this.prefix=""}w.extend(Le.prototype,{add:function(e){return e&&!this.contains(e)&&(this.cls._map[e]=!0,this.cls.push(e),this._change()),this},remove:function(e){if(this.contains(e)){var t=void 0;for(t=0;t<this.cls.length&&this.cls[t]!==e;t++);this.cls.splice(t,1),delete this.cls._map[e],this._change()}return this},toggle:function(e,t){var n=this.contains(e);return n!==t&&(n?this.remove(e):this.add(e),this._change()),this},contains:function(e){return!!this.cls._map[e]},_change:function(){delete this.clsValue,this.onchange.call(this)}}),Le.prototype.toString=function(){var e;if(this.clsValue)return this.clsValue;e="";for(var t=0;t<this.cls.length;t++)0<t&&(e+=" "),e+=this.prefix+this.cls[t];return e};var Ie,Fe,Ue,Ve=/^([\w\\*]+)?(?:#([\w\-\\]+))?(?:\.([\w\\\.]+))?(?:\[\@?([\w\\]+)([\^\$\*!~]?=)([\w\\]+)\])?(?:\:(.+))?/i,je=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g,qe=/^\s*|\s*$/g,Ye=De.extend({init:function(e){var o=this.match;function s(e,t,n){var i;function r(e){e&&t.push(e)}return r(function(t){if(t)return t=t.toLowerCase(),function(e){return"*"===t||e.type===t}}((i=Ve.exec(e.replace(qe,"")))[1])),r(function(t){if(t)return function(e){return e._name===t}}(i[2])),r(function(n){if(n)return n=n.split("."),function(e){for(var t=n.length;t--;)if(!e.classes.contains(n[t]))return!1;return!0}}(i[3])),r(function(n,i,r){if(n)return function(e){var t=e[n]?e[n]():"";return i?"="===i?t===r:"*="===i?0<=t.indexOf(r):"~="===i?0<=(" "+t+" ").indexOf(" "+r+" "):"!="===i?t!==r:"^="===i?0===t.indexOf(r):"$="===i&&t.substr(t.length-r.length)===r:!!r}}(i[4],i[5],i[6])),r(function(i){var t;if(i)return(i=/(?:not\((.+)\))|(.+)/i.exec(i))[1]?(t=a(i[1],[]),function(e){return!o(e,t)}):(i=i[2],function(e,t,n){return"first"===i?0===t:"last"===i?t===n-1:"even"===i?t%2==0:"odd"===i?t%2==1:!!e[i]&&e[i]()})}(i[7])),t.pseudo=!!i[7],t.direct=n,t}function a(e,t){var n,i,r,o=[];do{if(je.exec(""),(i=je.exec(e))&&(e=i[3],o.push(i[1]),i[2])){n=i[3];break}}while(i);for(n&&a(n,t),e=[],r=0;r<o.length;r++)">"!==o[r]&&e.push(s(o[r],[],">"===o[r-1]));return t.push(e),t}this._selectors=a(e,[])},match:function(e,t){var n,i,r,o,s,a,l,u,c,d,f,h,m;for(n=0,i=(t=t||this._selectors).length;n<i;n++){for(m=e,h=0,r=(o=(s=t[n]).length)-1;0<=r;r--)for(u=s[r];m;){if(u.pseudo)for(c=d=(f=m.parent().items()).length;c--&&f[c]!==m;);for(a=0,l=u.length;a<l;a++)if(!u[a](m,c,d)){a=l+1;break}if(a===l){h++;break}if(r===o-1)break;m=m.parent()}if(h===o)return!0}return!1},find:function(e){var t,n,u=[],i=this._selectors;function c(e,t,n){var i,r,o,s,a,l=t[n];for(i=0,r=e.length;i<r;i++){for(a=e[i],o=0,s=l.length;o<s;o++)if(!l[o](a,i,r)){o=s+1;break}if(o===s)n===t.length-1?u.push(a):a.items&&c(a.items(),t,n+1);else if(l.direct)return;a.items&&c(a.items(),t,n)}}if(e.items){for(t=0,n=i.length;t<n;t++)c(e.items(),i[t],0);1<n&&(u=function(e){for(var t,n=[],i=e.length;i--;)(t=e[i]).__checked||(n.push(t),t.__checked=1);for(i=n.length;i--;)delete n[i].__checked;return n}(u))}return Ie||(Ie=Ye.Collection),new Ie(u)}}),$e=Array.prototype.push,Xe=Array.prototype.slice;Ue={length:0,init:function(e){e&&this.add(e)},add:function(e){return w.isArray(e)?$e.apply(this,e):e instanceof Fe?this.add(e.toArray()):$e.call(this,e),this},set:function(e){var t,n=this,i=n.length;for(n.length=0,n.add(e),t=n.length;t<i;t++)delete n[t];return n},filter:function(t){var e,n,i,r,o=[];for("string"==typeof t?(t=new Ye(t),r=function(e){return t.match(e)}):r=t,e=0,n=this.length;e<n;e++)r(i=this[e])&&o.push(i);return new Fe(o)},slice:function(){return new Fe(Xe.apply(this,arguments))},eq:function(e){return-1===e?this.slice(e):this.slice(e,+e+1)},each:function(e){return w.each(this,e),this},toArray:function(){return w.toArray(this)},indexOf:function(e){for(var t=this.length;t--&&this[t]!==e;);return t},reverse:function(){return new Fe(w.toArray(this).reverse())},hasClass:function(e){return!!this[0]&&this[0].classes.contains(e)},prop:function(t,n){var e;return n!==undefined?(this.each(function(e){e[t]&&e[t](n)}),this):(e=this[0])&&e[t]?e[t]():void 0},exec:function(t){var n=w.toArray(arguments).slice(1);return this.each(function(e){e[t]&&e[t].apply(e,n)}),this},remove:function(){for(var e=this.length;e--;)this[e].remove();return this},addClass:function(t){return this.each(function(e){e.classes.add(t)})},removeClass:function(t){return this.each(function(e){e.classes.remove(t)})}},w.each("fire on off show hide append prepend before after reflow".split(" "),function(n){Ue[n]=function(){var t=w.toArray(arguments);return this.each(function(e){n in e&&e[n].apply(e,t)}),this}}),w.each("text name disabled active selected checked visible parent value data".split(" "),function(t){Ue[t]=function(e){return this.prop(t,e)}}),Fe=De.extend(Ue);var Je=Ye.Collection=Fe,Ge=function(e){this.create=e.create};Ge.create=function(r,o){return new Ge({create:function(t,n){var i,e=function(e){t.set(n,e.value)};return t.on("change:"+n,function(e){r.set(o,e.value)}),r.on("change:"+o,e),(i=t._bindings)||(i=t._bindings=[],t.on("destroy",function(){for(var e=i.length;e--;)i[e]()})),i.push(function(){r.off("change:"+o,e)}),r.get(o)}})};var Ke=tinymce.util.Tools.resolve("tinymce.util.Observable");function Ze(e){return 0<e.nodeType}var Qe,et,tt=De.extend({Mixins:[Ke],init:function(e){var t,n;for(t in e=e||{})(n=e[t])instanceof Ge&&(e[t]=n.create(this,t));this.data=e},set:function(t,n){var i,r,o=this.data[t];if(n instanceof Ge&&(n=n.create(this,t)),"object"==typeof t){for(i in t)this.set(i,t[i]);return this}return function e(t,n){var i,r;if(t===n)return!0;if(null===t||null===n)return t===n;if("object"!=typeof t||"object"!=typeof n)return t===n;if(w.isArray(n)){if(t.length!==n.length)return!1;for(i=t.length;i--;)if(!e(t[i],n[i]))return!1}if(Ze(t)||Ze(n))return t===n;for(i in r={},n){if(!e(t[i],n[i]))return!1;r[i]=!0}for(i in t)if(!r[i]&&!e(t[i],n[i]))return!1;return!0}(o,n)||(this.data[t]=n,r={target:this,name:t,value:n,oldValue:o},this.fire("change:"+t,r),this.fire("change",r)),this},get:function(e){return this.data[e]},has:function(e){return e in this.data},bind:function(e){return Ge.create(this,e)},destroy:function(){this.fire("destroy")}}),nt={},it={add:function(e){var t=e.parent();if(t){if(!t._layout||t._layout.isNative())return;nt[t._id]||(nt[t._id]=t),Qe||(Qe=!0,u.requestAnimationFrame(function(){var e,t;for(e in Qe=!1,nt)(t=nt[e]).state.get("rendered")&&t.reflow();nt={}},document.body))}},remove:function(e){nt[e._id]&&delete nt[e._id]}},rt="onmousewheel"in document,ot=!1,st=0,at={Statics:{classPrefix:"mce-"},isRtl:function(){return et.rtl},classPrefix:"mce-",init:function(t){var e,n,i=this;function r(e){var t;for(e=e.split(" "),t=0;t<e.length;t++)i.classes.add(e[t])}i.settings=t=w.extend({},i.Defaults,t),i._id=t.id||"mceu_"+st++,i._aria={role:t.role},i._elmCache={},i.$=ke,i.state=new tt({visible:!0,active:!1,disabled:!1,value:""}),i.data=new tt(t.data),i.classes=new Le(function(){i.state.get("rendered")&&(i.getEl().className=this.toString())}),i.classes.prefix=i.classPrefix,(e=t.classes)&&(i.Defaults&&(n=i.Defaults.classes)&&e!==n&&r(n),r(e)),w.each("title text name visible disabled active value".split(" "),function(e){e in t&&i[e](t[e])}),i.on("click",function(){if(i.disabled())return!1}),i.settings=t,i.borderBox=Be(t.border),i.paddingBox=Be(t.padding),i.marginBox=Be(t.margin),t.hidden&&i.hide()},Properties:"parent,name",getContainerElm:function(){var e=A.getUiContainer(this);return e||He.getContainer()},getParentCtrl:function(e){for(var t,n=this.getRoot().controlIdLookup;e&&n&&!(t=n[e.id]);)e=e.parentNode;return t},initLayoutRect:function(){var e,t,n,i,r,o,s,a,l,u,c=this,d=c.settings,f=c.getEl();e=c.borderBox=c.borderBox||Oe(f,"border"),c.paddingBox=c.paddingBox||Oe(f,"padding"),c.marginBox=c.marginBox||Oe(f,"margin"),u=He.getSize(f),a=d.minWidth,l=d.minHeight,r=a||u.width,o=l||u.height,n=d.width,i=d.height,s=void 0!==(s=d.autoResize)?s:!n&&!i,n=n||r,i=i||o;var h=e.left+e.right,m=e.top+e.bottom,g=d.maxWidth||65535,p=d.maxHeight||65535;return c._layoutRect=t={x:d.x||0,y:d.y||0,w:n,h:i,deltaW:h,deltaH:m,contentW:n-h,contentH:i-m,innerW:n-h,innerH:i-m,startMinWidth:a||0,startMinHeight:l||0,minW:Math.min(r,g),minH:Math.min(o,p),maxW:g,maxH:p,autoResize:s,scrollW:0},c._lastLayoutRect={},t},layoutRect:function(e){var t,n,i,r,o,s=this,a=s._layoutRect;return a||(a=s.initLayoutRect()),e?(i=a.deltaW,r=a.deltaH,e.x!==undefined&&(a.x=e.x),e.y!==undefined&&(a.y=e.y),e.minW!==undefined&&(a.minW=e.minW),e.minH!==undefined&&(a.minH=e.minH),(n=e.w)!==undefined&&(n=(n=n<a.minW?a.minW:n)>a.maxW?a.maxW:n,a.w=n,a.innerW=n-i),(n=e.h)!==undefined&&(n=(n=n<a.minH?a.minH:n)>a.maxH?a.maxH:n,a.h=n,a.innerH=n-r),(n=e.innerW)!==undefined&&(n=(n=n<a.minW-i?a.minW-i:n)>a.maxW-i?a.maxW-i:n,a.innerW=n,a.w=n+i),(n=e.innerH)!==undefined&&(n=(n=n<a.minH-r?a.minH-r:n)>a.maxH-r?a.maxH-r:n,a.innerH=n,a.h=n+r),e.contentW!==undefined&&(a.contentW=e.contentW),e.contentH!==undefined&&(a.contentH=e.contentH),(t=s._lastLayoutRect).x===a.x&&t.y===a.y&&t.w===a.w&&t.h===a.h||((o=et.repaintControls)&&o.map&&!o.map[s._id]&&(o.push(s),o.map[s._id]=!0),t.x=a.x,t.y=a.y,t.w=a.w,t.h=a.h),s):a},repaint:function(){var e,t,n,i,r,o,s,a,l,u,c=this;l=document.createRange?function(e){return e}:Math.round,e=c.getEl().style,i=c._layoutRect,a=c._lastRepaintRect||{},o=(r=c.borderBox).left+r.right,s=r.top+r.bottom,i.x!==a.x&&(e.left=l(i.x)+"px",a.x=i.x),i.y!==a.y&&(e.top=l(i.y)+"px",a.y=i.y),i.w!==a.w&&(u=l(i.w-o),e.width=(0<=u?u:0)+"px",a.w=i.w),i.h!==a.h&&(u=l(i.h-s),e.height=(0<=u?u:0)+"px",a.h=i.h),c._hasBody&&i.innerW!==a.innerW&&(u=l(i.innerW),(n=c.getEl("body"))&&((t=n.style).width=(0<=u?u:0)+"px"),a.innerW=i.innerW),c._hasBody&&i.innerH!==a.innerH&&(u=l(i.innerH),(n=n||c.getEl("body"))&&((t=t||n.style).height=(0<=u?u:0)+"px"),a.innerH=i.innerH),c._lastRepaintRect=a,c.fire("repaint",{},!1)},updateLayoutRect:function(){var e=this;e.parent()._lastRect=null,He.css(e.getEl(),{width:"",height:""}),e._layoutRect=e._lastRepaintRect=e._lastLayoutRect=null,e.initLayoutRect()},on:function(e,t){var n,i,r,o=this;return lt(o).on(e,"string"!=typeof(n=t)?n:function(e){return i||o.parentsAndSelf().each(function(e){var t=e.settings.callbacks;if(t&&(i=t[n]))return r=e,!1}),i?i.call(r,e):(e.action=n,void this.fire("execute",e))}),o},off:function(e,t){return lt(this).off(e,t),this},fire:function(e,t,n){if((t=t||{}).control||(t.control=this),t=lt(this).fire(e,t),!1!==n&&this.parent)for(var i=this.parent();i&&!t.isPropagationStopped();)i.fire(e,t,!1),i=i.parent();return t},hasEventListeners:function(e){return lt(this).has(e)},parents:function(e){var t,n=new Je;for(t=this.parent();t;t=t.parent())n.add(t);return e&&(n=n.filter(e)),n},parentsAndSelf:function(e){return new Je(this).add(this.parents(e))},next:function(){var e=this.parent().items();return e[e.indexOf(this)+1]},prev:function(){var e=this.parent().items();return e[e.indexOf(this)-1]},innerHtml:function(e){return this.$el.html(e),this},getEl:function(e){var t=e?this._id+"-"+e:this._id;return this._elmCache[t]||(this._elmCache[t]=ke("#"+t)[0]),this._elmCache[t]},show:function(){return this.visible(!0)},hide:function(){return this.visible(!1)},focus:function(){try{this.getEl().focus()}catch(e){}return this},blur:function(){return this.getEl().blur(),this},aria:function(e,t){var n=this,i=n.getEl(n.ariaTarget);return void 0===t?n._aria[e]:(n._aria[e]=t,n.state.get("rendered")&&i.setAttribute("role"===e?e:"aria-"+e,t),n)},encode:function(e,t){return!1!==t&&(e=this.translate(e)),(e||"").replace(/[&<>"]/g,function(e){return"&#"+e.charCodeAt(0)+";"})},translate:function(e){return et.translate?et.translate(e):e},before:function(e){var t=this.parent();return t&&t.insert(e,t.items().indexOf(this),!0),this},after:function(e){var t=this.parent();return t&&t.insert(e,t.items().indexOf(this)),this},remove:function(){var t,e,n=this,i=n.getEl(),r=n.parent();if(n.items){var o=n.items().toArray();for(e=o.length;e--;)o[e].remove()}r&&r.items&&(t=[],r.items().each(function(e){e!==n&&t.push(e)}),r.items().set(t),r._lastRect=null),n._eventsRoot&&n._eventsRoot===n&&ke(i).off();var s=n.getRoot().controlIdLookup;return s&&delete s[n._id],i&&i.parentNode&&i.parentNode.removeChild(i),n.state.set("rendered",!1),n.state.destroy(),n.fire("remove"),n},renderBefore:function(e){return ke(e).before(this.renderHtml()),this.postRender(),this},renderTo:function(e){return ke(e||this.getContainerElm()).append(this.renderHtml()),this.postRender(),this},preRender:function(){},render:function(){},renderHtml:function(){return'<div id="'+this._id+'" class="'+this.classes+'"></div>'},postRender:function(){var e,t,n,i,r,o=this,s=o.settings;for(i in o.$el=ke(o.getEl()),o.state.set("rendered",!0),s)0===i.indexOf("on")&&o.on(i.substr(2),s[i]);if(o._eventsRoot){for(n=o.parent();!r&&n;n=n.parent())r=n._eventsRoot;if(r)for(i in r._nativeEvents)o._nativeEvents[i]=!0}ut(o),s.style&&(e=o.getEl())&&(e.setAttribute("style",s.style),e.style.cssText=s.style),o.settings.border&&(t=o.borderBox,o.$el.css({"border-top-width":t.top,"border-right-width":t.right,"border-bottom-width":t.bottom,"border-left-width":t.left}));var a=o.getRoot();for(var l in a.controlIdLookup||(a.controlIdLookup={}),(a.controlIdLookup[o._id]=o)._aria)o.aria(l,o._aria[l]);!1===o.state.get("visible")&&(o.getEl().style.display="none"),o.bindStates(),o.state.on("change:visible",function(e){var t,n=e.value;o.state.get("rendered")&&(o.getEl().style.display=!1===n?"none":"",o.getEl().getBoundingClientRect()),(t=o.parent())&&(t._lastRect=null),o.fire(n?"show":"hide"),it.add(o)}),o.fire("postrender",{},!1)},bindStates:function(){},scrollIntoView:function(e){var t,n,i,r,o,s,a=this.getEl(),l=a.parentNode,u=function(e,t){var n,i,r=e;for(n=i=0;r&&r!==t&&r.nodeType;)n+=r.offsetLeft||0,i+=r.offsetTop||0,r=r.offsetParent;return{x:n,y:i}}(a,l);return t=u.x,n=u.y,i=a.offsetWidth,r=a.offsetHeight,o=l.clientWidth,s=l.clientHeight,"end"===e?(t-=o-i,n-=s-r):"center"===e&&(t-=o/2-i/2,n-=s/2-r/2),l.scrollLeft=t,l.scrollTop=n,this},getRoot:function(){for(var e,t=this,n=[];t;){if(t.rootControl){e=t.rootControl;break}n.push(t),t=(e=t).parent()}e||(e=this);for(var i=n.length;i--;)n[i].rootControl=e;return e},reflow:function(){it.remove(this);var e=this.parent();return e&&e._layout&&!e._layout.isNative()&&e.reflow(),this}};function lt(n){return n._eventDispatcher||(n._eventDispatcher=new Ae({scope:n,toggleEvent:function(e,t){t&&Ae.isNative(e)&&(n._nativeEvents||(n._nativeEvents={}),n._nativeEvents[e]=!0,n.state.get("rendered")&&ut(n))}})),n._eventDispatcher}function ut(a){var e,t,n,l,i,r;function o(e){var t=a.getParentCtrl(e.target);t&&t.fire(e.type,e)}function s(){var e=l._lastHoverCtrl;e&&(e.fire("mouseleave",{target:e.getEl()}),e.parents().each(function(e){e.fire("mouseleave",{target:e.getEl()})}),l._lastHoverCtrl=null)}function u(e){var t,n,i,r=a.getParentCtrl(e.target),o=l._lastHoverCtrl,s=0;if(r!==o){if((n=(l._lastHoverCtrl=r).parents().toArray().reverse()).push(r),o){for((i=o.parents().toArray().reverse()).push(o),s=0;s<i.length&&n[s]===i[s];s++);for(t=i.length-1;s<=t;t--)(o=i[t]).fire("mouseleave",{target:o.getEl()})}for(t=s;t<n.length;t++)(r=n[t]).fire("mouseenter",{target:r.getEl()})}}function c(e){e.preventDefault(),"mousewheel"===e.type?(e.deltaY=-.025*e.wheelDelta,e.wheelDeltaX&&(e.deltaX=-.025*e.wheelDeltaX)):(e.deltaX=0,e.deltaY=e.detail),e=a.fire("wheel",e)}if(i=a._nativeEvents){for((n=a.parents().toArray()).unshift(a),e=0,t=n.length;!l&&e<t;e++)l=n[e]._eventsRoot;for(l||(l=n[n.length-1]||a),a._eventsRoot=l,t=e,e=0;e<t;e++)n[e]._eventsRoot=l;var d=l._delegates;for(r in d||(d=l._delegates={}),i){if(!i)return!1;"wheel"!==r||ot?("mouseenter"===r||"mouseleave"===r?l._hasMouseEnter||(ke(l.getEl()).on("mouseleave",s).on("mouseover",u),l._hasMouseEnter=1):d[r]||(ke(l.getEl()).on(r,o),d[r]=!0),i[r]=!1):rt?ke(a.getEl()).on("mousewheel",c):ke(a.getEl()).on("DOMMouseScroll",c)}}}w.each("text title visible disabled active value".split(" "),function(t){at[t]=function(e){return 0===arguments.length?this.state.get(t):(void 0!==e&&this.state.set(t,e),this)}});var ct=et=De.extend(at),dt=function(e){return!!e.getAttribute("data-mce-tabstop")};function ft(e){var o,r,n=e.root;function i(e){return e&&1===e.nodeType}try{o=document.activeElement}catch(t){o=document.body}function s(e){return i(e=e||o)?e.getAttribute("role"):null}function a(e){for(var t,n=e||o;n=n.parentNode;)if(t=s(n))return t}function l(e){var t=o;if(i(t))return t.getAttribute("aria-"+e)}function u(e){var t=e.tagName.toUpperCase();return"INPUT"===t||"TEXTAREA"===t||"SELECT"===t}function c(t){var r=[];return function e(t){if(1===t.nodeType&&"none"!==t.style.display&&!t.disabled){var n;(u(n=t)&&!n.hidden||dt(n)||/^(button|menuitem|checkbox|tab|menuitemcheckbox|option|gridcell|slider)$/.test(s(n)))&&r.push(t);for(var i=0;i<t.childNodes.length;i++)e(t.childNodes[i])}}(t||n.getEl()),r}function d(e){var t,n;(n=(e=e||r).parents().toArray()).unshift(e);for(var i=0;i<n.length&&!(t=n[i]).settings.ariaRoot;i++);return t}function f(e,t){return e<0?e=t.length-1:e>=t.length&&(e=0),t[e]&&t[e].focus(),e}function h(e,t){var n=-1,i=d();t=t||c(i.getEl());for(var r=0;r<t.length;r++)t[r]===o&&(n=r);n+=e,i.lastAriaIndex=f(n,t)}function m(){"tablist"===a()?h(-1,c(o.parentNode)):r.parent().submenu?b():h(-1)}function g(){var e=s(),t=a();"tablist"===t?h(1,c(o.parentNode)):"menuitem"===e&&"menu"===t&&l("haspopup")?y():h(1)}function p(){h(-1)}function v(){var e=s(),t=a();"menuitem"===e&&"menubar"===t?y():"button"===e&&l("haspopup")?y({key:"down"}):h(1)}function b(){r.fire("cancel")}function y(e){e=e||{},r.fire("click",{target:o,aria:e})}return r=n.getParentCtrl(o),n.on("keydown",function(e){function t(e,t){u(o)||dt(o)||"slider"!==s(o)&&!1!==t(e)&&e.preventDefault()}if(!e.isDefaultPrevented())switch(e.keyCode){case 37:t(e,m);break;case 39:t(e,g);break;case 38:t(e,p);break;case 40:t(e,v);break;case 27:b();break;case 14:case 13:case 32:t(e,y);break;case 9:!function(e){if("tablist"===a()){var t=c(r.getEl("body"))[0];t&&t.focus()}else h(e.shiftKey?-1:1)}(e),e.preventDefault()}}),n.on("focusin",function(e){o=e.target,r=e.control}),{focusFirst:function(e){var t=d(e),n=c(t.getEl());t.settings.ariaRemember&&"lastAriaIndex"in t?f(t.lastAriaIndex,n):f(0,n)}}}var ht={},mt=ct.extend({init:function(e){var t=this;t._super(e),(e=t.settings).fixed&&t.state.set("fixed",!0),t._items=new Je,t.isRtl()&&t.classes.add("rtl"),t.bodyClasses=new Le(function(){t.state.get("rendered")&&(t.getEl("body").className=this.toString())}),t.bodyClasses.prefix=t.classPrefix,t.classes.add("container"),t.bodyClasses.add("container-body"),e.containerCls&&t.classes.add(e.containerCls),t._layout=v.create((e.layout||"")+"layout"),t.settings.items?t.add(t.settings.items):t.add(t.render()),t._hasBody=!0},items:function(){return this._items},find:function(e){return(e=ht[e]=ht[e]||new Ye(e)).find(this)},add:function(e){return this.items().add(this.create(e)).parent(this),this},focus:function(e){var t,n,i,r=this;if(!e||!(n=r.keyboardNav||r.parents().eq(-1)[0].keyboardNav))return i=r.find("*"),r.statusbar&&i.add(r.statusbar.items()),i.each(function(e){if(e.settings.autofocus)return t=null,!1;e.canFocus&&(t=t||e)}),t&&t.focus(),r;n.focusFirst(r)},replace:function(e,t){for(var n,i=this.items(),r=i.length;r--;)if(i[r]===e){i[r]=t;break}0<=r&&((n=t.getEl())&&n.parentNode.removeChild(n),(n=e.getEl())&&n.parentNode.removeChild(n)),t.parent(this)},create:function(e){var t,n=this,i=[];return w.isArray(e)||(e=[e]),w.each(e,function(e){e&&(e instanceof ct||("string"==typeof e&&(e={type:e}),t=w.extend({},n.settings.defaults,e),e.type=t.type=t.type||e.type||n.settings.defaultType||(t.defaults?t.defaults.type:null),e=v.create(t)),i.push(e))}),i},renderNew:function(){var i=this;return i.items().each(function(e,t){var n;e.parent(i),e.state.get("rendered")||((n=i.getEl("body")).hasChildNodes()&&t<=n.childNodes.length-1?ke(n.childNodes[t]).before(e.renderHtml()):ke(n).append(e.renderHtml()),e.postRender(),it.add(e))}),i._layout.applyClasses(i.items().filter(":visible")),i._lastRect=null,i},append:function(e){return this.add(e).renderNew()},prepend:function(e){return this.items().set(this.create(e).concat(this.items().toArray())),this.renderNew()},insert:function(e,t,n){var i,r,o;return e=this.create(e),i=this.items(),!n&&t<i.length-1&&(t+=1),0<=t&&t<i.length&&(r=i.slice(0,t).toArray(),o=i.slice(t).toArray(),i.set(r.concat(e,o))),this.renderNew()},fromJSON:function(e){for(var t in e)this.find("#"+t).value(e[t]);return this},toJSON:function(){var i={};return this.find("*").each(function(e){var t=e.name(),n=e.value();t&&void 0!==n&&(i[t]=n)}),i},renderHtml:function(){var e=this,t=e._layout,n=this.settings.role;return e.preRender(),t.preRender(e),'<div id="'+e._id+'" class="'+e.classes+'"'+(n?' role="'+this.settings.role+'"':"")+'><div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+(e.settings.html||"")+t.renderHtml(e)+"</div></div>"},postRender:function(){var e,t=this;return t.items().exec("postRender"),t._super(),t._layout.postRender(t),t.state.set("rendered",!0),t.settings.style&&t.$el.css(t.settings.style),t.settings.border&&(e=t.borderBox,t.$el.css({"border-top-width":e.top,"border-right-width":e.right,"border-bottom-width":e.bottom,"border-left-width":e.left})),t.parent()||(t.keyboardNav=ft({root:t})),t},initLayoutRect:function(){var e=this._super();return this._layout.recalc(this),e},recalc:function(){var e=this,t=e._layoutRect,n=e._lastRect;if(!n||n.w!==t.w||n.h!==t.h)return e._layout.recalc(e),t=e.layoutRect(),e._lastRect={x:t.x,y:t.y,w:t.w,h:t.h},!0},reflow:function(){var e;if(it.remove(this),this.visible()){for(ct.repaintControls=[],ct.repaintControls.map={},this.recalc(),e=ct.repaintControls.length;e--;)ct.repaintControls[e].repaint();"flow"!==this.settings.layout&&"stack"!==this.settings.layout&&this.repaint(),ct.repaintControls=[]}return this}});function gt(e){var t,n;if(e.changedTouches)for(t="screenX screenY pageX pageY clientX clientY".split(" "),n=0;n<t.length;n++)e[t[n]]=e.changedTouches[0][t[n]]}function pt(e,h){var m,g,t,p,v,b,y,x=h.document||document;h=h||{};var w=x.getElementById(h.handle||e);t=function(e){var t,n,i,r,o,s,a,l,u,c,d,f=(t=x,u=Math.max,n=t.documentElement,i=t.body,r=u(n.scrollWidth,i.scrollWidth),o=u(n.clientWidth,i.clientWidth),s=u(n.offsetWidth,i.offsetWidth),a=u(n.scrollHeight,i.scrollHeight),l=u(n.clientHeight,i.clientHeight),{width:r<s?o:r,height:a<u(n.offsetHeight,i.offsetHeight)?l:a});gt(e),e.preventDefault(),g=e.button,c=w,b=e.screenX,y=e.screenY,d=window.getComputedStyle?window.getComputedStyle(c,null).getPropertyValue("cursor"):c.runtimeStyle.cursor,m=ke("<div></div>").css({position:"absolute",top:0,left:0,width:f.width,height:f.height,zIndex:2147483647,opacity:1e-4,cursor:d}).appendTo(x.body),ke(x).on("mousemove touchmove",v).on("mouseup touchend",p),h.start(e)},v=function(e){if(gt(e),e.button!==g)return p(e);e.deltaX=e.screenX-b,e.deltaY=e.screenY-y,e.preventDefault(),h.drag(e)},p=function(e){gt(e),ke(x).off("mousemove touchmove",v).off("mouseup touchend",p),m.remove(),h.stop&&h.stop(e)},this.destroy=function(){ke(w).off()},ke(w).on("mousedown touchstart",t)}var vt,bt,yt,xt,wt={init:function(){this.on("repaint",this.renderScroll)},renderScroll:function(){var p=this;function n(){var m,g,e;function t(e,t,n,i,r,o){var s,a,l,u,c,d,f,h;if(a=p.getEl("scroll"+e)){if(f=t.toLowerCase(),h=n.toLowerCase(),ke(p.getEl("absend")).css(f,p.layoutRect()[i]-1),!r)return void ke(a).css("display","none");ke(a).css("display","block"),s=p.getEl("body"),l=p.getEl("scroll"+e+"t"),u=s["client"+n]-4,c=(u-=m&&g?a["client"+o]:0)/s["scroll"+n],(d={})[f]=s["offset"+t]+2,d[h]=u,ke(a).css(d),(d={})[f]=s["scroll"+t]*c,d[h]=u*c,ke(l).css(d)}}e=p.getEl("body"),m=e.scrollWidth>e.clientWidth,g=e.scrollHeight>e.clientHeight,t("h","Left","Width","contentW",m,"Height"),t("v","Top","Height","contentH",g,"Width")}p.settings.autoScroll&&(p._hasScroll||(p._hasScroll=!0,function(){function e(s,a,l,u,c){var d,e=p._id+"-scroll"+s,t=p.classPrefix;ke(p.getEl()).append('<div id="'+e+'" class="'+t+"scrollbar "+t+"scrollbar-"+s+'"><div id="'+e+'t" class="'+t+'scrollbar-thumb"></div></div>'),p.draghelper=new pt(e+"t",{start:function(){d=p.getEl("body")["scroll"+a],ke("#"+e).addClass(t+"active")},drag:function(e){var t,n,i,r,o=p.layoutRect();n=o.contentW>o.innerW,i=o.contentH>o.innerH,r=p.getEl("body")["client"+l]-4,t=(r-=n&&i?p.getEl("scroll"+s)["client"+c]:0)/p.getEl("body")["scroll"+l],p.getEl("body")["scroll"+a]=d+e["delta"+u]/t},stop:function(){ke("#"+e).removeClass(t+"active")}})}p.classes.add("scroll"),e("v","Top","Height","Y","Width"),e("h","Left","Width","X","Height")}(),p.on("wheel",function(e){var t=p.getEl("body");t.scrollLeft+=10*(e.deltaX||0),t.scrollTop+=10*e.deltaY,n()}),ke(p.getEl("body")).on("scroll",n)),n())}},_t=mt.extend({Defaults:{layout:"fit",containerCls:"panel"},Mixins:[wt],renderHtml:function(){var e=this,t=e._layout,n=e.settings.html;return e.preRender(),t.preRender(e),void 0===n?n='<div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+t.renderHtml(e)+"</div>":("function"==typeof n&&(n=n.call(e)),e._hasBody=!1),'<div id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1" role="group">'+(e._preBodyHtml||"")+n+"</div>"}}),Rt={resizeToContent:function(){this._layoutRect.autoResize=!0,this._lastRect=null,this.reflow()},resizeTo:function(e,t){if(e<=1||t<=1){var n=He.getWindowSize();e=e<=1?e*n.w:e,t=t<=1?t*n.h:t}return this._layoutRect.autoResize=!1,this.layoutRect({minW:e,minH:t,w:e,h:t}).reflow()},resizeBy:function(e,t){var n=this.layoutRect();return this.resizeTo(n.w+e,n.h+t)}},Ct=[],kt=[];function Et(e,t){for(;e;){if(e===t)return!0;e=e.parent()}}function Ht(){vt||(vt=function(e){2!==e.button&&function(e){for(var t=Ct.length;t--;){var n=Ct[t],i=n.getParentCtrl(e.target);if(n.settings.autohide){if(i&&(Et(i,n)||n.parent()===i))continue;(e=n.fire("autohide",{target:e.target})).isDefaultPrevented()||n.hide()}}}(e)},ke(document).on("click touchstart",vt))}function St(r){var e=He.getViewPort().y;function t(e,t){for(var n,i=0;i<Ct.length;i++)if(Ct[i]!==r)for(n=Ct[i].parent();n&&(n=n.parent());)n===r&&Ct[i].fixed(e).moveBy(0,t).repaint()}r.settings.autofix&&(r.state.get("fixed")?r._autoFixY>e&&(r.fixed(!1).layoutRect({y:r._autoFixY}).repaint(),t(!1,r._autoFixY-e)):(r._autoFixY=r.layoutRect().y,r._autoFixY<e&&(r.fixed(!0).layoutRect({y:0}).repaint(),t(!0,e-r._autoFixY))))}function Mt(e,t){var n,i,r=Tt.zIndex||65535;if(e)kt.push(t);else for(n=kt.length;n--;)kt[n]===t&&kt.splice(n,1);if(kt.length)for(n=0;n<kt.length;n++)kt[n].modal&&(r++,i=kt[n]),kt[n].getEl().style.zIndex=r,kt[n].zIndex=r,r++;var o=ke("#"+t.classPrefix+"modal-block",t.getContainerElm())[0];i?ke(o).css("z-index",i.zIndex-1):o&&(o.parentNode.removeChild(o),xt=!1),Tt.currentZIndex=r}var Tt=_t.extend({Mixins:[Ne,Rt],init:function(e){var i=this;i._super(e),(i._eventsRoot=i).classes.add("floatpanel"),e.autohide&&(Ht(),function(){if(!yt){var e=document.documentElement,t=e.clientWidth,n=e.clientHeight;yt=function(){document.all&&t===e.clientWidth&&n===e.clientHeight||(t=e.clientWidth,n=e.clientHeight,Tt.hideAll())},ke(window).on("resize",yt)}}(),Ct.push(i)),e.autofix&&(bt||(bt=function(){var e;for(e=Ct.length;e--;)St(Ct[e])},ke(window).on("scroll",bt)),i.on("move",function(){St(this)})),i.on("postrender show",function(e){if(e.control===i){var t,n=i.classPrefix;i.modal&&!xt&&((t=ke("#"+n+"modal-block",i.getContainerElm()))[0]||(t=ke('<div id="'+n+'modal-block" class="'+n+"reset "+n+'fade"></div>').appendTo(i.getContainerElm())),u.setTimeout(function(){t.addClass(n+"in"),ke(i.getEl()).addClass(n+"in")}),xt=!0),Mt(!0,i)}}),i.on("show",function(){i.parents().each(function(e){if(e.state.get("fixed"))return i.fixed(!0),!1})}),e.popover&&(i._preBodyHtml='<div class="'+i.classPrefix+'arrow"></div>',i.classes.add("popover").add("bottom").add(i.isRtl()?"end":"start")),i.aria("label",e.ariaLabel),i.aria("labelledby",i._id),i.aria("describedby",i.describedBy||i._id+"-none")},fixed:function(e){var t=this;if(t.state.get("fixed")!==e){if(t.state.get("rendered")){var n=He.getViewPort();e?t.layoutRect().y-=n.y:t.layoutRect().y+=n.y}t.classes.toggle("fixed",e),t.state.set("fixed",e)}return t},show:function(){var e,t=this._super();for(e=Ct.length;e--&&Ct[e]!==this;);return-1===e&&Ct.push(this),t},hide:function(){return Pt(this),Mt(!1,this),this._super()},hideAll:function(){Tt.hideAll()},close:function(){return this.fire("close").isDefaultPrevented()||(this.remove(),Mt(!1,this)),this},remove:function(){Pt(this),this._super()},postRender:function(){return this.settings.bodyRole&&this.getEl("body").setAttribute("role",this.settings.bodyRole),this._super()}});function Pt(e){var t;for(t=Ct.length;t--;)Ct[t]===e&&Ct.splice(t,1);for(t=kt.length;t--;)kt[t]===e&&kt.splice(t,1)}Tt.hideAll=function(){for(var e=Ct.length;e--;){var t=Ct[e];t&&t.settings.autohide&&(t.hide(),Ct.splice(e,1))}};var Wt=function(e,t){return!(!e||t.settings.ui_container)},Nt=function(s,e,t){var a,n,l=p.DOM,i=s.getParam("fixed_toolbar_container");i&&(n=l.select(i)[0]);var r=function(){if(a&&a.moveRel&&a.visible()&&!a._fixed){var e=s.selection.getScrollContainer(),t=s.getBody(),n=0,i=0;if(e){var r=l.getPos(t),o=l.getPos(e);n=Math.max(0,o.x-r.x),i=Math.max(0,o.y-r.y)}a.fixed(!1).moveRel(t,s.rtl?["tr-br","br-tr"]:["tl-bl","bl-tl","tr-br"]).moveBy(n,i)}},o=function(){a&&(a.show(),r(),l.addClass(s.getBody(),"mce-edit-focus"))},u=function(){a&&(a.hide(),Tt.hideAll(),l.removeClass(s.getBody(),"mce-edit-focus"))},c=function(){a?a.visible()||o():(a=e.panel=v.create({type:n?"panel":"floatpanel",role:"application",classes:"tinymce tinymce-inline",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:Wt(n,s),fixed:Wt(n,s),border:1,items:[!1===d(s)?null:{type:"menubar",border:"0 0 1 0",items:ce(s)},z(s,f(s))]}),A.setUiContainer(s,a),x(s),n?a.renderTo(n).reflow():a.renderTo().reflow(),_(s,a),o(),V(s),s.on("nodeChange",r),s.on("ResizeWindow",r),s.on("activate",o),s.on("deactivate",u),s.nodeChanged())};return s.settings.content_editable=!0,s.on("focus",function(){!1===m(s)&&t.skinUiCss?l.styleSheetLoader.load(t.skinUiCss,c,c):c()}),s.on("blur hide",u),s.on("remove",function(){a&&(a.remove(),a=null)}),!1===m(s)&&t.skinUiCss?l.styleSheetLoader.load(t.skinUiCss,we(s)):we(s)(),{}};function Dt(i,r){var o,s,a=this,l=ct.classPrefix;a.show=function(e,t){function n(){o&&(ke(i).append('<div class="'+l+"throbber"+(r?" "+l+"throbber-inline":"")+'"></div>'),t&&t())}return a.hide(),o=!0,e?s=u.setTimeout(n,e):n(),a},a.hide=function(){var e=i.lastChild;return u.clearTimeout(s),e&&-1!==e.className.indexOf("throbber")&&e.parentNode.removeChild(e),o=!1,a}}var At=function(e,t){var n;e.on("ProgressState",function(e){n=n||new Dt(t.panel.getEl("body")),e.state?n.show(e.time):n.hide()})},Bt=function(e,t,n){var i=function(e){var t=e.settings,n=t.skin,i=t.skin_url;if(!1!==n){var r=n||"lightgray";i=i?e.documentBaseURI.toAbsolute(i):l.baseURL+"/skins/"+r}return i}(e);return i&&(n.skinUiCss=i+"/skin.min.css",e.contentCSS.push(i+"/content"+(e.inline?".inline":"")+".min.css")),At(e,t),e.getParam("inline",!1,"boolean")?Nt(e,t,n):Ce(e,t,n)},Ot=ct.extend({Mixins:[Ne],Defaults:{classes:"widget tooltip tooltip-n"},renderHtml:function(){var e=this,t=e.classPrefix;return'<div id="'+e._id+'" class="'+e.classes+'" role="presentation"><div class="'+t+'tooltip-arrow"></div><div class="'+t+'tooltip-inner">'+e.encode(e.state.get("text"))+"</div></div>"},bindStates:function(){var t=this;return t.state.on("change:text",function(e){t.getEl().lastChild.innerHTML=t.encode(e.value)}),t._super()},repaint:function(){var e,t;e=this.getEl().style,t=this._layoutRect,e.left=t.x+"px",e.top=t.y+"px",e.zIndex=131070}}),zt=ct.extend({init:function(i){var r=this;r._super(i),i=r.settings,r.canFocus=!0,i.tooltip&&!1!==zt.tooltips&&(r.on("mouseenter",function(e){var t=r.tooltip().moveTo(-65535);if(e.control===r){var n=t.text(i.tooltip).show().testMoveRel(r.getEl(),["bc-tc","bc-tl","bc-tr"]);t.classes.toggle("tooltip-n","bc-tc"===n),t.classes.toggle("tooltip-nw","bc-tl"===n),t.classes.toggle("tooltip-ne","bc-tr"===n),t.moveRel(r.getEl(),n)}else t.hide()}),r.on("mouseleave mousedown click",function(){r.tooltip().remove(),r._tooltip=null})),r.aria("label",i.ariaLabel||i.tooltip)},tooltip:function(){return this._tooltip||(this._tooltip=new Ot({type:"tooltip"}),A.inheritUiContainer(this,this._tooltip),this._tooltip.renderTo()),this._tooltip},postRender:function(){var e=this,t=e.settings;e._super(),e.parent()||!t.width&&!t.height||(e.initLayoutRect(),e.repaint()),t.autofocus&&e.focus()},bindStates:function(){var t=this;function n(e){t.aria("disabled",e),t.classes.toggle("disabled",e)}function i(e){t.aria("pressed",e),t.classes.toggle("active",e)}return t.state.on("change:disabled",function(e){n(e.value)}),t.state.on("change:active",function(e){i(e.value)}),t.state.get("disabled")&&n(!0),t.state.get("active")&&i(!0),t._super()},remove:function(){this._super(),this._tooltip&&(this._tooltip.remove(),this._tooltip=null)}}),Lt=zt.extend({Defaults:{value:0},init:function(e){this._super(e),this.classes.add("progress"),this.settings.filter||(this.settings.filter=function(e){return Math.round(e)})},renderHtml:function(){var e=this._id,t=this.classPrefix;return'<div id="'+e+'" class="'+this.classes+'"><div class="'+t+'bar-container"><div class="'+t+'bar"></div></div><div class="'+t+'text">0%</div></div>'},postRender:function(){return this._super(),this.value(this.settings.value),this},bindStates:function(){var t=this;function n(e){e=t.settings.filter(e),t.getEl().lastChild.innerHTML=e+"%",t.getEl().firstChild.firstChild.style.width=e+"%"}return t.state.on("change:value",function(e){n(e.value)}),n(t.state.get("value")),t._super()}}),It=function(e,t){e.getEl().lastChild.textContent=t+(e.progressBar?" "+e.progressBar.value()+"%":"")},Ft=ct.extend({Mixins:[Ne],Defaults:{classes:"widget notification"},init:function(e){var t=this;t._super(e),t.maxWidth=e.maxWidth,e.text&&t.text(e.text),e.icon&&(t.icon=e.icon),e.color&&(t.color=e.color),e.type&&t.classes.add("notification-"+e.type),e.timeout&&(e.timeout<0||0<e.timeout)&&!e.closeButton?t.closeButton=!1:(t.classes.add("has-close"),t.closeButton=!0),e.progressBar&&(t.progressBar=new Lt),t.on("click",function(e){-1!==e.target.className.indexOf(t.classPrefix+"close")&&t.close()})},renderHtml:function(){var e,t=this,n=t.classPrefix,i="",r="",o="";return t.icon&&(i='<i class="'+n+"ico "+n+"i-"+t.icon+'"></i>'),e=' style="max-width: '+t.maxWidth+"px;"+(t.color?"background-color: "+t.color+';"':'"'),t.closeButton&&(r='<button type="button" class="'+n+'close" aria-hidden="true">\xd7</button>'),t.progressBar&&(o=t.progressBar.renderHtml()),'<div id="'+t._id+'" class="'+t.classes+'"'+e+' role="presentation">'+i+'<div class="'+n+'notification-inner">'+t.state.get("text")+"</div>"+o+r+'<div style="clip: rect(1px, 1px, 1px, 1px);height: 1px;overflow: hidden;position: absolute;width: 1px;" aria-live="assertive" aria-relevant="additions" aria-atomic="true"></div></div>'},postRender:function(){var e=this;return u.setTimeout(function(){e.$el.addClass(e.classPrefix+"in"),It(e,e.state.get("text"))},100),e._super()},bindStates:function(){var t=this;return t.state.on("change:text",function(e){t.getEl().firstChild.innerHTML=e.value,It(t,e.value)}),t.progressBar&&(t.progressBar.bindStates(),t.progressBar.state.on("change:value",function(e){It(t,t.state.get("text"))})),t._super()},close:function(){return this.fire("close").isDefaultPrevented()||this.remove(),this},repaint:function(){var e,t;e=this.getEl().style,t=this._layoutRect,e.left=t.x+"px",e.top=t.y+"px",e.zIndex=65534}});function Ut(o){var s=function(e){return e.inline?e.getElement():e.getContentAreaContainer()};return{open:function(e,t){var n,i=w.extend(e,{maxWidth:(n=s(o),He.getSize(n).width)}),r=new Ft(i);return 0<(r.args=i).timeout&&(r.timer=setTimeout(function(){r.close(),t()},i.timeout)),r.on("close",function(){t()}),r.renderTo(),r},close:function(e){e.close()},reposition:function(e){var t;t=e,oe.each(t,function(e){e.moveTo(0,0)}),function(n){if(0<n.length){var e=n.slice(0,1)[0],t=s(o);e.moveRel(t,"tc-tc"),oe.each(n,function(e,t){0<t&&e.moveRel(n[t-1].getEl(),"bc-tc")})}}(e)},getArgs:function(e){return e.args}}}var Vt=[],jt="";function qt(e){var t,n=ke("meta[name=viewport]")[0];!1!==pe.overrideViewPort&&(n||((n=document.createElement("meta")).setAttribute("name","viewport"),document.getElementsByTagName("head")[0].appendChild(n)),(t=n.getAttribute("content"))&&void 0!==jt&&(jt=t),n.setAttribute("content",e?"width=device-width,initial-scale=1.0,user-scalable=0,minimum-scale=1.0,maximum-scale=1.0":jt))}function Yt(e,t){(function(){for(var e=0;e<Vt.length;e++)if(Vt[e]._fullscreen)return!0;return!1})()&&!1===t&&ke([document.documentElement,document.body]).removeClass(e+"fullscreen")}var $t=Tt.extend({modal:!0,Defaults:{border:1,layout:"flex",containerCls:"panel",role:"dialog",callbacks:{submit:function(){this.fire("submit",{data:this.toJSON()})},close:function(){this.close()}}},init:function(e){var n=this;n._super(e),n.isRtl()&&n.classes.add("rtl"),n.classes.add("window"),n.bodyClasses.add("window-body"),n.state.set("fixed",!0),e.buttons&&(n.statusbar=new _t({layout:"flex",border:"1 0 0 0",spacing:3,padding:10,align:"center",pack:n.isRtl()?"start":"end",defaults:{type:"button"},items:e.buttons}),n.statusbar.classes.add("foot"),n.statusbar.parent(n)),n.on("click",function(e){var t=n.classPrefix+"close";(He.hasClass(e.target,t)||He.hasClass(e.target.parentNode,t))&&n.close()}),n.on("cancel",function(){n.close()}),n.on("move",function(e){e.control===n&&Tt.hideAll()}),n.aria("describedby",n.describedBy||n._id+"-none"),n.aria("label",e.title),n._fullscreen=!1},recalc:function(){var e,t,n,i,r=this,o=r.statusbar;r._fullscreen&&(r.layoutRect(He.getWindowSize()),r.layoutRect().contentH=r.layoutRect().innerH),r._super(),e=r.layoutRect(),r.settings.title&&!r._fullscreen&&(t=e.headerW)>e.w&&(n=e.x-Math.max(0,t/2),r.layoutRect({w:t,x:n}),i=!0),o&&(o.layoutRect({w:r.layoutRect().innerW}).recalc(),(t=o.layoutRect().minW+e.deltaW)>e.w&&(n=e.x-Math.max(0,t-e.w),r.layoutRect({w:t,x:n}),i=!0)),i&&r.recalc()},initLayoutRect:function(){var e,t=this,n=t._super(),i=0;if(t.settings.title&&!t._fullscreen){e=t.getEl("head");var r=He.getSize(e);n.headerW=r.width,n.headerH=r.height,i+=n.headerH}t.statusbar&&(i+=t.statusbar.layoutRect().h),n.deltaH+=i,n.minH+=i,n.h+=i;var o=He.getWindowSize();return n.x=t.settings.x||Math.max(0,o.w/2-n.w/2),n.y=t.settings.y||Math.max(0,o.h/2-n.h/2),n},renderHtml:function(){var e=this,t=e._layout,n=e._id,i=e.classPrefix,r=e.settings,o="",s="",a=r.html;return e.preRender(),t.preRender(e),r.title&&(o='<div id="'+n+'-head" class="'+i+'window-head"><div id="'+n+'-title" class="'+i+'title">'+e.encode(r.title)+'</div><div id="'+n+'-dragh" class="'+i+'dragh"></div><button type="button" class="'+i+'close" aria-hidden="true"><i class="mce-ico mce-i-remove"></i></button></div>'),r.url&&(a='<iframe src="'+r.url+'" tabindex="-1"></iframe>'),void 0===a&&(a=t.renderHtml(e)),e.statusbar&&(s=e.statusbar.renderHtml()),'<div id="'+n+'" class="'+e.classes+'" hidefocus="1"><div class="'+e.classPrefix+'reset" role="application">'+o+'<div id="'+n+'-body" class="'+e.bodyClasses+'">'+a+"</div>"+s+"</div></div>"},fullscreen:function(e){var n,t,i=this,r=document.documentElement,o=i.classPrefix;if(e!==i._fullscreen)if(ke(window).on("resize",function(){var e;if(i._fullscreen)if(n)i._timer||(i._timer=u.setTimeout(function(){var e=He.getWindowSize();i.moveTo(0,0).resizeTo(e.w,e.h),i._timer=0},50));else{e=(new Date).getTime();var t=He.getWindowSize();i.moveTo(0,0).resizeTo(t.w,t.h),50<(new Date).getTime()-e&&(n=!0)}}),t=i.layoutRect(),i._fullscreen=e){i._initial={x:t.x,y:t.y,w:t.w,h:t.h},i.borderBox=Be("0"),i.getEl("head").style.display="none",t.deltaH-=t.headerH+2,ke([r,document.body]).addClass(o+"fullscreen"),i.classes.add("fullscreen");var s=He.getWindowSize();i.moveTo(0,0).resizeTo(s.w,s.h)}else i.borderBox=Be(i.settings.border),i.getEl("head").style.display="",t.deltaH+=t.headerH,ke([r,document.body]).removeClass(o+"fullscreen"),i.classes.remove("fullscreen"),i.moveTo(i._initial.x,i._initial.y).resizeTo(i._initial.w,i._initial.h);return i.reflow()},postRender:function(){var t,n=this;setTimeout(function(){n.classes.add("in"),n.fire("open")},0),n._super(),n.statusbar&&n.statusbar.postRender(),n.focus(),this.dragHelper=new pt(n._id+"-dragh",{start:function(){t={x:n.layoutRect().x,y:n.layoutRect().y}},drag:function(e){n.moveTo(t.x+e.deltaX,t.y+e.deltaY)}}),n.on("submit",function(e){e.isDefaultPrevented()||n.close()}),Vt.push(n),qt(!0)},submit:function(){return this.fire("submit",{data:this.toJSON()})},remove:function(){var e,t=this;for(t.dragHelper.destroy(),t._super(),t.statusbar&&this.statusbar.remove(),Yt(t.classPrefix,!1),e=Vt.length;e--;)Vt[e]===t&&Vt.splice(e,1);qt(0<Vt.length)},getContentWindow:function(){var e=this.getEl().getElementsByTagName("iframe")[0];return e?e.contentWindow:null}});!function(){if(!pe.desktop){var n={w:window.innerWidth,h:window.innerHeight};u.setInterval(function(){var e=window.innerWidth,t=window.innerHeight;n.w===e&&n.h===t||(n={w:e,h:t},ke(window).trigger("resize"))},100)}ke(window).on("resize",function(){var e,t,n=He.getWindowSize();for(e=0;e<Vt.length;e++)t=Vt[e].layoutRect(),Vt[e].moveTo(Vt[e].settings.x||Math.max(0,n.w/2-t.w/2),Vt[e].settings.y||Math.max(0,n.h/2-t.h/2))})}();var Xt,Jt=$t.extend({init:function(e){e={border:1,padding:20,layout:"flex",pack:"center",align:"center",containerCls:"panel",autoScroll:!0,buttons:{type:"button",text:"Ok",action:"ok"},items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200}},this._super(e)},Statics:{OK:1,OK_CANCEL:2,YES_NO:3,YES_NO_CANCEL:4,msgBox:function(e){var t,i=e.callback||function(){};function n(e,t,n){return{type:"button",text:e,subtype:n?"primary":"",onClick:function(e){e.control.parents()[1].close(),i(t)}}}switch(e.buttons){case Jt.OK_CANCEL:t=[n("Ok",!0,!0),n("Cancel",!1)];break;case Jt.YES_NO:case Jt.YES_NO_CANCEL:t=[n("Yes",1,!0),n("No",0)],e.buttons===Jt.YES_NO_CANCEL&&t.push(n("Cancel",-1));break;default:t=[n("Ok",!0,!0)]}return new $t({padding:20,x:e.x,y:e.y,minWidth:300,minHeight:100,layout:"flex",pack:"center",align:"center",buttons:t,title:e.title,role:"alertdialog",items:{type:"label",multiline:!0,maxWidth:500,maxHeight:200,text:e.text},onPostRender:function(){this.aria("describedby",this.items()[0]._id)},onClose:e.onClose,onCancel:function(){i(!1)}}).renderTo(document.body).reflow()},alert:function(e,t){return"string"==typeof e&&(e={text:e}),e.callback=t,Jt.msgBox(e)},confirm:function(e,t){return"string"==typeof e&&(e={text:e}),e.callback=t,e.buttons=Jt.OK_CANCEL,Jt.msgBox(e)}}}),Gt=function(n){return{renderUI:function(e){return Bt(n,this,e)},resizeTo:function(e,t){return me(n,e,t)},resizeBy:function(e,t){return ge(n,e,t)},getNotificationManagerImpl:function(){return Ut(n)},getWindowManagerImpl:function(){return{open:function(n,e,t){var i;return n.title=n.title||" ",n.url=n.url||n.file,n.url&&(n.width=parseInt(n.width||320,10),n.height=parseInt(n.height||240,10)),n.body&&(n.items={defaults:n.defaults,type:n.bodyType||"form",items:n.body,data:n.data,callbacks:n.commands}),n.url||n.buttons||(n.buttons=[{text:"Ok",subtype:"primary",onclick:function(){i.find("form")[0].submit()}},{text:"Cancel",onclick:function(){i.close()}}]),(i=new $t(n)).on("close",function(){t(i)}),n.data&&i.on("postRender",function(){this.find("*").each(function(e){var t=e.name();t in n.data&&e.value(n.data[t])})}),i.features=n||{},i.params=e||{},i=i.renderTo(document.body).reflow()},alert:function(e,t,n){var i;return(i=Jt.alert(e,function(){t()})).on("close",function(){n(i)}),i},confirm:function(e,t,n){var i;return(i=Jt.confirm(e,function(e){t(e)})).on("close",function(){n(i)}),i},close:function(e){e.close()},getParams:function(e){return e.params},setParams:function(e,t){e.params=t}}}}},Kt=De.extend({Defaults:{firstControlClass:"first",lastControlClass:"last"},init:function(e){this.settings=w.extend({},this.Defaults,e)},preRender:function(e){e.bodyClasses.add(this.settings.containerClass)},applyClasses:function(e){var t,n,i,r,o=this.settings;t=o.firstControlClass,n=o.lastControlClass,e.each(function(e){e.classes.remove(t).remove(n).add(o.controlClass),e.visible()&&(i||(i=e),r=e)}),i&&i.classes.add(t),r&&r.classes.add(n)},renderHtml:function(e){var t="";return this.applyClasses(e.items()),e.items().each(function(e){t+=e.renderHtml()}),t},recalc:function(){},postRender:function(){},isNative:function(){return!1}}),Zt=Kt.extend({Defaults:{containerClass:"abs-layout",controlClass:"abs-layout-item"},recalc:function(e){e.items().filter(":visible").each(function(e){var t=e.settings;e.layoutRect({x:t.x,y:t.y,w:t.w,h:t.h}),e.recalc&&e.recalc()})},renderHtml:function(e){return'<div id="'+e._id+'-absend" class="'+e.classPrefix+'abs-end"></div>'+this._super(e)}}),Qt=zt.extend({Defaults:{classes:"widget btn",role:"button"},init:function(e){var t,n=this;n._super(e),e=n.settings,t=n.settings.size,n.on("click mousedown",function(e){e.preventDefault()}),n.on("touchstart",function(e){n.fire("click",e),e.preventDefault()}),e.subtype&&n.classes.add(e.subtype),t&&n.classes.add("btn-"+t),e.icon&&n.icon(e.icon)},icon:function(e){return arguments.length?(this.state.set("icon",e),this):this.state.get("icon")},repaint:function(){var e,t=this.getEl().firstChild;t&&((e=t.style).width=e.height="100%"),this._super()},renderHtml:function(){var e,t,n=this,i=n._id,r=n.classPrefix,o=n.state.get("icon"),s=n.state.get("text"),a="",l=n.settings;return(e=l.image)?(o="none","string"!=typeof e&&(e=window.getSelection?e[0]:e[1]),e=" style=\"background-image: url('"+e+"')\""):e="",s&&(n.classes.add("btn-has-text"),a='<span class="'+r+'txt">'+n.encode(s)+"</span>"),o=o?r+"ico "+r+"i-"+o:"",t="boolean"==typeof l.active?' aria-pressed="'+l.active+'"':"",'<div id="'+i+'" class="'+n.classes+'" tabindex="-1"'+t+'><button id="'+i+'-button" role="presentation" type="button" tabindex="-1">'+(o?'<i class="'+o+'"'+e+"></i>":"")+a+"</button></div>"},bindStates:function(){var o=this,n=o.$,i=o.classPrefix+"txt";function s(e){var t=n("span."+i,o.getEl());e?(t[0]||(n("button:first",o.getEl()).append('<span class="'+i+'"></span>'),t=n("span."+i,o.getEl())),t.html(o.encode(e))):t.remove(),o.classes.toggle("btn-has-text",!!e)}return o.state.on("change:text",function(e){s(e.value)}),o.state.on("change:icon",function(e){var t=e.value,n=o.classPrefix;t=(o.settings.icon=t)?n+"ico "+n+"i-"+o.settings.icon:"";var i=o.getEl().firstChild,r=i.getElementsByTagName("i")[0];t?(r&&r===i.firstChild||(r=document.createElement("i"),i.insertBefore(r,i.firstChild)),r.className=t):r&&i.removeChild(r),s(o.state.get("text"))}),o._super()}}),en=Qt.extend({init:function(e){e=w.extend({text:"Browse...",multiple:!1,accept:null},e),this._super(e),this.classes.add("browsebutton"),e.multiple&&this.classes.add("multiple")},postRender:function(){var n=this,t=He.create("input",{type:"file",id:n._id+"-browse",accept:n.settings.accept});n._super(),ke(t).on("change",function(e){var t=e.target.files;n.value=function(){return t.length?n.settings.multiple?t:t[0]:null},e.preventDefault(),t.length&&n.fire("change",e)}),ke(t).on("click",function(e){e.stopPropagation()}),ke(n.getEl("button")).on("click",function(e){e.stopPropagation(),t.click()}),n.getEl().appendChild(t)},remove:function(){ke(this.getEl("button")).off(),ke(this.getEl("input")).off(),this._super()}}),tn=mt.extend({Defaults:{defaultType:"button",role:"group"},renderHtml:function(){var e=this,t=e._layout;return e.classes.add("btn-group"),e.preRender(),t.preRender(e),'<div id="'+e._id+'" class="'+e.classes+'"><div id="'+e._id+'-body">'+(e.settings.html||"")+t.renderHtml(e)+"</div></div>"}}),nn=zt.extend({Defaults:{classes:"checkbox",role:"checkbox",checked:!1},init:function(e){var t=this;t._super(e),t.on("click mousedown",function(e){e.preventDefault()}),t.on("click",function(e){e.preventDefault(),t.disabled()||t.checked(!t.checked())}),t.checked(t.settings.checked)},checked:function(e){return arguments.length?(this.state.set("checked",e),this):this.state.get("checked")},value:function(e){return arguments.length?this.checked(e):this.checked()},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix;return'<div id="'+t+'" class="'+e.classes+'" unselectable="on" aria-labelledby="'+t+'-al" tabindex="-1"><i class="'+n+"ico "+n+'i-checkbox"></i><span id="'+t+'-al" class="'+n+'label">'+e.encode(e.state.get("text"))+"</span></div>"},bindStates:function(){var o=this;function t(e){o.classes.toggle("checked",e),o.aria("checked",e)}return o.state.on("change:text",function(e){o.getEl("al").firstChild.data=o.translate(e.value)}),o.state.on("change:checked change:value",function(e){o.fire("change"),t(e.value)}),o.state.on("change:icon",function(e){var t=e.value,n=o.classPrefix;if(void 0===t)return o.settings.icon;t=(o.settings.icon=t)?n+"ico "+n+"i-"+o.settings.icon:"";var i=o.getEl().firstChild,r=i.getElementsByTagName("i")[0];t?(r&&r===i.firstChild||(r=document.createElement("i"),i.insertBefore(r,i.firstChild)),r.className=t):r&&i.removeChild(r)}),o.state.get("checked")&&t(!0),o._super()}}),rn=tinymce.util.Tools.resolve("tinymce.util.VK"),on=zt.extend({init:function(i){var r=this;r._super(i),i=r.settings,r.classes.add("combobox"),r.subinput=!0,r.ariaTarget="inp",i.menu=i.menu||i.values,i.menu&&(i.icon="caret"),r.on("click",function(e){var t=e.target,n=r.getEl();if(ke.contains(n,t)||t===n)for(;t&&t!==n;)t.id&&-1!==t.id.indexOf("-open")&&(r.fire("action"),i.menu&&(r.showMenu(),e.aria&&r.menu.items()[0].focus())),t=t.parentNode}),r.on("keydown",function(e){var t;13===e.keyCode&&"INPUT"===e.target.nodeName&&(e.preventDefault(),r.parents().reverse().each(function(e){if(e.toJSON)return t=e,!1}),r.fire("submit",{data:t.toJSON()}))}),r.on("keyup",function(e){if("INPUT"===e.target.nodeName){var t=r.state.get("value"),n=e.target.value;n!==t&&(r.state.set("value",n),r.fire("autocomplete",e))}}),r.on("mouseover",function(e){var t=r.tooltip().moveTo(-65535);if(r.statusLevel()&&-1!==e.target.className.indexOf(r.classPrefix+"status")){var n=r.statusMessage()||"Ok",i=t.text(n).show().testMoveRel(e.target,["bc-tc","bc-tl","bc-tr"]);t.classes.toggle("tooltip-n","bc-tc"===i),t.classes.toggle("tooltip-nw","bc-tl"===i),t.classes.toggle("tooltip-ne","bc-tr"===i),t.moveRel(e.target,i)}})},statusLevel:function(e){return 0<arguments.length&&this.state.set("statusLevel",e),this.state.get("statusLevel")},statusMessage:function(e){return 0<arguments.length&&this.state.set("statusMessage",e),this.state.get("statusMessage")},showMenu:function(){var e,t=this,n=t.settings;t.menu||((e=n.menu||[]).length?e={type:"menu",items:e}:e.type=e.type||"menu",t.menu=v.create(e).parent(t).renderTo(t.getContainerElm()),t.fire("createmenu"),t.menu.reflow(),t.menu.on("cancel",function(e){e.control===t.menu&&t.focus()}),t.menu.on("show hide",function(e){e.control.items().each(function(e){e.active(e.value()===t.value())})}).fire("show"),t.menu.on("select",function(e){t.value(e.control.value())}),t.on("focusin",function(e){"INPUT"===e.target.tagName.toUpperCase()&&t.menu.hide()}),t.aria("expanded",!0)),t.menu.show(),t.menu.layoutRect({w:t.layoutRect().w}),t.menu.moveRel(t.getEl(),t.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"])},focus:function(){this.getEl("inp").focus()},repaint:function(){var e,t,n=this,i=n.getEl(),r=n.getEl("open"),o=n.layoutRect(),s=0,a=i.firstChild;n.statusLevel()&&"none"!==n.statusLevel()&&(s=parseInt(He.getRuntimeStyle(a,"padding-right"),10)-parseInt(He.getRuntimeStyle(a,"padding-left"),10)),e=r?o.w-He.getSize(r).width-10:o.w-10;var l=document;return l.all&&(!l.documentMode||l.documentMode<=8)&&(t=n.layoutRect().h-2+"px"),ke(a).css({width:e-s,lineHeight:t}),n._super(),n},postRender:function(){var t=this;return ke(this.getEl("inp")).on("change",function(e){t.state.set("value",e.target.value),t.fire("change",e)}),t._super()},renderHtml:function(){var e,t,n,i=this,r=i._id,o=i.settings,s=i.classPrefix,a=i.state.get("value")||"",l="",u="";return"spellcheck"in o&&(u+=' spellcheck="'+o.spellcheck+'"'),o.maxLength&&(u+=' maxlength="'+o.maxLength+'"'),o.size&&(u+=' size="'+o.size+'"'),o.subtype&&(u+=' type="'+o.subtype+'"'),n='<i id="'+r+'-status" class="mce-status mce-ico" style="display: none"></i>',i.disabled()&&(u+=' disabled="disabled"'),(e=o.icon)&&"caret"!==e&&(e=s+"ico "+s+"i-"+o.icon),t=i.state.get("text"),(e||t)&&(l='<div id="'+r+'-open" class="'+s+"btn "+s+'open" tabIndex="-1" role="button"><button id="'+r+'-action" type="button" hidefocus="1" tabindex="-1">'+("caret"!==e?'<i class="'+e+'"></i>':'<i class="'+s+'caret"></i>')+(t?(e?" ":"")+t:"")+"</button></div>",i.classes.add("has-open")),'<div id="'+r+'" class="'+i.classes+'"><input id="'+r+'-inp" class="'+s+'textbox" value="'+i.encode(a,!1)+'" hidefocus="1"'+u+' placeholder="'+i.encode(o.placeholder)+'" />'+n+l+"</div>"},value:function(e){return arguments.length?(this.state.set("value",e),this):(this.state.get("rendered")&&this.state.set("value",this.getEl("inp").value),this.state.get("value"))},showAutoComplete:function(e,i){var r=this;if(0!==e.length){r.menu?r.menu.items().remove():r.menu=v.create({type:"menu",classes:"combobox-menu",layout:"flow"}).parent(r).renderTo(),w.each(e,function(e){var t,n;r.menu.add({text:e.title,url:e.previewUrl,match:i,classes:"menu-item-ellipsis",onclick:(t=e.value,n=e.title,function(){r.fire("selectitem",{title:n,value:t})})})}),r.menu.renderNew(),r.hideMenu(),r.menu.on("cancel",function(e){e.control.parent()===r.menu&&(e.stopPropagation(),r.focus(),r.hideMenu())}),r.menu.on("select",function(){r.focus()});var t=r.layoutRect().w;r.menu.layoutRect({w:t,minW:0,maxW:t}),r.menu.repaint(),r.menu.reflow(),r.menu.show(),r.menu.moveRel(r.getEl(),r.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"])}else r.hideMenu()},hideMenu:function(){this.menu&&this.menu.hide()},bindStates:function(){var r=this;r.state.on("change:value",function(e){r.getEl("inp").value!==e.value&&(r.getEl("inp").value=e.value)}),r.state.on("change:disabled",function(e){r.getEl("inp").disabled=e.value}),r.state.on("change:statusLevel",function(e){var t=r.getEl("status"),n=r.classPrefix,i=e.value;He.css(t,"display","none"===i?"none":""),He.toggleClass(t,n+"i-checkmark","ok"===i),He.toggleClass(t,n+"i-warning","warn"===i),He.toggleClass(t,n+"i-error","error"===i),r.classes.toggle("has-status","none"!==i),r.repaint()}),He.on(r.getEl("status"),"mouseleave",function(){r.tooltip().hide()}),r.on("cancel",function(e){r.menu&&r.menu.visible()&&(e.stopPropagation(),r.hideMenu())});var n=function(e,t){t&&0<t.items().length&&t.items().eq(e)[0].focus()};return r.on("keydown",function(e){var t=e.keyCode;"INPUT"===e.target.nodeName&&(t===rn.DOWN?(e.preventDefault(),r.fire("autocomplete"),n(0,r.menu)):t===rn.UP&&(e.preventDefault(),n(-1,r.menu)))}),r._super()},remove:function(){ke(this.getEl("inp")).off(),this.menu&&this.menu.remove(),this._super()}}),sn=on.extend({init:function(e){var t=this;e.spellcheck=!1,e.onaction&&(e.icon="none"),t._super(e),t.classes.add("colorbox"),t.on("change keyup postrender",function(){t.repaintColor(t.value())})},repaintColor:function(e){var t=this.getEl("open"),n=t?t.getElementsByTagName("i")[0]:null;if(n)try{n.style.background=e}catch(i){}},bindStates:function(){var t=this;return t.state.on("change:value",function(e){t.state.get("rendered")&&t.repaintColor(e.value)}),t._super()}}),an=Qt.extend({showPanel:function(){var t=this,e=t.settings;if(t.classes.add("opened"),t.panel)t.panel.show();else{var n=e.panel;n.type&&(n={layout:"grid",items:n}),n.role=n.role||"dialog",n.popover=!0,n.autohide=!0,n.ariaRoot=!0,t.panel=new Tt(n).on("hide",function(){t.classes.remove("opened")}).on("cancel",function(e){e.stopPropagation(),t.focus(),t.hidePanel()}).parent(t).renderTo(t.getContainerElm()),t.panel.fire("show"),t.panel.reflow()}var i=t.panel.testMoveRel(t.getEl(),e.popoverAlign||(t.isRtl()?["bc-tc","bc-tl","bc-tr"]:["bc-tc","bc-tr","bc-tl","tc-bc","tc-br","tc-bl"]));t.panel.classes.toggle("start","l"===i.substr(-1)),t.panel.classes.toggle("end","r"===i.substr(-1));var r="t"===i.substr(0,1);t.panel.classes.toggle("bottom",!r),t.panel.classes.toggle("top",r),t.panel.moveRel(t.getEl(),i)},hidePanel:function(){this.panel&&this.panel.hide()},postRender:function(){var t=this;return t.aria("haspopup",!0),t.on("click",function(e){e.control===t&&(t.panel&&t.panel.visible()?t.hidePanel():(t.showPanel(),t.panel.focus(!!e.aria)))}),t._super()},remove:function(){return this.panel&&(this.panel.remove(),this.panel=null),this._super()}}),ln=p.DOM,un=an.extend({init:function(e){this._super(e),this.classes.add("splitbtn"),this.classes.add("colorbutton")},color:function(e){return e?(this._color=e,this.getEl("preview").style.backgroundColor=e,this):this._color},resetColor:function(){return this._color=null,this.getEl("preview").style.backgroundColor=null,this},renderHtml:function(){var e=this,t=e._id,n=e.classPrefix,i=e.state.get("text"),r=e.settings.icon?n+"ico "+n+"i-"+e.settings.icon:"",o=e.settings.image?" style=\"background-image: url('"+e.settings.image+"')\"":"",s="";return i&&(e.classes.add("btn-has-text"),s='<span class="'+n+'txt">'+e.encode(i)+"</span>"),'<div id="'+t+'" class="'+e.classes+'" role="button" tabindex="-1" aria-haspopup="true"><button role="presentation" hidefocus="1" type="button" tabindex="-1">'+(r?'<i class="'+r+'"'+o+"></i>":"")+'<span id="'+t+'-preview" class="'+n+'preview"></span>'+s+'</button><button type="button" class="'+n+'open" hidefocus="1" tabindex="-1"> <i class="'+n+'caret"></i></button></div>'},postRender:function(){var t=this,n=t.settings.onclick;return t.on("click",function(e){e.aria&&"down"===e.aria.key||e.control!==t||ln.getParent(e.target,"."+t.classPrefix+"open")||(e.stopImmediatePropagation(),n.call(t,e))}),delete t.settings.onclick,t._super()}}),cn=tinymce.util.Tools.resolve("tinymce.util.Color"),dn=zt.extend({Defaults:{classes:"widget colorpicker"},init:function(e){this._super(e)},postRender:function(){var n,i,r,o,s,a=this,l=a.color();function u(e,t){var n,i,r=He.getPos(e);return n=t.pageX-r.x,i=t.pageY-r.y,{x:n=Math.max(0,Math.min(n/e.clientWidth,1)),y:i=Math.max(0,Math.min(i/e.clientHeight,1))}}function c(e,t){var n=(360-e.h)/360;He.css(r,{top:100*n+"%"}),t||He.css(s,{left:e.s+"%",top:100-e.v+"%"}),o.style.background=cn({s:100,v:100,h:e.h}).toHex(),a.color().parse({s:e.s,v:e.v,h:e.h})}function e(e){var t;t=u(o,e),n.s=100*t.x,n.v=100*(1-t.y),c(n),a.fire("change")}function t(e){var t;t=u(i,e),(n=l.toHsv()).h=360*(1-t.y),c(n,!0),a.fire("change")}i=a.getEl("h"),r=a.getEl("hp"),o=a.getEl("sv"),s=a.getEl("svp"),a._repaint=function(){c(n=l.toHsv())},a._super(),a._svdraghelper=new pt(a._id+"-sv",{start:e,drag:e}),a._hdraghelper=new pt(a._id+"-h",{start:t,drag:t}),a._repaint()},rgb:function(){return this.color().toRgb()},value:function(e){if(!arguments.length)return this.color().toHex();this.color().parse(e),this._rendered&&this._repaint()},color:function(){return this._color||(this._color=cn()),this._color},renderHtml:function(){var e,t=this._id,o=this.classPrefix,s="#ff0000,#ff0080,#ff00ff,#8000ff,#0000ff,#0080ff,#00ffff,#00ff80,#00ff00,#80ff00,#ffff00,#ff8000,#ff0000";return e='<div id="'+t+'-h" class="'+o+'colorpicker-h" style="background: -ms-linear-gradient(top,#ff0000,#ff0080,#ff00ff,#8000ff,#0000ff,#0080ff,#00ffff,#00ff80,#00ff00,#80ff00,#ffff00,#ff8000,#ff0000);background: linear-gradient(to bottom,#ff0000,#ff0080,#ff00ff,#8000ff,#0000ff,#0080ff,#00ffff,#00ff80,#00ff00,#80ff00,#ffff00,#ff8000,#ff0000);">'+function(){var e,t,n,i,r="";for(n="filter:progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr=",e=0,t=(i=s.split(",")).length-1;e<t;e++)r+='<div class="'+o+'colorpicker-h-chunk" style="height:'+100/t+"%;"+n+i[e]+",endColorstr="+i[e+1]+");-ms-"+n+i[e]+",endColorstr="+i[e+1]+')"></div>';return r}()+'<div id="'+t+'-hp" class="'+o+'colorpicker-h-marker"></div></div>','<div id="'+t+'" class="'+this.classes+'"><div id="'+t+'-sv" class="'+o+'colorpicker-sv"><div class="'+o+'colorpicker-overlay1"><div class="'+o+'colorpicker-overlay2"><div id="'+t+'-svp" class="'+o+'colorpicker-selector1"><div class="'+o+'colorpicker-selector2"></div></div></div></div></div>'+e+"</div>"}}),fn=zt.extend({init:function(e){e=w.extend({height:100,text:"Drop an image here",multiple:!1,accept:null},e),this._super(e),this.classes.add("dropzone"),e.multiple&&this.classes.add("multiple")},renderHtml:function(){var e,t,n=this.settings;return e={id:this._id,hidefocus:"1"},t=He.create("div",e,"<span>"+this.translate(n.text)+"</span>"),n.height&&He.css(t,"height",n.height+"px"),n.width&&He.css(t,"width",n.width+"px"),t.className=this.classes,t.outerHTML},postRender:function(){var i=this,e=function(e){e.preventDefault(),i.classes.toggle("dragenter"),i.getEl().className=i.classes};i._super(),i.$el.on("dragover",function(e){e.preventDefault()}),i.$el.on("dragenter",e),i.$el.on("dragleave",e),i.$el.on("drop",function(e){if(e.preventDefault(),!i.state.get("disabled")){var t=function(e){var t=i.settings.accept;if("string"!=typeof t)return e;var n=new RegExp("("+t.split(/\s*,\s*/).join("|")+")$","i");return w.grep(e,function(e){return n.test(e.name)})}(e.dataTransfer.files);i.value=function(){return t.length?i.settings.multiple?t:t[0]:null},t.length&&i.fire("change",e)}})},remove:function(){this.$el.off(),this._super()}}),hn=zt.extend({init:function(e){var n=this;e.delimiter||(e.delimiter="\xbb"),n._super(e),n.classes.add("path"),n.canFocus=!0,n.on("click",function(e){var t;(t=e.target.getAttribute("data-index"))&&n.fire("select",{value:n.row()[t],index:t})}),n.row(n.settings.row)},focus:function(){return this.getEl().firstChild.focus(),this},row:function(e){return arguments.length?(this.state.set("row",e),this):this.state.get("row")},renderHtml:function(){return'<div id="'+this._id+'" class="'+this.classes+'">'+this._getDataPathHtml(this.state.get("row"))+"</div>"},bindStates:function(){var t=this;return t.state.on("change:row",function(e){t.innerHtml(t._getDataPathHtml(e.value))}),t._super()},_getDataPathHtml:function(e){var t,n,i=e||[],r="",o=this.classPrefix;for(t=0,n=i.length;t<n;t++)r+=(0<t?'<div class="'+o+'divider" aria-hidden="true"> '+this.settings.delimiter+" </div>":"")+'<div role="button" class="'+o+"path-item"+(t===n-1?" "+o+"last":"")+'" data-index="'+t+'" tabindex="-1" id="'+this._id+"-"+t+'" aria-level="'+(t+1)+'">'+i[t].name+"</div>";return r||(r='<div class="'+o+'path-item">\xa0</div>'),r}}),mn=hn.extend({postRender:function(){var o=this,s=o.settings.editor;function a(e){if(1===e.nodeType){if("BR"===e.nodeName||e.getAttribute("data-mce-bogus"))return!0;if("bookmark"===e.getAttribute("data-mce-type"))return!0}return!1}return!1!==s.settings.elementpath&&(o.on("select",function(e){s.focus(),s.selection.select(this.row()[e.index].element),s.nodeChanged()}),s.on("nodeChange",function(e){for(var t=[],n=e.parents,i=n.length;i--;)if(1===n[i].nodeType&&!a(n[i])){var r=s.fire("ResolveName",{name:n[i].nodeName.toLowerCase(),target:n[i]});if(r.isDefaultPrevented()||t.push({name:r.name,element:n[i]}),r.isPropagationStopped())break}o.row(t)})),o._super()}}),gn=mt.extend({Defaults:{layout:"flex",align:"center",defaults:{flex:1}},renderHtml:function(){var e=this,t=e._layout,n=e.classPrefix;return e.classes.add("formitem"),t.preRender(e),'<div id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1">'+(e.settings.title?'<div id="'+e._id+'-title" class="'+n+'title">'+e.settings.title+"</div>":"")+'<div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+(e.settings.html||"")+t.renderHtml(e)+"</div></div>"}}),pn=mt.extend({Defaults:{containerCls:"form",layout:"flex",direction:"column",align:"stretch",flex:1,padding:15,labelGap:30,spacing:10,callbacks:{submit:function(){this.submit()}}},preRender:function(){var i=this,e=i.items();i.settings.formItemDefaults||(i.settings.formItemDefaults={layout:"flex",autoResize:"overflow",defaults:{flex:1}}),e.each(function(e){var t,n=e.settings.label;n&&((t=new gn(w.extend({items:{type:"label",id:e._id+"-l",text:n,flex:0,forId:e._id,disabled:e.disabled()}},i.settings.formItemDefaults))).type="formitem",e.aria("labelledby",e._id+"-l"),"undefined"==typeof e.settings.flex&&(e.settings.flex=1),i.replace(e,t),t.add(e))})},submit:function(){return this.fire("submit",{data:this.toJSON()})},postRender:function(){this._super(),this.fromJSON(this.settings.data)},bindStates:function(){var n=this;function e(){var e,t,i=0,r=[];if(!1!==n.settings.labelGapCalc)for(("children"===n.settings.labelGapCalc?n.find("formitem"):n.items()).filter("formitem").each(function(e){var t=e.items()[0],n=t.getEl().clientWidth;i=i<n?n:i,r.push(t)}),t=n.settings.labelGap||0,e=r.length;e--;)r[e].settings.minWidth=i+t}n._super(),n.on("show",e),e()}}),vn=pn.extend({Defaults:{containerCls:"fieldset",layout:"flex",direction:"column",align:"stretch",flex:1,padding:"25 15 5 15",labelGap:30,spacing:10,border:1},renderHtml:function(){var e=this,t=e._layout,n=e.classPrefix;return e.preRender(),t.preRender(e),'<fieldset id="'+e._id+'" class="'+e.classes+'" hidefocus="1" tabindex="-1">'+(e.settings.title?'<legend id="'+e._id+'-title" class="'+n+'fieldset-title">'+e.settings.title+"</legend>":"")+'<div id="'+e._id+'-body" class="'+e.bodyClasses+'">'+(e.settings.html||"")+t.renderHtml(e)+"</div></fieldset>"}}),bn=0,yn=function(e){var t=(new Date).getTime();return e+"_"+Math.floor(1e9*Math.random())+ ++bn+String(t)},xn=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:H.constant(e)}},wn={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",e),"HTML must have a single root node";return xn(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return xn(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return xn(n)},fromDom:xn,fromPoint:function(e,t,n){return N.from(e.dom().elementFromPoint(t,n)).map(xn)}},_n=function(e){var t,n=!1;return function(){return n||(n=!0,t=e.apply(null,arguments)),t}},Rn=9,Cn=1,kn=3,En=function(e){return e.dom().nodeType},Hn=function(t){return function(e){return En(e)===t}},Sn=(Hn(Cn),Hn(kn),Hn(Rn),_n(function(){return Sn(wn.fromDom(document))}),function(e){var t=e.dom().body;if(null===t||t===undefined)throw"Body is not available yet";return wn.fromDom(t)}),Mn=(Xt=Object.keys)===undefined?function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t}:Xt,Tn=function(e,t){for(var n=Mn(e),i=0,r=n.length;i<r;i++){var o=n[i];t(e[o],o,e)}},Pn=function(i,r){var o={};return Tn(i,function(e,t){var n=r(e,t,i);o[n.k]=n.v}),o},Wn=function(e,n){var i=[];return Tn(e,function(e,t){i.push(n(e,t))}),i},Nn=function(e){return Wn(e,function(e){return e})},Dn={bifilter:function(e,n){var i={},r={};return Tn(e,function(e,t){(n(e,t)?i:r)[t]=e}),{t:i,f:r}},each:Tn,map:function(e,i){return Pn(e,function(e,t,n){return{k:t,v:i(e,t,n)}})},mapToArray:Wn,tupleMap:Pn,find:function(e,t){for(var n=Mn(e),i=0,r=n.length;i<r;i++){var o=n[i],s=e[o];if(t(s,o,e))return N.some(s)}return N.none()},keys:Mn,values:Nn,size:function(e){return Nn(e).length}},An=function(e){return e.slice(0).sort()},Bn=function(e,t){throw new Error("All required keys ("+An(e).join(", ")+") were not specified. Specified keys were: "+An(t).join(", ")+".")},On=function(e){throw new Error("Unsupported keys for object: "+An(e).join(", "))},zn=function(t,e){if(!q.isArray(e))throw new Error("The "+t+" fields must be an array. Was: "+e+".");oe.each(e,function(e){if(!q.isString(e))throw new Error("The value "+e+" in the "+t+" fields was not a string.")})},Ln=function(e){var n=An(e);oe.find(n,function(e,t){return t<n.length-1&&e===n[t+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+n.join(", ")+"].")})},In={immutable:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(t.length!==n.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+n.length+" arguments");var i={};return oe.each(t,function(e,t){i[e]=H.constant(n[t])}),i}},immutableBag:function(r,o){var s=r.concat(o);if(0===s.length)throw new Error("You must specify at least one required or optional field.");return zn("required",r),zn("optional",o),Ln(s),function(t){var n=Dn.keys(t);oe.forall(r,function(e){return oe.contains(n,e)})||Bn(r,n);var e=oe.filter(n,function(e){return!oe.contains(s,e)});0<e.length&&On(e);var i={};return oe.each(r,function(e){i[e]=H.constant(t[e])}),oe.each(o,function(e){i[e]=H.constant(Object.prototype.hasOwnProperty.call(t,e)?N.some(t[e]):N.none())}),i}}},Fn=("undefined"!=typeof window?window:Function("return this;")(),function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var i=e[n];if(i.test(t))return i}return undefined}(e,t);if(!n)return{major:0,minor:0};var i=function(e){return Number(t.replace(n,"$"+e))};return Vn(i(1),i(2))}),Un=function(){return Vn(0,0)},Vn=function(e,t){return{major:e,minor:t}},jn={nu:Vn,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?Un():Fn(e,n)},unknown:Un},qn="Firefox",Yn=function(e,t){return function(){return t===e}},$n=function(e){var t=e.current;return{current:t,version:e.version,isEdge:Yn("Edge",t),isChrome:Yn("Chrome",t),isIE:Yn("IE",t),isOpera:Yn("Opera",t),isFirefox:Yn(qn,t),isSafari:Yn("Safari",t)}},Xn={unknown:function(){return $n({current:undefined,version:jn.unknown()})},nu:$n,edge:H.constant("Edge"),chrome:H.constant("Chrome"),ie:H.constant("IE"),opera:H.constant("Opera"),firefox:H.constant(qn),safari:H.constant("Safari")},Jn="Windows",Gn="Android",Kn="Solaris",Zn="FreeBSD",Qn=function(e,t){return function(){return t===e}},ei=function(e){var t=e.current;return{current:t,version:e.version,isWindows:Qn(Jn,t),isiOS:Qn("iOS",t),isAndroid:Qn(Gn,t),isOSX:Qn("OSX",t),isLinux:Qn("Linux",t),isSolaris:Qn(Kn,t),isFreeBSD:Qn(Zn,t)}},ti={unknown:function(){return ei({current:undefined,version:jn.unknown()})},nu:ei,windows:H.constant(Jn),ios:H.constant("iOS"),android:H.constant(Gn),linux:H.constant("Linux"),osx:H.constant("OSX"),solaris:H.constant(Kn),freebsd:H.constant(Zn)},ni=function(e,t){var n=String(t).toLowerCase();return oe.find(e,function(e){return e.search(n)})},ii=function(e,n){return ni(e,n).map(function(e){var t=jn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},ri=function(e,n){return ni(e,n).map(function(e){var t=jn.detect(e.versionRegexes,n);return{current:e.name,version:t}})},oi=function(e,t){return-1!==e.indexOf(t)},si=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ai=function(t){return function(e){return oi(e,t)}},li=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return oi(e,"edge/")&&oi(e,"chrome")&&oi(e,"safari")&&oi(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,si],search:function(e){return oi(e,"chrome")&&!oi(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return oi(e,"msie")||oi(e,"trident")}},{name:"Opera",versionRegexes:[si,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ai("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ai("firefox")},{name:"Safari",versionRegexes:[si,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(oi(e,"safari")||oi(e,"mobile/"))&&oi(e,"applewebkit")}}],ui=[{name:"Windows",search:ai("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return oi(e,"iphone")||oi(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ai("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:ai("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ai("linux"),versionRegexes:[]},{name:"Solaris",search:ai("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ai("freebsd"),versionRegexes:[]}],ci={browsers:H.constant(li),oses:H.constant(ui)},di=function(e){var t,n,i,r,o,s,a,l,u,c,d,f=ci.browsers(),h=ci.oses(),m=ii(f,e).fold(Xn.unknown,Xn.nu),g=ri(h,e).fold(ti.unknown,ti.nu);return{browser:m,os:g,deviceType:(n=m,i=e,r=(t=g).isiOS()&&!0===/ipad/i.test(i),o=t.isiOS()&&!r,s=t.isAndroid()&&3===t.version.major,a=t.isAndroid()&&4===t.version.major,l=r||s||a&&!0===/mobile/i.test(i),u=t.isiOS()||t.isAndroid(),c=u&&!l,d=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(i),{isiPad:H.constant(r),isiPhone:H.constant(o),isTablet:H.constant(l),isPhone:H.constant(c),isTouch:H.constant(u),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:H.constant(d)})}},fi=_n(function(){var e=navigator.userAgent;return di(e)}),hi=Cn,mi=Rn,gi=function(e){return e.nodeType!==hi&&e.nodeType!==mi||0===e.childElementCount},pi={all:function(e,t){var n=t===undefined?document:t.dom();return gi(n)?[]:oe.map(n.querySelectorAll(e),wn.fromDom)},is:function(e,t){var n=e.dom();if(n.nodeType!==hi)return!1;if(n.matches!==undefined)return n.matches(t);if(n.msMatchesSelector!==undefined)return n.msMatchesSelector(t);if(n.webkitMatchesSelector!==undefined)return n.webkitMatchesSelector(t);if(n.mozMatchesSelector!==undefined)return n.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},one:function(e,t){var n=t===undefined?document:t.dom();return gi(n)?N.none():N.from(n.querySelector(e)).map(wn.fromDom)}},vi=(fi().browser.isIE(),In.immutable("element","offset"),function(e,t){return pi.all(t,e)}),bi=w.trim,yi=function(t){return function(e){if(e&&1===e.nodeType){if(e.contentEditable===t)return!0;if(e.getAttribute("data-mce-contenteditable")===t)return!0}return!1}},xi=yi("true"),wi=yi("false"),_i=function(e,t,n,i,r){return{type:e,title:t,url:n,level:i,attach:r}},Ri=function(e){return e.innerText||e.textContent},Ci=function(e){return(t=e)&&"A"===t.nodeName&&(t.id||t.name)&&Ei(e);var t},ki=function(e){return e&&/^(H[1-6])$/.test(e.nodeName)},Ei=function(e){return function(e){for(;e=e.parentNode;){var t=e.contentEditable;if(t&&"inherit"!==t)return xi(e)}return!1}(e)&&!wi(e)},Hi=function(e){return ki(e)&&Ei(e)},Si=function(e){var t,n,i=(t=e).id?t.id:yn("h");return _i("header",Ri(e),"#"+i,ki(n=e)?parseInt(n.nodeName.substr(1),10):0,function(){e.id=i})},Mi=function(e){var t=e.id||e.name,n=Ri(e);return _i("anchor",n||"#"+t,"#"+t,0,H.noop)},Ti=function(e){var t,n;return t="h1,h2,h3,h4,h5,h6,a:not([href])",n=e,oe.map(vi(wn.fromDom(n),t),function(e){return e.dom()})},Pi=function(e){return 0<bi(e.title).length},Wi=function(e){var t,n,i=Ti(e);return oe.filter((n=i,oe.map(oe.filter(n,Hi),Si)).concat((t=i,oe.map(oe.filter(t,Ci),Mi))),Pi)},Ni={},Di=function(e){return{title:e.title,value:{title:{raw:e.title},url:e.url,attach:e.attach}}},Ai=function(e,t){return{title:e,value:{title:e,url:t,attach:H.noop}}},Bi=function(e,t,n){var i=t in e?e[t]:n;return!1===i?null:i},Oi=function(e,i,r,t){var n,o,s,a,l={title:"-"},u=function(e){var t=e.hasOwnProperty(r)?e[r]:[],n=oe.filter(t,function(e){return t=e,n=i,!oe.exists(n,function(e){return e.url===t});var t,n});return w.map(n,function(e){return{title:e,value:{title:e,url:e,attach:H.noop}}})},c=function(t){var e,n=oe.filter(i,function(e){return e.type===t});return e=n,w.map(e,Di)};return!1===t.typeahead_urls?[]:"file"===r?(n=[zi(e,u(Ni)),zi(e,c("header")),zi(e,(o=c("anchor"),s=Bi(t,"anchor_top","#top"),a=Bi(t,"anchor_bottom","#bottom"),null!==s&&o.unshift(Ai("<top>",s)),null!==a&&o.push(Ai("<bottom>",a)),o))],oe.foldl(n,function(e,t){return 0===e.length||0===t.length?e.concat(t):e.concat(l,t)},[])):zi(e,u(Ni))},zi=function(e,t){var n=e.toLowerCase(),i=w.grep(t,function(e){return-1!==e.title.toLowerCase().indexOf(n)});return 1===i.length&&i[0].title===e?[]:i},Li=function(r,i,o,s){var t=function(e){var t=Wi(o),n=Oi(e,t,s,i);r.showAutoComplete(n,e)};r.on("autocomplete",function(){t(r.value())}),r.on("selectitem",function(e){var t=e.value;r.value(t.url);var n,i=(n=t.title).raw?n.raw:n;"image"===s?r.fire("change",{meta:{alt:i,attach:t.attach}}):r.fire("change",{meta:{text:i,attach:t.attach}}),r.focus()}),r.on("click",function(e){0===r.value().length&&"INPUT"===e.target.nodeName&&t("")}),r.on("PostRender",function(){r.getRoot().on("submit",function(e){var t,n,i;e.isDefaultPrevented()||(t=r.value(),i=Ni[n=s],/^https?/.test(t)&&(i?oe.indexOf(i,t).isNone()&&(Ni[n]=i.slice(0,5).concat(t)):Ni[n]=[t]))})})},Ii=function(o,e,n){var i=e.filepicker_validator_handler;i&&o.state.on("change:value",function(e){var t;0!==(t=e.value).length?i({url:t,type:n},function(e){var t,n,i,r=(n=(t=e).status,i=t.message,"valid"===n?{status:"ok",message:i}:"unknown"===n?{status:"warn",message:i}:"invalid"===n?{status:"warn",message:i}:{status:"none",message:""});o.statusMessage(r.message),o.statusLevel(r.status)}):o.statusLevel("none")})},Fi=on.extend({Statics:{clearHistory:function(){Ni={}}},init:function(e){var t,n,i,r=this,o=window.tinymce?window.tinymce.activeEditor:l.activeEditor,s=o.settings,a=e.filetype;e.spellcheck=!1,(i=s.file_picker_types||s.file_browser_callback_types)&&(i=w.makeMap(i,/[, ]/)),i&&!i[a]||(!(n=s.file_picker_callback)||i&&!i[a]?!(n=s.file_browser_callback)||i&&!i[a]||(t=function(){n(r.getEl("inp").id,r.value(),a,window)}):t=function(){var e=r.fire("beforecall").meta;e=w.extend({filetype:a},e),n.call(o,function(e,t){r.value(e).fire("change",{meta:t})},r.value(),e)}),t&&(e.icon="browse",e.onaction=t),r._super(e),r.classes.add("filepicker"),Li(r,s,o.getBody(),a),Ii(r,s,a)}}),Ui=Zt.extend({recalc:function(e){var t=e.layoutRect(),n=e.paddingBox;e.items().filter(":visible").each(function(e){e.layoutRect({x:n.left,y:n.top,w:t.innerW-n.right-n.left,h:t.innerH-n.top-n.bottom}),e.recalc&&e.recalc()})}}),Vi=Zt.extend({recalc:function(e){var t,n,i,r,o,s,a,l,u,c,d,f,h,m,g,p,v,b,y,x,w,_,R,C,k,E,H,S,M,T,P,W,N,D,A,B,O,z=[],L=Math.max,I=Math.min;for(i=e.items().filter(":visible"),r=e.layoutRect(),o=e.paddingBox,s=e.settings,f=e.isRtl()?s.direction||"row-reversed":s.direction,a=s.align,l=e.isRtl()?s.pack||"end":s.pack,u=s.spacing||0,"row-reversed"!==f&&"column-reverse"!==f||(i=i.set(i.toArray().reverse()),f=f.split("-")[0]),"column"===f?(C="y",_="h",R="minH",k="maxH",H="innerH",E="top",S="deltaH",M="contentH",D="left",W="w",T="x",P="innerW",N="minW",A="right",B="deltaW",O="contentW"):(C="x",_="w",R="minW",k="maxW",H="innerW",E="left",S="deltaW",M="contentW",D="top",W="h",T="y",P="innerH",N="minH",A="bottom",B="deltaH",O="contentH"),d=r[H]-o[E]-o[E],w=c=0,t=0,n=i.length;t<n;t++)m=(h=i[t]).layoutRect(),d-=t<n-1?u:0,0<(g=h.settings.flex)&&(c+=g,m[k]&&z.push(h),m.flex=g),d-=m[R],w<(p=o[D]+m[N]+o[A])&&(w=p);if((y={})[R]=d<0?r[R]-d+r[S]:r[H]-d+r[S],y[N]=w+r[B],y[M]=r[H]-d,y[O]=w,y.minW=I(y.minW,r.maxW),y.minH=I(y.minH,r.maxH),y.minW=L(y.minW,r.startMinWidth),y.minH=L(y.minH,r.startMinHeight),!r.autoResize||y.minW===r.minW&&y.minH===r.minH){for(b=d/c,t=0,n=z.length;t<n;t++)(v=(m=(h=z[t]).layoutRect())[k])<(p=m[R]+m.flex*b)?(d-=m[k]-m[R],c-=m.flex,m.flex=0,m.maxFlexSize=v):m.maxFlexSize=0;for(b=d/c,x=o[E],y={},0===c&&("end"===l?x=d+o[E]:"center"===l?(x=Math.round(r[H]/2-(r[H]-d)/2)+o[E])<0&&(x=o[E]):"justify"===l&&(x=o[E],u=Math.floor(d/(i.length-1)))),y[T]=o[D],t=0,n=i.length;t<n;t++)p=(m=(h=i[t]).layoutRect()).maxFlexSize||m[R],"center"===a?y[T]=Math.round(r[P]/2-m[W]/2):"stretch"===a?(y[W]=L(m[N]||0,r[P]-o[D]-o[A]),y[T]=o[D]):"end"===a&&(y[T]=r[P]-m[W]-o.top),0<m.flex&&(p+=m.flex*b),y[_]=p,y[C]=x,h.layoutRect(y),h.recalc&&h.recalc(),x+=p+u}else if(y.w=y.minW,y.h=y.minH,e.layoutRect(y),this.recalc(e),null===e._lastRect){var F=e.parent();F&&(F._lastRect=null,F.recalc())}}}),ji=Kt.extend({Defaults:{containerClass:"flow-layout",controlClass:"flow-layout-item",endClass:"break"},recalc:function(e){e.items().filter(":visible").each(function(e){e.recalc&&e.recalc()})},isNative:function(){return!0}}),qi=function(e,t){return pi.one(t,e)},Yi=function(e,t){return function(){e.execCommand("mceToggleFormat",!1,t)}},$i=function(e,t,n){var i=function(e){n(e,t)};e.formatter?e.formatter.formatChanged(t,i):e.on("init",function(){e.formatter.formatChanged(t,i)})},Xi=function(e,n){return function(t){$i(e,n,function(e){t.control.active(e)})}},Ji=function(i){var t=["alignleft","aligncenter","alignright","alignjustify"],r="alignleft",e=[{text:"Left",icon:"alignleft",onclick:Yi(i,"alignleft")},{text:"Center",icon:"aligncenter",onclick:Yi(i,"aligncenter")},{text:"Right",icon:"alignright",onclick:Yi(i,"alignright")},{text:"Justify",icon:"alignjustify",onclick:Yi(i,"alignjustify")}];i.addMenuItem("align",{text:"Align",menu:e}),i.addButton("align",{type:"menubutton",icon:r,menu:e,onShowMenu:function(e){var n=e.control.menu;w.each(t,function(t,e){n.items().eq(e).each(function(e){return e.active(i.formatter.match(t))})})},onPostRender:function(e){var n=e.control;w.each(t,function(t,e){$i(i,t,function(e){n.icon(r),e&&n.icon(t)})})}}),w.each({alignleft:["Align left","JustifyLeft"],aligncenter:["Align center","JustifyCenter"],alignright:["Align right","JustifyRight"],alignjustify:["Justify","JustifyFull"],alignnone:["No alignment","JustifyNone"]},function(e,t){i.addButton(t,{active:!1,tooltip:e[0],cmd:e[1],onPostRender:Xi(i,t)})})},Gi=function(e){return e?e.split(",")[0]:""},Ki=function(l,u){return function(){var a=this;l.on("init nodeChange",function(e){var t,n,i,r,o=l.queryCommandValue("FontName"),s=(t=u,r=(n=o)?n.toLowerCase():"",w.each(t,function(e){e.value.toLowerCase()===r&&(i=e.value)}),w.each(t,function(e){i||Gi(e.value).toLowerCase()!==Gi(r).toLowerCase()||(i=e.value)}),i);a.value(s||null),!s&&o&&a.text(Gi(o))})}},Zi=function(n){n.addButton("fontselect",function(){var e,t=(e=function(e){for(var t=(e=e.replace(/;$/,"").split(";")).length;t--;)e[t]=e[t].split("=");return e}(n.settings.font_formats||"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"),w.map(e,function(e){return{text:{raw:e[0]},value:e[1],textStyle:-1===e[1].indexOf("dings")?"font-family:"+e[1]:""}}));return{type:"listbox",text:"Font Family",tooltip:"Font Family",values:t,fixedWidth:!0,onPostRender:Ki(n,t),onselect:function(e){e.control.settings.value&&n.execCommand("FontName",!1,e.control.settings.value)}}})},Qi=function(e){Zi(e)},er=function(e,t){return/[0-9.]+px$/.test(e)?(n=72*parseInt(e,10)/96,i=t||0,r=Math.pow(10,i),Math.round(n*r)/r+"pt"):e;var n,i,r},tr=function(e,t,n){var i;return w.each(e,function(e){e.value===n?i=n:e.value===t&&(i=t)}),i},nr=function(n){n.addButton("fontsizeselect",function(){var e,s,a,t=(e=n.settings.fontsize_formats||"10px 11px 12px 14px 16px 18px 20px 24px 36px",w.map(e.split(" "),function(e){var t=e,n=e,i=e.split("=");return 1<i.length&&(t=i[0],n=i[1]),{text:t,value:n}}));return{type:"listbox",text:"Font Sizes",tooltip:"Font Sizes",values:t,fixedWidth:!0,onPostRender:(s=n,a=t,function(){var o=this;s.on("init nodeChange",function(e){var t,n,i,r;if(t=s.queryCommandValue("FontSize"))for(i=3;!r&&0<=i;i--)n=er(t,i),r=tr(a,n,t);o.value(r||null),r||o.text(n)})}),onclick:function(e){e.control.settings.value&&n.execCommand("FontSize",!1,e.control.settings.value)}}})},ir=function(e){nr(e)},rr=function(n,e){var i=e.length;return w.each(e,function(e){e.menu&&(e.hidden=0===rr(n,e.menu));var t=e.format;t&&(e.hidden=!n.formatter.canApply(t)),e.hidden&&i--}),i},or=function(n,e){var i=e.items().length;return e.items().each(function(e){e.menu&&e.visible(0<or(n,e.menu)),!e.menu&&e.settings.menu&&e.visible(0<rr(n,e.settings.menu));var t=e.settings.format;t&&e.visible(n.formatter.canApply(t)),e.visible()||i--}),i},sr=function(e){var i,r,o,t,s,n,a,l,u=(r=0,o=[],t=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],s=function(e){var i=[];if(e)return w.each(e,function(e){var t={text:e.title,icon:e.icon};if(e.items)t.menu=s(e.items);else{var n=e.format||"custom"+r++;e.format||(e.name=n,o.push(e)),t.format=n,t.cmd=e.cmd}i.push(t)}),i},(i=e).on("init",function(){w.each(o,function(e){i.formatter.register(e.name,e)})}),{type:"menu",items:i.settings.style_formats_merge?i.settings.style_formats?s(t.concat(i.settings.style_formats)):s(t):s(i.settings.style_formats||t),onPostRender:function(e){i.fire("renderFormatsMenu",{control:e.control})},itemDefaults:{preview:!0,textStyle:function(){if(this.settings.format)return i.formatter.getCssText(this.settings.format)},onPostRender:function(){var n=this;n.parent().on("show",function(){var e,t;(e=n.settings.format)&&(n.disabled(!i.formatter.canApply(e)),n.active(i.formatter.match(e))),(t=n.settings.cmd)&&n.active(i.queryCommandState(t))})},onclick:function(){this.settings.format&&Yi(i,this.settings.format)(),this.settings.cmd&&i.execCommand(this.settings.cmd)}}});n=u,e.addMenuItem("formats",{text:"Formats",menu:n}),l=u,(a=e).addButton("styleselect",{type:"menubutton",text:"Formats",menu:l,onShowMenu:function(){a.settings.style_formats_autohide&&or(a,this.menu)}})},ar=function(n,e){return function(){var r,o,s,t=[];return w.each(e,function(e){t.push({text:e[0],value:e[1],textStyle:function(){return n.formatter.getCssText(e[1])}})}),{type:"listbox",text:e[0][0],values:t,fixedWidth:!0,onselect:function(e){if(e.control){var t=e.control.value();Yi(n,t)()}},onPostRender:(r=n,o=t,function(){var t=this;r.on("nodeChange",function(e){var n=r.formatter,i=null;w.each(e.parents,function(t){if(w.each(o,function(e){if(s?n.matchNode(t,s,{value:e.value})&&(i=e.value):n.matchNode(t,e.value)&&(i=e.value),i)return!1}),i)return!1}),t.value(i)})})}}},lr=function(e){var t,n,i=function(e){for(var t=(e=e.replace(/;$/,"").split(";")).length;t--;)e[t]=e[t].split("=");return e}(e.settings.block_formats||"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre");e.addMenuItem("blockformats",{text:"Blocks",menu:(t=e,n=i,w.map(n,function(e){return{text:e[0],onclick:Yi(t,e[1]),textStyle:function(){return t.formatter.getCssText(e[1])}}}))}),e.addButton("formatselect",ar(e,i))},ur=function(t,e){var n,i;if("string"==typeof e)i=e.split(" ");else if(w.isArray(e))return oe.flatten(w.map(e,function(e){return ur(t,e)}));return n=w.grep(i,function(e){return"|"===e||e in t.menuItems}),w.map(n,function(e){return"|"===e?{text:"-"}:t.menuItems[e]})},cr=function(e){return e&&"-"===e.text},dr=function(e){var t=oe.filter(e,function(e,t,n){return!cr(e)||!cr(n[t-1])});return oe.filter(t,function(e,t,n){return!cr(e)||0<t&&t<n.length-1})},fr=function(e){var t,n,i,r,o=e.settings.insert_button_items;return dr(o?ur(e,o):(t=e,n="insert",i=[{text:"-"}],r=w.grep(t.menuItems,function(e){return e.context===n}),w.each(r,function(e){"before"===e.separator&&i.push({text:"|"}),e.prependToContext?i.unshift(e):i.push(e),"after"===e.separator&&i.push({text:"|"})}),i))},hr=function(e){var t;(t=e).addButton("insert",{type:"menubutton",icon:"insert",menu:[],oncreatemenu:function(){this.menu.add(fr(t)),this.menu.renderNew()}})},mr=function(e){var n,i,r;n=e,w.each({bold:"Bold",italic:"Italic",underline:"Underline",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript"},function(e,t){n.addButton(t,{active:!1,tooltip:e,onPostRender:Xi(n,t),onclick:Yi(n,t)})}),i=e,w.each({outdent:["Decrease indent","Outdent"],indent:["Increase indent","Indent"],cut:["Cut","Cut"],copy:["Copy","Copy"],paste:["Paste","Paste"],help:["Help","mceHelp"],selectall:["Select all","SelectAll"],visualaid:["Visual aids","mceToggleVisualAid"],newdocument:["New document","mceNewDocument"],removeformat:["Clear formatting","RemoveFormat"],remove:["Remove","Delete"]},function(e,t){i.addButton(t,{tooltip:e[0],cmd:e[1]})}),r=e,w.each({blockquote:["Blockquote","mceBlockQuote"],subscript:["Subscript","Subscript"],superscript:["Superscript","Superscript"]},function(e,t){r.addButton(t,{active:!1,tooltip:e[0],cmd:e[1],onPostRender:Xi(r,t)})})},gr=function(e){var n;mr(e),n=e,w.each({bold:["Bold","Bold","Meta+B"],italic:["Italic","Italic","Meta+I"],underline:["Underline","Underline","Meta+U"],strikethrough:["Strikethrough","Strikethrough"],subscript:["Subscript","Subscript"],superscript:["Superscript","Superscript"],removeformat:["Clear formatting","RemoveFormat"],newdocument:["New document","mceNewDocument"],cut:["Cut","Cut","Meta+X"],copy:["Copy","Copy","Meta+C"],paste:["Paste","Paste","Meta+V"],selectall:["Select all","SelectAll","Meta+A"]},function(e,t){n.addMenuItem(t,{text:e[0],icon:t,shortcut:e[2],cmd:e[1]})}),n.addMenuItem("codeformat",{text:"Code",icon:"code",onclick:Yi(n,"code")})},pr=function(n,i){return function(){var e=this,t=function(){var e="redo"===i?"hasRedo":"hasUndo";return!!n.undoManager&&n.undoManager[e]()};e.disabled(!t()),n.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",function(){e.disabled(n.readonly||!t())})}},vr=function(e){var t,n;(t=e).addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onPostRender:pr(t,"undo"),cmd:"undo"}),t.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onPostRender:pr(t,"redo"),cmd:"redo"}),(n=e).addButton("undo",{tooltip:"Undo",onPostRender:pr(n,"undo"),cmd:"undo"}),n.addButton("redo",{tooltip:"Redo",onPostRender:pr(n,"redo"),cmd:"redo"})},br=function(e){var t,n;(t=e).addMenuItem("visualaid",{text:"Visual aids",selectable:!0,onPostRender:(n=t,function(){var t=this;n.on("VisualAid",function(e){t.active(e.hasVisual)}),t.active(n.hasVisual)}),cmd:"mceToggleVisualAid"})},yr={setup:function(e){var t;e.rtl&&(ct.rtl=!0),e.on("mousedown",function(){Tt.hideAll()}),(t=e).settings.ui_container&&(pe.container=qi(wn.fromDom(document.body),t.settings.ui_container).fold(H.constant(null),function(e){return e.dom()})),zt.tooltips=!pe.iOS,ct.translate=function(e){return l.translate(e)},lr(e),Ji(e),gr(e),vr(e),ir(e),Qi(e),sr(e),br(e),hr(e)}},xr=Zt.extend({recalc:function(e){var t,n,i,r,o,s,a,l,u,c,d,f,h,m,g,p,v,b,y,x,w,_,R,C,k,E,H,S,M=[],T=[];t=e.settings,r=e.items().filter(":visible"),o=e.layoutRect(),i=t.columns||Math.ceil(Math.sqrt(r.length)),n=Math.ceil(r.length/i),b=t.spacingH||t.spacing||0,y=t.spacingV||t.spacing||0,x=t.alignH||t.align,w=t.alignV||t.align,p=e.paddingBox,S="reverseRows"in t?t.reverseRows:e.isRtl(),x&&"string"==typeof x&&(x=[x]),w&&"string"==typeof w&&(w=[w]);for(d=0;d<i;d++)M.push(0);for(f=0;f<n;f++)T.push(0);for(f=0;f<n;f++)for(d=0;d<i&&(c=r[f*i+d]);d++)C=(u=c.layoutRect()).minW,k=u.minH,M[d]=C>M[d]?C:M[d],T[f]=k>T[f]?k:T[f];for(E=o.innerW-p.left-p.right,d=_=0;d<i;d++)_+=M[d]+(0<d?b:0),E-=(0<d?b:0)+M[d];for(H=o.innerH-p.top-p.bottom,f=R=0;f<n;f++)R+=T[f]+(0<f?y:0),H-=(0<f?y:0)+T[f];if(_+=p.left+p.right,R+=p.top+p.bottom,(l={}).minW=_+(o.w-o.innerW),l.minH=R+(o.h-o.innerH),l.contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH,l.minW=Math.min(l.minW,o.maxW),l.minH=Math.min(l.minH,o.maxH),l.minW=Math.max(l.minW,o.startMinWidth),l.minH=Math.max(l.minH,o.startMinHeight),!o.autoResize||l.minW===o.minW&&l.minH===o.minH){var P;o.autoResize&&((l=e.layoutRect(l)).contentW=l.minW-o.deltaW,l.contentH=l.minH-o.deltaH),P="start"===t.packV?0:0<H?Math.floor(H/n):0;var W=0,N=t.flexWidths;if(N)for(d=0;d<N.length;d++)W+=N[d];else W=i;var D=E/W;for(d=0;d<i;d++)M[d]+=N?N[d]*D:D;for(m=p.top,f=0;f<n;f++){for(h=p.left,a=T[f]+P,d=0;d<i&&(c=r[S?f*i+i-1-d:f*i+d]);d++)g=c.settings,u=c.layoutRect(),s=Math.max(M[d],u.startMinWidth),u.x=h,u.y=m,"center"===(v=g.alignH||(x?x[d]||x[0]:null))?u.x=h+s/2-u.w/2:"right"===v?u.x=h+s-u.w:"stretch"===v&&(u.w=s),"center"===(v=g.alignV||(w?w[d]||w[0]:null))?u.y=m+a/2-u.h/2:"bottom"===v?u.y=m+a-u.h:"stretch"===v&&(u.h=a),c.layoutRect(u),h+=s+b,c.recalc&&c.recalc();m+=a+y}}else if(l.w=l.minW,l.h=l.minH,e.layoutRect(l),this.recalc(e),null===e._lastRect){var A=e.parent();A&&(A._lastRect=null,A.recalc())}}}),wr=zt.extend({renderHtml:function(){var e=this;return e.classes.add("iframe"),e.canFocus=!1,'<iframe id="'+e._id+'" class="'+e.classes+'" tabindex="-1" src="'+(e.settings.url||"javascript:''")+'" frameborder="0"></iframe>'},src:function(e){this.getEl().src=e},html:function(e,t){var n=this,i=this.getEl().contentWindow.document.body;return i?(i.innerHTML=e,t&&t()):u.setTimeout(function(){n.html(e)}),this}}),_r=zt.extend({init:function(e){this._super(e),this.classes.add("widget").add("infobox"),this.canFocus=!1},severity:function(e){this.classes.remove("error"),this.classes.remove("warning"),this.classes.remove("success"),this.classes.add(e)},help:function(e){this.state.set("help",e)},renderHtml:function(){var e=this,t=e.classPrefix;return'<div id="'+e._id+'" class="'+e.classes+'"><div id="'+e._id+'-body">'+e.encode(e.state.get("text"))+'<button role="button" tabindex="-1"><i class="'+t+"ico "+t+'i-help"></i></button></div></div>'},bindStates:function(){var t=this;return t.state.on("change:text",function(e){t.getEl("body").firstChild.data=t.encode(e.value),t.state.get("rendered")&&t.updateLayoutRect()}),t.state.on("change:help",function(e){t.classes.toggle("has-help",e.value),t.state.get("rendered")&&t.updateLayoutRect()}),t._super()}}),Rr=zt.extend({init:function(e){var t=this;t._super(e),t.classes.add("widget").add("label"),t.canFocus=!1,e.multiline&&t.classes.add("autoscroll"),e.strong&&t.classes.add("strong")},initLayoutRect:function(){var e=this,t=e._super();return e.settings.multiline&&(He.getSize(e.getEl()).width>t.maxW&&(t.minW=t.maxW,e.classes.add("multiline")),e.getEl().style.width=t.minW+"px",t.startMinH=t.h=t.minH=Math.min(t.maxH,He.getSize(e.getEl()).height)),t},repaint:function(){return this.settings.multiline||(this.getEl().style.lineHeight=this.layoutRect().h+"px"),this._super()},severity:function(e){this.classes.remove("error"),this.classes.remove("warning"),this.classes.remove("success"),this.classes.add(e)},renderHtml:function(){var e,t,n=this,i=n.settings.forId,r=n.settings.html?n.settings.html:n.encode(n.state.get("text"));return!i&&(t=n.settings.forName)&&(e=n.getRoot().find("#"+t)[0])&&(i=e._id),i?'<label id="'+n._id+'" class="'+n.classes+'"'+(i?' for="'+i+'"':"")+">"+r+"</label>":'<span id="'+n._id+'" class="'+n.classes+'">'+r+"</span>"},bindStates:function(){var t=this;return t.state.on("change:text",function(e){t.innerHtml(t.encode(e.value)),t.state.get("rendered")&&t.updateLayoutRect()}),t._super()}}),Cr=mt.extend({Defaults:{role:"toolbar",layout:"flow"},init:function(e){this._super(e),this.classes.add("toolbar")},postRender:function(){return this.items().each(function(e){e.classes.add("toolbar-item")}),this._super()}}),kr=Cr.extend({Defaults:{role:"menubar",containerCls:"menubar",ariaRoot:!0,defaults:{type:"menubutton"}}}),Er=Qt.extend({init:function(e){var t=this;t._renderOpen=!0,t._super(e),e=t.settings,t.classes.add("menubtn"),e.fixedWidth&&t.classes.add("fixed-width"),t.aria("haspopup",!0),t.state.set("menu",e.menu||t.render())},showMenu:function(e){var t,n=this;if(n.menu&&n.menu.visible()&&!1!==e)return n.hideMenu();n.menu||(t=n.state.get("menu")||[],n.classes.add("opened"),t.length?t={type:"menu",animate:!0,items:t}:(t.type=t.type||"menu",t.animate=!0),t.renderTo?n.menu=t.parent(n).show().renderTo():n.menu=v.create(t).parent(n).renderTo(),n.fire("createmenu"),n.menu.reflow(),n.menu.on("cancel",function(e){e.control.parent()===n.menu&&(e.stopPropagation(),n.focus(),n.hideMenu())}),n.menu.on("select",function(){n.focus()}),n.menu.on("show hide",function(e){e.control===n.menu&&(n.activeMenu("show"===e.type),n.classes.toggle("opened","show"===e.type)),n.aria("expanded","show"===e.type)}).fire("show")),n.menu.show(),n.menu.layoutRect({w:n.layoutRect().w}),n.menu.repaint(),n.menu.moveRel(n.getEl(),n.isRtl()?["br-tr","tr-br"]:["bl-tl","tl-bl"]),n.fire("showmenu")},hideMenu:function(){this.menu&&(this.menu.items().each(function(e){e.hideMenu&&e.hideMenu()}),this.menu.hide())},activeMenu:function(e){this.classes.toggle("active",e)},renderHtml:function(){var e,t=this,n=t._id,i=t.classPrefix,r=t.settings.icon,o=t.state.get("text"),s="";return(e=t.settings.image)?(r="none","string"!=typeof e&&(e=window.getSelection?e[0]:e[1]),e=" style=\"background-image: url('"+e+"')\""):e="",o&&(t.classes.add("btn-has-text"),s='<span class="'+i+'txt">'+t.encode(o)+"</span>"),r=t.settings.icon?i+"ico "+i+"i-"+r:"",t.aria("role",t.parent()instanceof kr?"menuitem":"button"),'<div id="'+n+'" class="'+t.classes+'" tabindex="-1" aria-labelledby="'+n+'"><button id="'+n+'-open" role="presentation" type="button" tabindex="-1">'+(r?'<i class="'+r+'"'+e+"></i>":"")+s+' <i class="'+i+'caret"></i></button></div>'},postRender:function(){var r=this;return r.on("click",function(e){e.control===r&&function(e,t){for(;e;){if(t===e)return!0;e=e.parentNode}return!1}(e.target,r.getEl())&&(r.focus(),r.showMenu(!e.aria),e.aria&&r.menu.items().filter(":visible")[0].focus())}),r.on("mouseenter",function(e){var t,n=e.control,i=r.parent();n&&i&&n instanceof Er&&n.parent()===i&&(i.items().filter("MenuButton").each(function(e){e.hideMenu&&e!==n&&(e.menu&&e.menu.visible()&&(t=!0),e.hideMenu())}),t&&(n.focus(),n.showMenu()))}),r._super()},bindStates:function(){var e=this;return e.state.on("change:menu",function(){e.menu&&e.menu.remove(),e.menu=null}),e._super()},remove:function(){this._super(),this.menu&&this.menu.remove()}}),Hr=Tt.extend({Defaults:{defaultType:"menuitem",border:1,layout:"stack",role:"application",bodyRole:"menu",ariaRoot:!0},init:function(e){if(e.autohide=!0,e.constrainToViewport=!0,"function"==typeof e.items&&(e.itemsFactory=e.items,e.items=[]),e.itemDefaults)for(var t=e.items,n=t.length;n--;)t[n]=w.extend({},e.itemDefaults,t[n]);this._super(e),this.classes.add("menu"),e.animate&&11!==pe.ie&&this.classes.add("animate")},repaint:function(){return this.classes.toggle("menu-align",!0),this._super(),this.getEl().style.height="",this.getEl("body").style.height="",this},cancel:function(){this.hideAll(),this.fire("select")},load:function(){var t,n=this;function i(){n.throbber&&(n.throbber.hide(),n.throbber=null)}n.settings.itemsFactory&&(n.throbber||(n.throbber=new Dt(n.getEl("body"),!0),0===n.items().length?(n.throbber.show(),n.fire("loading")):n.throbber.show(100,function(){n.items().remove(),n.fire("loading")}),n.on("hide close",i)),n.requestTime=t=(new Date).getTime(),n.settings.itemsFactory(function(e){0!==e.length?n.requestTime===t&&(n.getEl().style.width="",n.getEl("body").style.width="",i(),n.items().remove(),n.getEl("body").innerHTML="",n.add(e),n.renderNew(),n.fire("loaded")):n.hide()}))},hideAll:function(){return this.find("menuitem").exec("hideMenu"),this._super()},preRender:function(){var n=this;return n.items().each(function(e){var t=e.settings;if(t.icon||t.image||t.selectable)return!(n._hasIcons=!0)}),n.settings.itemsFactory&&n.on("postrender",function(){n.settings.itemsFactory&&n.load()}),n.on("show hide",function(e){e.control===n&&("show"===e.type?u.setTimeout(function(){n.classes.add("in")},0):n.classes.remove("in"))}),n._super()}}),Sr=Er.extend({init:function(i){var t,r,o,n,s=this;s._super(i),i=s.settings,s._values=t=i.values,t&&("undefined"!=typeof i.value&&function e(t){for(var n=0;n<t.length;n++){if(r=t[n].selected||i.value===t[n].value)return o=o||t[n].text,s.state.set("value",t[n].value),!0;if(t[n].menu&&e(t[n].menu))return!0}}(t),!r&&0<t.length&&(o=t[0].text,s.state.set("value",t[0].value)),s.state.set("menu",t)),s.state.set("text",i.text||o),s.classes.add("listbox"),s.on("select",function(e){var t=e.control;n&&(e.lastControl=n),i.multiple?t.active(!t.active()):s.value(e.control.value()),n=t})},value:function(t){return 0===arguments.length?this.state.get("value"):(void 0===t||(this.settings.values?0<w.grep(this.settings.values,function(e){return e.value===t}).length?this.state.set("value",t):null===t&&this.state.set("value",null):this.state.set("value",t)),this)},bindStates:function(){var i=this;return i.on("show",function(e){var t,n;t=e.control,n=i.value(),t instanceof Hr&&t.items().each(function(e){e.hasMenus()||e.active(e.value()===n)})}),i.state.on("change:value",function(t){var n=function e(t,n){var i;if(t)for(var r=0;r<t.length;r++){if(t[r].value===n)return t[r];if(t[r].menu&&(i=e(t[r].menu,n)))return i}}(i.state.get("menu"),t.value);n?i.text(n.text):i.text(i.settings.text)}),i._super()}}),Mr=zt.extend({Defaults:{border:0,role:"menuitem"},init:function(e){var t,n=this;n._super(e),e=n.settings,n.classes.add("menu-item"),e.menu&&n.classes.add("menu-item-expand"),e.preview&&n.classes.add("menu-item-preview"),"-"!==(t=n.state.get("text"))&&"|"!==t||(n.classes.add("menu-item-sep"),n.aria("role","separator"),n.state.set("text","-")),e.selectable&&(n.aria("role","menuitemcheckbox"),n.classes.add("menu-item-checkbox"),e.icon="selected"),e.preview||e.selectable||n.classes.add("menu-item-normal"),n.on("mousedown",function(e){e.preventDefault()}),e.menu&&!e.ariaHideMenu&&n.aria("haspopup",!0)},hasMenus:function(){return!!this.settings.menu},showMenu:function(){var t,n=this,e=n.settings,i=n.parent();if(i.items().each(function(e){e!==n&&e.hideMenu()}),e.menu){(t=n.menu)?t.show():((t=e.menu).length?t={type:"menu",items:t}:t.type=t.type||"menu",i.settings.itemDefaults&&(t.itemDefaults=i.settings.itemDefaults),(t=n.menu=v.create(t).parent(n).renderTo()).reflow(),t.on("cancel",function(e){e.stopPropagation(),n.focus(),t.hide()}),t.on("show hide",function(e){e.control.items&&e.control.items().each(function(e){e.active(e.settings.selected)})}).fire("show"),t.on("hide",function(e){e.control===t&&n.classes.remove("selected")}),t.submenu=!0),t._parentMenu=i,t.classes.add("menu-sub");var r=t.testMoveRel(n.getEl(),n.isRtl()?["tl-tr","bl-br","tr-tl","br-bl"]:["tr-tl","br-bl","tl-tr","bl-br"]);t.moveRel(n.getEl(),r),r="menu-sub-"+(t.rel=r),t.classes.remove(t._lastRel).add(r),t._lastRel=r,n.classes.add("selected"),n.aria("expanded",!0)}},hideMenu:function(){var e=this;return e.menu&&(e.menu.items().each(function(e){e.hideMenu&&e.hideMenu()}),e.menu.hide(),e.aria("expanded",!1)),e},renderHtml:function(){var e,t=this,n=t._id,i=t.settings,r=t.classPrefix,o=t.state.get("text"),s=t.settings.icon,a="",l=i.shortcut,u=t.encode(i.url);function c(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function d(e){var t=i.match||"";return t?e.replace(new RegExp(c(t),"gi"),function(e){return"!mce~match["+e+"]mce~match!"}):e}function f(e){return e.replace(new RegExp(c("!mce~match["),"g"),"<b>").replace(new RegExp(c("]mce~match!"),"g"),"</b>")}return s&&t.parent().classes.add("menu-has-icons"),i.image&&(a=" style=\"background-image: url('"+i.image+"')\""),l&&(l=function(e){var t,n,i={};for(i=pe.mac?{alt:"&#x2325;",ctrl:"&#x2318;",shift:"&#x21E7;",meta:"&#x2318;"}:{meta:"Ctrl"},e=e.split("+"),t=0;t<e.length;t++)(n=i[e[t].toLowerCase()])&&(e[t]=n);return e.join("+")}(l)),s=r+"ico "+r+"i-"+(t.settings.icon||"none"),e="-"!==o?'<i class="'+s+'"'+a+"></i>\xa0":"",o=f(t.encode(d(o))),u=f(t.encode(d(u))),'<div id="'+n+'" class="'+t.classes+'" tabindex="-1">'+e+("-"!==o?'<span id="'+n+'-text" class="'+r+'text">'+o+"</span>":"")+(l?'<div id="'+n+'-shortcut" class="'+r+'menu-shortcut">'+l+"</div>":"")+(i.menu?'<div class="'+r+'caret"></div>':"")+(u?'<div class="'+r+'menu-item-link">'+u+"</div>":"")+"</div>"},postRender:function(){var t=this,n=t.settings,e=n.textStyle;if("function"==typeof e&&(e=e.call(this)),e){var i=t.getEl("text");i&&(i.setAttribute("style",e),t._textStyle=e)}return t.on("mouseenter click",function(e){e.control===t&&(n.menu||"click"!==e.type?(t.showMenu(),e.aria&&t.menu.focus(!0)):(t.fire("select"),u.requestAnimationFrame(function(){t.parent().hideAll()})))}),t._super(),t},hover:function(){return this.parent().items().each(function(e){e.classes.remove("selected")}),this.classes.toggle("selected",!0),this},active:function(e){return function(e,t){var n=e._textStyle;if(n){var i=e.getEl("text");i.setAttribute("style",n),t&&(i.style.color="",i.style.backgroundColor="")}}(this,e),void 0!==e&&this.aria("checked",e),this._super(e)},remove:function(){this._super(),this.menu&&this.menu.remove()}}),Tr=nn.extend({Defaults:{classes:"radio",role:"radio"}}),Pr=zt.extend({renderHtml:function(){var e=this,t=e.classPrefix;return e.classes.add("resizehandle"),"both"===e.settings.direction&&e.classes.add("resizehandle-both"),e.canFocus=!1,'<div id="'+e._id+'" class="'+e.classes+'"><i class="'+t+"ico "+t+'i-resize"></i></div>'},postRender:function(){var t=this;t._super(),t.resizeDragHelper=new pt(this._id,{start:function(){t.fire("ResizeStart")},drag:function(e){"both"!==t.settings.direction&&(e.deltaX=0),t.fire("Resize",e)},stop:function(){t.fire("ResizeEnd")}})},remove:function(){return this.resizeDragHelper&&this.resizeDragHelper.destroy(),this._super()}});function Wr(e){var t="";if(e)for(var n=0;n<e.length;n++)t+='<option value="'+e[n]+'">'+e[n]+"</option>";return t}var Nr=zt.extend({Defaults:{classes:"selectbox",role:"selectbox",options:[]},init:function(e){var n=this;n._super(e),n.settings.size&&(n.size=n.settings.size),n.settings.options&&(n._options=n.settings.options),n.on("keydown",function(e){var t;13===e.keyCode&&(e.preventDefault(),n.parents().reverse().each(function(e){if(e.toJSON)return t=e,!1}),n.fire("submit",{data:t.toJSON()}))})},options:function(e){return arguments.length?(this.state.set("options",e),this):this.state.get("options")},renderHtml:function(){var e,t=this,n="";return e=Wr(t._options),t.size&&(n=' size = "'+t.size+'"'),'<select id="'+t._id+'" class="'+t.classes+'"'+n+">"+e+"</select>"},bindStates:function(){var t=this;return t.state.on("change:options",function(e){t.getEl().innerHTML=Wr(e.value)}),t._super()}});function Dr(e,t,n){return e<t&&(e=t),n<e&&(e=n),e}function Ar(e,t,n){e.setAttribute("aria-"+t,n)}function Br(e,t){var n,i,r,o,s;"v"===e.settings.orientation?(r="top",i="height",n="h"):(r="left",i="width",n="w"),s=e.getEl("handle"),o=((e.layoutRect()[n]||100)-He.getSize(s)[i])*((t-e._minValue)/(e._maxValue-e._minValue))+"px",s.style[r]=o,s.style.height=e.layoutRect().h+"px",Ar(s,"valuenow",t),Ar(s,"valuetext",""+e.settings.previewFilter(t)),Ar(s,"valuemin",e._minValue),Ar(s,"valuemax",e._maxValue)}var Or=zt.extend({init:function(e){var t=this;e.previewFilter||(e.previewFilter=function(e){return Math.round(100*e)/100}),t._super(e),t.classes.add("slider"),"v"===e.orientation&&t.classes.add("vertical"),t._minValue=q.isNumber(e.minValue)?e.minValue:0,t._maxValue=q.isNumber(e.maxValue)?e.maxValue:100,t._initValue=t.state.get("value")},renderHtml:function(){var e=this._id,t=this.classPrefix;return'<div id="'+e+'" class="'+this.classes+'"><div id="'+e+'-handle" class="'+t+'slider-handle" role="slider" tabindex="-1"></div></div>'},reset:function(){this.value(this._initValue).repaint()},postRender:function(){var e,t,n,i,r,o,s,a,l,u,c,d,f,h,m=this;e=m._minValue,t=m._maxValue,"v"===m.settings.orientation?(n="screenY",i="top",r="height",o="h"):(n="screenX",i="left",r="width",o="w"),m._super(),function(o,s){function t(e){var t,n,i,r;t=Dr(t=(((t=m.value())+(r=n=o))/((i=s)-r)+.05*e)*(i-n)-n,o,s),m.value(t),m.fire("dragstart",{value:t}),m.fire("drag",{value:t}),m.fire("dragend",{value:t})}m.on("keydown",function(e){switch(e.keyCode){case 37:case 38:t(-1);break;case 39:case 40:t(1)}})}(e,t),s=e,a=t,l=m.getEl("handle"),m._dragHelper=new pt(m._id,{handle:m._id+"-handle",start:function(e){u=e[n],c=parseInt(m.getEl("handle").style[i],10),d=(m.layoutRect()[o]||100)-He.getSize(l)[r],m.fire("dragstart",{value:h})},drag:function(e){var t=e[n]-u;f=Dr(c+t,0,d),l.style[i]=f+"px",h=s+f/d*(a-s),m.value(h),m.tooltip().text(""+m.settings.previewFilter(h)).show().moveRel(l,"bc tc"),m.fire("drag",{value:h})},stop:function(){m.tooltip().hide(),m.fire("dragend",{value:h})}})},repaint:function(){this._super(),Br(this,this.value())},bindStates:function(){var t=this;return t.state.on("change:value",function(e){Br(t,e.value)}),t._super()}}),zr=zt.extend({renderHtml:function(){return this.classes.add("spacer"),this.canFocus=!1,'<div id="'+this._id+'" class="'+this.classes+'"></div>'}}),Lr=Er.extend({Defaults:{classes:"widget btn splitbtn",role:"button"},repaint:function(){var e,t,n=this.getEl(),i=this.layoutRect();return this._super(),e=n.firstChild,t=n.lastChild,ke(e).css({width:i.w-He.getSize(t).width,height:i.h-2}),ke(t).css({height:i.h-2}),this},activeMenu:function(e){ke(this.getEl().lastChild).toggleClass(this.classPrefix+"active",e)},renderHtml:function(){var e,t,n=this,i=n._id,r=n.classPrefix,o=n.state.get("icon"),s=n.state.get("text"),a=n.settings,l="";return(e=a.image)?(o="none","string"!=typeof e&&(e=window.getSelection?e[0]:e[1]),e=" style=\"background-image: url('"+e+"')\""):e="",o=a.icon?r+"ico "+r+"i-"+o:"",s&&(n.classes.add("btn-has-text"),l='<span class="'+r+'txt">'+n.encode(s)+"</span>"),t="boolean"==typeof a.active?' aria-pressed="'+a.active+'"':"",'<div id="'+i+'" class="'+n.classes+'" role="button"'+t+' tabindex="-1"><button type="button" hidefocus="1" tabindex="-1">'+(o?'<i class="'+o+'"'+e+"></i>":"")+l+'</button><button type="button" class="'+r+'open" hidefocus="1" tabindex="-1">'+(n._menuBtnText?(o?"\xa0":"")+n._menuBtnText:"")+' <i class="'+r+'caret"></i></button></div>'},postRender:function(){var n=this.settings.onclick;return this.on("click",function(e){var t=e.target;if(e.control===this)for(;t;){if(e.aria&&"down"!==e.aria.key||"BUTTON"===t.nodeName&&-1===t.className.indexOf("open"))return e.stopImmediatePropagation(),void(n&&n.call(this,e));t=t.parentNode}}),delete this.settings.onclick,this._super()}}),Ir=ji.extend({Defaults:{containerClass:"stack-layout",controlClass:"stack-layout-item",endClass:"break"},isNative:function(){return!0}}),Fr=_t.extend({Defaults:{layout:"absolute",defaults:{type:"panel"}},activateTab:function(n){var e;this.activeTabId&&(e=this.getEl(this.activeTabId),ke(e).removeClass(this.classPrefix+"active"),e.setAttribute("aria-selected","false")),this.activeTabId="t"+n,(e=this.getEl("t"+n)).setAttribute("aria-selected","true"),ke(e).addClass(this.classPrefix+"active"),this.items()[n].show().fire("showtab"),this.reflow(),this.items().each(function(e,t){n!==t&&e.hide()})},renderHtml:function(){var i=this,e=i._layout,r="",o=i.classPrefix;return i.preRender(),e.preRender(i),i.items().each(function(e,t){var n=i._id+"-t"+t;e.aria("role","tabpanel"),e.aria("labelledby",n),r+='<div id="'+n+'" class="'+o+'tab" unselectable="on" role="tab" aria-controls="'+e._id+'" aria-selected="false" tabIndex="-1">'+i.encode(e.settings.title)+"</div>"}),'<div id="'+i._id+'" class="'+i.classes+'" hidefocus="1" tabindex="-1"><div id="'+i._id+'-head" class="'+o+'tabs" role="tablist">'+r+'</div><div id="'+i._id+'-body" class="'+i.bodyClasses+'">'+e.renderHtml(i)+"</div></div>"},postRender:function(){var i=this;i._super(),i.settings.activeTab=i.settings.activeTab||0,i.activateTab(i.settings.activeTab),this.on("click",function(e){var t=e.target.parentNode;if(t&&t.id===i._id+"-head")for(var n=t.childNodes.length;n--;)t.childNodes[n]===e.target&&i.activateTab(n)})},initLayoutRect:function(){var e,t,n,i=this;t=(t=He.getSize(i.getEl("head")).width)<0?0:t,n=0,i.items().each(function(e){t=Math.max(t,e.layoutRect().minW),n=Math.max(n,e.layoutRect().minH)}),i.items().each(function(e){e.settings.x=0,e.settings.y=0,e.settings.w=t,e.settings.h=n,e.layoutRect({x:0,y:0,w:t,h:n})});var r=He.getSize(i.getEl("head")).height;return i.settings.minWidth=t,i.settings.minHeight=n+r,(e=i._super()).deltaH+=r,e.innerH=e.h-e.deltaH,e}}),Ur=zt.extend({init:function(e){var n=this;n._super(e),n.classes.add("textbox"),e.multiline?n.classes.add("multiline"):(n.on("keydown",function(e){var t;13===e.keyCode&&(e.preventDefault(),n.parents().reverse().each(function(e){if(e.toJSON)return t=e,!1}),n.fire("submit",{data:t.toJSON()}))}),n.on("keyup",function(e){n.state.set("value",e.target.value)}))},repaint:function(){var e,t,n,i,r,o=this,s=0;e=o.getEl().style,t=o._layoutRect,r=o._lastRepaintRect||{};var a=document;return!o.settings.multiline&&a.all&&(!a.documentMode||a.documentMode<=8)&&(e.lineHeight=t.h-s+"px"),i=(n=o.borderBox).left+n.right+8,s=n.top+n.bottom+(o.settings.multiline?8:0),t.x!==r.x&&(e.left=t.x+"px",r.x=t.x),t.y!==r.y&&(e.top=t.y+"px",r.y=t.y),t.w!==r.w&&(e.width=t.w-i+"px",r.w=t.w),t.h!==r.h&&(e.height=t.h-s+"px",r.h=t.h),o._lastRepaintRect=r,o.fire("repaint",{},!1),o},renderHtml:function(){var t,e,n=this,i=n.settings;return t={id:n._id,hidefocus:"1"},w.each(["rows","spellcheck","maxLength","size","readonly","min","max","step","list","pattern","placeholder","required","multiple"],function(e){t[e]=i[e]}),n.disabled()&&(t.disabled="disabled"),i.subtype&&(t.type=i.subtype),(e=He.create(i.multiline?"textarea":"input",t)).value=n.state.get("value"),e.className=n.classes.toString(),e.outerHTML},value:function(e){return arguments.length?(this.state.set("value",e),this):(this.state.get("rendered")&&this.state.set("value",this.getEl().value),this.state.get("value"))},postRender:function(){var t=this;t.getEl().value=t.state.get("value"),t._super(),t.$el.on("change",function(e){t.state.set("value",e.target.value),t.fire("change",e)})},bindStates:function(){var t=this;return t.state.on("change:value",function(e){t.getEl().value!==e.value&&(t.getEl().value=e.value)}),t.state.on("change:disabled",function(e){t.getEl().disabled=e.value}),t._super()},remove:function(){this.$el.off(),this._super()}}),Vr=function(){return{Selector:Ye,Collection:Je,ReflowQueue:it,Control:ct,Factory:v,KeyboardNavigation:ft,Container:mt,DragHelper:pt,Scrollable:wt,Panel:_t,Movable:Ne,Resizable:Rt,FloatPanel:Tt,Window:$t,MessageBox:Jt,Tooltip:Ot,Widget:zt,Progress:Lt,Notification:Ft,Layout:Kt,AbsoluteLayout:Zt,Button:Qt,ButtonGroup:tn,Checkbox:nn,ComboBox:on,ColorBox:sn,PanelButton:an,ColorButton:un,ColorPicker:dn,Path:hn,ElementPath:mn,FormItem:gn,Form:pn,FieldSet:vn,FilePicker:Fi,FitLayout:Ui,FlexLayout:Vi,FlowLayout:ji,FormatControls:yr,GridLayout:xr,Iframe:wr,InfoBox:_r,Label:Rr,Toolbar:Cr,MenuBar:kr,MenuButton:Er,MenuItem:Mr,Throbber:Dt,Menu:Hr,ListBox:Sr,Radio:Tr,ResizeHandle:Pr,SelectBox:Nr,Slider:Or,Spacer:zr,SplitButton:Lr,StackLayout:Ir,TabPanel:Fr,TextBox:Ur,DropZone:fn,BrowseButton:en}},jr=function(n){n.ui?w.each(Vr(),function(e,t){n.ui[t]=e}):n.ui=Vr()};w.each(Vr(),function(e,t){v.add(t,e)}),jr(window.tinymce?window.tinymce:{}),o.add("simple",function(e){return yr.setup(e),Gt(e)})}();