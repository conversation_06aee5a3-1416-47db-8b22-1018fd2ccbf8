/**
 +----------------------------------------------------------
 * tinymce初始化
 +----------------------------------------------------------
 */
$(document).ready(function() {
    // 恢复父级菜单可点击
    if ($(window).width() > 768) {
      var tinymce_width = 800;
      var toolbar = 'fontsizeselect bold italic strikethrough forecolor backcolor alignleft aligncenter alignright alignjustify outdent indent numlist bullist code | link unlink media map charmap table hr removeformat undo redo help fullscreen';
    } else {
      var tinymce_width = 290;
      var toolbar = 'fontsizeselect bold italic forecolor backcolor | link unlink table hr removeformat undo redo fullscreen';
    }
    
 
    tinymce.init({
        selector:'textarea#content',
        language:'zh_CN',
        width:tinymce_width+'px',
        height:'360px',
        menubar:false,
        skin:'simple',
        theme:'simple',
        convert_urls :false,
        plugins: [
          'autolink lists link charmap textcolor',
          'code hr pagebreak map textpattern image imagetools',
          'media table help wordcount fullscreen'
        ],
        toolbar: toolbar
    });
});


/**
 +----------------------------------------------------------
 *  tinymce插入HTML
 +----------------------------------------------------------
 */
function insertMce(html) {
    tinyMCE.execCommand('mceInsertContent', false, html); 
}