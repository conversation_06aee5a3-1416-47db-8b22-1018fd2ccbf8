/*基础UI构建
*/
/* common layer */
.edui-notadd * {
    box-sizing: border-box;
}
.edui-notadd .edui-box {
    border: none;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

.edui-notadd a.edui-box {
    display: block;
    text-decoration: none;
    color: black;
}

.edui-notadd a.edui-box:hover {
    text-decoration: none;
}

.edui-notadd a.edui-box:active {
    text-decoration: none;
}

.edui-notadd table.edui-box {
    border-collapse: collapse;
}

.edui-notadd ul.edui-box {
    list-style-type: none;
}

div.edui-box {
    position: relative;
    display: -moz-inline-box !important;
    display: inline-block !important;
    vertical-align: middle;
}

.edui-notadd .edui-clearfix {
    zoom: 1
}

.edui-notadd .edui-clearfix:after {
    content: '\20';
    display: block;
    clear: both;
}

 * html div.edui-box {
    display: inline !important;
}

*:first-child+html div.edui-box {
    display: inline !important;
}

/* control layout */
.edui-notadd .edui-button-body, .edui-splitbutton-body, .edui-menubutton-body, .edui-combox-body {
    position: relative;
}

.edui-notadd .edui-popup {
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
}

.edui-notadd .edui-popup .edui-shadow {
    position: absolute;
    z-index: -1;
}

.edui-notadd .edui-popup .edui-bordereraser {
    position: absolute;
    overflow: hidden;
}

.edui-notadd .edui-tablepicker .edui-canvas {
    position: relative;
}

.edui-notadd .edui-tablepicker .edui-canvas .edui-overlay {
    position: absolute;
}

.edui-notadd .edui-dialog-modalmask, .edui-dialog-dragmask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.edui-notadd .edui-toolbar {
    position: relative;
}

/*
 * default theme
 */
.edui-notadd .edui-label {
    cursor: default;
}

.edui-notadd span.edui-clickable {
    color: #666;
    cursor: pointer;
    text-decoration: none;
    padding-left: 5px;
}

.edui-notadd span.edui-unclickable {
    color: gray;
    cursor: default;
}
/* 工具栏 */
.edui-notadd .edui-toolbar {
    cursor: default;
    -webkit-user-select: none;
    -moz-user-select: none;
    overflow: hidden; /*全屏下单独一行不占位*/
    zoom: 1;
    width:auto;
    height:auto;
}

.edui-notadd .edui-toolbar .edui-button,
.edui-notadd .edui-toolbar .edui-splitbutton,
.edui-notadd .edui-toolbar .edui-menubutton,
.edui-notadd .edui-toolbar .edui-combox {
    padding: 4px 0 !important;
}
/*UI工具栏、编辑区域、底部*/
.edui-notadd .edui-editor {
    border: 1px solid #e5e5e5;
    background-color: white;
    position: relative;
    overflow: visible;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    font-family: "Helvetica Neue", Helvetica, Arial, "Hiragino Sans GB", "Hiragino Sans GB W3", "Microsoft YaHei", STXihei, STHeiti, Heiti, SimSun, sans-serif;
}

.edui-notadd .edui-dialog .edui-dialog-body {
    font-family: "Helvetica Neue", Helvetica, Arial, "Hiragino Sans GB", "Hiragino Sans GB W3", "Microsoft YaHei", STXihei, STHeiti, Heiti, SimSun, sans-serif;
}

.edui-editor div{
    width:auto;
    height:auto;
    line-height: 1 !important;
}
.edui-notadd .edui-editor-toolbarbox {
    position: relative;
    zoom: 1;
    border-top-left-radius:2px;
    border-top-right-radius:2px;
}

.edui-notadd .edui-editor-toolbarboxouter {
    border-bottom: 1px solid #e5e5e5;
    background-color: #f3f3f3;
}

.edui-notadd .edui-editor-toolbarboxinner {
    /*padding: 2px;*/
}

.edui-notadd .edui-editor-iframeholder .view p {
    overflow: hidden;
}

.edui-notadd .edui-editor-iframeholder {
    position: relative;
}

.edui-notadd .edui-editor-bottomContainer {
    overflow: hidden;
    padding: 0 4px;
    border-top: 1px solid #e5e5e5;
}

.edui-notadd .edui-editor-bottomContainer table {
    width: 100%;
    height: 0;
    overflow: hidden;
    border-spacing: 0;
}

.edui-notadd .edui-editor-bottomContainer td {
    white-space: nowrap;
    line-height: 20px;
    font-size: 12px;
    font-family: Arial, Helvetica, Tahoma, Verdana, Sans-Serif;
}

.edui-notadd .edui-editor-wordcount {
    text-align: right;
    margin-right: 5px;
    color: #aaa;
}
.edui-notadd .edui-editor-scale {
    width: 12px;
}
.edui-notadd .edui-editor-scale .edui-editor-icon {
    float: right;
    width: 100%;
    height: 12px;
    margin-top: 10px;
    background: url(../images/scale.png) no-repeat;
    cursor: se-resize;
}
.edui-notadd .edui-editor-breadcrumb {
    margin: 2px 0 0 3px;
}

.edui-notadd .edui-editor-breadcrumb span {
    cursor: pointer;
    /*text-decoration: underline;*/
    color: blue;
}

.edui-notadd .edui-toolbar .edui-for-fullscreen {
    float: right;
}

.edui-notadd .edui-bubble .edui-popup-content {
    border: 1px solid #e5e5e5;
    background-color: #f3f3f3;
    padding: 10px;
    font-size: 10pt;
    font-family: "宋体";
}

.edui-notadd .edui-bubble .edui-shadow {
    /*box-shadow: 1px 1px 3px #818181;*/
    /*-webkit-box-shadow: 2px 2px 3px #818181;*/
    /*-moz-box-shadow: 2px 2px 3px #818181;*/
    /*filter: progid:DXImageTransform.Microsoft.Blur(PixelRadius = '2', MakeShadow = 'true', ShadowOpacity = '0.5');*/
}

.edui-notadd .edui-editor-toolbarmsg {
    background-color: #FFF6D9;
    border-bottom: 1px solid #ccc;
    position: absolute;
    bottom: -25px;
    left: 0;
    z-index: 1009;
    width: 99.9%;
}

.edui-notadd .edui-editor-toolbarmsg-upload {
    font-size: 14px;
    color: blue;
    width: 100px;
    height: 16px;
    line-height: 16px;
    cursor: pointer;
    position: absolute;
    top: 5px;
    left: 350px;
}

.edui-notadd .edui-editor-toolbarmsg-label {
    font-size: 12px;
    line-height: 16px;
    padding: 4px;
}

.edui-notadd .edui-editor-toolbarmsg-close {
    float: right;
    width: 20px;
    height: 16px;
    line-height: 16px;
    cursor: pointer;
    color: red;
}

.edui-iconfont {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

#edui1_imagescale_cover {

}
.edui-notadd  .edui-editor-imagescale {

}
/*可选中菜单按钮*/
.edui-notadd .edui-list .edui-bordereraser {
    display: none;
}

.edui-notadd .edui-listitem {
    white-space: nowrap;
}

.edui-notadd .edui-list .edui-state-hover {
    position: relative;
    background-color: #f3f3f3;
    padding: 0;
}

.edui-notadd .edui-for-fontfamily .edui-listitem-label {
    min-width: 130px;
    _width: 120px;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    padding-left: 5px;
}
.edui-notadd .edui-for-insertcode .edui-listitem-label {
    min-width: 120px;
    _width: 120px;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    padding-left: 5px;
}
.edui-notadd .edui-for-underline .edui-listitem-label {
    min-width: 120px;
    _width: 120px;
    padding: 3px 5px;
    font-size: 12px;
}

.edui-notadd .edui-for-fontsize .edui-listitem-label {
    min-width: 120px;
    _width: 120px;
    padding: 3px 5px;

}

.edui-notadd .edui-for-paragraph .edui-listitem-label {
    min-width: 200px;
    _width: 200px;
    padding: 2px 5px;
}

.edui-notadd .edui-for-rowspacingtop .edui-listitem-label,
.edui-notadd .edui-for-rowspacingbottom .edui-listitem-label {
    min-width: 53px;
    _width: 53px;
    padding: 2px 5px;
}

.edui-notadd .edui-for-lineheight .edui-listitem-label {
    min-width: 53px;
    _width: 53px;
    padding: 2px 5px;
}

.edui-notadd .edui-for-customstyle .edui-listitem-label {
    min-width: 200px;
    _width: 200px;
    width: 200px !important;
    padding: 2px 5px;
}
/* 可选中按钮弹出菜单*/
.edui-notadd .edui-menu {
    z-index: 3000;
}

.edui-notadd .edui-menu .edui-popup-content {
    padding: 0;
    overflow: hidden;
}

.edui-notadd .edui-menu-body {
    _width: 150px;
    min-width: 170px;
}

.edui-notadd .edui-menuitem-body {
    font-size: 14px;
    color: #666;
}

.edui-notadd .edui-menuitem {
    height: 30px;
    cursor: default;
    vertical-align: top;
}

.edui-notadd .edui-menuitem .edui-icon {
    line-height: 1.3;
}

.edui-notadd .edui-menuitem .edui-label {
    font-size: 12px;
    line-height: 20px;
    height: 20px;
    padding-left: 10px;
}

.edui-notadd .edui-state-checked .edui-menuitem-body {
    background: url("../images/icons-all.gif") no-repeat 6px -205px;
}

.edui-notadd .edui-state-disabled .edui-menuitem-label {
    color: gray;
}

.edui-notadd .edui-menu-body .edui-menuitem.edui-for-setbordervisible {
    padding-left: 25px;
}


/*不可选中菜单按钮 */
.edui-notadd .edui-toolbar .edui-combox-body .edui-button-body {
    width: 60px;
    font-size: 12px;
    height: 20px;
    line-height: 20px !important;
    padding-left: 5px;
    white-space: nowrap;
    margin: 0 3px 0 0;
}

.edui-notadd .edui-toolbar .edui-combox-body .edui-arrow::after {
    height: 25px;
    line-height: 24px;
    font-family:"edui-notadd" !important;
    font-size: 26px;
    font-style:normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    content: "\e64f";
    /*padding: 10px 0;*/
    display: block;
}

.edui-notadd .edui-toolbar .edui-combox .edui-combox-body {
    border: 1px solid #CCC;
    background-color: white;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    margin: 0 10px;
    height: 26px;
    line-height: 26px;
}

.edui-notadd .edui-toolbar .edui-combox-body .edui-splitborder {
    display: none;
}

.edui-notadd .edui-toolbar .edui-combox-body .edui-arrow {
    border-left: 1px solid #CCC;
}

.edui-notadd .edui-toolbar .edui-state-hover .edui-combox-body {
    background-color: #f3f3f3;
    border: 1px solid #ccc;
}

.edui-notadd .edui-toolbar .edui-state-hover .edui-combox-body .edui-arrow {
    border-left: 1px solid #ccc;
}

.edui-notadd .edui-toolbar .edui-state-checked .edui-combox-body {
    background-color: #FFE69F;
    border: 1px solid #DCAC6C;
}

.edui-toolbar .edui-state-checked .edui-combox-body .edui-arrow {
    border-left: 1px solid #DCAC6C;
}

.edui-toolbar .edui-state-disabled .edui-combox-body {
    background-color: #F0F0EE;
    opacity: 0.3;
    filter: alpha(opacity = 30);
}

.edui-toolbar .edui-state-opened .edui-combox-body {
    background-color: white;
    border: 1px solid gray;
}
/*普通按钮样式及状态*/
.edui-notadd .edui-toolbar .edui-button .edui-icon,
.edui-notadd .edui-toolbar .edui-menubutton .edui-icon,
.edui-notadd .edui-toolbar .edui-splitbutton .edui-icon {
    padding: 5px !important;
    display: block;
    height: 26px !important;
    width: 26px !important;
    line-height: 1 !important;
}

.edui-notadd .edui-toolbar .edui-button .edui-icon::before,
.edui-notadd .edui-toolbar .edui-menubutton .edui-icon::before,
.edui-notadd .edui-toolbar .edui-splitbutton .edui-icon::before {
    display: none;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-hover .edui-icon::before,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-hover .edui-icon::before,
.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-hover .edui-icon::before {
    display: block;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-hover .edui-icon svg,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-hover .edui-icon svg,
.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-hover .edui-icon svg {
    display: none;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-checked .edui-icon::before,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-checked .edui-icon::before,
.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-checked .edui-icon::before {
    display: block;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-checked .edui-icon svg,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-checked .edui-icon svg,
.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-checked .edui-icon svg {
    display: none;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-active .edui-icon::before,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-active .edui-icon::before,
.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-active .edui-icon::before {
    display: block;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-active .edui-icon svg,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-active .edui-icon svg,
.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-active .edui-icon svg {
    display: none;
}

.edui-dialog-buttons .edui-icon svg {
    display: none;
}

.edui-notadd .edui-toolbar .edui-button .edui-icon {
    color: #666;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-checked .edui-icon {
    color: #fff;
}

.edui-notadd .edui-toolbar .edui-button .edui-button-wrap {
    position: relative;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-hover .edui-button-wrap {
    background-color: #e5e5e5;
    padding: 0;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-checked .edui-button-wrap {
    background-color: #666666;
    padding: 0;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-active .edui-button-wrap {
    background-color: #666666;
    padding: 0;
}

.edui-notadd .edui-toolbar .edui-button .edui-state-active .edui-icon {
    color: #fff;
}

.edui-notadd .edui-toolbar .edui-state-disabled .edui-label {
    color: #ccc;
}

.edui-notadd .edui-toolbar .edui-state-disabled .edui-icon {
    opacity: 0.3;
    filter: alpha(opacity=30);
}

/* toolbar icons */
.edui-notadd .edui-for-undo .edui-icon {
    /*background-position: -160px 0;*/
}

.edui-notadd  .edui-for-redo .edui-icon {
    /*background-position: -100px 0;*/
}

.edui-notadd  .edui-for-bold .edui-icon {
    /*background-position: 0 0;*/
}

.edui-notadd  .edui-for-italic .edui-icon {
    /*background-position: -60px 0;*/
}

.edui-notadd  .edui-for-fontborder .edui-icon {
    /*background-position:-160px -40px;*/
}
.edui-notadd  .edui-for-underline .edui-icon {
    /*background-position: -140px 0;*/
}

.edui-notadd  .edui-for-strikethrough .edui-icon {
    /*background-position: -120px 0;*/
}

.edui-notadd  .edui-for-subscript .edui-icon {
    /*background-position: -600px 0;*/
}

.edui-notadd  .edui-for-superscript .edui-icon {
    /*background-position: -620px 0;*/
}

.edui-notadd  .edui-for-blockquote .edui-icon {
    /*background-position: -220px 0;*/
}

.edui-notadd  .edui-for-forecolor .edui-icon {
    /*background-position: -720px 0;*/
}

.edui-notadd  .edui-for-backcolor .edui-icon {
    /*background-position: -760px 0;*/
}

.edui-notadd  .edui-for-inserttable .edui-icon {
    /*background-position: -580px -20px;*/
}

.edui-notadd  .edui-for-autotypeset .edui-icon {
    /*background-position: -640px -40px;*/
}

.edui-notadd  .edui-for-justifyleft .edui-icon {
    /*background-position: -460px 0;*/
}

.edui-notadd  .edui-for-justifycenter .edui-icon {
    /*background-position: -420px 0;*/
}

.edui-notadd  .edui-for-justifyright .edui-icon {
    /*background-position: -480px 0;*/
}

.edui-notadd  .edui-for-justifyjustify .edui-icon {
    /*background-position: -440px 0;*/
}

.edui-notadd  .edui-for-insertorderedlist .edui-icon {
    /*background-position: -80px 0;*/
}

.edui-notadd  .edui-for-insertunorderedlist .edui-icon {
    /*background-position: -20px 0;*/
}

.edui-notadd  .edui-for-lineheight .edui-icon {
    /*background-position: -725px -40px;*/
}

.edui-notadd  .edui-for-rowspacingbottom .edui-icon {
    /*background-position: -745px -40px;*/
}

.edui-notadd  .edui-for-rowspacingtop .edui-icon {
    /*background-position: -765px -40px;*/
}

.edui-notadd  .edui-for-horizontal .edui-icon {
    /*background-position: -360px 0;*/
}

.edui-notadd  .edui-for-link .edui-icon {
    /*background-position: -500px 0;*/
}

.edui-notadd  .edui-for-code .edui-icon {
    /*background-position: -440px -40px;*/
}

.edui-notadd  .edui-for-insertimage .edui-icon {
    /*background-position: -726px -77px;*/
}

.edui-notadd  .edui-for-insertframe .edui-icon {
    /*background-position: -240px -40px;*/
}

.edui-notadd  .edui-for-emoticon .edui-icon {
    /*background-position: -60px -20px;*/
}

.edui-notadd  .edui-for-spechars .edui-icon {
    /*background-position: -240px 0;*/
}

.edui-notadd  .edui-for-help .edui-icon {
    /*background-position: -340px 0;*/
}

.edui-notadd  .edui-for-print .edui-icon {
    /*background-position: -440px -20px;*/
}

.edui-notadd  .edui-for-preview .edui-icon {
    /*background-position: -420px -20px;*/
}

.edui-notadd  .edui-for-selectall .edui-icon {
    /*background-position: -400px -20px;*/
}

.edui-notadd  .edui-for-searchreplace .edui-icon {
    /*background-position: -520px -20px;*/
}

.edui-notadd  .edui-for-map .edui-icon {
    /*background-position: -40px -40px;*/
}

.edui-notadd  .edui-for-gmap .edui-icon {
    /*background-position: -260px -40px;*/
}

.edui-notadd  .edui-for-insertvideo .edui-icon {
    /*background-position: -320px -20px;*/
}

.edui-notadd  .edui-for-time .edui-icon {
    /*background-position: -160px -20px;*/
}

.edui-notadd  .edui-for-date .edui-icon {
    /*background-position: -140px -20px;*/
}

.edui-notadd  .edui-for-cut .edui-icon {
    /*background-position: -680px 0;*/
}

.edui-notadd  .edui-for-copy .edui-icon {
    /*background-position: -700px 0;*/
}

.edui-notadd  .edui-for-paste .edui-icon {
    /*background-position: -560px 0;*/
}

.edui-notadd  .edui-for-formatmatch .edui-icon {
    /*background-position: -40px 0;*/
}

.edui-notadd  .edui-for-pasteplain .edui-icon {
    /*background-position: -360px -20px;*/
}

.edui-notadd  .edui-for-directionalityltr .edui-icon {
    /*background-position: -20px -20px;*/
}

.edui-notadd  .edui-for-directionalityrtl .edui-icon {
    /*background-position: -40px -20px;*/
}

.edui-notadd  .edui-for-source .edui-icon {
    /*background-position: -261px -0px;*/
}

.edui-notadd  .edui-for-removeformat .edui-icon {
    /*background-position: -580px 0;*/
}

.edui-notadd  .edui-for-unlink .edui-icon {
    /*background-position: -640px 0;*/
}

.edui-notadd  .edui-for-touppercase .edui-icon {
    /*background-position: -786px 0;*/
}

.edui-notadd  .edui-for-tolowercase .edui-icon {
    /*background-position: -806px 0;*/
}

.edui-notadd  .edui-for-insertrow .edui-icon {
    /*background-position: -478px -76px;*/
}

.edui-notadd  .edui-for-insertrownext .edui-icon {
    /*background-position: -498px -76px;*/
}

.edui-notadd  .edui-for-insertcol .edui-icon {
    /*background-position: -455px -76px;*/
}

.edui-notadd  .edui-for-insertcolnext  .edui-icon {
    /*background-position: -429px -76px;*/
}

.edui-notadd  .edui-for-mergeright .edui-icon {
    /*background-position: -60px -40px;*/
}

.edui-notadd  .edui-for-mergedown .edui-icon {
    /*background-position: -80px -40px;*/
}

.edui-notadd  .edui-for-splittorows .edui-icon {
    /*background-position: -100px -40px;*/
}

.edui-notadd  .edui-for-splittocols .edui-icon {
    /*background-position: -120px -40px;*/
}

.edui-notadd  .edui-for-insertparagraphbeforetable .edui-icon {
    /*background-position: -140px -40px;*/
}

.edui-notadd  .edui-for-deleterow .edui-icon {
    /*background-position: -660px -20px;*/
}

.edui-notadd  .edui-for-deletecol .edui-icon {
    /*background-position: -640px -20px;*/
}

.edui-notadd  .edui-for-splittocells .edui-icon {
    /*background-position: -800px -20px;*/
}

.edui-notadd  .edui-for-mergecells .edui-icon {
    /*background-position: -760px -20px;*/
}

.edui-notadd  .edui-for-deletetable .edui-icon {
    /*background-position: -620px -20px;*/
}

.edui-notadd  .edui-for-cleardoc .edui-icon {
    /*background-position: -520px 0;*/
}

.edui-notadd  .edui-for-fullscreen .edui-icon {
    /*background-position: -100px -20px;*/
}

.edui-notadd  .edui-for-anchor .edui-icon {
    /*background-position: -200px 0;*/
}

.edui-notadd  .edui-for-pagebreak .edui-icon {
    /*background-position: -460px -40px;*/
}

.edui-notadd  .edui-for-imagenone .edui-icon {
    /*background-position: -480px -40px;*/
}

.edui-notadd  .edui-for-imageleft .edui-icon {
    /*background-position: -500px -40px;*/
}

.edui-notadd  .edui-for-wordimage .edui-icon {
    /*background-position: -660px -40px;*/
}

.edui-notadd  .edui-for-imageright .edui-icon {
    /*background-position: -520px -40px;*/
}

.edui-notadd  .edui-for-imagecenter .edui-icon {
    /*background-position: -540px -40px;*/
}

.edui-notadd  .edui-for-indent .edui-icon {
    /*background-position: -400px 0;*/
}

.edui-notadd  .edui-for-outdent .edui-icon {
    /*background-position: -540px 0;*/
}

.edui-notadd  .edui-for-webapp .edui-icon {
    /*background-position: -601px -40px*/
}

.edui-notadd  .edui-for-table .edui-icon {
    /*background-position: -580px -20px;*/
}

.edui-notadd  .edui-for-edittable .edui-icon {
    /*background-position: -420px -40px;*/
}

.edui-notadd  .edui-for-template .edui-icon {
    /*background-position: -339px -40px;*/
}

.edui-notadd  .edui-for-delete .edui-icon {
    /*background-position: -360px -40px;*/
}

.edui-notadd  .edui-for-attachment .edui-icon {
    /*background-position: -620px -40px;*/
}

.edui-notadd  .edui-for-edittd .edui-icon {
    /*background-position: -700px -40px;*/
}

.edui-notadd  .edui-for-snapscreen .edui-icon {
    /*background-position: -581px -40px*/
}

.edui-notadd  .edui-for-scrawl .edui-icon {
    /*background-position: -801px -41px*/
}

.edui-notadd  .edui-for-background .edui-icon {
    /*background-position: -680px -40px;*/
}

.edui-notadd  .edui-for-music .edui-icon {
    /*background-position: -18px -40px*/
}

.edui-notadd  .edui-for-formula .edui-icon {
    /*background-position: -200px -40px*/
}

.edui-notadd  .edui-for-aligntd  .edui-icon {
    /*background-position: -236px -76px;*/
}

.edui-notadd  .edui-for-insertparagraphtrue  .edui-icon {
    /*background-position: -625px -76px;*/
}

.edui-notadd  .edui-for-insertparagraph  .edui-icon {
    /*background-position: -602px -76px;*/
}

.edui-notadd  .edui-for-insertcaption  .edui-icon {
    /*background-position: -336px -76px;*/
}

.edui-notadd  .edui-for-deletecaption  .edui-icon {
    /*background-position: -362px -76px;*/
}

.edui-notadd  .edui-for-inserttitle  .edui-icon {
    /*background-position: -286px -76px;*/
}

.edui-notadd  .edui-for-deletetitle  .edui-icon {
    /*background-position: -311px -76px;*/
}

.edui-notadd  .edui-for-aligntable  .edui-icon {
    /*background-position: -440px 0;*/
}

.edui-notadd  .edui-for-tablealignment-left  .edui-icon {
    /*background-position: -460px 0;*/
}

.edui-notadd  .edui-for-tablealignment-center  .edui-icon {
    /*background-position: -420px 0;*/
}

.edui-notadd  .edui-for-tablealignment-right  .edui-icon {
    /*background-position: -480px 0;*/
}

.edui-notadd  .edui-for-drafts  .edui-icon {
    /*background-position: -560px 0;*/
}

.edui-notadd  .edui-for-charts  .edui-icon {
    /*background: url( ../images/charts.png ) no-repeat 2px 3px!important;*/
}

.edui-notadd  .edui-for-inserttitlecol  .edui-icon {
    /*background-position: -673px -76px;*/
}

.edui-notadd  .edui-for-deletetitlecol  .edui-icon {
    /*background-position: -698px -76px;*/
}

.edui-notadd  .edui-for-simpleupload  .edui-icon {
    /*background-position: -380px 0px;*/
}

@font-face {font-family: "edui-notadd";
  src: url('../fonts/iconfont.eot?t=1544182120898'); /* IE9*/
  src: url('../fonts/iconfont.eot?t=1544182120898#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff'),
  url('../fonts/iconfont.ttf?t=1544182120898') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('../fonts/iconfont.svg?t=1544182120898#edui-notadd') format('svg'); /* iOS 4.1- */
}

.edui-notadd .edui-icon{
  font-family:"edui-notadd" !important;
  font-size:16px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.edui-iconfont {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.edui-for-close .edui-icon:before { content: "\e654"; }

.edui-for-searchreplace .edui-icon:before { content: "\e70f"; }

.edui-for-italic .edui-icon:before { content: "\e62d"; }

.edui-for-insertcaption .edui-icon:before { content: "\e657"; }

.edui-for-insertparagraph .edui-icon:before { content: "\e62e"; }

.edui-for-inserttitlecol .edui-icon:before { content: "\e659"; }

.edui-for-insertimage .edui-icon:before { content: "\e617"; }

.edui-for-previousstep .edui-icon:before { content: "\e630"; }

.edui-for-nextstep .edui-icon:before { content: "\e631"; }

.edui-for-scaleboard .edui-icon:before { content: "\e632"; }

.edui-for-brush .edui-icon:before { content: "\e633"; }

.edui-for-background .edui-icon:before { content: "\e65d"; }

.edui-for-strikethrough .edui-icon:before { content: "\e60c"; }

.edui-for-spechars .edui-icon:before { content: "\e603"; }

.edui-for-clearboard .edui-icon:before { content: "\e634"; }

.edui-for-bold .edui-icon:before { content: "\e604"; }

.edui-for-fullscreen .edui-icon:before { content: "\e656"; }

.edui-for-formatmatch .edui-icon:before { content: "\e60d"; }

.edui-for-underline .edui-icon:before { content: "\e605"; }

.edui-for-removeformat .edui-icon:before { content: "\e60e"; }

.edui-for-blockquote .edui-icon:before { content: "\e60f"; }

.edui-for-anchor .edui-icon:before { content: "\e618"; }

.edui-for-help .edui-icon:before { content: "\e619"; }

.edui-for-horizontal .edui-icon:before { content: "\e638"; }

.edui-for-simpleupload .edui-icon:before { content: "\e61a"; }

.edui-for-indent .edui-icon:before { content: "\e61b"; }

.edui-for-justifycenter .edui-icon:before { content: "\e61c"; }

.edui-for-justifyleft .edui-icon:before { content: "\e61d"; }

.edui-for-justifyjustify .edui-icon:before { content: "\e61e"; }

.edui-for-justifyright .edui-icon:before { content: "\e61f"; }

.edui-for-link .edui-icon:before { content: "\e620"; }

.edui-for-cleardoc .edui-icon:before { content: "\e621"; }

.edui-for-drafts .edui-icon:before { content: "\e610"; }

.edui-for-subscript .edui-icon:before { content: "\e611"; }

.edui-for-unlink .edui-icon:before { content: "\e622"; }

.edui-for-superscript .edui-icon:before { content: "\e612"; }

.edui-for-forecolor .edui-icon:before { content: "\e63a"; }

.edui-for-backcolor .edui-icon:before { content: "\e655"; }

.edui-for-touppercase .edui-icon:before { content: "\e623"; }

.edui-for-tolowercase .edui-icon:before { content: "\e624"; }

.edui-for-insertvideo .edui-icon:before { content: "\e627"; }

.edui-for-emotion .edui-icon:before { content: "\e606"; }

.edui-for-pasteplain .edui-icon:before { content: "\e613"; }

.edui-for-preview .edui-icon:before { content: "\e63b"; }

.edui-for-print .edui-icon:before { content: "\e63c"; }

.edui-for-selectall .edui-icon:before { content: "\e614"; }

.edui-for-mergecells .edui-icon:before { content: "\e63d"; }

.edui-for-deletecol .edui-icon:before { content: "\e63e"; }

.edui-for-deleterow .edui-icon:before { content: "\e63f"; }

.edui-for-attachment .edui-icon:before { content: "\e628"; }

.edui-for-music .edui-icon:before { content: "\e640"; }

.edui-for-gmap .edui-icon:before { content: "\e629"; }

.edui-for-insertframe .edui-icon:before { content: "\e645"; }

.edui-for-pdfformat .edui-icon:before { content: "\e62f"; }

.edui-for-word .edui-icon:before { content: "\e646"; }

.edui-for-excel .edui-icon:before { content: "\e647"; }

.edui-for-time .edui-icon:before { content: "\e64a"; }

.edui-for-snapscreen .edui-icon:before { content: "\e650"; }

.edui-for-wordimage .edui-icon:before { content: "\e652"; }

.edui-for-edittd .edui-icon:before { content: "\e65a"; }

.edui-for-lineheight .edui-icon:before { content: "\e62a"; }

.edui-for-rowspacingbottom .edui-icon:before { content: "\e62b"; }

.edui-for-rowspacingtop .edui-icon:before { content: "\e62c"; }

.edui-for-scrawl .edui-icon:before { content: "\e616"; }

.edui-for-redo .edui-icon:before { content: "\e609"; }

.edui-for-undo .edui-icon:before { content: "\e600"; }

.edui-for-inserttitle .edui-icon:before { content: "\e65b"; }

.edui-for-insertparagraphtrue .edui-icon:before { content: "\e660"; }

.edui-for-aligntable .edui-icon:before { content: "\e662"; }

.edui-for-table .edui-icon:before { content: "\e664"; }

.edui-for-tablealignment-left .edui-icon:before { content: "\e663"; }

.edui-for-tablealignment-center .edui-icon:before { content: "\e665"; }

.edui-for-tablealignment-right .edui-icon:before { content: "\e666"; }

.edui-for-paste .edui-icon:before { content: "\e667"; }

.edui-for-map .edui-icon:before { content: "\e668"; }

.edui-for-directionalityrtl .edui-icon:before { content: "\e601"; }

.edui-for-imagecenter .edui-icon:before { content: "\e602"; }

.edui-for-imagenone .edui-icon:before { content: "\e607"; }

.edui-for-fontborder .edui-icon:before { content: "\e608"; }

.edui-for-edittable .edui-icon:before { content: "\e60a"; }

.edui-for-imageleft .edui-icon:before { content: "\e60b"; }

.edui-for-imageright .edui-icon:before { content: "\e615"; }

.edui-for-insertcol .edui-icon:before { content: "\e625"; }

.edui-for-insertcolnext .edui-icon:before { content: "\e626"; }

.edui-for-insertorderedlist .edui-icon:before { content: "\e635"; }

.edui-for-insertparagraphbeforetable .edui-icon:before { content: "\e636"; }

.edui-for-insertrow .edui-icon:before { content: "\e637"; }

.edui-for-insertrownext .edui-icon:before { content: "\e639"; }

.edui-for-insertunorderedlist .edui-icon:before { content: "\e641"; }

.edui-for-mergeright .edui-icon:before { content: "\e642"; }

.edui-for-mergedown .edui-icon:before { content: "\e643"; }

.edui-for-inserttable .edui-icon:before { content: "\e644"; }

.edui-for-pagebreak .edui-icon:before { content: "\e648"; }

.edui-for-source .edui-icon:before { content: "\e649"; }

.edui-for-splittorows .edui-icon:before { content: "\e64b"; }

.edui-for-splittocols .edui-icon:before { content: "\e64c"; }

.edui-for-splittocells .edui-icon:before { content: "\e64d"; }

.edui-for-arrow .edui-icon:before { content: "\e64f"; }

.edui-for-aligntd .edui-icon:before { content: "\e651"; }

.edui-for-autotypeset .edui-icon:before { content: "\e653"; }

.edui-for-charts .edui-icon:before { content: "\e658"; }

.edui-for-closeerror .edui-icon:before { content: "\e65c"; }

.edui-for-copy .edui-icon:before { content: "\e65f"; }

.edui-for-date .edui-icon:before { content: "\e661"; }

.edui-for-deletetable .edui-icon:before { content: "\e669"; }

.edui-for-directionalityltr .edui-icon:before { content: "\e66a"; }

.edui-for-arrowright .edui-icon:before { content: "\e66b"; }

.edui-for-tableleft .edui-icon:before { content: "\e66c"; }

.edui-for-tableright .edui-icon:before { content: "\e66d"; }

.edui-for-tablecenter .edui-icon:before { content: "\e66e"; }

.edui-for-videoleft .edui-icon:before { content: "\e66f"; }

.edui-for-videocenter .edui-icon:before { content: "\e670"; }

.edui-for-videonone .edui-icon:before { content: "\e671"; }

.edui-for-videoright .edui-icon:before { content: "\e672"; }

.edui-for-template .edui-icon:before { content: "\e64e"; }

.edui-for-addfile .edui-icon:before { content: "\e673"; }

.edui-for-selected .edui-icon:before { content: "\e674"; }

.edui-for-pickarea .edui-icon:before { content: "\e675"; }

.edui-for-overlay .edui-icon:before { content: "\e676"; }

.edui-for-preitem .edui-icon:before { content: "\e677"; }

.edui-for-preitem1 .edui-icon:before { content: "\e678"; }

.edui-for-preitem2 .edui-icon:before { content: "\e679"; }

.edui-for-preitem3 .edui-icon:before { content: "\e67a"; }

.edui-for-preitem4 .edui-icon:before { content: "\e67b"; }


/*splitbutton*/
.edui-notadd .edui-toolbar .edui-splitbutton-body .edui-arrow::after,
.edui-notadd .edui-toolbar .edui-menubutton-body .edui-arrow::after {
    font-family:"edui-notadd" !important;
    font-size:16px;
    font-style:normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    content: "\e64f";
    display: block;
    position: relative;
    right: 5px;
}

.edui-notadd .edui-toolbar .edui-splitbutton .edui-splitbutton-body,
.edui-notadd .edui-toolbar .edui-menubutton .edui-menubutton-body {
    /*padding: 1px;*/
}

.edui-notadd .edui-toolbar .edui-splitborder {
    height: 20px;
}

.edui-notadd .edui-toolbar .edui-state-hover .edui-splitborder {
    /*width: 1px;*/
    /*border-left: 0px solid #dcac6c;*/
}

.edui-notadd .edui-toolbar .edui-state-active .edui-splitborder {
    /*width: 0;*/
    /*border-left: 1px solid gray;*/
}

.edui-notadd .edui-toolbar .edui-state-opened .edui-splitborder {
    /*width: 1px;*/
    /*border: 0;*/
}

.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-checked .edui-icon,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-checked .edui-icon {
    color: #fff;
}

.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-hover .edui-splitbutton-body,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-hover .edui-menubutton-body {
    background-color: #e5e5e5;
    /*border: 1px solid #dcac6c;*/
    padding: 0;
}

.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-checked .edui-splitbutton-body,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-checked .edui-menubutton-body {
    background-color: #666;
    padding: 0;
}

.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-active .edui-splitbutton-body,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-active .edui-menubutton-body {
    background-color: #666;
    padding: 0;
}

.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-active .edui-icon,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-active .edui-icon {
    color: #fff;
}

.edui-notadd .edui-state-disabled .edui-arrow {
    opacity: 0.3;
    _filter: alpha(opacity = 30);
}

.edui-notadd .edui-toolbar .edui-splitbutton .edui-state-opened .edui-splitbutton-body,
.edui-notadd .edui-toolbar .edui-menubutton .edui-state-opened .edui-menubutton-body {
    padding: 0;
}

.edui-notadd .edui-for-insertorderedlist .edui-bordereraser,
.edui-notadd .edui-for-lineheight .edui-bordereraser,
.edui-notadd .edui-for-rowspacingtop .edui-bordereraser,
.edui-notadd .edui-for-rowspacingbottom .edui-bordereraser,
.edui-notadd .edui-for-insertunorderedlist .edui-bordereraser {
    background-color: white;
}

/* 解决嵌套导致的图标问题 */
.edui-notadd .edui-for-insertorderedlist .edui-popup-body .edui-icon,
.edui-notadd .edui-for-lineheight .edui-popup-body .edui-icon,
.edui-notadd .edui-for-rowspacingtop .edui-popup-body .edui-icon,
.edui-notadd .edui-for-rowspacingbottom .edui-popup-body .edui-icon,
.edui-notadd .edui-for-insertunorderedlist .edui-popup-body .edui-icon {
    background-image: none  ;
}

/* 弹出菜单 */
.edui-notadd .edui-popup {
    z-index: 3000;
    width:auto;
    height:auto;

}

.edui-notadd .edui-popup .edui-shadow {
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.edui-notadd .edui-popup-content {
    border:1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, 0.2);
    *border-right-width: 2px;
    *border-bottom-width: 2px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-box-shadow: 0 3px 4px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 3px 4px rgba(0, 0, 0, 0.2);
    box-shadow: 0 3px 4px rgba(0, 0, 0, 0.2);
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    padding: 10px;
    background:#ffffff;
}

.edui-notadd .edui-popup .edui-bordereraser {
    background-color: white;
    height: 3px;
    display: none;
}

.edui-notadd .edui-menu .edui-bordereraser {
    height: 3px;
}

.edui-notadd .edui-anchor-topleft .edui-bordereraser {
    left: 1px;
    top: -2px;
}

.edui-notadd .edui-anchor-topright .edui-bordereraser {
    right: 1px;
    top: -2px;
}

.edui-notadd .edui-anchor-bottomleft .edui-bordereraser {
    left: 0;
    bottom: -6px;
    height: 7px;
    border-left: 1px solid gray;
    border-right: 1px solid gray;
}

.edui-notadd .edui-anchor-bottomright .edui-bordereraser {
    right: 0;
    bottom: -6px;
    height: 7px;
    border-left: 1px solid gray;
    border-right: 1px solid gray;
}

.edui-popup div{
    width:auto;
    height:auto;
}

.edui-notadd .edui-bubble .edui-popup-body{
    width: 355px !important;
    height: 40px !important;
}
.edui-notadd .edui-editor-messageholder {
    display: block;
    width: 150px;
    height: auto;
    border: 0;
    margin: 0;
    padding: 0;
    position: absolute;
    top: 28px;
    right: 3px;
}

.edui-notadd .edui-message{
    min-height: 10px;
    text-shadow: 0 1px 0 rgba(255,255,255,0.5);
    padding: 0;
    margin-bottom: 3px;
    position: relative;
}
.edui-notadd .edui-message-body{
    border-radius: 3px;
    padding: 8px 15px 8px 8px;
    color: #c09853;
    background-color: #fcf8e3;
    border: 1px solid #fbeed5;
}
.edui-notadd .edui-message-type-info{
    color: #3a87ad;
    background-color: #d9edf7;
    border-color: #bce8f1
}
.edui-notadd .edui-message-type-success{
    color: #468847;
    background-color: #dff0d8;
    border-color: #d6e9c6
}
.edui-notadd .edui-message-type-danger,
.edui-notadd .edui-message-type-error{
    color: #b94a48;
    background-color: #f2dede;
    border-color: #eed3d7
}
.edui-notadd .edui-message .edui-message-closer {
    display: block;
    width: 16px;
    height: 16px;
    line-height: 16px;
    position: absolute;
    top: 0;
    right: 0;
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
    float: right;
    font-size: 20px;
    font-weight: bold;
    color: #999;
    text-shadow: 0 1px 0 #fff;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
}
.edui-notadd .edui-message .edui-message-content {
    font-size: 10pt;
    word-wrap: break-word;
    word-break: normal;
}
/* 弹出对话框按钮和对话框大小 */
.edui-notadd .edui-dialog {
    z-index: 2000;
    position: absolute;

}

.edui-dialog div{
    width:auto;
}

.edui-notadd .edui-dialog-wrap {
    margin-right: 6px;
    margin-bottom: 6px;
}

.edui-notadd .edui-dialog-fullscreen-flag {
    margin-right: 0;
    margin-bottom: 0;
}

.edui-notadd .edui-dialog-body {
    box-sizing: content-box;
    position: relative;
    padding:2px;
    _zoom: 1;
}

.edui-notadd .edui-dialog-fullscreen-flag .edui-dialog-body {
    padding: 0;
}

.edui-notadd .edui-dialog-shadow {
    position: absolute;
    z-index: -1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, 0.2);
    *border-right-width: 2px;
    *border-bottom-width: 2px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
}

.edui-notadd .edui-dialog-foot {
    background-color: white;
}

.edui-notadd .edui-dialog-titlebar {
    height: 50px;
    border-bottom: 2px solid #ccc;
    position: relative;
    cursor: move;
}
.edui-notadd .edui-dialog-caption {
    font-weight: bold;
    font-size: 16px;
    line-height: 50px;
    padding-left: 20px;
    color: #444;
}

.edui-notadd .edui-dialog-draghandle {
    height: 50px;
}

.edui-notadd .edui-dialog-closebutton {
    position: absolute !important;
    right: 20px;
    top: 15px;
}

.edui-notadd .edui-dialog-closebutton .edui-button-body {
    height: 20px;
    width: 20px;
    cursor: pointer;
    /*background: url("../images/icons-all.gif") no-repeat 0 -59px;*/
}

.edui-notadd .edui-dialog-closebutton .edui-state-hover .edui-button-body {
    /*background: url("../images/icons-all.gif") no-repeat 0 -89px;*/
}

.edui-notadd .edui-dialog-foot {
    position: relative;
    height: 56px;
    border-top: 2px solid #ccc;
}

.edui-notadd .edui-dialog-buttons {
    position: absolute;
    right: 10px;
    bottom: 10px;
}

.edui-notadd .edui-dialog-buttons .edui-button {
    margin-right: 10px;
}

.edui-notadd .edui-dialog-buttons .edui-okbutton {
    background-color: #3498db;
    border-radius: 5px;
    color: #fff;
}

.edui-notadd .edui-dialog-buttons .edui-cancelbutton {
    background-color: #f3f3f3;
    border-radius: 5px;
    color: #898989;
}

.edui-notadd .edui-dialog-buttons .edui-button .edui-button-body {
    height: 34px;
    width: 94px;
    font-size: 12px;
    line-height: 34px;
    text-align: center;
    cursor: default;
    border-radius: 5px;
}


.edui-notadd .edui-dialog iframe {
    border: 0;
    padding: 0;
    margin: 0;
    vertical-align: top;
}

.edui-notadd .edui-dialog-modalmask {
    opacity: 0.3;
    filter: alpha(opacity = 30);
    background-color: #000;
    position: absolute;
}

.edui-notadd .edui-dialog-dragmask {
    position: absolute;
    /*z-index: 2001;*/
    background-color: transparent;
    cursor: move;
}

.edui-notadd .edui-dialog-content {
    position: relative;
}

.edui-notadd .dialogcontmask {
    cursor: move;
    visibility: hidden;
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    filter: alpha(opacity = 0);
}

/*link-dialog*/
.edui-notadd .edui-for-link .edui-dialog-content {
    width: 420px;
    height: 257px;
    overflow: hidden;
}
.edui-dialog .edui-for-link .edui-dialog-body {
    height: 368px !important;
    width: 420px !important;
}

/*background-dialog*/
.edui-notadd .edui-for-background .edui-dialog-content {
    width: 465px;
    height: 287px;
    overflow: hidden;
}
.edui-dialog .edui-for-background .edui-dialog-body {
    height: 398px !important;
    width: 465px !important;
}

/*template-dialog*/
.edui-notadd .edui-for-template .edui-dialog-content {
    width: 682px;
    height: 406px;
    overflow: hidden;
}
.edui-dialog .edui-for-template .edui-dialog-body {
    height: 516px !important;
    width: 682px !important;
}

/*scrawl-dialog*/
.edui-notadd .edui-for-scrawl .edui-dialog-content {
    width: 640px;
    height: 435px;
}
.edui-dialog .edui-for-scrawl .edui-dialog-body {
    height: 545px !important;
    width: 640px !important;
}

/*spechars-dialog*/
.edui-notadd .edui-for-spechars .edui-dialog-content {
    width: 620px;
    height: 500px;
    *width: 630px;
    *height: 570px;
}

/*image-dialog*/
.edui-notadd .edui-for-insertimage .edui-dialog-content {
    width: 641px;
    height: 455px;
    overflow: hidden;
}
/*webapp-dialog*/
.edui-notadd .edui-for-webapp .edui-dialog-content {
    width: 560px;
    _width: 565px;
    height: 450px;
    overflow: hidden;
}

/*image-insertframe*/
.edui-notadd .edui-for-insertframe .edui-dialog-content {
    width: 400px;
    height: 255px;
    overflow: hidden;
}
.edui-dialog .edui-for-insertframe .edui-dialog-body {
    height: 365px !important;
    width: 400px !important;
}

/*wordImage-dialog*/
.edui-notadd .edui-for-wordimage .edui-dialog-content {
    width: 620px;
    height: 380px;
    overflow: hidden;
}

/*attachment-dialog*/
.edui-notadd .edui-for-attachment .edui-dialog-content {
    width: 650px;
    height: 400px;
    overflow: hidden;
}

/*map-dialog*/
.edui-notadd .edui-for-map .edui-dialog-content {
    width: 640px;
    height: 455px;
}
.edui-dialog .edui-for-map .edui-dialog-body {
    height: 562px !important;
    width: 640px !important;
}

/*gmap-dialog*/
.edui-notadd .edui-for-gmap .edui-dialog-content {
    width: 640px;
    height: 452px;
}
.edui-dialog .edui-for-gmap .edui-dialog-body {
    height: 563px !important;
    width: 640px !important;
}

/*video-dialog*/
.edui-notadd .edui-for-insertvideo .edui-dialog-content {
    width: 641px;
    height: 450px;
}
.edui-dialog .edui-for-insertvideo .edui-dialog-body {
    height: 560px !important;
    width: 640px !important;
}

/*anchor-dialog*/
.edui-notadd .edui-for-anchor .edui-dialog-content {
    width: 320px;
    height: 60px;
    overflow: hidden;
}

/*searchreplace-dialog*/
.edui-notadd .edui-for-searchreplace .edui-dialog-content {
    width: 404px;
    height: 310px;
}

.edui-dialog .edui-for-searchreplace .edui-dialog-body {
    height: 364px !important;
    width: 404px !important;
}

/*help-dialog*/
.edui-notadd .edui-for-help .edui-dialog-content {
    width: 400px;
    height: 420px;
}

/*edittable-dialog*/
.edui-notadd .edui-for-edittable .edui-dialog-content {
    width: 540px;
    _width:590px;
    height: 335px;
}

/*edittip-dialog*/
.edui-notadd .edui-for-edittip .edui-dialog-content {
    width: 225px;
    height: 60px;
}

/*edittd-dialog*/
.edui-notadd .edui-for-edittd .edui-dialog-content {
    width: 240px;
    height: 50px;
}
/*snapscreen-dialog*/
.edui-notadd .edui-for-snapscreen .edui-dialog-content {
    width: 400px;
    height: 220px;
}

/*music-dialog*/
.edui-notadd .edui-for-music .edui-dialog-content {
    width: 630px;
    height: 449px;
}
.edui-dialog .edui-for-music .edui-dialog-body {
    height: 560px !important;
    width: 630px !important;
}

.edui-dialog-body .edui-dialog-foot .edui-icon:before {
    content: '';
}

.edui-dialog-body .edui-dialog-titlebar .edui-icon:before {
    content: '\e654';
    font-size: 18px;
    font-weight: bold;
    color: #ccc;
}

.edui-dialog .edui-dialog-fullscreen-flag .edui-dialog-content {
    padding: 20px;
}

.icon-delete:before {
    content: '\e654';
}

/*段落弹出菜单*/
.edui-notadd .edui-for-paragraph .edui-listitem-label {
    font-family: Tahoma, Verdana, Arial, Helvetica;
}

.edui-notadd .edui-for-paragraph .edui-listitem-label .edui-for-p {
    font-size: 22px;
    line-height: 27px;
}

.edui-notadd .edui-for-paragraph .edui-listitem-label .edui-for-h1 {
    font-weight: bolder;
    font-size: 32px;
    line-height: 36px;
}

.edui-notadd .edui-for-paragraph .edui-listitem-label .edui-for-h2 {
    font-weight: bolder;
    font-size: 27px;
    line-height: 29px;
}

.edui-notadd .edui-for-paragraph .edui-listitem-label .edui-for-h3 {
    font-weight: bolder;
    font-size: 19px;
    line-height: 23px;
}

.edui-notadd .edui-for-paragraph .edui-listitem-label .edui-for-h4 {
    font-weight: bolder;
    font-size: 16px;
    line-height: 19px
}

.edui-notadd .edui-for-paragraph .edui-listitem-label .edui-for-h5 {
    font-weight: bolder;
    font-size: 13px;
    line-height: 16px;
}

.edui-notadd .edui-for-paragraph .edui-listitem-label .edui-for-h6 {
    font-weight: bolder;
    font-size: 12px;
    line-height: 14px;
}
/* 表格弹出菜单 */
.edui-notadd .edui-for-inserttable .edui-splitborder {
    display: none
}
.edui-notadd .edui-for-inserttable  .edui-splitbutton-body .edui-arrow {
    width: 0
}
.edui-notadd .edui-toolbar .edui-for-inserttable  .edui-state-active .edui-splitborder{
    /*border-left: 1px solid transparent;*/
}
.edui-notadd .edui-tablepicker .edui-infoarea {
    height: 14px;
    line-height: 14px;
    font-size: 12px;
    width: 220px;
    margin-bottom: 3px;
    clear: both;
}

.edui-notadd .edui-tablepicker .edui-infoarea .edui-label {
    float: left;
}

.edui-notadd .edui-dialog-buttons .edui-label {
    line-height: 34px;
    font-size: 14px;
}

.edui-notadd .edui-tablepicker .edui-infoarea .edui-clickable {
    float: right;
}

.edui-notadd .edui-tablepicker .edui-pickarea {
    background: url("../images/unhighlighted.gif") repeat;
    height: 220px;
    width: 220px;
}

.edui-notadd .edui-tablepicker .edui-pickarea .edui-overlay {
    background: url("../images/highlighted.gif") repeat;
}

/* 颜色弹出菜单 */
.edui-notadd .edui-colorpicker-topbar {
    height: 27px;
    width: 200px;
}

.edui-notadd .edui-colorpicker-preview {
    height: 20px;
    border: 1px solid #ccc;
    margin-left: 1px;
    width: 128px;
    float: left;
    border-radius: 5px;
}

.edui-notadd .edui-colorpicker-nocolor {
    float: right;
    margin-right: 1px;
    font-size: 12px;
    line-height: 12px;
    height: 22px;
    border: 1px solid #ccc;
    padding: 3px 5px;
    cursor: pointer;
    border-radius: 5px;
}

.edui-notadd .edui-colorpicker-tablefirstrow {
    height: 30px;
}

.edui-notadd .edui-colorpicker-colorcell {
    width: 14px;
    height: 14px;
    display: block;
    margin: 0;
    cursor: pointer;
}

.edui-notadd .edui-colorpicker-colorcell:hover {
    width: 14px;
    height: 14px;
    margin: 0;
}
.edui-notadd .edui-colorpicker-advbtn{
    display: block;
    text-align: center;
    cursor: pointer;
    height:20px;
}
.arrow_down{
    background: white url('../images/arrow_down.png') no-repeat center;
}
.arrow_up{
    background: white url('../images/arrow_up.png') no-repeat center;
}
/*高级的样式*/
.edui-colorpicker-adv{
    position: relative;
    overflow: hidden;
    height: 180px;
    display: none;
}
.edui-colorpicker-plant, .edui-colorpicker-hue {
    border: solid 1px #666;
}
.edui-colorpicker-pad {
    width: 150px;
    height: 150px;
    left: 14px;
    top: 13px;
    position: absolute;
    background: red;
    overflow: hidden;
    cursor: crosshair;
}
.edui-colorpicker-cover{
    position: absolute;
    top: 0;
    left: 0;
    width: 150px;
    height: 150px;
    background: url("../images/tangram-colorpicker.png") -160px -200px;
}
.edui-colorpicker-padDot{
    position: absolute;
    top: 0;
    left: 0;
    width: 11px;
    height: 11px;
    overflow: hidden;
    background: url(../images/tangram-colorpicker.png) 0px -200px repeat-x;
    z-index: 1000;

}
.edui-colorpicker-sliderMain {
    position: absolute;
    left: 171px;
    top: 13px;
    width: 19px;
    height: 152px;
    background: url(../images/tangram-colorpicker.png) -179px -12px no-repeat;

}
.edui-colorpicker-slider {
    width: 100%;
    height: 100%;
    cursor: pointer;
}
.edui-colorpicker-thumb{
    position: absolute;
    top: 0;
    cursor: pointer;
    height: 3px;
    left: -1px;
    right: -1px;
    border: 1px solid black;
    background: white;
    opacity: .8;
}
/*自动排版弹出菜单*/
.edui-notadd .edui-autotypesetpicker .edui-autotypesetpicker-body {
    font-size: 12px;
    margin-bottom: 3px;
    clear: both;
}
.edui-notadd .edui-autotypesetpicker .edui-autotypesetpicker-body tr:last-child td:last-child button {
    border-radius: 3px;
    border: 1px solid #ddd;
    padding: 2px 8px;
    background-color: #f3f3f3;
}
.edui-notadd .edui-autotypesetpicker-body table {
    border-collapse: separate;
    border-spacing: 2px;
}

.edui-notadd .edui-autotypesetpicker-body td {
    font-size: 12px;
    word-wrap:break-word;
}

.edui-notadd .edui-autotypesetpicker-body td input {
    margin: 3px 3px 3px 4px;
    *margin: 1px 0 0 0;
}
/*自动排版弹出菜单*/
.edui-notadd .edui-cellalignpicker .edui-cellalignpicker-body {
    width: 70px;
    font-size: 12px;
    cursor: default;
}

.edui-notadd .edui-cellalignpicker-body table {
    border-collapse: separate;
    border-spacing: 0;
}
.edui-notadd .edui-cellalignpicker-body td{
    padding: 1px;
}
.edui-notadd .edui-cellalignpicker-body .edui-icon{
    height: 20px;
    width: 20px;
    padding: 1px;
    color: #666;
}

.edui-notadd .edui-cellalignpicker-body .edui-left{
    background-position: 0 0;
}

.edui-notadd .edui-cellalignpicker-body .edui-left:before{
    content: '\e66c';
}

.edui-notadd .edui-cellalignpicker-body .edui-center:before{
    content: '\e66e';
}

.edui-notadd .edui-cellalignpicker-body .edui-right:before{
    content: '\e66d';
}

.edui-notadd .edui-cellalignpicker-body .edui-center{
    background-position: -25px 0;
}
.edui-notadd .edui-cellalignpicker-body .edui-right{
    background-position: -51px 0;
}

.edui-notadd .edui-cellalignpicker-body td.edui-state-hover .edui-left{
    background-position: -73px 0;
}

.edui-notadd .edui-cellalignpicker-body td.edui-state-hover .edui-center{
    background-position: -98px 0;
}

.edui-notadd .edui-cellalignpicker-body td.edui-state-hover .edui-right{
    background-position: -124px 0;
}

.edui-notadd .edui-cellalignpicker-body td.edui-cellalign-selected .edui-left {
    background-position: -146px 0;
    background-color: #f1f4f5;
}

.edui-notadd .edui-cellalignpicker-body td.edui-cellalign-selected .edui-center {
    background-position: -245px 0;
}

.edui-notadd .edui-cellalignpicker-body td.edui-cellalign-selected .edui-right {
    background-position: -271px 0;
}
/*分隔线*/
.edui-notadd .edui-toolbar .edui-separator {
    display: none;
}

/*颜色按钮 */
.edui-notadd .edui-toolbar .edui-colorbutton .edui-colorlump {
    position: absolute;
    overflow: hidden;
    bottom: 5px;
    left: 9px;
    width: 18px;
    height: 4px;
}
/*表情按钮及弹出菜单*/
/*去除了表情的下拉箭头*/
.edui-notadd .edui-for-emotion .edui-icon {
    background-position: -60px -20px;
}
.edui-notadd .edui-for-emotion .edui-popup-content iframe
{
    width: 514px;
    height: 380px;
    overflow: hidden;
}
.edui-notadd .edui-for-emotion .edui-popup-content
{
    position: relative;
    z-index: 555
}

.edui-notadd .edui-for-emotion .edui-splitborder {
    display: none
}

.edui-notadd .edui-for-emotion .edui-splitbutton-body .edui-arrow
{
    width: 0
}
.edui-notadd .edui-toolbar .edui-for-emotion  .edui-state-active .edui-splitborder
{
    /*border-left: 1px solid transparent;*/
}
/*contextmenu*/
.edui-notadd .edui-hassubmenu .edui-arrow {
    height: 20px;
    width: 20px;
    float: right;
}

.edui-notadd .edui-hassubmenu .edui-arrow::after {
    font-family: "edui-notadd" !important;
    font-size: 20px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    content: '\e66b';
    line-height: 1;
    display: block;
}

.edui-notadd .edui-menu-body .edui-menuitem {
    padding: 5px 10px;
}

.edui-notadd .edui-menuseparator {
    margin: 2px 0;
    height: 1px;
    overflow: hidden;
    display: none;
}

.edui-notadd .edui-menuseparator-inner {
    border-bottom: 1px solid #e2e3e3;
    margin-left: 29px;
    margin-right: 1px;
}

.edui-notadd .edui-menu-body .edui-state-hover {
    background-color: #f3f3f3;
}

.edui-notadd .edui-menu-body .edui-for-tablesort {
    padding-left: 25px;
}

.edui-notadd .edui-menu-body .edui-for-borderBack {
    padding-left: 25px;
}
/*弹出菜单*/
.edui-notadd .edui-shortcutmenu {
    padding: 2px;
    width: 190px;
    height: 50px;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 5px;
}

/*粘贴弹出菜单*/
.edui-notadd .edui-wordpastepop .edui-popup-content{
    border: none;
    padding: 0;
    width: 54px;
    height: 21px;
}
.edui-notadd  .edui-pasteicon {
    width: 100%;
    height: 100%;
    background-image: url('../images/wordpaste.png');
    background-position: 0 0;
}

.edui-notadd  .edui-pasteicon.edui-state-opened {
    background-position: 0 -34px;
}

.edui-notadd  .edui-pastecontainer {
    position: relative;
    visibility: hidden;
    width: 97px;
    background: #fff;
    border: 1px solid #ccc;
}

.edui-notadd  .edui-pastecontainer .edui-title {
    font-weight: bold;
    background: #F8F8FF;
    height: 25px;
    line-height: 25px;
    font-size: 12px;
    padding-left: 5px;
}

.edui-notadd  .edui-pastecontainer .edui-button {
    overflow: hidden;
    margin: 3px 0;
}

.edui-notadd  .edui-pastecontainer .edui-button .edui-richtxticon,
.edui-notadd  .edui-pastecontainer .edui-button .edui-tagicon,
.edui-notadd  .edui-pastecontainer .edui-button .edui-plaintxticon{
    float: left;
    cursor: pointer;
    width: 29px;
    height: 29px;
    margin-left: 5px;
    background-image: url('../images/wordpaste.png');
    background-repeat: no-repeat;
}
.edui-notadd  .edui-pastecontainer .edui-button .edui-richtxticon {
    margin-left: 0;
    background-position: -109px 0;
}
.edui-notadd  .edui-pastecontainer .edui-button .edui-tagicon {
    background-position: -148px 1px;
}

.edui-notadd  .edui-pastecontainer .edui-button .edui-plaintxticon {
    background-position: -72px 0;
}

.edui-notadd  .edui-pastecontainer .edui-button .edui-state-hover .edui-richtxticon {
    background-position: -109px -34px;
}
.edui-notadd  .edui-pastecontainer .edui-button .edui-state-hover .edui-tagicon{
    background-position: -148px -34px;
}
.edui-notadd  .edui-pastecontainer .edui-button  .edui-state-hover .edui-plaintxticon{
    background-position: -72px -34px;
}