{"name": "@notadd/neditor", "title": "neditor", "description": "Neditor富文本web编辑器", "version": "2.1.18", "homepage": "https://github.com/notadd/neditor", "author": {"name": "Notadd", "url": "http://www.notadd.com"}, "repository": {"type": "git", "url": "https://github.com/notadd/neditor.git"}, "keywords": ["neditor", "web editor", "javascript", "rich editor"], "bugs": {"url": "https://github.com/notadd/neditor/issues"}, "dependencies": {}, "devDependencies": {"conventional-changelog-cli": "2.0.21", "gh-pages": "2.0.1", "grunt": "0.4.5", "grunt-cli": "1.3.2", "grunt-contrib-clean": "0.7.0", "grunt-contrib-concat": "1.0.1", "grunt-contrib-copy": "0.4.1", "grunt-contrib-cssmin": "0.6.2", "grunt-contrib-uglify": "1.0.2", "grunt-text-replace": "0.4.0", "grunt-transcoding": "0.1.3"}, "scripts": {"build": "grunt neditor", "publish": "cd dist && npm publish --access public", "deploy": "gh-pages -d dist", "changelog": "rm -rf CHANGELOG.md && conventional-changelog -p angular -r 4 -i CHANGELOG.md -s"}}