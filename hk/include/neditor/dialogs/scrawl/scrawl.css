/*common
*/
body {
    margin: 0;
}

table {
    width: 100%;
}

table td {
    padding: 2px 4px;
    vertical-align: middle;
}

a {
    text-decoration: none;
}

em {
    font-style: normal;
}

.border_style1 {
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 2px 2px 5px #d3d6da;
}

/*module
*/
.main {
    margin: 20px 20px 0;
    overflow: hidden;
}

.hot {
    float: left;
}

.drawBoard {
    position: relative;
    cursor: crosshair;
}

.brushBorad {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 998;
}

.picBoard {
    border: none;
    text-align: center;
    line-height: 300px;
    cursor: default;
}

.operateBar {
    margin-top: 10px;
    font-size: 12px;
    text-align: center;
}

.operateBar span {
    margin-left: 10px;
    margin-right: 18px;
}

.drawToolbar {
    float: right;
    width: 175px;
    height: 372px;
    overflow: hidden;
}

.colorBar {
    margin-top: 10px;
    margin-left: 10px;
    font-size: 12px;
    text-align: center;
}

#J_removeImg {
    display: block;
    margin-top: 18px;
}

#J_addImg {
    display: block;
    margin-top: 16px;
}

.colorBar #J_colorList tr {
    height: 32px;
}

.colorBar a {
    display: block;
    width: 16px;
    height: 16px;
    border: 1px solid #1006F1;
    border-radius: 8px;
    box-shadow: 2px 2px 5px #d3d6da;
    opacity: 0.6
}

.sectionBar {
    margin-top: 20px;
    font-size: 12px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

/*.sectionBar a{display:inline-block;width:10px;height:12px;color: #888;text-indent: -999px;opacity: 0.3}*/
/*.size1{background: url('images/size.png') 1px center no-repeat ;}*/
/*.size2{background: url('images/size.png') -10px center no-repeat;}*/
/*.size3{background: url('images/size.png') -22px center no-repeat;}*/
/*.size4{background: url('images/size.png') -35px center no-repeat;}*/

.size1 {
    width: 4px;
    height: 4px;
    border-radius: 2px;
    text-indent: -999px;
    opacity: 0.3;
    display: block;
    background-color: #3498db;
    margin-right: 17px;
    margin-left: 15px;
}

.size2 {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    text-indent: -999px;
    opacity: 0.3;
    display: block;
    margin-right: 17px;
    background-color: #3498db;

}

.size3 {
    width: 12px;
    height: 12px;
    border-radius: 6px;
    text-indent: -999px;
    opacity: 0.3;
    display: block;
    background-color: #3498db;
    margin-right: 17px;
}

.size4 {
    width: 16px;
    height: 16px;
    border-radius: 8px;
    text-indent: -999px;
    opacity: 0.3;
    display: block;
    background-color: #3498db;
}

.addImgH {
    position: relative;
}

.addImgH_form {
    position: absolute;
    left: 18px;
    top: -1px;
    width: 75px;
    height: 21px;
    opacity: 0;
    cursor: pointer;
}

.addImgH_form input {
    width: 100%;
}

/*scrawl遮罩层
*/
.maskLayerNull {
    display: none;
}

.maskLayer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.7;
    background-color: #fff;
    text-align: center;
    font-weight: bold;
    line-height: 410px;
    z-index: 1000;
}

.maskLayer input {
    border-radius: 2px;
    border: 1px solid #ccc;
    padding: 4px 12px;
}

/*btn state
*/
.previousStepH .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
}

.previousStepH .text {
    color: #888;
    cursor: pointer;
}

.previousStep .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
}

.previousStep .text {
    color: #ccc;
    cursor: default;
}

.nextStepH .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
}

.nextStepH .text {
    color: #888;
    cursor: pointer;
}

.nextStep .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
}

.nextStep .text {
    color: #ccc;
    cursor: default;
}

.clearBoardH .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    /*background-image: url('images/empty.png');*/
    cursor: default;
}

.clearBoardH .text {
    color: #888;
    cursor: pointer;
}

.clearBoard .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    /*background-image: url('images/empty.png');*/
    cursor: default;
}

.clearBoard .text {
    color: #ccc;
    cursor: default;
}

.scaleBoardH .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
}

.scaleBoardH .text {
    color: #888;
    cursor: pointer;
}

.scaleBoard .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
}

.scaleBoard .text {
    color: #ccc;
    cursor: default;
}

.removeImgH .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('images/delimgH.png');
    cursor: pointer;
}

.removeImgH .text {
    color: #888;
    cursor: pointer;
}

.removeImg .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('images/delimg.png');
    cursor: default;
}

.removeImg .text {
    color: #fff;
    cursor: default;
    padding: 7px 12px;
    border-radius: 6px;
    background-color: #f25f5f;
}

.addImgH .icon {
    vertical-align: top;
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('images/addimg.png')
}

.addImgH .text {
    color: #888;
    cursor: pointer;
    padding: 7px 12px;
    border-radius: 6px;
    background-color: #f3f3f3;
}

/*icon
*/
.brushIcon {
    display: inline-block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin-top: -5px;
}

.eraserIcon {
    display: inline-block;
    width: 16px;
    height: 16px;
    font-size: 18px !important;
    margin-top: -16px;
}

.icon {
    font-size: 18px;
}