<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
<script type="text/javascript" src="images/jquery.tab.js"></script>
</head>
<body>
<div id="dcWrap"> {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain"> {include file="handle.htm"}
  <div id="subBox">
   <div id="sMenu">
    <h3><i class="fa fa-mobile"></i>{$lang.mobile}</h3>
    <ul>
     <li><a href="mobile.php"{if $rec eq 'system'} class="cur"{/if}>{$lang.mobile_system}</a></li>
     <li><a href="mobile.php?rec=nav"{if $rec eq 'nav'} class="cur"{/if}>{$lang.mobile_nav}</a></li>
     <li><a href="mobile.php?rec=show"{if $rec eq 'show'} class="cur"{/if}>{$lang.mobile_show}</a></li>
     <!-- {if !$pure_mode} -->
     <li><a href="mobile.php?rec=theme"{if $rec eq 'theme'} class="cur"{/if}>{$lang.mobile_theme}</a></li>
     <!-- {/if} -->
    </ul>
   </div>
   <div id="sMain">
    <div class="mainBox" style="{$workspace.height}">
     <!-- {if $rec eq 'system'} 手机版设置 开始 -->
     <h3>{$ur_here}</h3>
     <div class="system">
      <form action="mobile.php?rec=system&act=update" method="post" enctype="multipart/form-data">
       <table width="100%" border="0" cellpadding="0" cellspacing="0" class="formTable">
        <!-- {foreach from=$cfg_list_mobile item=cfg_list} -->
        <!-- {if $cfg_list.type eq 'array'} -->
         <!-- {foreach from=$cfg_list.value item=cfg} -->
         <tr>
          <th>{$cfg.lang}</th>
          <td>
           <input type="text" name="{$cfg.name}" value="{$cfg.value}" size="80" class="inpMain" />
           <!-- {if $cfg.cue} -->
            <p class="cue">{$cfg.cue}</p>
           <!-- {/if} -->
          </td>
         </tr>
         <!-- {/foreach} -->
        <!-- {else} -->
        <tr>
         <th>{$cfg_list.lang}</th>
         <td>
          <!-- {if $cfg_list.type eq 'radio'} -->
          <label for="{$cfg_list.name}_0">
           <input type="radio" name="{$cfg_list.name}" id="{$cfg_list.name}_0" value="0"{if $cfg_list.value eq '0'} checked="true"{/if}>
           {$lang.no}</label>
          <label for="{$cfg_list.name}_1">
           <input type="radio" name="{$cfg_list.name}" id="{$cfg_list.name}_1" value="1"{if $cfg_list.value eq '1'} checked="true"{/if}>
           {$lang.yes}</label>
          <!-- {elseif $cfg_list.type eq 'select'} -->
          <select name="{$cfg_list.name}">
           <!-- {foreach from=$cfg_list.box key=name item=value} -->
           <option value="{$value}"{if $cfg_list.value eq $value} selected{/if}>{$value}</option>
           <!-- {/foreach} -->
          </select>
          <!-- {elseif $cfg_list.type eq 'file'} -->
          <input type="file" name="{$cfg_list.name}" size="18" />
          {if $cfg_list.value}<a href="../{$cfg_list.value}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}
          <!-- {elseif $cfg_list.type eq 'textarea'} -->
          <textarea name="{$cfg_list.name}" cols="70" rows="5" class="textArea" />{$cfg_list.value}</textarea>
          <!-- {else} -->
          <input type="text" name="{$cfg_list.name}" value="{$cfg_list.value}" size="80" class="inpMain" />
          <!-- {/if} -->
          <!-- {if $cfg_list.cue} -->
           <!-- {if $cfg_list.type eq 'radio' || $cfg_list.type eq 'select'} -->
           <span class="cue ml">{$cfg_list.cue}</span>
           <!-- {else} -->
           <p class="cue">{$cfg_list.cue}</p>
           <!-- {/if} -->
          <!-- {/if} -->
          <!-- {if $cfg_list.name eq 'mobile_logo' && $cfg_list.value} -->
          <p class="cue">{$lang.mobile_logo_del}</p>
          <!-- {/if} -->
         </td>
        </tr>
        <!-- {/if} -->
        <!-- {/foreach} -->
        <tr>
         <th>{$lang.mobile_subdir_binding}</th>
         <td>
          <!-- {if $subdir_binding} -->
          <a href="mobile.php?rec=system&act=create_subdir_binding" class="btnSet">{$lang.mobile_subdir_binding_recreate}</a>
          <a href="mobile.php?rec=system&act=del_subdir_binding" class="btnSet">{$lang.mobile_subdir_binding_del}</a>
          <a href="{$subdir_binding}" class="btnSet" target="_blank">{$lang.mobile_subdir_binding_text}：{$subdir_binding}</a>
          <!-- {else} -->
          <a href="mobile.php?rec=system&act=create_subdir_binding" class="btnSet">{$lang.mobile_subdir_binding_create}</a>
          <!-- {/if} -->
          <p class="cue" style="padding-bottom: 20px;">{$lang.mobile_subdir_binding_cue}</p>
         </td>
        </tr>
        <tr>
         <th></th>
         <td>
          <input type="hidden" name="token" value="{$token}" />
          <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
         </td>
        </tr>
       </table>
      </form>
     </div>
     <!-- {/if} 手机版设置 结束 --> 
     <!-- {if $rec eq 'nav'} 手机版导航 开始 -->
     <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
      <!-- {if $act eq 'default'} 导航列表 -->
      <table width="100%" border="0" cellpadding="10" cellspacing="0" class="tableBasic">
       <tr>
        <th width="113" align="center">{$lang.nav_name}</th>
        <th align="left">{$lang.nav_link}</th>
        <th width="80" align="center">{$lang.sort}</th>
        <th width="120" align="center">{$lang.handler}</th>
       </tr>
       <!-- {foreach from=$nav_list item=nav_list} -->
       <tr>
        <td>{$nav_list.mark} {$nav_list.nav_name}</td>
        <td>{$nav_list.guide}</td>
        <td align="center">{$nav_list.sort}</td>
        <td align="center"><a href="mobile.php?rec=nav&act=edit&id={$nav_list.id}">{$lang.edit}</a> | <a href="mobile.php?rec=nav&act=del&id={$nav_list.id}">{$lang.del}</a></td>
       </tr>
       <!-- {/foreach} -->
      </table>
      <!-- {/if} -->
      <!-- {if $act eq 'add'} 导航添加 -->
      <script type="text/javascript">
      {literal}
       $(function(){ $(".idTabs").idTabs(); });
      {/literal} 
      </script>
      <div class="idTabs">
        <ul class="tab">
          <li><a href="#nav_add">{$lang.nav_inside}</a></li>
          <li><a href="#nav_defined">{$lang.nav_defined}</a></li>
        </ul>
        <div class="items">
          <div id="nav_add">
           <form action="mobile.php?rec=nav&act=insert" method="post">
            <table width="100%" border="0" cellpadding="5" cellspacing="1" class="tableBasic">
             <tr>
              <td width="80" height="35" align="right">{$lang.nav_system}</td>
              <td>
               <select name="nav_menu" onchange="change('nav_name', this)">
                <option value="">{$lang.nav_menu}</option>
                <!-- {foreach from=$catalog item=catalog} -->
                <option value="{$catalog.module},{$catalog.guide}"{if $catalog.cur} selected="selected"{/if} title="{$catalog.name}">{$catalog.mark} {$catalog.name}</option>
                <!-- {/foreach} -->
               </select>
              </td>
             </tr>
             <tr>
              <td width="80" height="35" align="right">{$lang.nav_name}</td>
              <td>
               <input type="text" id="nav_name" name="nav_name" value="" size="40" class="inpMain" />
              </td>
             </tr>
             <tr>
              <td height="35" align="right">{$lang.sort}</td>
              <td>
               <input type="text" name="sort" value="50" size="5" class="inpMain" />
              </td>
             </tr>
             <tr>
              <td></td>
              <td>
               <input type="hidden" name="token" value="{$token}" />
               <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
              </td>
             </tr>
            </table>
           </form>
          </div>
          <div id="nav_defined">
           <form action="mobile.php?rec=nav&act=insert" method="post">
            <table width="100%" border="0" cellpadding="5" cellspacing="1" class="tableBasic">
             <tr>
              <td width="80" height="35" align="right">{$lang.nav_name}</td>
              <td>
               <input type="text" name="nav_name" value="" size="40" class="inpMain" />
              </td>
             </tr>
             <tr>
              <td height="35" align="right">{$lang.nav_link}</td>
              <td>
               <input type="text" name="guide" value="" size="80" class="inpMain" />
              </td>
             </tr>
             <tr>
              <td height="35" align="right">{$lang.sort}</td>
              <td>
               <input type="text" name="sort" value="50" size="5" class="inpMain" />
              </td>
             </tr>
             <tr>
              <td></td>
              <td>
               <input type="hidden" name="token" value="{$token}" />
               <input type="hidden" name="nav_menu" value="nav,0" />
               <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
              </td>
             </tr>
            </table>
           </form>
          </div>
        </div>
      </div>
      <!-- {/if} -->
      <!-- {if $act eq 'edit'} 导航编辑 -->
      <form action="mobile.php?rec=nav&act=update" method="post">
       <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
         <th colspan="2">&nbsp;</th>
        </tr>
        <!-- {if $nav_info.module neq 'nav'} -->
        <tr>
         <td width="80" height="35" align="right">{$lang.nav_system}</td>
         <td>
          <select name="nav_menu" onchange="change('nav_name', this)">
           <option value="">{$lang.nav_menu}</option>
           <!-- {foreach from=$catalog item=catalog} -->
           <option value="{$catalog.module},{$catalog.guide}"{if $catalog.cur} selected="selected"{/if} title="{$catalog.name}">{$catalog.mark} {$catalog.name}</option>
           <!-- {/foreach} -->
          </select>
         </td>
        </tr>
        <!-- {/if} -->
        <tr>
         <td width="80" height="35" align="right">{$lang.nav_name}</td>
         <td>
          <input type="text" id="nav_name" name="nav_name" value="{$nav_info.nav_name}" size="40" class="inpMain" />
         </td>
        </tr>
       <tr>
         <td height="35" align="right">{$lang.nav_link}</td>
         <td>
          <!-- {if $nav_info.module eq 'nav'} -->
          <input type="text" name="guide" value="{$nav_info.url}" size="60" class="inpMain" />
          <!-- {else} -->
          <input type="text" name="guide_no" value="{$nav_info.url}" size="60" readOnly="true" class="inpMain" />
          <b class="cue">{$lang.nav_not_modify}</b>
          <!-- {/if} -->
         </td>
        </tr>
        <tr>
         <td height="35" align="right">{$lang.sort}</td>
         <td>
          <input type="text" name="sort" value="{if $nav_info.sort}{$nav_info.sort}{else}50{/if}" size="5" class="inpMain" />
         </td>
        </tr>
        <tr>
         <td></td>
         <td>
          <input type="hidden" name="token" value="{$token}" />
          <input type="hidden" name="id" value="{$nav_info.id}" />
          <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
         </td>
        </tr>
       </table>
      </form>
      <!-- {/if} -->
     <!-- {/if} 手机版导航 结束 --> 
     <!-- {if $rec eq 'show'} 手机版幻灯 开始 -->
     <h3>{$ur_here}</h3>
     <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableSimple simpleModule">
      <tr>
       <th>{$lang.show_add}</th>
       <th>{$lang.show_list}</th>
      </tr>
      <tr>
       <td width="350" valign="top"><form action="mobile.php?rec=show&act={if $show}update{else}insert{/if}"{if $show} class="formEdit"{/if} method="post" enctype="multipart/form-data">
         <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableOnebor">
          <tr>
           <td><b>{$lang.show_name}</b>
            <input type="text" name="show_name" value="{$show.show_name}" size="40" class="inpMain" /></td>
          </tr>
          <tr>
           <td><b>{$lang.show_img}</b>
            <input type="file" name="show_img" class="inpFlie" />
            {if $show.show_img}<a href="{$show.show_img}" target="_blank"><img src="images/icon_yes.png"></a>{else}{/if} </td>
          </tr>
          <tr>
           <td><b>{$lang.show_link}</b>
            <input type="text" name="show_link" value="{$show.show_link}" size="40" class="inpMain" /></td>
          </tr>
          <tr>
           <td><b>{$lang.show_text}</b>
            <textarea name="show_text" cols="50" rows="4" class="textArea">{$show.show_text}</textarea>
           </td>
          </tr>
          <tr>
           <td><b>{$lang.sort}</b>
            <input type="text" name="sort" value="{if $show.sort}{$show.sort}{else}50{/if}" size="20" class="inpMain" /></td>
          </tr>
          <tr>
           <td><!-- {if $show} --> 
            <a href="mobile.php?rec=show" class="btnGray">{$lang.cancel}</a>
            <input type="hidden" name="id" value="{$show.id}">
            <!-- {/if} -->
            <input type="hidden" name="token" value="{$token}" />
            <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" /></td>
          </tr>
         </table>
        </form></td>
       <td valign="top"><table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableOnebor">
         <tr>
          <td width="100">{$lang.show_name}</td>
          <td></td>
          <td width="50" align="center">{$lang.sort}</td>
          <td width="80" align="center">{$lang.handler}</td>
         </tr>
         <!-- {foreach from=$show_list item=show_list} -->
         <tr{if $show_list.id eq $id} class="active"{/if}>
         <td><a href="{$show_list.show_img}" target="_blank"><img src="{$show_list.show_img}" width="100" /></a></td>
          <td>{$show_list.show_name}</td>
          <td align="center">{$show_list.sort}</td>
          <td align="center"><a href="mobile.php?rec=show&act=edit&id={$show_list.id}">{$lang.edit}</a> | <a href="mobile.php?rec=show&act=del&id={$show_list.id}">{$lang.del}</a></td>
         </tr>
         <!-- {/foreach} -->
        </table></td>
      </tr>
     </table>
     <!-- {/if} 手机版幻灯 结束 --> 
     <!-- {if $rec eq 'theme'} 手机版模板 开始 -->
     <div id="theme">
      <h3>{$ur_here}</h3>
      <ul class="tab">
       <li><a href="mobile.php?rec=theme"{if $act eq 'default'} class="selected"{/if}>{$lang.theme_list}</a></li>
       <li><a href="mobile.php?rec=theme&act=install"{if $act eq 'install'} class="selected"{/if}>{$lang.theme_install}{if $unum.theme}<span class="unum"><span>{$unum.theme}</span></span>{/if}</a></li>
      </ul>
      <!-- {if $act eq 'default'} -->
      <div class="enable">
       <h2>{$lang.theme_enabled}</h2>
       <p><img src="{$theme_enable.image}" width="170" height="230"></p>
       <dl>
        <dt>{$theme_enable.theme_name}</dt>
        <dd>{$lang.version}：{$theme_enable.version}</dd>
        <dd>{$lang.author}：<a href="{$theme_enable.author_uri}" target="_blank">{$theme_enable.author}</a></dd>
        <dd>{$lang.theme_description}：{$theme_enable.description}</dd>
       </dl>
      </div>
      <div class="themeList">
       <h2>{$lang.theme_installed}</h2>
       <!-- {foreach from=$theme_list item=theme} -->
       <dl class="mobile">
        <p><a href="mobile.php?rec=theme&act=enable&unique_id={$theme.unique_id}"><img src="{$theme.image}" width="170" height="230"></a></p>
        <dt>{$theme.theme_name} {$theme_enable.version}</dt>
        <dd>{$lang.author}：<a href="{$theme.author_uri}" target="_blank">{$theme.author}</a></dd>
        <dd class="btnList"><a href="mobile.php?rec=theme&act=del&unique_id={$theme.unique_id}" class="del">{$lang.del}</a> <span><a href="mobile.php?rec=theme&act=enable&unique_id={$theme.unique_id}">{$lang.enabled}</a> <a href="javascript:void(0)" onclick="douFrame('{$theme.theme_name}', 'https://api.douphp.com/extend.php?rec=client&id={$theme.unique_id}', 'cloud.php?rec=details')">{$lang.theme_preview}</a></span></dd>
       </dl>
       <!-- {/foreach} -->
      </div>
      <!-- {/if} -->
      <!-- {if $act eq 'install'} -->
      <div class="selector"></div>
      <div class="cloudList themeList">
      </div>
      <script type="text/javascript">get_cloud_list('mobile', '{$get}', '{$localsite}')</script>
      <div class="pager"></div>
      <!-- {/if} -->
     </div>
     <!-- {/if} 手机版模板 结束 --> 
    </div>
   </div>
  </div>
 </div>
 {include file="footer.htm"} </div>
</body>
</html>