<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
</head>
<body>
<div id="dcWrap"> {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain"> {include file="handle.htm"}
  <div class="mainBox" style="{$workspace.height}">
   <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
   <!-- {if $rec eq 'default'} 数据备份 -->
   <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
    <form name="myform" method="post" action="backup.php?rec=backup">
     <tr>
      <th align="center"> <input name='chkall' type='checkbox' id='chkall' onclick='selectcheckbox(this.form)' value='check' checked>
      </th>
      <th align="left">{$lang.backup_table_name}</th>
      <th class="m-none" align="center">{$lang.backup_table_engine}</th>
      <th class="m-none" align="center">{$lang.backup_table_rows}</th>
      <th class="m-none" align="center">{$lang.backup_data_length}</th>
      <th class="m-none" align="center">{$lang.backup_index_length}</th>
      <th class="m-none" align="center">{$lang.backup_data_free}</th>
     </tr>
     <!-- {foreach from=$tables item=tables} -->
     <tr>
      <td align="center"><input type=checkbox name=tables[] value={$tables.Name} checked></td>
      <td align="left">{$tables.Name}</td>
      <td class="m-none" align="center">{$tables.Engine}</td>
      <td class="m-none" align="center">{$tables.Rows}</td>
      <td class="m-none" align="center">{$tables.Data_length}</td>
      <td class="m-none" align="center">{$tables.Index_length}</td>
      <td class="m-none" align="center">{$tables.Data_free}</td>
     </tr>
     <!-- {/foreach} -->
     <tr>
      <td colspan="7" align="right">{$lang.backup_total_size} {$totalsize} KB </td>
     </tr>
     <tr>
      <td colspan="7" align="center">{$lang.backup_vol_set}</td>
     </tr>
     <tr>
      <td colspan="7" align="center"> {$lang.backup_sql_filename}：
       <input type="text" class="inpMain" name="filename" value="{$filename}" size=30>
       {$lang.backup_vol_size}：
       <input type="text" class="inpMain" name="vol_size" value="2048" size="5">
       K </td>
     </tr>
     <tr>
      <td height="26" colspan="7"><input type="hidden" name="token" value="{$token}" />
       <input type="hidden" name="totalsize" value="{$totalsize}">
       <input type="submit" name="submit" class="btn" value="{$lang.backup_submit}"  onClick="document.myform.action='backup.php?rec=backup'"></td>
     </tr>
    </form>
   </table>
   <!--{/if}--> 
   <!-- {if $rec eq 'restore'} 恢复备份 -->
   <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
    <tr>
     <th width="" align="left">{$lang.backup_sql_filename}</th>
     <th class="m-none" width="100">{$lang.backup_sql_filesize}</th>
     <th class="m-none" width="160">{$lang.backup_sql_maketime}</th>
     <th width="140">{$lang.handler}</th>
    </tr>
    <!-- {foreach from=$infos item=file} -->
    <tr {if $file.number gt 1}class="child"{/if}>
     <td align="left">{$file.filename}</td>
     <td class="m-none" align="center">{$file.filesize}</td>
     <td class="m-none" align="center">{$file.maketime}</td>
     <td align="center"><!-- {if $file.number le 1} --><a href="backup.php?rec=import&sql_filename={$file.filename}&token={$token}">{$lang.backup_sql_pre}</a> | <a href=backup.php?rec=down&sql_filename={$file.filename}>{$lang.down}</a> | <a href=backup.php?rec=del&sql_filename={$file.filename}>{$lang.del}</a><!-- {/if} --></td>
    </tr>
    <!-- {/foreach} -->
   </table>
   <!--{/if}--> 
  </div>
 </div>
 {include file="footer.htm"} </div>
</body>
</html>