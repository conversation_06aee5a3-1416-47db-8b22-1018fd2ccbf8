<link href="include/UEditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet">
<!-- {if $js neq 'no'} -->
<script type="text/javascript" charset="utf-8" src="include/UEditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="include/UEditor/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="include/UEditor/lang/zh-cn/zh-cn.js"></script>
<!-- {/if} -->
<div id="{$name}File" class="fileBox">
    <ul class="fileBtn">
        <li class="btnFile" onclick="fileBox('content', '{$name}', '{$cur}', '{$item_id}');">{$lang.file_insert_image}</li>
        <li class="fileStatus" style="display:none"><img src="images/loader.gif" alt="uploading" /></li>
    </ul>
</div>
<script type="text/plain" id="{$name}" name="{$name}" class="editor">{$value}</script>
<script type="text/javascript">var ue = UE.getEditor('{$name}')</script>
