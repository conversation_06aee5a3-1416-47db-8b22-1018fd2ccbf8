/*(C) 2013-2024 DouCo Co.,Ltd.*/
/* 全局通用
----------------------------------------------- */
body {
 background-color: #EEEEEE;
 font-family: Microsoft Yahei, \5FAE\8F6F\96C5\9ED1, \5B8B\4F53, Arial, Verdana, sans-serif;
 font-size: 12px;
 color: #333;
 margin: 0;
 padding: 0;
}
body, button, input, textarea {
 font-size: 12px;
 line-height: 1.531;
 outline: none;
 margin: 0;
 padding: 0;
 border: 0;
}
p, ul, ol, dl, dt, dd, form, blockquote {
 margin: 0;
 padding: 0;
}
h1, h2, h3, h4, h5, h6 {
 font-size: 12px;
 margin: 0;
 padding: 0;
 font-weight: normal;
}
img {
 border: 0;
}
ul, ol {
 list-style: none;
}
img {
 border: 0;
}
a {
 text-decoration: none;
 color: #626262;
}
a:hover {
 text-decoration: none;
}
input, textarea, select {
 vertical-align: middle;
}
input:-webkit-autofill {
 -webkit-box-shadow: 0 0 0px 1000px #EEEEEE inset !important;
}
label {
 cursor: pointer;
}
*:focus {
 outline: none;
}
em, i {
 font-style: normal;
}
.bold {
 font-weight: bold;
}
.clear {
 clear: both;
 display: block;
 height: 0;
 line-height: 0;
 font-size: 0;
}
.clearfix:after {
 content: ".";
 display: block;
 height: 0;
 clear: both;
 visibility: hidden;
}
*html .clearfix {
 height: 1%;
}
* + html .clearfix {
 height: 1%;
}
.none {
 display: none;
}
.cue {
 color: #999;
 line-height: 160%;
}
p.cue {
 margin-top: 5px;
}
.cueRed {
 color: #C00;
}
.ml {
 padding-left: 10px;
}
.pl {
 padding-left: 10px;
}
.pr {
 padding-right: 10px;
}
.unread {
 font-weight: bold;
}
#vcode {
 cursor: pointer;
}
@media (max-width: 768px) {
 .m-none {
  display: none;
 }
}
@media (min-width: 768px) {
 .p-none {
  display: none;
 }
}
/* 主体框架
----------------------------------------------- */
#dcWrap {
 width: 100%;
 height: 100%;
 background-color: #FBFBFB;
 overflow: hidden;
 position: relative;
}
#dcHead {
 float: left;
 height: 50px;
 width: 100%;
 background-color: #F1F1F1;
 border-bottom: 1px solid #CCCCCC;
}
#dcLeft {
 float: left;
 width: 210px;
 background-color: #FBFBFB;
}
#dcMain {
 background-color: #EEEEEE;
 border-left: 1px solid #E5E5E5;
 margin-left: 210px;
 padding-top: 51px;
 *padding-top: 0;
 zoom: 1 /* padding-top:40px; */
}
@media (max-width: 768px) {
 #dcLeft {
  position: absolute;
  left: 0;
  top: 51px;
  width: auto;
  min-width: 50px;
  z-index: 99;
 }
 #dcLeft .switch-menu {
  width: 16px;
  line-height: 66px;
  background-color: #CCC;
  color: #FFF;
  text-align: center;
  position: absolute;
  right: -16px;
  top: 50px;
  font-family: Arial, Verdana, sans-serif;
 }
 #dcMain {
  margin-left: 51px;
 }
}
#dcFooter {
 height: 50px;
 border-top: 1px solid #CCCCCC;
 background-color: #EEE;
}
/* 公共顶部
----------------------------------------------- */
#head {
 position: relative;
}
/*- logo -*/
#head .logo {
 float: left;
 width: 50px;
 height: 50px;
 border-right: 1px solid #0065B0;
}
#head .logo a {
 display: block;
 width: 50px;
 height: 50px;
 overflow: hidden;
 text-indent: -9999px;
 background: #0072C6 url(../images/dclogo.png) no-repeat left top;
}
#head .logo a.authorized {
 background-color: #19B4EA;
}
/*- box -*/
#head .box {
 margin-left: 50px;
 height: 50px;
 line-height: 50px;
 color: #D9D9D9;
 font-size: 13px;
 font-weight: bold;
}
#head .box .siteName {
 float: left;
 padding-left: 15px;
 font-size: 13px;
 color: #555555;
}
@media (max-width: 768px) {
 #head .box .siteName {
  display: none;
 }
}
#head .box .nav {
 float: right;
}
#head .box .nav li {
 float: left;
}
#head .box .nav a {
 display: block;
 color: #000;
 padding: 0 25px;
}
@media (max-width: 768px) {
 #head .box .nav a {
  padding: 0 10px;
 }
}
#head .box .nav .active .parent {
 background-color: #FFF;
}
#head .box .nav a:hover {
 background-color: #19B4EA;
 color: #FFF;
}
#head .box .nav .active .parent:hover {
 color: #000;
}
#head .box .nav .dropMenu .menu {
 width: 100%;
 top: 50px;
}
/* 公共管理菜单
----------------------------------------------- */
#menu {
 font-size: 14px;
 font-weight: bold;
}
@media (max-width: 768px) {
 #menu {
  width: 51px;
  overflow: hidden;
 }
 #menu.open {
  width: 200px;
 }
}
#menu ul {
 width: 180px;
 border-top: 1px solid #F9F9F9;
 border-bottom: 1px solid #E5E5E5;
 padding: 4px 0;
}
#menu ul.top {
 width: 210px;
 border-top: 0;
 background-color: #FFF;
 height: 25px;
 padding: 10px 0;
}
#menu ul.bot {
 border-bottom: 0;
}
#menu li {
 width: 210px;
 height: 38px;
 overflow: hidden;
}
#menu li.cur {
 background-color: #19B4EA;
}
#menu li.cur a {
 color: #FFF;
}
#menu li a {
 display: block;
 height: 38px;
 line-height: 38px;
}
#menu ul.top li a {
 color: #19B4EA;
}
#menu li i {
 float: left;
 display: block;
 width: 52px;
 height: 38px;
 text-align: center;
 font: normal normal normal 14px/1 FontAwesome;
 text-rendering: auto;
 -webkit-font-smoothing: antialiased;
 -moz-osx-font-smoothing: grayscale;
 line-height: 38px;
 font-size: 17px;
 color: #999;
}
#menu li i::before {
 content: "\f10c";
}
#menu li.cur i {
 color: #FFF;
}
#menu li em {
 display: block;
 margin-left: 42px;
 height: 38px;
 cursor: pointer;
}
#menu .top li {
 height: 27px;
}
#menu .top li a {
 height: 27px;
 line-height: 27px;
}
#menu .top li i {
 height: 27px;
 line-height: 27px;
 font-size: 18px;
}
#menu .top li i.home::before {
 content: "\f015";
}
#menu li i.system::before {
 content: "\f013";
}
#menu li i.nav::before {
 content: "\f0e8";
}
#menu li i.show::before {
 content: "\f03e";
}
#menu li i.page::before {
 content: "\f1c4";
}
#menu li i.productCat::before {
 content: "\f009";
}
#menu li i.product::before {
 content: "\f00a";
}
#menu li i.articleCat::before {
 content: "\f00b";
}
#menu li i.article::before {
 content: "\f03a";
}
#menu li i.manager::before {
 content: "\f2be";
}
#menu li i.managerLog::before {
 content: "\f1da";
}
#menu li i.backup::before {
 content: "\f1c0";
}
#menu li i.link::before {
 content: "\f0c1";
}
#menu li i.guestbook::before {
 content: "\f040";
}
#menu li i.mobile::before {
 content: "\f10b";
 font-size: 24px;
}
#menu li i.user::before {
 content: "\f0c0";
}
#menu li i.order::before {
 content: "\f07a";
}
#menu li i.plugin::before {
 content: "\f12e";
}
#menu li i.menuPage::before {
 
}
#menu li i.theme::before {
 content: "\f108";
}
#menu li i.caseCat::before {
 content: "\f1d8";
}
#menu li i.case::before {
 content: "\f1d9";
}
#menu li i.downloadCat::before {
 content: "\f0ab";
}
#menu li i.download::before {
 content: "\f01a";
}
#menu li i.miniprogram::before {
 content: "\f1d7";
}
#menu li i.weixin::before {
 content: "\f1d7";
}
#menu li i.chat::before {
 content: "\f27a";
}
/* 快速操作
----------------------------------------------- */
#handle {
 background-color: #FFFFFF;
 border-bottom: 1px solid #E5E5E5;
 height: 45px;
 line-height: 45px;
 color: #A0A0A0;
 padding-left: 1px;
 margin-left: -1px;
 zoom: 1;
 font-weight: bold;
}
#handle ul {
 zoom: 1;
}
#handle li {
 float: left;
 background: url(../images/icon_handle_line.png) no-repeat right 50%;
}
#handle li.last {
 background: url();
}
#handle li a {
 display: block;
 padding: 0 25px 0 20px;
}
#handle li a i {
 display: block;
 width: 15px;
 height: 45px;
 float: left;
 line-height: 45px;
 font-size: 14px;
 color: #19B4EA;
}
#handle li a em {
 display: block;
 margin-left: 20px;
 font-size: 14px;
}
#handle .dropMenu b {
 width: 20px;
 height: 45px;
}
#handle .dropMenu .drop em {
 padding-right: 15px;
}
#handle .dropMenu .menu {
 width: 136px;
}
/* 通用样式
----------------------------------------------- */
/*- 公共主区域 -*/
.mainBox {
 padding: 25px 20px 50px 20px;
}
.mainBox h3 {
 border-bottom: 1px solid #D7D7D7;
 color: #555;
 font-size: 24px;
 font-weight: bold;
 padding-bottom: 20px;
 margin-bottom: 30px;
}
.mainBox h3 p {
 color: #999;
 font-size: 13px;
 margin-top: 5px;
 font-weight: normal;
}
.mainBox h3 .actionBtn {
 float: right;
 display: inline-block;
 background-color: #28B779;
 padding: 0 20px;
 height: 27px;
 line-height: 27px;
 color: #FFFFFF;
 font-size: 13px;
 font-weight: bold;
 margin-left: 10px;
}
.mainBox h3 .actionBtn.gray {
 background-color: #DDD;
 color: #666;
}
.mainBox h3 .add {
 background: #28B779 url(../images/action_btn.gif) no-repeat 20px 50%;
 padding-left: 40px;
}
.mainBox h3 .action {
 display: inline-block;
}
.mainBox h3 .action a {
 display: inline-block;
 margin-left: 10px;
 padding: 6px 40px;
 background-color: #F9F9F9;
 font-size: 14px;
}
.mainBox h3 .action a.cur {
 background-color: #19B4EA;
 color: #FFF;
}
.mainBox .filter {
 margin: 0 0 10px -2px;
 height: 35px;
}
.mainBox .filter form {
 float: left;
}
.mainBox .filter span {
 float: right;
}
@media (max-width: 768px) {
 .mainBox .filter .inpMain {
  width: 120px;
 }
 .mainBox .filter .btnGray {
  padding: 4px 5px;
 }
 .mainBox .filter span {
  display: none;
 }
}
.mainBox .action {
 margin: 10px 0 0 -2px;
}
/*- screen -*/
.mainBox .screen {
 margin-bottom: 15px;
}
.mainBox .screen a {
 display: inline-block;
 padding: 5px 10px;
 background-color: #DDD;
 color: #777;
}
.mainBox .screen a.cur {
 background-color: #19B4EA;
 color: #FFF;
}
.mainBox .warning {
 border: 1px solid #E6DB55;
 background: #FFFBCC;
 padding: 10px;
 margin-bottom: 20px;
}
.mainBox .warning .do {
 float: right;
 font-weight: bold;
}
/*- 下拉菜单 -*/
.dropMenu {
 position: relative;
}
.dropMenu .drop em {
 background: url(../images/icon_drop.png) no-repeat right 50%;
}
.dropMenu.active .drop em {
 background: url(../images/icon_drop_on.png) no-repeat right 50%;
}
.dropMenu .menu {
 display: none;
 position: absolute;
 border-left: 1px solid #EEE;
 border-right: 1px solid #EEE;
 line-height: 30px;
}
.dropMenu .menu a {
 display: block;
 border-bottom: 1px solid #EEE;
 background-color: #FFF;
 color: #555;
}
.dropMenu .menu a:hover {
 background-color: #19B4EA;
 color: #FFF;
}
.dropMenu.active .menu {
 display: block;
}
/*- fileBox -*/
.fileBox {
 zoom: 1;
 overflow: hidden;
}
.fileBox .fileBtn li {
 display: block;
 width: 130px;
 line-height: 30px;
 text-align: center;
 border: 1px solid #DDD;
 border-bottom: 0;
 background-color: #F4F4F4;
 cursor: pointer;
 font-size: 12px;
 color: #333;
}
.fileBox .fileBtn li img {
 margin-top: 12px;
}
.fileBox .fileAdd {
 float: left;
}
.fileBox .fileAdd li {
 width: 80px;
 height: 80px;
 line-height: 80px;
 text-align: center;
 border: 1px dotted #CCCCCC;
 background-color: #F4F4F4;
 cursor: pointer;
 font-size: 14px;
}
.fileBox .fileAdd li img {
 margin-top: 40px;
}
.fileBox .fileList {
 margin-left: 80px;
 zoom: 1;
 overflow: hidden;
}
.fileBox .fileList li {
 float: left;
 border: 1px dotted #CCCCCC;
 height: 80px;
 overflow: hidden;
 margin-left: 10px;
 position: relative;
}
.fileBox .fileList li img {
 width: 80px;
 height: 80px;
}
.fileBox .fileList li span {
 display: none;
 background-color: #60BBFF;
 color: #FFF;
 width: 20px;
 height: 20px;
 text-align: center;
 line-height: 20px;
 cursor: pointer;
 position: absolute;
 bottom: 0;
 right: 0;
}
.fileBox .fileList li:hover span {
 display: block;
}
/*- modelBox -*/
.modelBox {
 zoom: 1;
 overflow: hidden;
}
.modelBox .modelAdd {
 float: left;
 width: 80px;
 height: 80px;
 line-height: 80px;
 text-align: center;
 border: 1px dotted #CCCCCC;
 background-color: #EEE;
 font-size: 14px;
}
.modelBox .modelAdd .inpMain {
 border: 0;
 border-bottom: 1px solid #DDD;
 background-color: #F4F4F4;
 width: 100%;
 text-align: center;
}
.modelBox .modelAdd a {
 display: block;
 line-height: 48px;
}
.modelBox .modelList {
 margin-left: 80px;
 zoom: 1;
 overflow: hidden;
}
.modelBox .modelList li {
 float: left;
 border: 1px dotted #CCCCCC;
 height: 80px;
 overflow: hidden;
 margin-left: 10px;
 position: relative;
}
.modelBox .modelList li img {
 width: 80px;
 height: 80px;
}
.modelBox .modelList li span {
 display: none;
 background-color: #60BBFF;
 color: #FFF;
 width: 20px;
 height: 20px;
 text-align: center;
 line-height: 20px;
 cursor: pointer;
 position: absolute;
 bottom: 0;
 right: 0;
}
.modelBox .modelList li:hover span {
 display: block;
}
/*- bigfile -*/
.bigfile {
 display: inline-block;
}
.bigfile .progress-box {
 width: 100%;
 background-color: #EEE;
 background-image: linear-gradient(to bottom, #EEE, #EEE);
}
.bigfile .progress-box .finish {
 background-color: #19B4EA;
 background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
 background-size: 40px 40px;
 height: 2px;
}
.bigfile .upload-box {
 margin-top: 5px
}
.bigfile .upload-box .btn-upload {
 display: inline-block;
 position: relative;
 cursor: pointer;
 color: #555;
 background: #F4F4F4;
 border: 1px solid #DDD;
 overflow: hidden;
 line-height: 30px;
 padding: 0 50px;
 text-align: center;
}
.bigfile .upload-box .btn-upload input {
 position: absolute;
 font-size: 100px;
 right: 0;
 top: 0;
 opacity: 0;
 filter: alpha(opacity=0);
 cursor: pointer
}
.bigfile .upload-box .btn-upload .percent {
 padding-left: 5px;
 color: #999;
}
/*- noRepeatValue -*/
.noRepeatValue {
 display: inline-block;
 float: left;
 zoom: 1;
 overflow: hidden;
}
.noRepeatValue b {
 display: inline-block;
 background-color: #DDD;
 padding: 0 15px;
 line-height: 30px;
 text-align: center;
 cursor: pointer;
 margin-right: 6px;
}
.noRepeatValue b.cur {
 background-color: #FFF;
}
/* 首页
----------------------------------------------- */
#index #main {
 margin-right: 225px;
}
#index #right {
 float: right;
 width: 200px;
}
@media (max-width: 768px) {
 #index #main {
  margin-right: 0;
 }
}
/*- indexBox -*/
#index .indexBoxTwo {
 zoom: 1;
 overflow: hidden;
}
#index .indexBoxTwo .box-left {
 width: 50%;
 box-sizing: border-box;
 padding-right: 10px;
 float: left;
}
#index .indexBoxTwo .box-right {
 width: 50%;
 box-sizing: border-box;
 padding-left: 10px;
 float: left;
}
@media (max-width: 768px) {
 #index .indexBoxTwo .box-left {
  width: auto;
  padding-right: 0;
  float: none;
 }
 #index .indexBoxTwo .box-right {
  width: auto;
  padding-left: 0;
  float: none;
 }
}
#index .indexBox {
 border: 1px solid #DDDDDD;
 background-color: #E3E3E3;
 padding: 16px;
 margin-bottom: 20px;
 color: #535353;
}
#index .indexBox h2 {
 border-bottom: 1px solid #BBBBBB;
 color: #000;
 font-size: 13px;
 font-weight: bold;
 padding-bottom: 10px;
 margin-bottom: 15px;
}
#index .indexBox h2 em {
 margin-left: 8px;
 font-size: 12px;
 color: #999;
}
#index .indexBox h2 .close {
 font-weight: normal;
 font-size: 16px;
 font-family: Arial, Verdana, sans-serif;
 float: right;
 color: #999;
}
#index .indexBox h2 .close:hover {
 color: #333;
}
/*- quickStart -*/
#index .quickStart {
 zoom: 1;
 overflow: hidden;
}
#index .quickStart li {
 display: block;
 float: left;
 width: 120px;
 margin-right: 20px;
 padding-bottom: 20px;
}
#index .quickStart li span {
 display: block;
 border-right: 1px solid #CCC;
 padding-right: 20px;
}
@media (max-width: 768px) {
 #index .quickStart li {
  display: block;
  float: left;
  width: 50%;
  box-sizing: border-box;
  margin-right: 0;
  padding-bottom: 20px;
 }
 #index .quickStart li span {
  border-right: 0;
  padding-right: 20px;
 }
}
#index .quickStart li span em {
 display: block;
}
#index .quickStart li span a {
 display: inline-block;
 padding: 1px 20px;
 background-color: #CCC;
 margin-top: 10px;
}
#index .quickStart li:hover span a {
 background-color: #19B4EA;
 color: #FFF;
}
/*- backup -*/
#index .backupBox {
 margin-bottom: 20px;
}
#index .backupBox .indexBox {
 margin-bottom: 0;
}
#index .backupBox .backup dl {
 zoom: 1;
 overflow: hidden;
 margin-bottom: 10px;
}
#index .backupBox .backup dl.last {
 margin-bottom: 0;
}
#index .backupBox .backup dt {
 float: left;
}
#index .backupBox .backup dd.size {
 margin: 0 130px 0 240px;
 text-align: center;
}
#index .backupBox .backup dd.date {
 float: right;
}
@media (max-width: 768px) {
 #index .backupBox .backup dd.size {
  display: none;
 }
 #index .backupBox .backup dd.date {
  float: none;
  color: #999;
 }
 #index .backupBox .backup dt {
  float: none;
 }
}
#index .backupBox .prompt {
 padding: 12px 16px;
 height: 54px;
 background-color: #0072C6;
 color: #FFF;
}
#index .backupBox .prompt.red {
 background-color: #D60000;
}
#index .backupBox .prompt .text {
 float: left;
 font-size: 18px;
}
#index .backupBox .prompt .text em {
 display: block;
 font-size: 12px;
 margin-top: 5px;
}
#index .backupBox .prompt .btnBackup {
 display: block;
 float: right;
 width: 130px;
 text-align: right;
 line-height: 54px;
 font-size: 16px;
 color: #FFF;
}
@media (max-width: 768px) {
 #index .backupBox .prompt .btnBackup {
  width: 110px;
  line-height: 54px;
  font-size: 14px;
 }
}
#index .backupBox .prompt .btnBackup:hover {
 color: #EEE;
}
/*- adminLog -*/
#index .adminLog dl {
 border-bottom: 1px solid #CFCFCF;
 padding-bottom: 9px;
 margin-bottom: 8px;
 zoom: 1;
 overflow: hidden;
}
#index .adminLog dl.last {
 border-bottom: 0;
 margin-bottom: 0;
 padding-bottom: 0;
}
#index .adminLog dt {
 float: left;
}
#index .adminLog dd.name {
 margin: 0 130px 0 240px;
 text-align: center;
}
#index .adminLog dd.date {
 float: right;
}
@media (max-width: 768px) {
 #index .adminLog dd.name {
  margin: 0;
  float: right;
 }
 #index .adminLog dd.date {
  display: none;
 }
}
/*- siteInfo -*/
#index .siteInfo {
 height: 155px;
}
@media (max-width: 768px) {
 #index .siteInfo {
  height: auto;
 }
}
#index .siteInfo ul {
 border-bottom: 1px solid #CFCFCF;
 margin-bottom: 9px;
 padding-bottom: 4px;
 zoom: 1;
 overflow: hidden;
}
#index .siteInfo ul.last {
 border-bottom: 0;
 padding-bottom: 0;
 margin-bottom: 0;
}
#index .siteInfo ul li {
 float: left;
 width: 33%;
 margin-bottom: 8px;
}
#index .siteInfo ul li em {
 color: #999;
}
@media (max-width: 768px) {
 #index .siteInfo ul li {
  width: 50%;
 }
}
#index .siteInfo ul.long li {
 float: none;
 width: 100%;
}
/*- powered -*/
#index .powered li {
 margin-bottom: 8px;
}
/*- ipage -*/
#index .page {
 overflow: hidden;
}
#index .page a {
 display: block;
 background-color: #999999;
 color: #FFF;
 float: left;
 width: 70px;
 padding-top: 25px;
 height: 65px;
 text-align: center;
 margin: 0 15px 12px 0;
}
@media (max-width: 768px) {
 #index .page {
  margin-right: -15px;
 }
}
#index .page a.child1 {
 background-color: #CCCCCC;
 color: #666666;
}
#index .page a.child2 {
 background-color: #DEDEDE;
 color: #555555;
}
#index .page a.child3 {
 background-color: #E6E6E6;
 color: #444444;
}
#index .page a:hover {
 background: #19B4EA url(../images/icon_edit_white.png) no-repeat 50% 60px;
 color: #FFF;
}
/*- quickMenu -*/
#index .quickMenu h2 {
 background-color: #0072C6;
 line-height: 46px;
 font-size: 13px;
 color: #FFF;
 padding: 0 15px;
}
#index .quickMenu .menu {
 border: 1px solid #DDDDDD;
 border-top: none;
 background-color: #EEEEEE;
}
#index .quickMenu .menu dt {
 background-color: #E3E3E3;
 padding: 5px 15px;
 color: #333333;
}
#index .quickMenu .menu dt a {
 float: right;
 color: #0072C6;
}
#index .quickMenu .menu dd {
 padding: 8px 15px;
 color: #666;
}
/*- sysMsg -*/
#index .sysMsg {
 margin-bottom: 20px;
}
#index .sysMsg span {
 display: inline-block;
 padding: 10px 30px;
 background-color: #FFFBCC;
 color: #626262;
}
#index .sysMsg a {
 display: inline-block;
 padding: 10px 30px;
 background-color: #E3E3E3;
 border-right: 1px solid #D9D9D9;
}
#index .sysMsg a em {
 color: #999;
}
#index .sysMsg a:hover {
 background-color: #FFFBCC;
}
@media (max-width: 768px) {
 #index .sysMsg a.last {
  display: block;
  margin-top: 15px;
 }
}
/* 单页面
----------------------------------------------- */
#page dl {
 border: 1px dotted #CCC;
 background-color: #999;
 float: left;
 width: 120px;
 height: 100px;
 margin: 0 15px 15px 0;
 text-align: center;
 color: #FFF;
}
#page dl.child1 {
 border: 1px dotted #DDDDDD;
 background-color: #CCCCCC;
 color: #666666;
}
#page dl.child2 {
 border: 1px dotted #EEEEEE;
 background-color: #DEDEDE;
 color: #555555;
}
#page dl.child3 {
 border: 1px dotted #F6F6F6;
 background-color: #E6E6E6;
 color: #444444;
}
#page dt {
 padding: 15px 0;
}
#page dt p {
 padding-top: 5px;
 color: #999;
}
#page dd {
 color: #CCC;
}
#page dd a {
 color: #999;
}
#page dl.child0 dt p, #page dl.child0 a {
 color: #EEE;
}
/* 幻灯与其它首页
----------------------------------------------- */
/*- logo -*/
#siteHome .logo {
 margin-bottom: 10px;
}
#siteHome .logo img {
 height: 50px;
 border: 1px solid #DDD;
}
/*- slideShow -*/
#siteHome .slideBox {
 position: relative;
 background-color: #EEE;
 margin-bottom: 30px;
}
#siteHome .slideBox .slideShow {
 position: relative;
 list-style: none;
 overflow: hidden;
 width: 600px;
 margin: 0 auto;
}
#siteHome .slideBox .slideShow li {
 -webkit-backface-visibility: hidden;
 position: absolute;
 display: none;
 width: 100%;
 left: 0;
 top: 0;
}
#siteHome .slideBox .slideShow li:first-child {
 position: relative;
 display: block;
 float: left;
}
#siteHome .slideBox .slideShow li a {
 display: block;
 float: left;
}
#siteHome .slideBox .slideShow li a img {
 width: 100%;
}
#siteHome .slideBox .slideShow li a span {
 display: block;
 position: absolute;
 width: 100%;
 left: 0;
 top: 0;
 text-align: center;
}
#siteHome .slideBox .slideShow li a span em {
 display: none;
 background-color: #999;
 color: #FFF;
 padding: 2px 30px;
}
#siteHome .slideBox .slideShow li a:hover span em {
 display: inline-block;
 background-color: #0072C6;
 color: #FFF;
}
#siteHome .slideBox .slide_nav {
 display: inline-block;
 position: absolute;
 top: 50%;
 z-index: 2;
 margin-top: -20px;
 margin-left: 10px;
 overflow: hidden;
 opacity: .7;
 font-size: 40px;
 color: #555;
}
#siteHome .slideBox .next {
 right: 0;
 margin-right: 10px;
}
#siteHome .slideBox .slide_nav:active {
 opacity: 1;
}
#siteHome .slideBox .slide_tabs {
 position: absolute;
 left: 0;
 bottom: 10px;
 width: 100%;
 text-align: center;
 font-size: 0;
 z-index: 2;
}
#siteHome .slideBox .slide_tabs li {
 display: inline-block;
 margin: 0 3px;
 *display: inline;
 *zoom: 1;
}
#siteHome .slideBox .slide_tabs a {
 display: inline-block;
 width: 9px;
 height: 9px;
 border-radius: 50%;
 line-height: 20px;
 background-color: rgba(0, 0, 0, .3);
 background-color: #ccc\9;
 overflow: hidden;
 *display: inline;
 *zoom: 1;
}
#siteHome .slideBox .slide_tabs .slide_here a {
 background-color: rgba(0, 0, 0, .8);
 background-color: #666\9;
}
/* 内容盒子
----------------------------------------------- */
#box .instructions {
 margin-bottom: 20px;
 color: #999;
 line-height: 180%;
}
#box .instructions p {
 margin-bottom: 3px;
 color: #19B4EA;
}
#box .noRepeatValue {
 display: block;
 float: none;
 margin-top: 10px;
}
#box .noRepeatValue b {
 display: inline-block;
}
/* 简单模块
----------------------------------------------- */
.simpleModule {
 zoom: 1;
 overflow: hidden;
}
.simpleModule .title {
 border-bottom: 1px solid #DDD;
 color: #333;
 font-size: 16px;
 padding: 10px 7px;
}
.simpleModule .left {
 width: 400px;
 float: left;
}
.simpleModule.big .left {
 width: 650px;
}
.simpleModule .right {
 margin-left: 400px;
}
.simpleModule.big .right {
 margin-left: 650px;
}
.simpleModule .formBox {
 margin: 8px 20px 0 0;
}
.simpleModule form {
 padding: 0 7px;
}
@media (max-width: 768px) {
 .simpleModule .left, .simpleModule.big .left {
  width: auto;
  float: none;
  margin-bottom: 50px;
 }
 .simpleModule .right, .simpleModule.big .right {
  margin-left: 0;
 }
}
.simpleModule .formEdit {
 border: 4px solid #9FD7FF;
}
.simpleModule .active td {
 border-bottom: 4px solid #9FD7FF;
 background-color: #F5F5F5;
}
.simpleModule .formEdit .btn {
 float: right;
}
/* 云中心
----------------------------------------------- */
#cloud .filter a {
 margin-right: 20px;
}
#cloud .handbook {
 margin-bottom: 20px;
}
/*- handle -*/
#cloud .handle h2 {
 background: url(../images/icon_cloud_handle.png) no-repeat left top;
 height: 46px;
 padding-left: 45px;
 font-size: 18px;
}
#cloud .handle p {
 margin-bottom: 12px;
}
#cloud .handle a {
 margin-right: 15px;
}
#cloud .handle i {
 margin: 0 8px;
}
/*- order -*/
#cloud .order h2 {
 background: url(../images/icon_cloud_order.png) no-repeat left top;
 height: 46px;
 padding-left: 45px;
 font-size: 18px;
}
#cloud .order li {
 margin-bottom: 10px;
}
#cloud .order em {
 color: #999;
}
#cloud .order .btn, #cloud .order .btnPayment {
 margin-top: 20px;
}
/*- account -*/
#cloud .account {
 background: url(../images/icon_cloud_account.png) no-repeat center top;
 text-align: center;
 padding-top: 80px;
 margin-top: 150px;
}
#cloud .account .inpMain {
 margin-right: 20px;
}
#cloud .account em {
 font-size: 18px;
 margin-right: 20px;
 color: #19B4EA;
}
#cloud .account .guide {
 margin-top: 60px;
 color: #999;
}
#cloud .account .guide a {
 color: #19B4EA;
}
/* 模块扩展
----------------------------------------------- */
#module .handler .handbook {
 border: 1px solid #EEE;
 font-size: 14px;
 margin: 20px 0;
 padding: 15px;
 color: #555;
}
#module .handler .handbook a {
 color: #19B4EA;
 text-decoration: underline;
}
#module .handler .list h2 {
 background: url(../images/icon_cloud_uninstall.png) no-repeat left top;
 padding-left: 40px;
 height: 30px;
 color: #19B4EA;
 font-size: 16px;
 margin-bottom: 10px;
}
#module .handler .list h2.install {
 background: url(../images/icon_cloud_install.png) no-repeat left top;
}
#module .handler .list ul {
 zoom: 1;
 overflow: hidden;
}
#module .handler .list ul li {
 float: left;
 margin: 0 20px 20px 0;
 text-align: center;
}
#module .handler .list ul li em {
 display: block;
 border: 1px solid #C4C4C4;
 padding: 15px 50px;
 font-size: 14px;
 margin-bottom: 3px;
}
#module .handler .list ul li a {}
/* 模板扩展
----------------------------------------------- */
#theme .enable {
 border-bottom: 1px solid #DDD;
 padding: 30px 0;
 zoom: 1;
 overflow: hidden;
}
#theme .enable h2 {
 font-size: 14px;
 font-weight: bold;
 color: #999;
 margin-bottom: 10px;
}
#theme .enable p {
 float: left;
 padding: 4px;
 background-color: #FFF;
 border: 1px solid #DDD;
}
#theme .enable dl {
 float: left;
 padding: 10px;
}
#theme .enable dl dt {
 font-weight: bold;
 font-size: 14px;
 margin-bottom: 10px;
}
#theme .enable dl dd {
 margin-bottom: 5px;
}
/* -- themeList -- */
#theme .themeList {
 padding-top: 30px;
 zoom: 1;
 overflow: hidden;
}
#theme .themeList h2 {
 font-size: 14px;
 font-weight: bold;
 color: #19B4EA;
 margin-bottom: 15px;
}
#theme .themeList dl {
 border: 1px solid #DDD;
 width: 288px;
 background-color: #FAFAFA;
 float: left;
 margin: 0 20px 20px 0;
 padding-bottom: 5px;
}
#theme .themeList dl p {
 padding: 4px 4px 10px 4px;
 background-color: #FFF;
 border-bottom: 1px solid #EEE;
}
#theme .themeList dl p img {
 width: 280px;
 height: 175px;
}
#theme .themeList dl p a {
 display: block;
 position: relative;
}
#theme .themeList dl p a em {
 display: block;
 width: 280px;
 height: 175px;
 text-align: center;
 box-sizing: border-box;
 padding-top: 55px;
 position: absolute;
 left: 0;
 top: 0;
 background-color:rgba(0,0,0,0.4);
 color: #999;
}
#theme .themeList dl p a em i {
 font-size: 40px;
 color: #999;
 display: block;
}
#theme .themeList dl.mobile {
 width: 178px;
 margin-right: 40px;
}
#theme .themeList dl.mobile p img {
 width: 170px;
 height: 230px;
}
#theme .themeList dl dt {
 font-weight: bold;
 padding: 5px;
}
#theme .themeList dl dd {
 padding: 2px 5px;
}
#theme .themeList dl dd.btnList span a {
 color: #0072C6;
 margin-right: 10px;
}
#theme .themeList dl dd.btnList span a b {
 background-color: #28B779;
 padding: 0 10px;
 color: #FFF;
 -webkit-border-radius: 10px;
 border-radius: 10px;
}
#theme .themeList dl dd.btnList span em {
 margin-right: 10px;
}
#theme .themeList dl dd.btnList .del {
 float: right;
 color: #999;
}
/* 手机版
----------------------------------------------- */
#subBox {}
#subBox #sMenu {
 float: left;
 width: 120px;
}
#subBox #sMain {
 background-color: #F5F5F5;
 border-left: 1px solid #DDD;
 margin-left: 120px;
}
#subBox #sMenu h3 {
 background-color: #19B4EA;
 color: #FFF;
 font-size: 14px;
 font-weight: bold;
 text-align: center;
 padding: 30px 0;
}
#subBox #sMenu h3 i {
 display: block;
 height: 42px;
 margin-bottom: 8px;
 font-size: 38px;
}
@media (max-width: 768px) {
 #subBox #sMenu {
  float: none;
  width: auto;
 }
 #subBox #sMain {
  margin-left: 0;
 }
 #subBox #sMenu h3 {
  padding: 10px 0;
 }
 #subBox #sMenu h3 i {
  height: 42px;
 }
}
#subBox #sMenu ul {
 zoom: 1;
 overflow: hidden;
}
#subBox #sMenu li a {
 display: block;
 line-height: 50px;
 height: 50px;
 border-bottom: 1px solid #DDD;
 padding-left: 20px;
 font-weight: bold;
}
#subBox #sMenu li a.cur {
 background-color: #F5F5F5;
 margin-right: -1px;
}
@media (max-width: 768px) {
 #subBox #sMenu li a {
  float: left;
  line-height: 36px;
  height: 36px;
  padding-right: 20px;
  box-sizing: border-box;
  width: 33.3%;
 }
 #subBox #sMenu li a.cur {
  border-bottom: 1px solid #19B4EA;
 }
}
/* 小程序
----------------------------------------------- */
#miniprogram .release {
 line-height: 200%;
 font-size: 14px;
 color: #777;
}
#miniprogram .release a {
 color: #0072C6;
}
/* -- themeList -- */
#miniprogram .miniprogramList {
 zoom: 1;
 overflow: hidden;
}
#miniprogram .miniprogramList dl {
 border: 1px solid #DDD;
 width: 178px;
 background-color: #FAFAFA;
 float: left;
 margin: 0 40px 20px 0;
 padding-bottom: 5px;
}
#miniprogram .miniprogramList dl p {
 padding: 4px 4px 10px 4px;
 background-color: #FFF;
 border-bottom: 1px solid #EEE;
}
#miniprogram .miniprogramList dl p img {
 width: 170px;
 height: 230px;
}
#miniprogram .miniprogramList dl dt {
 font-weight: bold;
 padding: 5px;
}
#miniprogram .miniprogramList dl dd {
 padding: 2px 5px;
}
#miniprogram .miniprogramList dl dd.btnList span a {
 color: #0072C6;
 margin-right: 10px;
}
#miniprogram .miniprogramList dl dd.btnList span a b {
 background-color: #28B779;
 padding: 0 10px;
 color: #FFF;
 -webkit-border-radius: 10px;
 border-radius: 10px;
}
#miniprogram .miniprogramList dl dd.btnList span em {
 margin-right: 10px;
}
#miniprogram .miniprogramList dl dd.btnList .del {
 float: right;
 color: #999;
}
/* 用户登录
----------------------------------------------- */
#login {
 margin: 0px auto;
 width: 350px;
 margin-top: 40px;
}
@media (max-width: 768px) {
 #login {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
 }
}
#login .logo {
 background: url(../images/logo.png) no-repeat 0 0;
 width: 84px;
 height: 101px;
 margin: 0 auto 30px auto;
}
#login .logo.pure {
 height: 87px;
}
#login .form a {
 color: #0072C5;
}
#login .form dl dt {
 font-size: 14px;
 color: #555555;
 text-align: center;
 margin-bottom: 5px;
}
#login .form dl dd .input {
 color: #333;
 border: 2px solid #BBBBBB;
 background-color: #EEEEEE;
 padding: 5px 8px;
}
#login .form .basicBox dl {
 margin-bottom: 10px;
}
#login .form .basicBox dl dd .input {
 width: 100%;
 box-sizing: border-box;
}
#login .form .captchaBox {
 zoom: 1;
 overflow: hidden;
 position: relative;
 margin-bottom: 20px;
}
#login .form .captchaBox dl.captcha {
 float: left;
}
#login .form .captchaBox dl.captcha .input {
 text-transform: uppercase;
}
#login .form .captchaBox dl dd .input {
 width: 100px;
}
#login .form .captchaBox .captchaImg {
 position: absolute;
 left: 130px;
 bottom: 0;
}
#login .form .captchaBox .checkbox {
 position: absolute;
 right: 0;
 bottom: 0;
 color: #545454;
}
#login .form .btnBox .btn {
 width: 100%;
 margin-bottom: 20px;
}
#login .form .action {
 text-align: center;
 font-size: 14px;
 color: #545454;
}
#login .form .linkBox {
 text-align: center;
 font-size: 14px;
 margin-top: 120px;
}
#login .form .linkBox a {
 margin: 0 15px;
}
/* 其它模块
----------------------------------------------- */
#system .formBox {
 margin-top: 20px;
}
/* developer */
.developer {
 clear: both;
}
.developer h3 {
 border-bottom: 1px solid #D7D7D7;
 color: #555;
 font-size: 24px;
 font-weight: bold;
 padding: 20px 10px;
 margin-bottom: 30px;
}
.developer h3 .actionBtn {
 float: right;
 display: inline-block;
 background-color: #28B779;
 padding: 0 20px;
 height: 27px;
 line-height: 27px;
 color: #FFFFFF;
 font-size: 13px;
 font-weight: bold;
}
.developer h3 em {
 display: block;
 color: #999;
 font-size: 12px;
 font-weight: normal;
}
.developer .subAction {
 padding: 0 10px 15px 10px;
 border-bottom: 1px solid #EEE;
 margin-bottom: 30px;
}
.developer .box {
 padding: 0 10px 50px 10px;
}
.developer .submitBox {
 padding: 10px 0 50px 10px;
}
/* tool */
#tool .directoryCheck {
 font-family: \5b8b\4f53;
}
#tool .directoryCheck dl {
 padding: 10px;
}
#tool .directoryCheck dl.noWrite {
 background: #FFFBCC;
}
#tool .directoryCheck dt {
 border-bottom: 1px solid #DDD;
 padding-bottom: 5px;
 margin-bottom: 5px;
 font-size: 14px;
}
#tool .directoryCheck dl.noWrite dt {
 border-bottom: 1px solid #E6DB55;
}
#tool .directoryCheck dt em {
 float: right;
 color: #777;
}
#tool .directoryCheck dd {
 color: #999;
}
/* 信息提示
----------------------------------------------- */
/*- douMsg -*/
#douMsg {
 background: url(../images/icon_exc_small.gif) no-repeat left top;
 padding: 27px 0 0 70px;
 margin-top: 30px;
}
#douMsg h2 {
 font-size: 16px;
 font-weight: bold;
 color: #0574C7;
}
#douMsg dl {
 background: url(../images/icon_back_arrow.png) no-repeat right bottom;
}
#douMsg dt {
 padding: 10px 0 25px 0;
 font-size: 13px;
 color: #999999;
}
#douMsg dd {
 padding: 100px 20px 20px 0;
 font-size: 12px;
 text-align: right;
}
#douMsg dd a {
 color: #19B4EA;
}
/*- outMsg -*/
#outMsg {
 background: url(../images/icon_exc.gif) no-repeat left top;
 margin: 0px auto;
 width: 340px;
 padding: 30px 0 0 80px;
 margin-top: 160px;
}
#outMsg h2 {
 font-size: 22px;
 font-weight: bold;
 color: #0574C7;
}
#outMsg dt {
 padding: 10px 0 25px 0;
 font-size: 13px;
 color: #666666;
}
#outMsg dd {
 background: url(../images/icon_back.png) no-repeat left top;
 padding-left: 25px;
 font-size: 14px;
}
/* 全局样式
----------------------------------------------- */
/*- form -*/
.btn {
 display: inline-block;
 background-color: #0072C6;
 -moz-border-radius: 2px;
 -webkit-border-radius: 2px;
 border: 0;
 color: #FFF;
 font-size: 14px;
 padding: 4px 55px;
 font-weight: bold;
 text-transform: capitalize;
 cursor: pointer;
 -webkit-appearance: none;
 vertical-align: middle;
}
.btnGray {
 display: inline-block;
 background-color: #DDD;
 -moz-border-radius: 2px;
 -webkit-border-radius: 2px;
 border: 0;
 color: #666;
 font-size: 14px;
 padding: 4px 55px;
 font-weight: bold;
 text-transform: capitalize;
 cursor: pointer;
 -webkit-appearance: none;
 vertical-align: middle;
}
.btnPayment {
 display: inline-block;
 background-color: #ff4246;
 color: #FFF;
 padding: 7px 28px;
 text-transform: capitalize;
 cursor: pointer;
 font-weight: bold;
 font-size: 14px;
 text-align: center;
 -webkit-appearance: none;
}
.btnSet {
 display: inline-block;
 border: 1px solid #DDD;
 color: #555;
 line-height: 25px;
 padding: 0 15px;
 margin-top: 2px;
}
.btnSet:hover {
 background-color: #FFF;
 color: #000;
}
.btnSys {
 display: inline-block;
 border: 1px solid #DDD;
 color: #19B4EA;
 line-height: 18px;
 padding: 0 4px;
 margin: 0 4px;
}
.btnSys:hover {
 border: 1px solid #19B4EA;
 background-color: #19B4EA;
 color: #FFF;
}
.inpSet {
 border: 1px solid #DDD;
 background-color: #FBFBFB;
 padding: 4px 5px;
 color: #666;
 font-size: 12px;
 -webkit-appearance: none;
}
.inpMain {
 border: 1px solid #CCC;
 background-color: #F4F4F4;
 padding: 4px 5px;
 color: #666;
 font-size: 12px;
 line-height: 20px;
 -webkit-appearance: none;
}
.inpMain::placeholder {
 color: #CCC;
}
.inpFlie {
 border: 1px solid #CCC;
 background-color: #F4F4F4;
 padding: 5px 5px;
 color: #333;
 -webkit-appearance: none;
}
.textArea {
 border: 1px solid #CCC;
 background-color: #F4F4F4;
 padding: 4px 5px;
 color: #333;
 font-size: 12px;
 line-height: 20px;
 -webkit-appearance: none;
}
.textAreaAuto {
 border: 1px solid #CCC;
 background-color: #F4F4F4;
 padding: 0;
 color: #333;
 font-size: 12px;
 line-height: 20px;
 resize: none;
 min-height: 40px;
 -webkit-appearance: none;
}
.inpMain, .textArea, .textAreaAuto {
 max-width: 100%;
 box-sizing: border-box;
}
select {
 border: 1px solid #CCC;
 background-color: #F4F4F4;
 padding: 5px 5px 5px 2px;
}
/* -- color -- */
p.lang {
 margin-top: 5px;
}
span.lang {
 margin-left: 5px;
}
@media (max-width: 768px) {
 span.lang {
  display: block;
  margin-left: 0;
  margin-top: 5px;
 }
}
.lang a {
 display: inline-block;
 border: 1px solid #CCC;
 background-color: #F4F4F4;
 padding: 4px 20px;
 color: #999;
 font-size: 12px;
 line-height: 20px;
 cursor: pointer;
 -webkit-appearance: none;
 vertical-align: middle;
 margin-right: 5px;
}
.lang a.cur {
 color: #19B4EA;
}
.lang a:hover {
 border: 1px solid #0072C6;
 background-color: #0072C6;
 color: #FFF;
}
/* -- color -- */
.cRed {
 color: #F40;
}
.cOra {
 color: #f30;
}
.cGre {
 color: #0c6;
}
.cBlu {
 color: #69c;
}
.cGra {
 color: #999;
}
/*- tab -*/
.tab {
 border-bottom: 1px solid #19B4EA;
 font-weight: bold;
 font-size: 14px;
 zoom: 1;
 overflow: hidden;
}
.tab li {
 float: left;
 line-height: 45px;
 height: 45px;
}
.tab a {
 display: block;
 background-color: #F9F9F9;
 padding: 0 44px;
 text-decoration: none;
 color: #999;
}
.tab a.selected {
 background-color: #19B4EA;
 color: #FFF;
}
@media (max-width: 768px) {
 .tab {
  border-bottom: 1px solid #19B4EA;
  font-size: 12px;
  zoom: 1;
  overflow: hidden;
  height: auto;
 }
 .tab li {
  float: left;
  line-height: 35px;
  height: 35px;
 }
 .tab a {
  padding: 0 10px;
 }
 .tab a.selected {
  background-color: #19B4EA;
  color: #FFF;
 }
}
/*- miniTab -*/
.miniTab {
 border-bottom: 1px solid #19B4EA;
 font-weight: bold;
 font-size: 12px;
 overflow: hidden;
}
.miniTab a {
 display: block;
 float: left;
 line-height: 35px;
 height: 35px;
 background-color: #F9F9F9;
 padding: 0 20px;
 text-decoration: none;
 color: #888;
}
.miniTab a.cur {
 background-color: #19B4EA;
 color: #FFF;
}
/*- tabRadio -*/
.tabRadio {
 zoom: 1;
 overflow: hidden;
 padding: 10px 0;
 border-top: 1px solid #DDD;
 margin-top: 7px;
}
.tabRadio li {
 float: left;
 margin-right: 30px;
}
.tabRadio a {
 display: block;
}
.tabRadio a i {
 display: inline-block;
 width: 14px;
 height: 14px;
 margin-right: 5px;
 vertical-align: middle;
 background: url(../images/icon_tab_radio.png) no-repeat;
}
.tabRadio a.selected i {
 background-position: 0 -20px;
}
/*- tableBasic -*/
.tableBasic {
 color: #666666;
}
.tableBasic select {
 color: #999;
}
.tableBasic th {
 height: 25px;
 background-color: #DDD;
 border-bottom: 1px solid #DDD;
 color: #333;
}
.tableBasic td {
 height: 25px;
 border-bottom: 1px solid #DDD;
}
.tableBasic tr:hover td {
 background-color: #E9E9E9;
}
.tableBasic a {
 color: #0072C6;
}
/*- formTable -*/
.formTable {
 color: #888;
}
.formTable select {
 color: #999;
}
.formTable th {
 vertical-align: top;
 width: 160px;
 height: 30px;
 line-height: 30px;
 text-align: left;
 font-weight: normal;
 font-size: 14px;
 padding: 0 0 20px 10px;
}
.formTable td {
 vertical-align: top;
 line-height: 30px;
 color: #555;
}
.formTable td .cue {
 margin: 3px 0 10px 0;
}
/*- form-control -*/
.form-control {
 border: 1px solid #E9E9E9;
 padding: 6px 3px;
}
@media (min-width: 768px) {
 .form-control {
  display: inline-block;
  width: auto;
 }
}
/*- formBasic -*/
.formBasic {
 color: #666666;
}
.formBasic select {
 color: #999;
}
.formBasic dl {
 margin-bottom: 15px;
}
.formBasic dl.light {
 border: 2px solid #19B4EA;
 padding: 15px;
 box-sizing: border-box;
}
.formBasic dl.light dt {
 font-size: 24px;
 color: #333;
}
.formBasic dl .td {
 display: inline-block;
 margin-right: 20px;
}
.formBasic dl dt {
 font-size: 14px;
 margin-bottom: 5px;
}
.formBasic dl dd .newline {
 margin-top: 10px;
}
.formBasic dl dd select {
 color: #555;
}
/*- tableColumn -*/
.tableColumn td {
 padding-right: 20px;
}
/*- showHidden -*/
.showHidden {
 display: block;
 float: right;
 font-size: 12px;
 background-color: #CCC;
}
.showHidden b, .showHidden s {
 display: block;
 float: left;
 padding: 4px 15px;
 background-color: #CCC;
 color: #333;
 cursor: pointer;
 text-decoration: none;
}
.showHidden .d b {
 background-color: #0072C6;
 color: #FFF;
}
.showHidden .h s {
 background-color: #0072C6;
 color: #FFF;
}
/*- unum -*/
.unum {
 display: inline-block;
 background-color: #28B779;
 color: #fff;
 font-size: 9px;
 line-height: 17px;
 font-weight: 600;
 margin: 1px 0 0 2px;
 -webkit-border-radius: 10px;
 border-radius: 10px;
}
.unum span {
 display: block;
 padding: 0 6px;
}
/*- pager -*/
.pager {
 text-align: right;
 padding-top: 20px;
 color: #666;
}
@media (max-width: 768px) {
 .pager {
  text-align: center;
 }
 .pager em {
  display: block;
  margin-bottom: 5px;
 }
 .pager i {
  display: none;
 }
}
.pager a {
 color: #666;
 text-decoration: underline;
}
/*- popup -*/
.popup {
 visibility: hidden;
 top: 200px;
 right: 20px;
 width: 400px;
 background-color: #EEE;
 position: absolute;
 z-index: 1001;
 padding: 30px 40px 34px;
}
.popup.center {
 left: 50%;
 margin-left: -200px;
}
.popup.big {
 width: 600px;
}
.popup .edui-container {
 width: 600px;
}
@media (max-width: 768px) {
 .popup {
  width: 300px;
 }
}
.popup-bg {
 position: fixed;
 height: 100%;
 width: 100%;
 background: #000;
 background: rgba(0, 0, 0, .5);
 z-index: 1000;
 display: none;
 top: 0;
 left: 0;
}
.popup .title {
 font-size: 20px;
 font-weight: bold;
 margin-bottom: 10px;
 color: #000;
}
.popup .content {
 color: #555;
 margin-bottom: 10px;
 position: relative;
}
.popup .action a {
 margin-right: 10px;
}
.popup .content .inpMain, .popup .content .inpFlie {
 width: 100%;
 box-sizing: border-box;
}
.popup .content .icon {
 position: absolute;
 right: -20px;
 top: 12px;
}
.popup .content .btn {
 margin-top: 10px;
}
.popup .content .editor {
 height: 300px;
}
.popup .close-popup {
 font-size: 22px;
 position: absolute;
 top: 8px;
 right: 11px;
 line-height: 100%;
 color: #aaa;
 font-weight: bold;
 cursor: pointer;
}
/*- fragmentList -*/
.fragmentList {
 zoom: 1;
 overflow: hidden;
}
.fragmentList .area-box {
 zoom: 1;
 width: 100%;
 overflow: hidden;
 padding: 0 25px;
}
.fragmentList.inPage .area-box {
 padding: 10px 7px;
}
.fragmentList .area-box.bg {
 background-color: #EEE;
 border-top: 1px solid #DDD;
 border-bottom: 1px solid #DDD;
 box-sizing: border-box;
 padding: 25px 25px 10px 25px;
 margin-bottom: 25px;
}
.fragmentList .item {
 width: 180px;
 float: left;
 margin: 0 15px 15px 0;
 border: 1px solid #EEE;
}
.fragmentList .bg .item, .formBasic .fragmentList .item {
 border: 1px solid #DDD;
 background-color: #F5F5F5;
}
.fragmentList .name {
 font-size: 14px;
 font-weight: bold;
 padding: 4px 10px;
}
.fragmentList .name.parent {
 color: #0072C6;
}
.fragmentList .item .content {
 height: 57px;
 overflow: hidden;
 line-height: 180%;
 padding: 10px;
 background-color: #EEE;
 color: #999;
}
.fragmentList .item .content img {
 max-width: 100%;
 margin: 0 auto;
}
.fragmentList .edit {
 padding: 4px 10px;
}
.fragmentList .edit a {
 color: #28B779;
}
/*- boxList -*/
.boxList {
 zoom: 1;
 overflow: hidden;
 padding: 10px;
}
.boxList dl {
 float: left;
 width: 260px;
 margin: 0 15px 15px 0;
}
.boxList dt {
 border: 1px solid #DDD;
}
.boxList dt p {
 padding: 8px 10px;
}
.boxList dt p.cur {
 background-color: #60BBFF;
 color: #FFF;
}
.boxList dt em {
 display: block;
 margin: 0 10px 10px 10px;
 color: #999;
 height: 36px;
 overflow: hidden;
 text-overflow: ellipsis;
}
.boxList dd {
 color: #CCC;
}
.boxList dd span {
 float: right;
}
/*- maskBox -*/
#maskBox {
 position: relative;
}
#maskBox dt {
 font-size: 14px;
 margin-bottom: 30px;
 color: #999;
 font-weight: 700;
 zoom: 1;
 overflow: hidden;
 line-height: 28px;
}
#maskBox em, #maskBox form {
 float: left;
 margin-right: 20px;
}
#maskBox .count {
 position: relative;
}
#maskBox i {
 display: block;
 float: left;
 width: 30px;
 height: 30px;
 margin: 0 20px 20px 0;
}
#maskBox .maskBg {
 position: absolute;
 z-index: 1;
}
#maskBox .maskBg i {
 background: url(../images/icon_picture_big.png) no-repeat;
}
#maskBox #mask {
 position: absolute;
 z-index: 2;
}
#maskBox #mask i {
 background: url(../images/icon_picture_big.png) no-repeat left bottom;
}
#maskBox #success {
 background: #19B4EA url();
 display: none;
 width: 60px;
 padding: 0 10px;
 line-height: 30px;
 color: #FFF;
 font-weight: bold;
}
/*- douFrame -*/
#douFrame .bg {
 position: fixed;
 top: 0;
 left: 0;
 z-index: 10000001;
 width: 100%;
 height: 100%;
 background: #000;
 filter: alpha(opacity=45);
 opacity: 0.45
}
#douFrame .frame {
 position: fixed;
 _position: absolute;
 z-index: 10000002;
 overflow: hidden;
 padding: 0;
 left: 50%;
}
#douFrame .frame h2 {
 padding: 0 10px;
 background: #0072C6;
 line-height: 32px;
 color: #FFF;
 font-size: 14px;
}
#douFrame .frame h2 .close {
 background: url(../images/icon_fork.png) no-repeat;
 width: 12px;
 height: 12px;
 display: block;
 float: right;
 text-indent: -9999px;
 margin-top: 11px;
}
#douFrame .details {
 border: 2px solid #C4C4C4;
 background-color: #FFF;
 width: 800px;
 top: 100px;
 margin-left: -400px;
}
#douFrame .selectBox {
 border: 2px solid #FFF;
 background-color: #F5F5F5;
 width: 400px;
 top: 300px;
 margin-left: -200px;
 text-align: center;
 padding: 10px 0 30px 0;
}
#douFrame .selectBox a {
 margin: 0 25px;
}
/* 公共底部
----------------------------------------------- */
#footer {
 color: #000000;
}
#footer ul {
 text-align: center;
 padding: 15px 0;
}