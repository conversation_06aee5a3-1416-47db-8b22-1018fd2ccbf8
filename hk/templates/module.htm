<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
</head>
<body>
<div id="dcWrap">
 {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain">
  {include file="handle.htm"}
  <div id="module" class="mainBox" style="{$workspace.height}">
   <h3>{$ur_here}</h3>
   <ul class="tab">
    <li><a href="module.php"{if $rec eq 'default'} class="selected"{/if}>{$lang.module_install_cloud}</a></li>
    <li><a href="module.php?rec=install_local"{if $rec eq 'install_local'} class="selected"{/if}>{$lang.module_install_local}</a></li>
    <li><a href="module.php?rec=uninstall"{if $rec eq 'uninstall'} class="selected"{/if}>{$lang.module_uninstall}</a></li>
   </ul>
   <!-- {if $rec eq 'default'} 模块扩展 -->
   <div class="selector"></div>
   <div class="cloudList">
   </div>
   <script type="text/javascript">get_cloud_list('module', '{$get}', '{$localsite}')</script>
   <div class="pager"></div>
   <!-- {/if} -->
   <!-- {if $rec eq 'install_local'} 本地安装 -->
   <div class="handler">
    <div class="handbook">{$lang.module_install_local_cue}</div>
    <div class="list">
     <h2 class="install">{$lang.module_install_local_list}</h2>
     <ul>
     <!-- {foreach from=$install_list item=module} -->
     <li><em>{$module}</em><a href="cloud.php?rec=handle&type=module&mode=local&cloud_id={$module}">{$lang.module_install_local_btn}</a></li>
     <!-- {/foreach} --> 
     </ul>
    </div>
   </div>
   <!-- {/if} -->
   <!-- {if $rec eq 'uninstall'} 卸载模块 -->
   <div class="handler">
    <div class="handbook">{$lang.module_uninstall_cue}</div>
    <div class="list">
     <h2>{$lang.module_uninstall_list}</h2>
     <ul>
     <!-- {foreach from=$uninstall_list item=module} -->
     <li><em>{$module}</em><a href="module.php?rec=del&token={$token}&extend_id={$module}">{$lang.module_uninstall_btn}</a></li>
     <!-- {/foreach} --> 
     </ul>
    </div>
   </div>
   <!-- {/if} -->
  </div>
  </div>
 {include file="footer.htm"}
 </div>
</body>
</html>