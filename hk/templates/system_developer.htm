<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
</head>
<body>
<div id="dcWrap">
 {include file="header.htm"}
 <div class="developer">
  <h3><a href="system.php" class="actionBtn">{$lang.system}</a>{$lang.system_developer}<em>{$lang.system_developer_cue}</em></h3>
  <form action="system.php?rec=update" method="post" enctype="multipart/form-data">
   <!-- {foreach from=$cfg item=cfg} -->
   <div id="{$cfg.name}">
   <table width="100%" border="0" cellpadding="0" cellspacing="0" class="formTable">
    <!-- {foreach from=$cfg.list item=cfg_list} -->
    {if $cfg_list.type neq 'array'}
    <tr>
     <th>{$cfg_list.lang}</th>
     <td>
      <!-- {if $cfg_list.type eq 'radio'} -->
      <label for="{$cfg_list.name}_0">
       <input type="radio" name="{$cfg_list.name}" id="{$cfg_list.name}_0" value="0"{if $cfg_list.value eq '0'} checked="true"{/if}>
       {$lang.no}</label>
      <label for="{$cfg_list.name}_1">
       <input type="radio" name="{$cfg_list.name}" id="{$cfg_list.name}_1" value="1"{if $cfg_list.value eq '1'} checked="true"{/if}>
       {$lang.yes}</label>
      <!-- {elseif $cfg_list.type eq 'select'} -->
      <select name="{$cfg_list.name}">
       <!-- {foreach from=$cfg_list.box key=name item=value} -->
       <option value="{$value}"{if $cfg_list.value eq $value} selected{/if}>{$value}</option>
       <!-- {/foreach} -->
      </select>
      <!-- {elseif $cfg_list.type eq 'file'} -->
      <input type="file" name="{$cfg_list.name}" size="18" />
      {if $cfg_list.value}<a href="../{$cfg_list.value}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}
      <!-- {elseif $cfg_list.type eq 'textarea'} -->
      <textarea name="{$cfg_list.name}" cols="83" rows="8" class="textArea" />{$cfg_list.value}</textarea>
      <!-- {else} -->
      <input type="text" name="{$cfg_list.name}" value="{$cfg_list.value}" size="80" class="inpMain" />
      <!-- {/if} -->
      <!-- {if $cfg_list.cue} -->
       <!-- {if $cfg_list.type eq 'radio' || $cfg_list.type eq 'select'} -->
       <span class="cue ml">{$cfg_list.cue}</span>
       <!-- {else} -->
       <p class="cue">{$cfg_list.cue}</p>
       <!-- {/if} -->
      <!-- {/if} -->
     </td>
    </tr>
    {else}
    <!-- {foreach from=$cfg_list.value item=cfg} -->
    <tr>
     <th>{$cfg.lang}</th>
     <td>
      <input type="text" name="{$cfg.name}" value="{$cfg.value}" size="80" class="inpMain" />
      <!-- {if $cfg.cue} -->
      <p class="cue">{$cfg.cue}</p>
      <!-- {/if} -->
     </td>
    </tr>
    <!-- {/foreach} -->
    {/if}
    <!-- {/foreach} -->
   </table>
   </div>
   <!-- {/foreach} -->
   <table width="100%" border="0" cellpadding="0" cellspacing="0" class="formTable">
    <!-- {if $authorized && $partner_authorize} -->
    <tr>
     <th>{$lang.pure_mode}</th>
     <td>
      <label for="pure_mode_0">
       <input type="radio" name="pure_mode" id="pure_mode_0" value="0"{if $cfg_pure_mode.value eq '0'} checked="true"{/if}>
       {$lang.no}</label>
      <label for="pure_mode_1">
       <input type="radio" name="pure_mode" id="pure_mode_1" value="1"{if $cfg_pure_mode.value eq '1'} checked="true"{/if}>
       {$lang.yes}</label>
     <span class="cue ml">{$lang.pure_mode_cue}</span>
     </td>
    </tr>
    <!-- {/if} -->
    <tr>
     <th>{$lang.parameter}</th>
     <td>
      <a href="parameter.php" class="btnSet">{$lang.parameter_manager}</a>
     </td>
    </tr>
    <tr>
     <th>{$lang.tool_custom_admin_path}</th>
     <td><a href="tool.php?rec=custom_admin_path" class="btnSet">{$lang.tool_custom_admin_path_btn}</a></td>
    </tr>
   </table>
   <div style="padding: 10px 0 50px 10px">
    <input type="hidden" name="token" value="{$token}" />
    <input type="hidden" name="tab" value="developer" />
    <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
   </div>
   </form>
 </div>
 {include file="footer.htm"}
</div>
</body>
</html>