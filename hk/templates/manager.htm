<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
<link href="templates/home.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
</head>
<body>
<div id="dcWrap"> {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain"> {include file="handle.htm"}
  <div id="subBox">
   <div id="sMenu">
    <h3><i class="fa fa-user-circle-o"></i>{$lang.manager}</h3>
    <ul>
     <li><a href="manager.php"{if $cur eq 'manager'} class="cur"{/if}>{$lang.manager}</a></li>
     <li><a href="manager.php?rec=manager_log"{if $cur eq 'manager_log'} class="cur"{/if}>{$lang.manager_log}</a></li>
    </ul>
   </div>
   <div id="sMain">
    <div id="manager" class="mainBox" style="{$workspace.height}">
     <h3><!-- {if $rec neq 'manager_log'} 管理员列表 --><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a><!-- {/if} -->{$ur_here}</h3>
     <!-- {if $rec eq 'default'} 管理员列表 -->
     <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
      <tr>
       <th class="m-none" width="30" align="center">{$lang.record_id}</th>
       <th align="left">{$lang.manager_username}</th>
       <th class="m-none" align="center">{$lang.manager_email}</th>
       <th class="m-none" align="center">{$lang.manager_add_time}</th>
       <th class="m-none" align="center">{$lang.manager_last_login}</th>
       <th align="center">{$lang.handler}</th>
      </tr>
      <!-- {foreach from=$manager_list item=manager_list} -->
      <tr>
       <td class="m-none" align="center">{$manager_list.user_id}</td>
       <td>{$manager_list.user_name}</td>
       <td class="m-none" align="center">{$manager_list.email}</td>
       <td class="m-none" align="center">{$manager_list.add_time}</td>
       <td class="m-none" align="center">{$manager_list.last_login}</td>
       <td align="center"><a href="manager.php?rec=edit&id={$manager_list.user_id}">{$lang.edit}</a> | <a href="manager.php?rec=del&id={$manager_list.user_id}">{$lang.del}</a></td>
      </tr>
      <!-- {/foreach} -->
     </table>
     <!-- {/if} -->
     <!-- {if $rec eq 'add'} 管理员添加 -->
     <form action="manager.php?rec=insert" method="post">
      <div class="formBasic">
       <dl>
        <dt>{$lang.manager_username}</dt>
        <dd>
         <input type="text" name="user_name" size="40" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.manager_email}</dt>
        <dd>
         <input type="text" name="email" size="40" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.manager_password}</dt>
        <dd>
         <input type="password" name="password" size="40" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.manager_password_confirm}</dt>
        <dd>
         <input type="password" name="password_confirm" size="40" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <input type="hidden" name="token" value="{$token}" />
        <input type="submit" name="submit" class="btn" value="{$lang.btn_submit}" />
       </dl>
      </div>
     </form>
     <!-- {/if} -->
     <!-- {if $rec eq 'edit'} 管理员编辑 -->
     <form action="manager.php?rec=update" method="post">
      <div class="formBasic">
       <dl>
        <dt>{$lang.manager_username}</dt>
        <dd>
         <input type="text" name="user_name" value="{$manager_info.user_name}" size="40" class="inpMain" {if $user.action_list neq 'ALL'}readonly="true"{/if}/>
        </dd>
       </dl>
       <dl>
        <dt>{$lang.manager_email}</dt>
        <dd>
         <input type="text" name="email" value="{$manager_info.email}" size="40" class="inpMain" />
        </dd>
       </dl>
       <!-- {if $if_check} -->
       <dl>
        <dt>{$lang.manager_old_password}</dt>
        <dd>
         <input type="password" name="old_password" size="40" class="inpMain" />
        </dd>
       </dl>
       <!-- {/if} -->
       <dl>
        <dt>{$lang.manager_new_password}</dt>
        <dd>
         <input type="password" name="password" size="40" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.manager_new_password_confirm}</dt>
        <dd>
         <input type="password" name="password_confirm" size="40" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <input type="hidden" name="token" value="{$token}" />
        <input type="hidden" name="id" value="{$manager_info.user_id}" />
        <input type="submit" name="submit" class="btn" value="{$lang.btn_submit}" />
       </dl>
      </div>
     </form>
     <!-- {/if} -->
     <!-- {if $rec eq 'manager_log'} 操作日志 -->
     <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
      <tr>
       <th class="m-none" width="30" align="center">{$lang.record_id}</th>
       <th align="left">{$lang.manager_log_create_time}</th>
       <th align="center">{$lang.manager_log_user_name}</th>
       <th align="left">{$lang.manager_log_action}</th>
       <th class="m-none" align="center">{$lang.manager_log_ip}</th>
      </tr>
      <!-- {foreach from=$log_list item=log_list} -->
      <tr>
       <td align="center" valign="top" class="m-none">{$log_list.id}</td>
       <td valign="top">{$log_list.create_time}</td>
       <td align="center" valign="top">{$log_list.user_name}</td>
       <td align="left" valign="top">{$log_list.action}</td>
       <td align="center" valign="top" class="m-none">{$log_list.ip}</td>
      </tr>
      <!-- {/foreach} -->
     </table>
     {include file="pager.htm"}
     <!-- {/if} -->
    </div>
   </div>
  </div>
 </div>
 {include file="footer.htm"} </div>
</body>
</html>