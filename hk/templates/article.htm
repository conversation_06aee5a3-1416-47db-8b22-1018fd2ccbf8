<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
<script type="text/javascript" src="images/jquery.autotextarea.js"></script>
</head>
<body>
<div id="dcWrap">
 {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain">
   {include file="handle.htm"}
   <div class="mainBox" style="{$workspace.height}">
    <!-- {if $rec eq 'default'} 文章列表 -->
    <h3><a href="{$action_link.href}" class="actionBtn add">{$action_link.text}</a>{$ur_here}</h3>
    <div class="filter">
     <form action="article.php" method="post">
      <select name="cat_id">
       <option value="0">{$lang.uncategorized}</option>
       <!-- {foreach from=$article_category item=cate} -->
       <option value="{$cate.cat_id}"{if $cate.cat_id eq $cat_id} selected="selected"{/if}>{$cate.mark} {$cate.cat_name}</option>
       <!-- {/foreach} -->
      </select>
      <input name="keyword" type="text" class="inpMain" value="{$keyword}" size="20" />
      <input name="submit" class="btnGray" type="submit" value="{$lang.btn_filter}" />
     </form>
     <span>
      <!-- {if $open.sort} -->
      <a class="btnGray" href="tool.php?rec=sort&act=reset&module=article">{$lang.sort_reset}</a>
      <a class="btnGray" href="tool.php?rec=sort&act=close">{$lang.sort_close}</a>
      <!-- {else} -->
      <a class="btnGray" href="tool.php?rec=sort&act=open">{$lang.sort_open}</a>
      <!-- {/if} -->
     </span>
    </div>
    <div id="list">
     <form name="action" method="post" action="article.php?rec=action">
      <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
       <tr>
        <th width="22" align="center"><input name='chkall' type='checkbox' id='chkall' onclick='selectcheckbox(this.form)' value='check'></th>
        <th class="m-none" width="40" align="center">{$lang.record_id}</th>
        <th align="left">{$lang.article_name}</th>
        <th class="m-none" width="150" align="center">{$lang.article_category}</th>
        <!-- {if $open.sort} -->
        <th class="m-none" width="80" align="center">{$lang.sort}</th>
        <!-- {/if} -->
        <th class="m-none" width="80" align="center">{$lang.add_time}</th>
        <th width="80" align="center">{$lang.handler}</th>
       </tr>
       <!-- {foreach from=$article_list item=article} -->
       <tr>
        <td align="center"><input type="checkbox" name="checkbox[]" value="{$article.id}" /></td>
        <td class="m-none" align="center">{$article.id}</td>
        <td><a href="article.php?rec=edit&id={$article.id}">{$article.title}</a><!-- {if $article.image} --> <a href="{$article.image}" target="_blank"><img src="images/icon_picture.png" width="16" height="16" align="absMiddle"></a><!-- {/if} --></td>
        <td class="m-none" align="center"><!-- {if $article.cat_name} --><a href="article.php?cat_id={$article.cat_id}">{$article.cat_name}</a><!-- {else} -->{$lang.uncategorized}<!-- {/if} --></td>
        <!-- {if $open.sort} -->
        <td class="m-none" align="center">{$article.sort}</td>
        <!-- {/if} -->
        <td class="m-none" align="center">{$article.add_time}</td>
        <td align="center"><a href="article.php?rec=edit&id={$article.id}">{$lang.edit}</a> | <a href="article.php?rec=del&id={$article.id}">{$lang.del}</a></td>
       </tr>
       <!-- {/foreach} -->
      </table>
      <div class="action">
       <select name="action" onchange="douAction()">
        <option value="0">{$lang.select}</option>
        <option value="del_all">{$lang.del}</option>
        <option value="category_move">{$lang.category_move}</option>
       </select>
       <select name="new_cat_id" style="display:none">
        <option value="0">{$lang.uncategorized}</option>
        <!-- {foreach from=$article_category item=cate} -->
        <option value="{$cate.cat_id}"{if $cate.cat_id eq $cat_id} selected="selected"{/if}>{$cate.mark} {$cate.cat_name}</option>
        <!-- {/foreach} -->
       </select>
       <input name="submit" class="btn" type="submit" value="{$lang.btn_execute}" />
      </div>
     </form>
    </div>
    {include file="pager.htm"} 
    <!-- {/if} -->
    <!-- {if $rec eq 'add' || $rec eq 'edit'} 文章添加或编辑 -->
    <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
    <form action="article.php?rec={$form_action}" method="post" enctype="multipart/form-data">
     <div class="formBasic">
      <dl>
       <dt>{$lang.article_title}</dt>
       <dd>
        <input type="text" name="title" value="{$article.title}" size="80" class="inpMain" />
        <span class="lang">{$btn_lang.title}</span>
       </dd>
      </dl>
      <dl>
       <dt>{$lang.article_category}</dt>
       <dd>
        <select name="cat_id">
         <option value="0">{$lang.uncategorized}</option>
         <!-- {foreach from=$article_category item=cate} -->
         <option value="{$cate.cat_id}"{if $cate.cat_id eq $article.cat_id} selected="selected"{/if}>{$cate.mark} {$cate.cat_name}</option>
         <!-- {/foreach} -->
        </select>
       </dd>
      </dl>
      <!-- {if $article.defined} -->
      <dl>
       <dt valign="top">{$lang.article_defined}</dt>
       <dd>
        <textarea name="defined" id="defined" cols="50" class="textAreaAuto" style="height:{$article.defined_count}0px">{$article.defined}</textarea>
        <script type="text/javascript">
         {literal}
          $("#defined").autoTextarea({maxHeight:300});
         {/literal}
        </script>
        </dd>
      </dl>
      <!-- {/if} -->
      <dl>
       <dt valign="top">{$lang.article_content}</dt>
       <dd> 
        <!-- 编辑器（所需变量：$cur、$item_id、$item_content） -->
        {include file="editor.htm" name="content" value="$item_content"}
        <p class="lang">{$btn_lang.content}</p>
       </dd>
      </dl>
      <dl>
       <dt>{$lang.thumb}</dt>
       <dd>
        <input type="file" name="image" size="38" class="inpFlie" />
        {if $article.image}<a href="{$article.image}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}
        <p class="cue">{$setting.theme.article_img_size}</p>
       </dd>
      </dl>
      <!-- {if $open.sort} -->
      <dl>
       <dt>{$lang.sort}</dt>
       <dd><input type="text" name="sort" value="{if $article.sort}{$article.sort}{else}50{/if}" size="5" class="inpMain" /></dd>
      </dl>
      <!-- {/if} -->
      <dl>
       <dt>{$lang.add_time}</dt>
       <dd>
        <input type="text" name="add_time" value="{$article.add_time}" size="15" class="inpMain" />
       </dd>
      </dl>
      <dl>
       <dt>{$lang.keywords}</dt>
       <dd>
        <input type="text" name="keywords" value="{$article.keywords}" size="135" class="inpMain" />
        <span class="lang">{$btn_lang.keywords}</span>
       </dd>
      </dl>
      <dl>
       <dt>{$lang.description}</dt>
       <dd>
        <textarea name="description" cols="115" rows="3" class="textArea" />{$article.description}</textarea>
        <p class="lang">{$btn_lang.description}</p>
       </dd>
      </dl>
      <dl>
       <!-- {if !$open.sort} -->
       <input type="hidden" name="sort" value="{if $article.sort}{$article.sort}{else}50{/if}" />
       <!-- {/if} -->
       <input type="hidden" name="token" value="{$token}" />
       <input type="hidden" name="image" value="{$article.image}">
       <input type="hidden" name="id" value="{$article.id}">
       <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
      </dl>
     </div>
    </form>
    <!-- {/if} -->
   </div>
 </div>
 {include file="footer.htm"}
 </div>
<!-- {if $rec eq 'default'} 文章列表 -->
<script type="text/javascript">
{literal}onload = function() {document.forms['action'].reset();}{/literal}
</script>
<!-- {/if} -->
</body>
</html>