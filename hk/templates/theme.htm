<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
</head>
<body>
<div id="dcWrap"> {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain"> {include file="handle.htm"}
  <div id="theme" class="mainBox" style="{$workspace.height}">
   <h3>{$ur_here}</h3>
   <ul class="tab">
    <li><a href="theme.php"{if $rec eq 'default'} class="selected"{/if}>{$lang.theme_list}</a></li>
    <li><a href="theme.php?rec=install"{if $rec eq 'install'} class="selected"{/if}>{$lang.theme_install}{if $unum.theme}<span class="unum"><span>{$unum.theme}</span></span>{/if}</a></li>
   </ul>
   <!-- {if $rec eq 'default'} -->
   <div class="enable">
    <h2>{$lang.theme_enabled}</h2>
    <p><img src="{$theme_enable.image}" width="280" height="175"></p>
    <dl>
     <dt>{$theme_enable.theme_name} {$theme_enable.unique_id}</dt>
     <dd>{$lang.version}：{$theme_enable.version}</dd>
     <dd>{$lang.author}：<a href="{$theme_enable.author_uri}" target="_blank">{$theme_enable.author}</a></dd>
     <dd>{$lang.theme_description}：{$theme_enable.description}</dd>
    </dl>
   </div>
   <div class="themeList">
    <h2>{$lang.theme_installed}</h2>
    <!-- {foreach from=$theme_list item=theme} -->
    <dl>
     <p>
      <a href="theme.php?rec=enable&unique_id={$theme.unique_id}">
       <!-- {if $theme.cloud} --><em><i class="fa fa-cloud-download"></i>历史安装</em><!-- {/if} -->
       <img src="{$theme.image}" width="280" height="175">
      </a>
     </p>
     <dt>{$theme.theme_name} {$theme.unique_id}</dt>
     <dd>{$lang.author}：<a href="{$theme.author_uri}" target="_blank">{$theme.author}</a></dd>
     <dd class="btnList">
      <a href="theme.php?rec=del&unique_id={$theme.unique_id}" class="del">{$lang.del}</a>
      <span>
       <!-- {if $theme.cloud} -->
       <a href="javascript:;" class="actionBtn gray" data-popup-id="linkBox" data-title="安装提示" data-text="安装新模板会清空内容碎片和内容盒子，如果要保存原来模板的数据，安装前请先备份数据库和images/fragment、images/box这两个图片目录" data-btn-name="继续安装" data-align="center" data-btn-link="cloud.php?rec=handle&type=theme&cloud_id={$theme.unique_id}">{$lang.enabled}</a>
       <!-- {else} -->
       <a href="theme.php?rec=enable&unique_id={$theme.unique_id}">{$lang.enabled}</a>
       <!-- {/if} -->
       <a href="javascript:void(0)" onclick="douFrame('{$theme.theme_name}', 'https://api.douphp.com/extend.php?rec=client&id={$theme.unique_id}', 'cloud.php?rec=details')">{$lang.theme_preview}</a>
      </span>
     </dd>
    </dl>
    <!-- {/foreach} --> 
   </div>
   <!-- {/if} --> 
   <!-- {if $rec eq 'install'} 安装模板 -->
   <div class="selector"></div>
   <div class="cloudList"> </div>
   <script type="text/javascript">get_cloud_list('theme', '{$get}', '{$localsite}')</script>
   <div class="pager"></div>
   <!-- {/if} --> 
  </div>
 </div>
 {include file="footer.htm"} </div>
</body>
</html>