<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
<script type="text/javascript" src="images/jquery.form.min.js"></script>
<script type="text/javascript" src="images/jquery.autotextarea.js"></script>
</head>
<body>
<div id="dcWrap"> {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain"> {include file="handle.htm"}
  <div class="mainBox" style="{$workspace.height}"> 
   <!-- {if $rec eq 'default'} 下载列表 -->
   <h3><a href="{$action_link.href}" class="actionBtn add">{$action_link.text}</a>{$ur_here}</h3>
   <div class="filter">
    <form action="download.php" method="post">
     <select name="cat_id">
      <option value="0">{$lang.uncategorized}</option>
      <!-- {foreach from=$download_category item=cate} --> 
      <!-- {if $cate.cat_id eq $cat_id} -->
      <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
      <!-- {else} -->
      <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
      <!-- {/if} --> 
      <!-- {/foreach} -->
     </select>
     <input name="keyword" type="text" class="inpMain" value="{$keyword}" size="20" />
     <input name="submit" class="btnGray" type="submit" value="{$lang.btn_filter}" />
    </form>
    <span>
     <!-- {if $open.sort} -->
     <a class="btnGray" href="tool.php?rec=sort&act=close">{$lang.sort_close}</a>
     <!-- {else} -->
     <a class="btnGray" href="tool.php?rec=sort&act=open">{$lang.sort_open}</a>
     <!-- {/if} -->
    </span>
   </div>
   <div id="list">
    <form name="action" method="post" action="download.php?rec=action">
     <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
      <tr>
       <th width="22" align="center"><input name='chkall' type='checkbox' id='chkall' onclick='selectcheckbox(this.form)' value='check'></th>
       <th class="m-none" width="40" align="center">{$lang.record_id}</th>
       <th align="left">{$lang.download_name}</th>
       <th class="m-none" width="150" align="center">{$lang.download_category}</th>
       <th class="m-none" width="80" align="center">{$lang.download_size}</th>
       <!-- {if $open.sort} -->
       <th class="m-none" width="80" align="center">{$lang.sort}</th>
       <!-- {/if} -->
       <th class="m-none" width="80" align="center">{$lang.add_time}</th>
       <th width="80" align="center">{$lang.handler}</th>
      </tr>
      <!-- {foreach from=$download_list item=download} -->
      <tr>
       <td align="center"><input type="checkbox" name="checkbox[]" value="{$download.id}" /></td>
       <td class="m-none" align="center">{$download.id}</td>
       <td><a href="download.php?rec=edit&id={$download.id}">{$download.title}</a><!-- {if $download.image} --> <a href="{$download.image}" target="_blank"><img src="images/icon_picture.png" width="16" height="16" align="absMiddle"></a><!-- {/if} --></td>
       <td class="m-none" align="center"><!-- {if $download.cat_name} --><a href="download.php?cat_id={$download.cat_id}">{$download.cat_name}</a><!-- {else} -->{$lang.uncategorized}<!-- {/if} --></td>
       <td class="m-none" align="center">{$download.size}</td>
       <!-- {if $open.sort} -->
       <td class="m-none" width="80" align="center">{$download.sort}</td>
       <!-- {/if} -->
       <td class="m-none" align="center">{$download.add_time}</td>
       <td align="center"><a href="download.php?rec=edit&id={$download.id}">{$lang.edit}</a> | <a href="download.php?rec=del&id={$download.id}">{$lang.del}</a></td>
      </tr>
      <!-- {/foreach} -->
     </table>
     <div class="action">
      <select name="action" onchange="douAction()">
       <option value="0">{$lang.select}</option>
       <option value="del_all">{$lang.del}</option>
       <option value="category_move">{$lang.category_move}</option>
      </select>
      <select name="new_cat_id" style="display:none">
       <option value="0">{$lang.uncategorized}</option>
       <!-- {foreach from=$download_category item=cate} --> 
       <!-- {if $cate.cat_id eq $cat_id} -->
       <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
       <!-- {else} -->
       <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
       <!-- {/if} --> 
       <!-- {/foreach} -->
      </select>
      <input name="submit" class="btn" type="submit" value="{$lang.btn_execute}" />
     </div>
    </form>
   </div>
   {include file="pager.htm"} 
   <!-- {/if} --> 
   <!-- {if $rec eq 'add' || $rec eq 'edit'} 下载添加或编辑 -->
   <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
   <form action="download.php?rec={$form_action}" method="post" enctype="multipart/form-data">
    <div class="formBasic">
     <dl>
      <dt>{$lang.download_name}</dt>
      <dd>
       <input type="text" name="title" value="{$download.title}" size="80" class="inpMain" />
      </dd>
     </dl>
     <dl>
      <dt>{$lang.download_category}</dt>
      <dd>
       <select name="cat_id">
        <option value="0">{$lang.uncategorized}</option>
        <!-- {foreach from=$download_category item=cate} --> 
        <!-- {if $cate.cat_id eq $download.cat_id} -->
        <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
        <!-- {else} -->
        <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
        <!-- {/if} --> 
        <!-- {/foreach} -->
       </select>
      </dd>
     </dl>
     <!-- {if $download.defined} -->
     <dl>
      <dt>{$lang.download_defined}</dt>
      <dd>
       <textarea name="defined" id="defined" cols="50" class="textAreaAuto" style="height:{$download.defined_count}0px">{$download.defined}</textarea>
       <script type="text/javascript">
         {literal}
          $("#defined").autoTextarea({maxHeight:300});
         {/literal}
        </script> 
      </dd>
     </dl>
     <!-- {/if} -->
     <dl>
      <dt>{$lang.download_content}</dt>
      <dd> 
       <!-- FileBox -->
       <div id="contentFile" class="fileBox">
        <ul class="fileBtn">
         <li class="btnFile" onclick="fileBox('content');">{$lang.file_insert_image}</li>
         <li class="fileStatus" style="display:none"><img src="images/loader.gif" alt="uploading"/></li>
        </ul>
       </div>
       <!-- /FileBox --> 
       <!-- umeditor -->
       <link href="include/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
       <script type="text/javascript" src="include/umeditor/umeditor.config.js"></script>
       <script type="text/javascript" src="include/umeditor/umeditor.min.js"></script>
       <script type="text/javascript" src="include/umeditor/lang/zh-cn/zh-cn.js"></script>
       <script type="text/plain" id="content" name="content" class="editor">{$download.content}</script>
       <script type="text/javascript">
        {literal}
           //实例化编辑器
           var um = UM.getEditor('content');
           function insertHtml(html) {
               UM.getEditor('content').execCommand('insertHtml', html);
           }
        {/literal}
       </script>
       <!-- /umeditor -->
      </dd>
     </dl>
     <dl>
      <dt>{$lang.thumb}</dt>
      <dd>
       <input type="file" name="image" size="38" class="inpFlie" />
       {if $download.image}<a href="{$download.image}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}</dd>
     </dl>
     <!-- {if $open.sort} -->
     <dl>
      <dt>{$lang.sort}</dt>
      <dd><input type="text" name="sort" value="{if $download.sort}{$download.sort}{else}50{/if}" size="5" class="inpMain" /></dd>
     </dl>
     <!-- {/if} -->
     <dl>
      <dt>{$lang.download_link}</dt>
      <dd>
       <div class="bigfile">
        <input type="text" id="linkId" name="download_link" class="inpMain" value="{$download.download_link}" size="114" />
        <div class="progress-box">
         <div id="finishId" class="finish" style="width: 0%;" progress="0"></div>
        </div>
        <p id="cueId" class="cue"></p>
        <div class="upload-box">
         <a href="javascript:;" class="btn-upload">
          <input type="text" value="{$cur}" id="moduleId">
          <input type="file" id="fileId">
          {$lang.file_bigfile_btn} <em id="rateId" class="percent"></em>
         </a>
        </div>
        <script type="text/javascript" src="images/browser-md5-file.min.js"></script>
        <script type="text/javascript" src="images/bigfile.js"></script>
       </div>
      </dd>
     </dl>
     <dl>
      <dt>{$lang.download_size}</dt>
      <dd>
       <input type="text" name="size" value="{$download.size}" size="50" class="inpMain" />
      </dd>
     </dl>
     <dl>
      <dt>{$lang.keywords}</dt>
      <dd>
       <input type="text" name="keywords" value="{$download.keywords}" size="114" class="inpMain" />
      </dd>
     </dl>
     <dl>
      <dt>{$lang.description}</dt>
      <dd>
       <textarea name="description" cols="115" rows="3" class="textArea" />{$download.description}</textarea>
      </dd>
     </dl>
     <dl>
      <!-- {if !$open.sort} -->
      <input type="hidden" name="sort" value="{if $download.sort}{$download.sort}{else}50{/if}" />
      <!-- {/if} -->
      <input type="hidden" name="token" value="{$token}" />
      <input type="hidden" name="id" value="{$download.id}">
      <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
     </dl>
    </div>
   </form>
   <!-- {/if} --> 
  </div>
 </div>
 {include file="footer.htm"} </div>
<!-- {if $rec eq 'default'} --> 
<script type="text/javascript">
{literal}onload = function() {document.forms['action'].reset();}{/literal}
</script> 
<!-- {else} -->
{include file="filebox.htm"} 
<!-- {/if} -->
</body>
</html>