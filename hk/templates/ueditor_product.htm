<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
<script type="text/javascript" src="images/jquery.form.min.js"></script>
<script type="text/javascript" src="images/jquery.autotextarea.js"></script>
</head>
<body>
<div id="dcWrap"> {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain"> {include file="handle.htm"}
  <div class="mainBox" style="{$workspace.height}"> 
   <!-- {if $rec eq 'default'} 商品列表 -->
   <h3><a href="{$action_link.href}" class="actionBtn add">{$action_link.text}</a>{$ur_here}</h3>
   <div class="filter">
    <form action="product.php" method="post">
     <select name="cat_id">
      <option value="0">{$lang.uncategorized}</option>
      <!-- {foreach from=$product_category item=cate} --> 
      <!-- {if $cate.cat_id eq $cat_id} -->
      <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
      <!-- {else} -->
      <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
      <!-- {/if} --> 
      <!-- {/foreach} -->
     </select>
     <input name="keyword" type="text" class="inpMain" value="{$keyword}" size="20" />
     <input name="submit" class="btnGray" type="submit" value="{$lang.btn_filter}" />
    </form>
    <span> <a class="btnGray" href="product.php?rec=thumb">{$lang.product_thumb}</a> 
    <!-- {if $sort.handle} --> 
    <a class="btnGray" href="product.php?rec=sort&act=handle">{$lang.sort_close}</a> 
    <!-- {else} --> 
    <a class="btnGray" href="product.php?rec=sort&act=handle">{$lang.sort_product}</a> 
    <!-- {/if} --> 
    </span> </div>
   <!-- {if $sort.handle} -->
   <div class="homeSortRight">
    <ul class="homeSortBg">
     {$sort.bg}
    </ul>
    <ul class="homeSortList">
     <!-- {foreach from=$sort.list item=product} -->
     <li> <img src="{$product.image}" width="60" height="60"> <a href="product.php?rec=sort&act=cancel&id={$product.id}" title="{$lang.sort_cancel}">X</a> </li>
     <!-- {/foreach} -->
    </ul>
   </div>
   <!-- {/if} -->
   <div id="list"{if $sort.handle} class="homeSortLeft"{/if}>
    <form name="action" method="post" action="product.php?rec=action">
     <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
      <tr>
       <th width="22" align="center"><input name='chkall' type='checkbox' id='chkall' onclick='selectcheckbox(this.form)' value='check'></th>
       <th class="m-none" width="40" align="center">{$lang.record_id}</th>
       <th align="left">{$lang.product_name}</th>
       <th class="m-none" width="150" align="center">{$lang.product_category}</th>
       <!-- {if $site.stock} -->
       <th width="100" align="center">{$lang.product_stock}</th>
       <!-- {/if} -->
       <th class="m-none" width="80" align="center">{$lang.add_time}</th>
       <th width="80" align="center">{$lang.handler}</th>
      </tr>
      <!-- {foreach from=$product_list item=product} -->
      <tr>
       <td align="center"><input type="checkbox" name="checkbox[]" value="{$product.id}" /></td>
       <td class="m-none" align="center">{$product.id}</td>
       <td><a href="product.php?rec=edit&id={$product.id}">{$product.name}</a></td>
       <td class="m-none" align="center"><!-- {if $product.cat_name} --><a href="product.php?cat_id={$product.cat_id}">{$product.cat_name}</a><!-- {else} -->{$lang.uncategorized}<!-- {/if} --></td>
       <!-- {if $site.stock} -->
       <td align="center">{$product.stock}</td>
       <!-- {/if} -->
       <td class="m-none" align="center">{$product.add_time}</td>
       <td align="center"><!-- {if $sort.handle} --> 
        <a href="product.php?rec=sort&act=set&id={$product.id}">{$lang.sort_btn}</a> 
        <!-- {else} --> 
        <a href="product.php?rec=edit&id={$product.id}">{$lang.edit}</a> | <a href="product.php?rec=del&id={$product.id}">{$lang.del}</a> 
        <!-- {/if} --></td>
      </tr>
      <!-- {/foreach} -->
     </table>
     <div class="action">
      <select name="action" onchange="douAction()">
       <option value="0">{$lang.select}</option>
       <option value="del_all">{$lang.del}</option>
       <option value="category_move">{$lang.category_move}</option>
      </select>
      <select name="new_cat_id" style="display:none">
       <option value="0">{$lang.uncategorized}</option>
       <!-- {foreach from=$product_category item=cate} --> 
       <!-- {if $cate.cat_id eq $cat_id} -->
       <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
       <!-- {else} -->
       <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
       <!-- {/if} --> 
       <!-- {/foreach} -->
      </select>
      <input name="submit" class="btn" type="submit" value="{$lang.btn_execute}" />
     </div>
    </form>
   </div>
   <div class="clear"></div>
   {include file="pager.htm"} 
   <!-- {/if} --> 
   <!-- {if $rec eq 'add' || $rec eq 'edit'} 商品添加或编辑 -->
   <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
   <form action="product.php?rec={$form_action}" method="post" enctype="multipart/form-data">
    <div class="formBasic">
     <dl>
      <dt>{$lang.product_name}</dt>
      <dd>
       <input type="text" name="name" value="{$product.name}" size="80" class="inpMain" />
      </dd>
     </dl>
     <dl>
      <dt>{$lang.product_category}</dt>
      <dd>
       <select name="cat_id">
        <option value="0">{$lang.uncategorized}</option>
        <!-- {foreach from=$product_category item=cate} --> 
        <!-- {if $cate.cat_id eq $product.cat_id} -->
        <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
        <!-- {else} -->
        <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
        <!-- {/if} --> 
        <!-- {/foreach} -->
       </select>
      </dd>
     </dl>
     <dl>
      <dt>{$lang.product_price}</dt>
      <dd>
       <input type="text" name="price" value="{if $product.price}{$product.price}{else}0{/if}" size="40" class="inpMain" />
      </dd>
     </dl>
     <!-- {if $product.defined} -->
     <dl>
      <dt>{$lang.product_defined}</dt>
      <dd>
       <textarea name="defined" id="defined" cols="50" class="textAreaAuto" style="height:{$product.defined_count}0px">{$product.defined}</textarea>
       <script type="text/javascript">
         {literal}
         $("#defined").autoTextarea({maxHeight:300});
         {/literal}
        </script> 
      </dd>
     </dl>
     <!-- {/if} -->
     <dl>
      <dt>{$lang.product_content}</dt>
      <dd> 
       <!-- FileBox -->
       <div id="contentFile" class="fileBox">
        <ul class="fileBtn">
         <li class="btnFile" onclick="fileBox('content');">{$lang.file_insert_image}</li>
         <li class="fileStatus" style="display:none"><img src="images/loader.gif" alt="uploading"/></li>
        </ul>
       </div>
       <!-- /FileBox --> 
       <!-- TinyMCE -->
       <script type="text/javascript" charset="utf-8" src="include/UEditor/ueditor.config.js"></script>
       <script type="text/javascript" charset="utf-8" src="include/UEditor/ueditor.all.min.js"> </script>
       <script type="text/javascript" charset="utf-8" src="include/UEditor/lang/zh-cn/zh-cn.js"></script>
       <script>
       var ue = UE.getEditor('content');
       </script>
       <textarea id="content" name="content" rows="25">{$product.content}</textarea>
       <!-- /TinyMCE -->
       <!-- TinyMCE  
       <script type="text/javascript" charset="utf-8" src="include/tinymce/tinymce.min.js"></script> 
       <script type="text/javascript" charset="utf-8" src="include/tinymce/init.js"></script>
       <textarea id="content" name="content" rows="20">{$product.content}</textarea>
       /TinyMCE --> 
      </dd>
     </dl>
     <dl>
      <dt>{$lang.thumb}</dt>
      <dd>
       <input type="file" name="image" size="38" class="inpFlie" />
       {if $product.image}<a href="{$product.image}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}</dd>
     </dl>
     <!-- {if $site.stock} -->
     <dl>
      <dt>{$lang.product_stock}</dt>
      <dd>
       <input type="text" name="stock" value="{if $product.stock}{$product.stock}{else}100{/if}" size="10" class="inpMain" />
      </dd>
     </dl>
     <!-- {/if} -->
     <dl>
      <dt>{$lang.keywords}</dt>
      <dd>
       <input type="text" name="keywords" value="{$product.keywords}" size="114" class="inpMain" />
      </dd>
     </dl>
     <dl>
      <dt>{$lang.description}</dt>
      <dd>
       <textarea name="description" cols="115" rows="3" class="textArea" />{$product.description}</textarea>
      </dd>
     </dl>
     <dl>
      <input type="hidden" name="token" value="{$token}" />
      <input type="hidden" name="id" value="{$product.id}">
      <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
     </dl>
    </div>
   </form>
   <!-- {/if} --> 
   <!-- {if $rec eq 'thumb'} -->
   <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
   <script type="text/javascript">
    {literal}
     function mask(i) {
        document.getElementById('mask').innerHTML += i;
        document.getElementById('mask').scrollTop = 100000000;
     }
     function success() {
        var d=document.getElementById('success');
        d.style.display="block";
     }
    {/literal}
    </script>
   <dl id="maskBox">
    <dt><em>{$mask.count}</em><!-- {if !$mask.confirm} -->
     <form action="product.php?rec=thumb" method="post">
      <input name="confirm" class="btn" type="submit" value="{$lang.product_thumb_start}" />
     </form>
     <!-- {/if} --></dt>
    <dd class="maskBg">{$mask.bg}<i id="success">{$lang.product_thumb_succes}</i></dd>
    <dd id="mask"></dd>
   </dl>
   <!-- {/if} --> 
  </div>
 </div>
 {include file="footer.htm"} </div>
<!-- {if $rec eq 'default'} 商品列表 --> 
<script type="text/javascript">
{literal}onload = function() {document.forms['action'].reset();}{/literal}
</script> 
<!-- {else} --> 
{include file="filebox.htm"} 
<!-- {/if} --> 
<!-- {if $rec neq 're_thumb'} -->
</body>
</html>
<!-- {/if} -->
