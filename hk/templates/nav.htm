<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
<script type="text/javascript" src="images/jquery.tab.js"></script>
</head>
<body>
<div id="dcWrap"> {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain"> {include file="handle.htm"}
  <div class="mainBox" style="{$workspace.height}">
   <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
   <!-- {if $rec eq 'default'} 导航列表 -->
   <div class="navList">
    <!-- {if $_SYSTEM_SIGN != 'miniprogram'} -->
    <ul class="tab">
     <li><a href="nav.php"{if $type eq 'middle'} class="selected"{/if}>{$lang.nav_type_middle}</a></li>
     <li><a href="nav.php?type=top"{if $type eq 'top'} class="selected"{/if}>{$lang.nav_type_top}</a></li>
     <li><a href="nav.php?type=bottom"{if $type eq 'bottom'} class="selected"{/if}>{$lang.nav_type_bottom}</a></li>
    </ul>
    <!-- {/if} -->
    <table width="100%" border="0" cellpadding="10" cellspacing="0" class="tableBasic">
     <tr>
      <th width="123" align="left">{$lang.nav_name}</th>
      <th class="m-none" align="left">{$lang.nav_link}</th>
      <th class="m-none" width="80" align="center">{$lang.sort}</th>
      <th width="120" align="center">{$lang.handler}</th>
     </tr>
     <!-- {foreach from=$nav_list item=nav_list} -->
     <tr>
      <td>{$nav_list.mark} {$nav_list.nav_name}<!-- {if $nav_list.icon} --> <a href="{$nav_list.icon}" target="_blank"><img src="{$nav_list.icon}" width="12" height="12" align="absMiddle"></a><!-- {/if} --></td>
      <td class="m-none">{$nav_list.guide}</td>
      <td class="m-none" align="center">{$nav_list.sort}</td>
      <td align="center"><a href="nav.php?rec=edit&id={$nav_list.id}">{$lang.edit}</a> | <a href="nav.php?rec=del&id={$nav_list.id}">{$lang.del}</a></td>
     </tr>
     <!-- {/foreach} -->
    </table>
   </div>
   <!-- {/if} --> 
   <!-- {if $rec eq 'add'} 导航添加 --> 
   <script type="text/javascript">
     {literal}
     $(function(){ $(".idTabs").idTabs(); });
     {/literal}
    </script>
   <div class="idTabs">
    <!-- {if $_SYSTEM_SIGN != 'miniprogram'} -->
    <ul class="tab">
     <li><a href="#nav_add">{$lang.nav_inside}</a></li>
     <li><a href="#nav_defined">{$lang.nav_defined}</a></li>
    </ul>
    <!-- {/if} -->
    <div class="items" style="margin-top: 20px;">
     <div id="nav_add">
      <form action="nav.php?rec=insert" method="post" enctype="multipart/form-data">
       <div class="formBasic">
        <dl>
         <dt>{$lang.nav_system}</dt>
         <dd>
          <select name="nav_menu" onchange="change('nav_name', this)">
           <option value="">{$lang.nav_menu}</option>
           <!-- {foreach from=$catalog item=catalog} -->
           <option value="{$catalog.module},{$catalog.guide}"{if $catalog.cur} selected="selected"{/if} title="{$catalog.name}">{$catalog.mark} {$catalog.name}</option>
           <!-- {/foreach} -->
          </select>
         </dd>
        </dl>
        <dl>
         <dt>{$lang.nav_name}</dt>
         <dd>
          <input type="text" id="nav_name" name="nav_name" value="" size="40" class="inpMain" />
          <span class="lang">{$btn_lang.nav_name}</span>
          <p class="cue">{$lang.nav_name_cue}</p>
         </dd>
        </dl>
        <!-- {if $_SYSTEM_SIGN != 'miniprogram'} -->
        <!-- {if $site.open_icon} -->
        <dl>
         <dt>{$lang.icon}</dt>
         <dd>
          <input type="file" name="icon" size="38" class="inpFlie" />
          {if $nav_info.icon}<a href="{$nav_info.icon}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}</dd>
        </dl>
        <!-- {/if} -->
        <dl>
         <dt>{$lang.nav_type}</dt>
         <dd>
          <label for="type_0">
           <input type="radio" name="type" id="type_0" value="middle" checked="true" onChange="dou_callback('nav.php?rec=nav_select&id={$nav_info.id}', 'type', this.value, 'parent')">
           {$lang.nav_type_middle}</label>
          <label for="type_1">
           <input type="radio" name="type" id="type_1" value="top" onChange="dou_callback('nav.php?rec=nav_select&id={$nav_info.id}', 'type', this.value, 'parent')">
           {$lang.nav_type_top}</label>
          <label for="type_2">
           <input type="radio" name="type" id="type_2" value="bottom" onChange="dou_callback('nav.php?rec=nav_select&id={$nav_info.id}', 'type', this.value, 'parent')">
           {$lang.nav_type_bottom}</label>
         </dd>
        </dl>
        <dl>
         <dt>{$lang.parent}</dt>
         <dd id="parent">
          <select name="parent_id">
           <option value="0">{$lang.empty}</option>
           <!-- {foreach from=$nav_list item=list} -->
           <option value="{$list.id}">{$list.mark} {$list.nav_name}</option>
           <!-- {/foreach} -->
          </select>
         </dd>
        </dl>
        <!-- {/if} -->
        <dl>
         <dt>{$lang.sort}</dt>
         <dd>
          <input type="text" name="sort" value="50" size="5" class="inpMain" />
         </dd>
        </dl>
        <dl>
         <input type="hidden" name="token" value="{$token}" />
         <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
        </dl>
       </div>
      </form>
     </div>
     <!-- {if $_SYSTEM_SIGN != 'miniprogram'} -->
     <div id="nav_defined">
      <form action="nav.php?rec=insert" method="post" enctype="multipart/form-data">
       <div class="formBasic">
        <dl>
         <dt>{$lang.nav_name}</dt>
         <dd>
          <input type="text" name="nav_name" value="" size="40" class="inpMain" />
          <span class="lang">{$btn_lang.nav_name}</span>
          <p class="cue">{$lang.nav_name_cue}</p>
         </dd>
        </dl>
        <!-- {if $site.open_icon} -->
        <dl>
         <dt>{$lang.icon}</dt>
         <dd>
          <input type="file" name="icon" size="38" class="inpFlie" />
          {if $nav_info.icon}<a href="{$nav_info.icon}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}</dd>
        </dl>
        <!-- {/if} -->
        <dl>
         <dt>{$lang.nav_type}</dt>
         <dd>
          <label for="type_0">
           <input type="radio" name="type" id="type_0" value="middle" checked="true" onChange="dou_callback('nav.php?rec=nav_select&id={$nav_info.id}', 'type', this.value, 'parent_external')">
           {$lang.nav_type_middle}</label>
          <label for="type_1">
           <input type="radio" name="type" id="type_1" value="top" onChange="dou_callback('nav.php?rec=nav_select&id={$nav_info.id}', 'type', this.value, 'parent_external')">
           {$lang.nav_type_top}</label>
          <label for="type_2">
           <input type="radio" name="type" id="type_2" value="bottom" onChange="dou_callback('nav.php?rec=nav_select&id={$nav_info.id}', 'type', this.value, 'parent_external')">
           {$lang.nav_type_bottom}</label>
         </dd>
        </dl>
        <dl>
         <dt>{$lang.nav_link}</dt>
         <dd>
          <input type="text" name="guide" value="" size="80" class="inpMain" />
          <span class="lang">{$btn_lang.guide}</span>
         </dd>
        </dl>
        <dl>
         <dt>{$lang.parent}</dt>
         <dd id="parent_external">
          <select name="parent_id">
           <option value="0">{$lang.empty}</option>
           <!-- {foreach from=$nav_list item=list} -->
           <option value="{$list.id}">{$list.mark} {$list.nav_name}</option>
           <!-- {/foreach} -->
          </select>
         </dd>
        </dl>
        <dl>
         <dt>{$lang.sort}</dt>
         <dd>
          <input type="text" name="sort" value="50" size="5" class="inpMain" />
         </dd>
        </dl>
        <dl>
         <input type="hidden" name="token" value="{$token}" />
         <input type="hidden" name="nav_menu" value="nav,0" />
         <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
        </dl>
       </div>
      </form>
     </div>
     <!-- {/if} -->
    </div>
   </div>
   <!-- {/if} --> 
   <!-- {if $rec eq 'edit'} 导航编辑 -->
   <form action="nav.php?rec=update" method="post" enctype="multipart/form-data">
    <div class="formBasic"> 
     <!-- {if $nav_info.module neq 'nav'} -->
     <dl>
      <dt>{$lang.nav_system}</dt>
      <dd>
       <select name="nav_menu" onchange="change('nav_name', this)">
        <option value="">{$lang.nav_menu}</option>
        <!-- {foreach from=$catalog item=catalog} -->
        <option value="{$catalog.module},{$catalog.guide}"{if $catalog.cur} selected="selected"{/if} title="{$catalog.name}">{$catalog.mark} {$catalog.name}</option>
        <!-- {/foreach} -->
       </select>
      </dd>
     </dl>
     <!-- {/if} -->
     <dl>
      <dt>{$lang.nav_name}</dt>
      <dd>
       <input type="text" id="nav_name" name="nav_name" value="{$nav_info.nav_name}" size="40" class="inpMain" />
       <span class="lang">{$btn_lang.nav_name}</span>
       <p class="cue">{$lang.nav_name_cue}</p>
      </dd>
     </dl>
     <!-- {if $_SYSTEM_SIGN != 'miniprogram'} -->
     <!-- {if $site.open_icon} -->
     <dl>
      <dt>{$lang.icon}</dt>
      <dd>
       <input type="file" name="icon" size="38" class="inpFlie" />
       {if $nav_info.icon}<a href="{$nav_info.icon}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}</dd>
     </dl>
     <!-- {/if} -->
     <dl>
      <dt>{$lang.nav_type}</dt>
      <dd>
       <label for="type_0">
        <input type="radio" name="type" id="type_0" value="middle"{if $nav_info.type eq 'middle'} checked="true"{/if} onChange="dou_callback('nav.php?rec=nav_select&id={$nav_info.id}', 'type', this.value, 'parent')">
        {$lang.nav_type_middle}</label>
       <label for="type_1">
        <input type="radio" name="type" id="type_1" value="top"{if $nav_info.type eq 'top'} checked="true"{/if} onChange="dou_callback('nav.php?rec=nav_select&id={$nav_info.id}', 'type', this.value, 'parent')">
        {$lang.nav_type_top}</label>
       <label for="type_2">
        <input type="radio" name="type" id="type_2" value="bottom"{if $nav_info.type eq 'bottom'} checked="true"{/if} onChange="dou_callback('nav.php?rec=nav_select&id={$nav_info.id}', 'type', this.value, 'parent')">
        {$lang.nav_type_bottom}</label>
      </dd>
     </dl>
     <!-- {/if} -->
     <dl>
      <dt>{$lang.nav_link}</dt>
      <dd> 
       <!-- {if $nav_info.module eq 'nav'} -->
       <input type="text" name="guide" value="{$nav_info.url}" size="60" class="inpMain" />
       <span class="lang">{$btn_lang.guide}</span>
       <!-- {else} -->
       <input type="text" name="guide_no" value="{$nav_info.url}" size="60" readOnly="true" class="inpMain" />
       <b class="cue">{$lang.nav_not_modify}</b> 
       <!-- {/if} --></dd>
     </dl>
     <!-- {if $_SYSTEM_SIGN != 'miniprogram'} -->
     <dl>
      <dt>{$lang.parent}</dt>
      <dd id="parent">
       <select name="parent_id">
        <option value="0">{$lang.empty}</option>
        <!-- {foreach from=$nav_list item=nav_list} --> 
        <option value="{$nav_list.id}"{if $nav_list.id eq $nav_info.parent_id} selected{/if}>{$nav_list.mark} {$nav_list.nav_name}
        </option>
        <!-- {/foreach} -->
       </select>
      </dd>
     </dl>
     <!-- {/if} -->
     <dl>
      <dt>{$lang.sort}</dt>
      <dd>
       <input type="text" name="sort" value="{if $nav_info.sort}{$nav_info.sort}{else}50{/if}" size="5" class="inpMain" />
      </dd>
     </dl>
     <dl>
      <input type="hidden" name="token" value="{$token}" />
      <input type="hidden" name="id" value="{$nav_info.id}" />
      <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
     </dl>
    </div>
   </form>
   <!-- {/if} --> 
  </div>
 </div>
 {include file="footer.htm"} </div>
</body>
</html>