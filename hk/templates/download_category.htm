<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
</head>
<body>
<div id="dcWrap"> {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain"> {include file="handle.htm"}
  <div class="mainBox" style="{$workspace.height}"> 
   <!-- {if $rec eq 'default'} 分类列表 -->
   <h3><a href="{$action_link.href}" class="actionBtn add">{$action_link.text}</a>{$ur_here}</h3>
   <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
    <tr>
     <th width="120" align="left">{$lang.download_category_cat_name}</th>
     <th align="left">{$lang.unique}</th>
     <th class="m-none" align="left">{$lang.description}</th>
     <th class="m-none" width="60" align="center">{$lang.sort}</th>
     <th width="80" align="center">{$lang.handler}</th>
    </tr>
    <!-- {foreach from=$download_category item=cate} -->
    <tr>
     <td align="left">{$cate.mark} <a href="download.php?cat_id={$cate.cat_id}">{$cate.cat_name}</a></td>
     <td>{$cate.unique_id}</td>
     <td class="m-none">{$cate.description}</td>
     <td class="m-none" align="center">{$cate.sort}</td>
     <td align="center"><a href="download_category.php?rec=edit&cat_id={$cate.cat_id}">{$lang.edit}</a> | <a href="download_category.php?rec=del&cat_id={$cate.cat_id}">{$lang.del}</a></td>
    </tr>
    <!-- {/foreach} -->
   </table>
   <!-- {/if} --> 
   <!-- {if $rec eq 'add' || $rec eq 'edit'} 分类添加或编辑 -->
   <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
   <form action="download_category.php?rec={$form_action}" method="post">
    <div class="formBasic">
     <dl>
      <dt>{$lang.download_category_cat_name}</dt>
      <dd>
       <input type="text" name="cat_name" value="{$cat_info.cat_name}" size="40" class="inpMain" />
       <span class="lang">{$btn_lang.cat_name}</span></dd>
     </dl>
     <dl>
      <dt>{$lang.unique}</dt>
      <dd>
       <input type="text" name="unique_id" value="{$cat_info.unique_id}" size="40" class="inpMain" />
      </dd>
     </dl>
     <dl>
      <dt>{$lang.parent}</dt>
      <dd>
       <select name="parent_id">
        <option value="0">{$lang.empty}</option>
        <!-- {foreach from=$download_category item=cate} --> 
        <!-- {if $cate.cat_id eq $cat_info.parent_id} -->
        <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
        <!-- {else} -->
        <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
        <!-- {/if} --> 
        <!-- {/foreach} -->
       </select>
      </dd>
     </dl>
     <dl>
      <dt>{$lang.keywords}</dt>
      <dd>
       <input type="text" name="keywords" value="{$cat_info.keywords}" size="40" class="inpMain" />
       <span class="lang">{$btn_lang.keywords}</span></dd>
     </dl>
     <dl>
      <dt>{$lang.description}</dt>
      <dd>
       <textarea name="description" cols="60" rows="4" class="textArea">{$cat_info.description}</textarea>
       <p class="lang">{$btn_lang.description}</p>
      </dd>
     </dl>
     <dl>
      <dt>{$lang.sort}</dt>
      <dd>
       <input type="text" name="sort" value="{if $cat_info.sort}{$cat_info.sort}{else}50{/if}" size="5" class="inpMain" />
      </dd>
     </dl>
     <dl>
      <input type="hidden" name="token" value="{$token}" />
      <input type="hidden" name="cat_id" value="{$cat_info.cat_id}" />
      <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
     </dl>
    </div>
   </form>
   <!-- {/if} --> 
  </div>
 </div>
 {include file="footer.htm"} </div>
</body>
</html>