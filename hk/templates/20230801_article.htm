<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
<script type="text/javascript" src="images/jquery.form.min.js"></script>
<script type="text/javascript" src="images/jquery.autotextarea.js"></script>
</head>
<body>
<div id="dcWrap">
 {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain">
   {include file="handle.htm"}
   <div class="mainBox" style="{$workspace.height}">
    <!-- {if $rec eq 'default'} 文章列表 -->
    <h3><a href="{$action_link.href}" class="actionBtn add">{$action_link.text}</a>{$ur_here}</h3>
    <div class="filter">
    <form action="article.php" method="post">
     <select name="cat_id">
      <option value="0">{$lang.uncategorized}</option>
      <!-- {foreach from=$article_category item=cate} -->
      <!-- {if $cate.cat_id eq $cat_id} -->
      <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
      <!-- {else} -->
      <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
      <!-- {/if} -->
      <!-- {/foreach} -->
     </select>
     <input name="keyword" type="text" class="inpMain" value="{$keyword}" size="20" />
     <input name="submit" class="btnGray" type="submit" value="{$lang.btn_filter}" />
    </form>
    <span>
    <!-- {if $sort.handle} -->
    <a class="btnGray" href="article.php?rec=sort&act=handle">{$lang.sort_close}</a>
    <!-- {else} -->
    <a class="btnGray" href="article.php?rec=sort&act=handle">{$lang.sort_article}</a>
    <!-- {/if} -->
    </span>
    </div>
    <!-- {if $sort.handle} -->
    <div class="homeSortRight">
     <ul class="homeSortBg">
      {$sort.bg}
     </ul>
     <ul class="homeSortList">
      <!-- {foreach from=$sort.list item=article} -->
      <li>
       <em>{$article.title}</em>
       <a href="article.php?rec=sort&act=cancel&id={$article.id}" title="{$lang.sort_cancel}">X</a>
      </li>
      <!-- {/foreach} -->
     </ul>
    </div>
    <!-- {/if} -->
    <div id="list"{if $sort.handle} class="homeSortLeft"{/if}>
    <form name="action" method="post" action="article.php?rec=action">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
     <tr>
      <th width="22" align="center"><input name='chkall' type='checkbox' id='chkall' onclick='selectcheckbox(this.form)' value='check'></th>
      <th class="m-none" width="40" align="center">{$lang.record_id}</th>
      <th align="left">{$lang.article_name}</th>
      <th class="m-none" width="150" align="center">{$lang.article_category}</th>
      <th class="m-none" width="80" align="center">{$lang.add_time}</th>
      <th width="80" align="center">{$lang.handler}</th>
     </tr>
     <!-- {foreach from=$article_list item=article} -->
     <tr>
      <td align="center"><input type="checkbox" name="checkbox[]" value="{$article.id}" /></td>
      <td class="m-none" align="center">{$article.id}</td>
      <td><a href="article.php?rec=edit&id={$article.id}">{$article.title}</a><!-- {if $article.image} --> <a href="{$article.image}" target="_blank"><img src="images/icon_picture.png" width="16" height="16" align="absMiddle"></a><!-- {/if} --></td>
      <td class="m-none" align="center"><!-- {if $article.cat_name} --><a href="article.php?cat_id={$article.cat_id}">{$article.cat_name}</a><!-- {else} -->{$lang.uncategorized}<!-- {/if} --></td>
      <td class="m-none" align="center">{$article.add_time}</td>
      <td align="center">
       <!-- {if $sort.handle} -->
       <a href="article.php?rec=sort&act=set&id={$article.id}">{$lang.sort_btn}</a>
       <!-- {else} -->
       <a href="article.php?rec=edit&id={$article.id}">{$lang.edit}</a> | <a href="article.php?rec=del&id={$article.id}">{$lang.del}</a>
       <!-- {/if} -->
      </td>
     </tr>
     <!-- {/foreach} -->
    </table>
    <div class="action">
     <select name="action" onchange="douAction()">
      <option value="0">{$lang.select}</option>
      <option value="del_all">{$lang.del}</option>
      <option value="category_move">{$lang.category_move}</option>
     </select>
     <select name="new_cat_id" style="display:none">
      <option value="0">{$lang.uncategorized}</option>
      <!-- {foreach from=$article_category item=cate} -->
      <!-- {if $cate.cat_id eq $cat_id} -->
      <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
      <!-- {else} -->
      <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
      <!-- {/if} -->
      <!-- {/foreach} -->
     </select>
     <input name="submit" class="btn" type="submit" value="{$lang.btn_execute}" />
    </div>
    </form>
    </div>
    <div class="clear"></div>
    {include file="pager.htm"}
    <!-- {/if} -->
    <!-- {if $rec eq 'add' || $rec eq 'edit'} 文章添加或编辑 -->
    <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
    <form action="article.php?rec={$form_action}" method="post" enctype="multipart/form-data">
     <div class="formBasic">
      <dl>
       <dt>{$lang.article_name}</dt>
       <dd>
        <input type="text" name="title" value="{$article.title}" size="135" class="inpMain" />
       </dd>
      </dl>
      <dl>
       <dt>{$lang.article_category}</dt>
       <dd>
        <select name="cat_id">
         <option value="0">{$lang.uncategorized}</option>
         <!-- {foreach from=$article_category item=cate} -->
         <!-- {if $cate.cat_id eq $article.cat_id} -->
         <option value="{$cate.cat_id}" selected="selected">{$cate.mark} {$cate.cat_name}</option>
         <!-- {else} -->
         <option value="{$cate.cat_id}">{$cate.mark} {$cate.cat_name}</option>
         <!-- {/if} -->
         <!-- {/foreach} -->
        </select>
       </dd>
      </dl>
      <!-- {if $article.defined} -->
      <dl>
       <dt valign="top">{$lang.article_defined}</dt>
       <dd>
        <textarea name="defined" id="defined" cols="50" class="textAreaAuto" style="height:{$article.defined_count}0px">{$article.defined}</textarea>
        <script type="text/javascript">
         {literal}
          $("#defined").autoTextarea({maxHeight:300});
         {/literal}
        </script>
        </dd>
      </dl>
      <!-- {/if} -->
      <dl>
       <dt valign="top">{$lang.article_content}</dt>
       <dd>
        <!-- FileBox -->
        <div id="contentFile" class="fileBox">
         <ul class="fileBtn">
          <li class="btnFile" onclick="fileBox('content');">{$lang.file_insert_image}</li>
          <li class="fileStatus" style="display:none"><img src="images/loader.gif" alt="uploading"/></li>
         </ul>
        </div>
        <!-- /FileBox -->
        <!-- TinyMCE -->
        <script type="text/javascript" charset="utf-8" src="include/tinymce/tinymce.min.js"></script>
        <script type="text/javascript" charset="utf-8" src="include/tinymce/init.js"></script>
        <textarea id="content" name="content" rows="20">{$article.content}</textarea>
        <!-- /TinyMCE -->
       </dd>
      </dl>
      <dl>
       <dt>{$lang.thumb}</dt>
       <dd>
        <input type="file" name="image" size="38" class="inpFlie" />
        {if $article.image}<a href="{$article.image}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}</dd>
      </dl>
      <dl>
       <dt>{$lang.add_time}</dt>
       <dd>
        <input type="text" name="add_time" value="{$article.add_time}" size="15" class="inpMain" />
       </dd>
      </dl>
      <dl>
       <dt>{$lang.keywords}</dt>
       <dd>
        <input type="text" name="keywords" value="{$article.keywords}" size="135" class="inpMain" />
       </dd>
      </dl>
      <dl>
       <dt>{$lang.description}</dt>
       <dd>
        <textarea name="description" cols="115" rows="3" class="textArea" />{$article.description}</textarea>
       </dd>
      </dl>
      <dl>
       <input type="hidden" name="token" value="{$token}" />
       <input type="hidden" name="image" value="{$article.image}">
       <input type="hidden" name="id" value="{$article.id}">
       <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
      </dl>
     </div>
    </form>
    <!-- {/if} -->
   </div>
 </div>
 {include file="footer.htm"}
 </div>
<!-- {if $rec eq 'default'} 文章列表 -->
<script type="text/javascript">
{literal}onload = function() {document.forms['action'].reset();}{/literal}
</script>
<!-- {else} -->
{include file="filebox.htm"}
<!-- {/if} -->
</body>
</html>