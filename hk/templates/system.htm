<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
<script type="text/javascript" src="images/jquery.tab.js"></script>
</head>
<body>
<div id="dcWrap">
 {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain">
   {include file="handle.htm"}
   <div id="system" class="mainBox" style="{$workspace.height}">
    <h3><!-- {if $site.developer} --><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a><!-- {/if} -->{$ur_here}</h3>
    <div class="idTabs">
     <ul class="tab">
      <!-- {foreach from=$cfg item=tab} -->
      <li><a href="#{$tab.name}">{$tab.lang}</a></li>
      <!-- {/foreach} -->
      <!-- {if $parameter_system_list} -->
      <li><a href="#parameter">{$lang.parameter}</a></li>
      <!-- {/if} -->
      <!-- {if $sms_tab} -->
      <li><a href="sms.php">{$lang.sms}</a></li>
      <!-- {/if} -->
      <!-- {foreach from=$lang_list item=item} -->
      <li><a href="language.php?rec=system&language_pack={$item.language_pack}">{$item.name}{$lang.language_setting}</a></li>
      <!-- {/foreach} -->
     </ul>
     <div class="formBox">
      <form action="system.php?rec=update" method="post" enctype="multipart/form-data">
       <!-- {foreach from=$cfg item=cfg} -->
       <div id="{$cfg.name}">
        <div class="formBasic">
         <!-- {foreach from=$cfg.list item=cfg_list} -->
         {if $cfg_list.type neq 'array'}
         <dl{if $cfg_list.name eq $light} class="light"{/if}>
          <dt>{$cfg_list.lang}</dt>
          <dd>
           <!-- {if $cfg_list.type eq 'radio'} -->
           <label for="{$cfg_list.name}_0">
            <input type="radio" name="{$cfg_list.name}" id="{$cfg_list.name}_0" value="0"{if $cfg_list.value eq '0'} checked="true"{/if}>
            {$lang.no}</label>
           <label for="{$cfg_list.name}_1">
            <input type="radio" name="{$cfg_list.name}" id="{$cfg_list.name}_1" value="1"{if $cfg_list.value eq '1'} checked="true"{/if}>
            {$lang.yes}</label>
           <!-- {elseif $cfg_list.type eq 'select'} -->
           <select name="{$cfg_list.name}">
            <!-- {foreach from=$cfg_list.box key=name item=value} -->
            <option value="{$value}"{if $cfg_list.value eq $value} selected{/if}>{$value}</option>
            <!-- {/foreach} -->
           </select>
           <!-- {elseif $cfg_list.type eq 'file'} -->
           <input type="file" name="{$cfg_list.name}" size="18" />
           {if $cfg_list.value}<a href="../{$cfg_list.value}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}
           <!-- {elseif $cfg_list.type eq 'textarea'} -->
           <textarea name="{$cfg_list.name}" cols="83" rows="8" class="textArea" />{$cfg_list.value}</textarea>
           <!-- {else} -->
           {if $cfg_list.name eq 'domain'}
           <input type="text" name="{$cfg_list.name}" value="{$cfg_list.value}" placeholder="{$site.root_url}" size="80" class="inpMain" />
           {else}
           <input type="text" name="{$cfg_list.name}" value="{$cfg_list.value}" size="80" class="inpMain" />
           {/if}
           <!-- {/if} -->
           <!-- {if $cfg_list.cue} -->
            <!-- {if $cfg_list.type eq 'radio' || $cfg_list.type eq 'select'} -->
            <span class="cue ml">{$cfg_list.cue}</span>
            <!-- {else} -->
            <p class="cue">{$cfg_list.cue}</p>
            <!-- {/if} -->
           <!-- {/if} -->
          </dd>
         </dl>
         {else}
         <!-- {foreach from=$cfg_list.value item=cfg} -->
         <dl{if $cfg_list.name eq $light} class="light"{/if}>
          <dt>{$cfg.lang}</dt>
          <dd>
           <input type="text" name="{$cfg.name}" value="{$cfg.value}" size="80" class="inpMain" />
           <!-- {if $cfg.cue} -->
           <p class="cue">{$cfg.cue}</p>
           <!-- {/if} -->
          </dd>
         </dl>
         <!-- {/foreach} -->
         {/if}
         <!-- {/foreach} -->
         <!-- {if $cfg.name eq 'customer'} -->
         <!-- {foreach from=$parameter_customer_list item=parameter} -->
         <dl>
          <dt>{$parameter.lang}</dt>
          <dd>
           <input type="text" name="_parameter_{$parameter.name}" value="{$parameter.value}" size="80" class="inpMain">
           <!-- {if $parameter.cue} -->
											<p class="cue">{$parameter.cue}</p>
											<!-- {/if} -->
          </dd>
         </dl>
         <!-- {/foreach} -->
         <!-- {if $site.developer} 开发者模式才显示增加按钮 -->
         <dl>
          <dd>
           <a class="btnGray" href="parameter.php?rec=add&group=customer">增加</a>
											<p class="cue">如果需要可以增加更多的在线客服联系方式</p>
          </dd>
         </dl>
         <!-- {/if} /开发者模式才显示增加按钮 -->
         <!-- {/if} -->
        </div>
       </div>
       <!-- {/foreach} -->
       <!-- {if $parameter_system_list} -->
       <div id="parameter">
        <div class="formBasic">
         <!-- {foreach from=$parameter_system_list item=parameter} -->
         <dl>
          <dt>{$parameter.lang}</dt>
          <dd>
           <input type="text" name="_parameter_{$parameter.name}" value="{$parameter.value}" size="80" class="inpMain">
           <!-- {if $parameter.cue} -->
											<p class="cue">{$parameter.cue}</p>
											<!-- {/if} -->
          </dd>
         </dl>
         <!-- {/foreach} -->
        </div>
       </div>
       <!-- {/if} -->
       <div class="formBasic">
        <dl>
         <input type="hidden" name="token" value="{$token}" />
         <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
        </dl>
       </div>
       </form>
     </div>
    </div>
   </div>
 </div>
 {include file="footer.htm"}
</div>
<script type="text/javascript">
 {literal}$(function(){$(".idTabs").idTabs();});{/literal}
</script>
</body>
</html>