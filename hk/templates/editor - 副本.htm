<link href="include/umeditor/themes/default/css/umeditor.css" type="text/css" rel="stylesheet">
<!-- {if $js neq 'no'} -->
<script type="text/javascript" src="include/umeditor/umeditor.config.js"></script>
<script type="text/javascript" src="include/umeditor/umeditor.min.js"></script>
<script type="text/javascript" src="include/umeditor/lang/zh-cn/zh-cn.js"></script>
<!-- {/if} -->
<div id="{$name}File" class="fileBox">
 <ul class="fileBtn">
  <li class="btnFile" onclick="fileBox('content', '{$name}', '{$cur}', '{$item_id}');">{$lang.file_insert_image}</li>
  <li class="fileStatus" style="display:none"><img src="images/loader.gif" alt="uploading"/></li>
 </ul>
</div>
<script type="text/plain" id="{$name}" name="{$name}" class="editor">{$value}</script>
<script type="text/javascript">var um = UM.getEditor('{$name}')</script>