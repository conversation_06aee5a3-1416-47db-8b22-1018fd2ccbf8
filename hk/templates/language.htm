<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="copyright" content="DouCo Co.,Ltd." />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>{$lang.home}{if $ur_here} - {$ur_here} {/if}</title>
<link href="{$site.root_url}favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="templates/font-awesome.min.css" rel="stylesheet" type="text/css">
<link href="templates/public.css" rel="stylesheet" type="text/css">
{include file="javascript.htm"}
</head>
<body>
<div id="dcWrap">
 {include file="header.htm"}
 <div id="dcLeft">{include file="menu.htm"}</div>
 <div id="dcMain">
   {include file="handle.htm"}
   <div id="system" class="mainBox" style="{$workspace.height}">
    <!-- {if $rec eq 'default'} 语言列表 -->
    <h3><a href="{$action_link.href}" class="actionBtn add">{$action_link.text}</a>{$ur_here}</h3>
    <div id="list">
     <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
      <tr>
       <th width="120" align="left">{$lang.language_name}</th>
       <th class="m-none" align="center">{$lang.language_language_pack}</th>
       <th class="m-none" align="center">{$lang.language_site_logo}</th>
       <th class="m-none" width="60" align="center">{$lang.sort}</th>
       <th width="80" align="center">{$lang.handler}</th>
      </tr>
      <!-- {foreach from=$language_list item=language} -->
      <tr>
       <td>{$language.name}</td>
       <td class="m-none" align="center">{$language.language_pack}</td>
       <td class="m-none" align="center"><!-- {if $language.site_logo} --><img src="../{$language.site_logo}" height="40" /><!-- {/if} --></td>
       <td class="m-none" align="center">{$language.sort}</td>
       <td align="center"><a href="language.php?rec=edit&language_id={$language.language_id}">{$lang.edit}</a> | <a href="language.php?rec=del&language_id={$language.language_id}">{$lang.del}</a></td>
      </tr>
      <!-- {/foreach} -->
     </table>
    </div>
    <!-- {/if} -->
    <!-- {if $rec eq 'add' || $rec eq 'edit' || $rec eq 'system'} 语言添加或编辑 -->
    <h3><a href="{$action_link.href}" class="actionBtn">{$action_link.text}</a>{$ur_here}</h3>
    <!-- {if $rec eq 'system'} -->
    <div class="idTabs">
     <ul class="tab">
      <!-- {foreach from=$cfg item=tab} -->
      <li><a href="system.php#{$tab.name}">{$tab.lang}</a></li>
      <!-- {/foreach} -->
      <!-- {if $parameter_list} -->
      <li><a href="system.php#parameter">{$lang.parameter}</a></li>
      <!-- {/if} -->
      <!-- {if $sms_tab} -->
      <li><a href="sms.php">{$lang.sms}</a></li>
      <!-- {/if} -->
      <!-- {foreach from=$lang_list item=item} -->
      <li><a{if $item.cur} class="selected"{/if} href="language.php?rec=system&language_pack={$item.language_pack}">{$item.name}{$lang.language_setting}</a></li>
      <!-- {/foreach} -->
     </ul>
    </div>
    <!-- {/if} -->
    <div class="formBox">
     <form action="language.php?rec={$form_action}" method="post" enctype="multipart/form-data">
      <div class="formBasic">
       <!-- {if $rec neq 'system'} -->
       <dl>
        <dt>{$lang.language_name}</dt>
        <dd>
         <input type="text" name="name" value="{$language.name}" size="80" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.language_language_pack}</dt>
        <dd>
         <select name="language_pack">
          <option value="0">{$lang.uncategorized}</option>
          <!-- {foreach from=$language_pack item=pack} -->
          <option value="{$pack.value}"{if $pack.cur} selected="selected"{/if}{if $pack.disabled} disabled{/if}>{$pack.value}</option>
          <!-- {/foreach} -->
         </select>
         <p class="cue">{$lang.language_language_pack_cue}</p>
        </dd>
       </dl>
       <!-- {/if} -->
       <dl>
        <dt>{$lang.language_site_name}</dt>
        <dd>
         <input type="text" name="site_name" value="{$language.site_name}" size="80" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.language_site_title}</dt>
        <dd>
         <input type="text" name="site_title" value="{$language.site_title}" size="130" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.language_site_keywords}</dt>
        <dd>
         <input type="text" name="site_keywords" value="{$language.site_keywords}" size="130" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.language_site_description}</dt>
        <dd>
         <textarea name="site_description" cols="83" rows="3" class="textArea" />{$language.site_description}</textarea>
        </dd>
       </dl>
       <dl>
        <dt>{$lang.language_site_logo}</dt>
        <dd>
         <input type="file" name="site_logo" size="38" />
         {if $language.site_logo}<a href="../{$language.site_logo}" target="_blank"><img src="images/icon_yes.png"></a>{else}<img src="images/icon_no.png">{/if}</dd>
       </dl>
       <dl>
        <dt>{$lang.language_address}</dt>
        <dd>
         <input type="text" name="address" value="{$language.address}" size="80" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.language_tel}</dt>
        <dd>
         <input type="text" name="tel" value="{$language.tel}" size="40" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.language_fax}</dt>
        <dd>
         <input type="text" name="fax" value="{$language.fax}" size="40" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.language_email}</dt>
        <dd>
         <input type="text" name="email" value="{$language.email}" size="40" class="inpMain" />
        </dd>
       </dl>
       <dl>
        <dt>{$lang.sort}</dt>
        <dd><input type="text" name="sort" value="{if $language.sort}{$language.sort}{else}50{/if}" size="5" class="inpMain" /></dd>
       </dl>
       <dl>
        <!-- {if $rec eq 'system'} -->
        <input type="hidden" name="name" value="{$language.name}" />
        <input type="hidden" name="language_pack" value="{$language.language_pack}" />
        <input type="hidden" name="mode" value="system" />
        <!-- {/if} -->
        <input type="hidden" name="token" value="{$token}" />
        <input type="hidden" name="site_logo" value="{$language.site_logo}">
        <input type="hidden" name="language_id" value="{$language.language_id}">
        <input name="submit" class="btn" type="submit" value="{$lang.btn_submit}" />
       </dl>
      </div>
     </form>
    </div>
    <!-- {/if} -->
   </div>
 </div>
 {include file="footer.htm"}
 </div>
</body>
</html>