<?php

define('IN_DOUCO', true);

require (dirname(__FILE__) . '/include/init.php');

// rec操作项的初始化
$rec = $check->is_rec($_REQUEST['rec']) ? $_REQUEST['rec'] : 'default';

// 验证并获取合法的REQUEST
$module = $check->is_letter($_REQUEST['module']) ? $_REQUEST['module'] : '';

// 图片上传
include_once (ROOT_PATH . 'include/file.class.php');
$file = new File('images/' . $module . '/'); // 实例化类文件(文件上传路径，结尾加斜杠)

// 赋值给模板
$smarty->assign('rec', $rec);
$smarty->assign('cur', 'file');

/**
 * +----------------------------------------------------------
 * 文件上传盒子
 * +----------------------------------------------------------
 */
if ($rec == 'box') {
    // 验证并获取合法的REQUEST
    $item_id = $check->is_number($_REQUEST['item_id']) ? $_REQUEST['item_id'] : '';
    $type = $check->is_letter($_REQUEST['type']) ? $_REQUEST['type'] : '';
    $target = $check->is_basic_string($_REQUEST['target']) ? $_REQUEST['target'] : '';
    $img_width = $check->is_number($_REQUEST['img_width']) ? $_REQUEST['img_width'] : $_CFG['img_width'];

    // 文件上传盒子
    $custom_filename = $item_id . '_' . $target . '_' . $dou->create_rand_string('number', 6, time());
    $image = $file->box($module, $item_id, $target . '_file', $target, $custom_filename, '', '', $img_width, $_CFG['watermark']);
     
    if ($type == 'content') {
        $html = '<img src="' . $dou->dou_file($image) . '" data-file="' . $image . '" />';
    } else {
        $html = $dou->get_file_list($module, $item_id, $type);
    }
    
    echo $html;
}

/**
 * +----------------------------------------------------------
 * 文件删除
 * +----------------------------------------------------------
 */
elseif ($rec == 'del') {
    // 验证并获取合法的REQUEST
    $number = preg_match("/^[a-z0-9.]+$/", $_REQUEST['number']) ? $_REQUEST['number'] : '';
    $file_info = $dou->get_row('file', 'module, item_id, type', "number = '$number'");
    
    // 删除文件
    $dou->del_file($number);
 
    // 显示已经上传的文件列表
    $html = $dou->get_file_list($file_info['module'], $file_info['item_id'], $file_info['type']);
    
    echo $html;
} 

/**
 * +----------------------------------------------------------
 * 大文件上传
 * +----------------------------------------------------------
 */
elseif ($rec == 'bigfile') {
    $file_type = 'jpg,jpeg,gif,png,zip,rar,pdf,xls,xlsx,doc,docx,wmv,avi,mp4,flv';
 
    if ($_REQUEST['act'] == 'ext') {
        $name = explode(".", $_REQUEST['check_filename']); // 将上传前的文件以“.”分开取得文件类型
        $img_count = count($name); // 获得截取的数量
        $img_type = $name[$img_count - 1]; // 取得文件的类型
        if (stripos($file_type, $img_type) === false) {
            echo $_LANG['file_support'] . $file_type . $_LANG['file_support_no'] . $img_type;
        }
        exit;
    }
     
    // 调用方法，返回结果
    $file->bigfile('file', $_POST['blob_num'], $_POST['total_blob_num'], $_POST['file_name'], $_POST['file_md5_value'], $file_type);
}

?>