<?php

define('IN_DOUCO', true);

require (dirname(__FILE__) . '/include/init.php');

// rec操作项的初始化
$rec = $check->is_rec($_REQUEST['rec']) ? $_REQUEST['rec'] : 'default';

// 验证并获取合法的ID，如果不合法将其设定为-1
$cat_id = $firewall->get_legal_id('article_category', $_REQUEST['cat_id'], $_REQUEST['unique_id']);
if ($cat_id == -1 || $cat_id == 0) {
    $cat_id = $dou->get_one("SELECT cat_id FROM " . $dou->table('article_category') . " ORDER BY sort ASC, cat_id ASC");
}

// 主数据
if ($rec == 'default') {
    // 获取上一个和下一个分类ID
    $id_list = $dou->module_data('article', 'catid');
    $cur_key = array_search($cat_id, $id_list);
    $near_id['previous'] = $id_list[$cur_key - 1] ? $id_list[$cur_key - 1] : '';
    $near_id['next'] = $id_list[$cur_key + 1] ? $id_list[$cur_key + 1] : '';

    // 分类产品列表
    $data['main'] = $dou->module_data('article', 'main', $cat_id, '', $near_id);

    // 分类菜单
    $data['tree'] = $dou->module_data('article', 'tree', $cat_id);
    
    // 当前cat_id
    $data['cat_id'] = $cat_id;
    
    // 当前NAV的位置，用于计算当前顶部菜单的滚动位置
    $data['current_nav'] = $cur_key;
}

// 标题
elseif ($rec == 'title') {
    $data['title'] = $_LANG['article_category'];
}

// 列表高度
elseif ($rec == 'height') {
    $height = $dou->num_rows($dou->query("SELECT * FROM " . $dou->table('article') . " WHERE cat_id IN (" . $cat_id . $dou->dou_child_id('article_category', $cat_id) . ")")) * 70;
    $data['height'] = $height >= 350 ? $height : 350;
}

echo json_encode($data);

?>