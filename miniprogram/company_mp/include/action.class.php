<?php

if (!defined('IN_DOUCO')) {
    die('Hacking attempt');
}
class Action extends Common {
    /**
     * +----------------------------------------------------------
     * 获取产品目录
     * +----------------------------------------------------------
     * $table 数据表名
     * $type 数据类型
     * $current_id 当前页面栏目ID
     * $parent_id 默认获取一级导航
     * +----------------------------------------------------------
     */
    function module_data($module, $type = null, $current_id = null, $parent_id = 0, $near_id = '') {
        if (is_numeric($parent_id)) {
            $where = " WHERE parent_id = '$parent_id'";
        }
        
        $sql = "SELECT * FROM " . $this->table($module . '_category') . $where . " ORDER BY sort ASC, cat_id ASC";
        $query = $this->query($sql);
        while ($row = $this->fetch_array($query)) {
            if ($type == 'id_list') {
                $data[] = $row['cat_id'];
            } elseif ($type == 'tree') {
                $data[] = array (
                        "cat_id" => $row['cat_id'],
                        "cat_name" => $row['cat_name'],
                        "cur" => $row['cat_id'] == $current_id ? true : false
                );
            } else {
               $list = array();
               if ($row['cat_id'] == $current_id || in_array($row['cat_id'], $near_id)) {
                    $list = $this->mp_get_list($module, $row['cat_id']);
                }
                $data[] = array (
                        "cat_id" => $row['cat_id'],
                        "list" => $list
                );
            }
        }
        
        return $data;
    }
    
    /**
     * +----------------------------------------------------------
     * 获取列表
     * +----------------------------------------------------------
     * $module 模块
     * $cat_id 分类ID
     * $num 调用数量
     * $sort 排序
     * +----------------------------------------------------------
     */
    function mp_get_list($module, $cat_id = '', $num = '', $sort = '') {
        $where = $cat_id == 'ALL' ? '' : " WHERE cat_id IN (" . $cat_id . $this->dou_child_id($module . '_category', $cat_id) . ")";
        $sort = $sort ? $sort . ',' : '';
        $limit = $num ? ' LIMIT ' . $num : '';
        
        $sql = "SELECT * FROM " . $this->table($module) . $where . " ORDER BY " . $sort . "id DESC" . $limit;
        $query = $this->query($sql);
        $i = 0;
        while ($row = $this->fetch_array($query)) {
            $i++;
            $item['id'] = $row['id'];
            if ($row['title']) $item['title'] = $row['title'];
            if ($row['name']) $item['name'] = $row['name'];
            if (!empty($row['price'])) $item['price'] = $row['price'] > 0 ? $this->price_format($row['price']) : $GLOBALS['_LANG']['price_discuss'];
            if ($row['click']) $item['click'] = $row['click'];

            $item['add_time'] = date("Y-m-d", $row['add_time']);
            $item['description'] = $row['description'] ? $row['description'] : $this->dou_substr($row['content'], 320);
            $item['thumb'] = $this->dou_file($row['image'], true);
            $item['url'] = $this->rewrite_url($module, $row['id']);
            $item['bor'] = $i%2 == 0 ? ' noBor' : '';
            
            $list[] = $item;
        }
        
        return $list;
    }
 
    /**
     * +----------------------------------------------------------
     * 获取单页面列表
     * +----------------------------------------------------------
     * $module 模块
     * $cat_id 分类ID
     * $num 调用数量
     * $sort 排序
     * +----------------------------------------------------------
     */
    function mp_get_page($type = '', $current_id = '', $parent_id = 0, $near_id = '') {
        if ($parent_id) {
            $where = " WHERE parent_id = '$parent_id'";
        }
        
        $sql = "SELECT id, page_name FROM " . $this->table('page') . $where . " ORDER BY id ASC";
        $query = $this->query($sql);
        while ($row = $this->fetch_array($query)) {
            if ($type == 'id_list') {
                $data[] = $row['id'];
            } elseif ($type == 'tree') {
                $data[] = array (
                        "id" => $row['id'],
                        "page_name" => $row['page_name'],
                        "cur" => $row['id'] == $current_id ? true : false
                );
            } elseif ($type == 'main') {
                $content = ' '; // 这里一定要设置至少一个空格，不然在wxParse使用中会出错
                if ($row['id'] == $current_id || in_array($row['id'], $near_id)) {
                    $content = $this->get_one("SELECT content FROM " . $this->table('page') . " WHERE id = '" . $row['id'] . "'");
                }
                $data[] = array (
                        "id" => $row['id'],
                        "page_name" => $row['page_name'],
                        "content" => $content
                );
            }
        }
        
        return $data;
    }
    
}

?>