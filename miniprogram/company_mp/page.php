<?php

define('IN_DOUCO', true);

require (dirname(__FILE__) . '/include/init.php');

// 验证并获取合法的ID，如果不合法将其设定为-1
$id = $firewall->get_legal_id('page', $_REQUEST['id'], $_REQUEST['unique_id']);
if ($id == -1) {
    $id = $dou->get_one("SELECT id FROM " . $dou->table('page') . " ORDER BY id ASC");
}

// 获取上一个和下一个分类ID
$id_list = $dou->mp_get_page('id_list');
$cur_key = array_search($id, $id_list);
$near_id['previous'] = $id_list[$cur_key - 1] ? $id_list[$cur_key - 1] : '';
$near_id['next'] = $id_list[$cur_key + 1] ? $id_list[$cur_key + 1] : '';

// 标题
$data['title'] = $dou->get_one("SELECT page_name FROM " . $dou->table('page') . " WHERE id = '$id'");

// 分类菜单
$data['tree'] = $dou->mp_get_page('tree', $id);

// 当前cat_id
$data['id'] = $id;

// 当前NAV的位置，用于计算当前顶部菜单的滚动位置
$data['current_nav'] = $cur_key;

// 分类产品列表
$data['main'] = $dou->mp_get_page('main', $id, 0, $near_id);

echo json_encode($data);

?>