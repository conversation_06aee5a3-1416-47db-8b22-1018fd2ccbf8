/*
Theme Name: DouPHP Default
Theme URI: http://demo.douco.com/
Description: DouPHP 默认模板
Version: 1.0
Author: DouCo Co.,Ltd.
Author URI: http://www.douco.com/
*/

/* 初始化
----------------------------------------------- */
page {
 background-color:#F1F1F1;
 height:100%;
 font-size: 13px;
 color: #555;
}
.body {
 background-color:#FFFFFF;
}
.miniH {
 min-height: 1100rpx;
 background-color:#FFFFFF;
}
/* -- nav -- */
.nav {
 height: 31px;
}
.nav .navScroll {
 position:fixed;
 top:0;
 white-space: nowrap;
 height: 31px;
 border-bottom: solid #EEEEEE 1px;
 background-color: #FFFFFF;
}
.nav .navScroll .item {
 display: inline-block;
 height: 30px;
 line-height: 30px;
 margin: 0 8px;
}
.nav .navScroll .active {
 border-bottom: solid #FF0000 1px;
}
/* -- incBox -- */
.incBox {
 margin-bottom: 5px;
 background-color:#FFF;
 height:100%;
}
.incBox .head {
 font-weight: bold;
 font-size: 16px;
 color: #0072C6;
 padding:15px 10px 8px 10px;
}
.incBox .head .more {
 float: right;
 font-size: 13px;
 color: #777777;
}
/* productList
----------------------------------------------- */
.productList {
 zoom: 1;
 overflow: hidden;
 background-color:#FFF;
 font-size: 13px;
}
.productList .item {
 float: left;
 width: 50%;
 height: 190px;
}
.productList .item .border {
 text-align: center;
 border-right: solid #EEEEEE 1px;
 border-top: solid #EEEEEE 1px;
 padding: 10rpx;
}
.productList .item .noBor {
 border-right: none;
}
.productList .item .img {
 margin-bottom: 10rpx;
}
.productList .item .name {
 margin-bottom: 6rpx;
 overflow: hidden;
 text-overflow: ellipsis;
 white-space: nowrap;
}
.productList .item .price {
 color: #999999;
}
/* articleList
----------------------------------------------- */
.articleList {
 background-color:#FFF;
}
.articleList .item {
 border-top: solid #EEEEEE 1px;
 padding: 13px;
 height:43px;
}
.articleList .title {
 margin-bottom: 5px;
 font-size: 13px;
 overflow: hidden;
 text-overflow:ellipsis;
 white-space: nowrap;
}
.articleList .info {
 color: #999999;
}
.articleList .info text {
 margin-right:8px;
}
/* footer
----------------------------------------------- */
.footer {
 display: none;
 background-color: #EEEEEE;
 padding: 10px 0;
 text-align: center;
 color: #999999;
}