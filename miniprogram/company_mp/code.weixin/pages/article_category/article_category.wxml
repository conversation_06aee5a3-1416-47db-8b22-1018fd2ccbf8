<!--article_category.wxml-->
<view class="wrapper">
  <view class='nav'>
    <scroll-view class="navScroll" scroll-x="true" scroll-left="{{scrollLeft}}" scroll-with-animation="true">
      <view class="item{{tree.cur == true ? ' active' : ''}}" wx:for="{{tree}}" wx:key="{{tree.cat_id}}" wx:for-item="tree" data-catid="{{tree.cat_id}}" bindtap="douMenu">{{tree.cat_name}}</view>
    </scroll-view>
  </view>

  <view class='body'>
    <swiper current="{{cur}}" current-item-id="{{currentItemId}}" bindanimationfinish="swiperfinishchange" bindchange="swiperbindchange" style="min-height:{{height}}px">
      <block wx:for="{{main_data}}" wx:key="d" wx:for-item="data">
        <swiper-item item-id="{{data.cat_id}}">
          <view class="articleList" wx:if="{{data.list}}">
            <navigator class="item" hover-class="mouseDown" wx:for="{{data.list}}" wx:key="{{data.cat_id}}" wx:for-item="article" url="/pages/article/article?id={{article.id}}">
              <view class="title">{{article.title}}</view>
              <view class="info">
                <text>{{lang.add_time}}：{{article.add_time}}</text>
                <text>{{lang.click}}：{{article.click}}</text>
              </view>
            </navigator>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <view class="footer" wx:if="{{lang.powered_by}}">{{lang.powered_by}}</view>

</view>