<!--article.wxml-->
<view class="wrapper">
  <view class='body miniH'>
    <view id="article">
      <view class="title">{{article.title}}</view>
      <view class="info">
        {{lang.add_time}}：{{article.add_time}} {{lang.click}}：{{article.click}}
        <block wx:for="{{defined}}" wx:key="defined" wx:for-item="defined">
          {{defined.arr}}：{{defined.value}}
        </block>
      </view>
      <view class="content">
        <import src="../../wxParse/wxParse.wxml" />
        <template is="wxParse" data="{{wxParseData:content.nodes}}" />
      </view>
    </view>
  </view>
  <view class="footer" wx:if="{{lang.powered_by}}">{{lang.powered_by}}</view>
</view>