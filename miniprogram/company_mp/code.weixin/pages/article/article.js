// article.js
// 获取应用实例
const app = getApp()

// 引入wxParse富文本插件
var WxParse = require('../../wxParse/wxParse.js');

// 初始化
var domain = app.globalData.domain

// 引入utils全局函数
var utils = require('../../utils/util.js');

Page({
  onLoad: function(options) {
    var that = this

    // 显示转发按钮
    utils.showShareMenu()

    wx.request({
      url: domain + 'databox.php?rec=common',
      success: function(res) {
        that.setData({
          lang: res.data.lang,
          site: res.data.site
        })
      },
    })

    wx.request({
      url: domain + 'article.php',
      data: {
        id: options.id
      },
      success: function(res) {
        wx.setNavigationBarTitle({
          title: res.data.title
        })

        // 格式化HTML
        var content = res.data.article.content;
        WxParse.wxParse('content', 'html', content, that, 5);

        that.setData({
          article: res.data.article,
          defined: res.data.defined
        })
      },
    })

  }

})