<!--page.wxml-->
<import src="../../wxParse/wxParse.wxml" />
<view class="wrapper">
  <view class='nav'>
    <scroll-view class="navScroll" scroll-x="true" scroll-left="{{scrollLeft}}" scroll-with-animation="true">
      <view class="item{{tree.cur == true ? ' active' : ''}}" wx:for="{{tree}}" wx:key="{{tree.id}}" wx:for-item="tree" data-id="{{tree.id}}" bindtap="douMenu">{{tree.page_name}}</view>
    </scroll-view>
  </view>
  <view class='body'>
    <view id="page">
      <swiper current="{{cur}}" current-item-id="{{currentItemId}}" bindanimationfinish="swiperfinishchange" bindchange="swiperbindchange" style="min-height:830rpx;height:{{swiperHeight}}px">
        <block wx:for="{{main_data}}" wx:key="item" wx:for-item="item">
          <swiper-item item-id="{{item[0].id}}">
            <view id="con{{item[0].id}}" class="content">
              <template is="wxParse" data="{{wxParseData:item}}"/>
            </view>
          </swiper-item>
        </block>
      </swiper>
    </view>
  </view>

  <view class="footer" wx:if="{{lang.powered_by}}">{{lang.powered_by}}</view>

</view>