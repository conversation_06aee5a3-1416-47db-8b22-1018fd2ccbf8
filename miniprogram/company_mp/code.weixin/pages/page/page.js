// page.js
// 获取应用实例
const app = getApp()

// 引入wxParse富文本插件
var WxParse = require('../../wxParse/wxParse.js');

// 初始化
var domain = app.globalData.domain

// 引入utils全局函数
var utils = require('../../utils/util.js');

Page({
  data: {
    duration: 200, // 滑动动画时长
  },

  onLoad: function(options) {
    this.loadData(options.id)
  },

  // 获取数据
  loadData: function(id = 1) {
    var that = this

    // 显示转发按钮
    utils.showShareMenu()

    that.setData({
      currentItemId: id
    })

    wx.request({
      url: domain + 'page.php',
      data: {
        id: id
      },
      success: function(res) {
        wx.setNavigationBarTitle({
          title: res.data.title
        })
        
        let listRes = res.data.main
        for (let i = 0; i < listRes.length; i++) {
          WxParse.wxParse('content' + i, 'html', listRes[i].content, that);
          if (i === listRes.length - 1) {
            WxParse.wxParseTemArray("listArr", 'content', listRes.length, that)
          }
        }
        
        let list = that.data.listArr
        
        list.map((item, index, arr)=>{
          arr[index][0].id = listRes[index]['id']
          arr[index][0].page_name = listRes[index]['page_name']
        });
 
        that.setData({
          tree: res.data.tree,
          main_data: list,
          currentItemId: res.data.id
        })
        
        that.scrollLeft(res.data.current_nav) // 载入当前nav是第几个，计算左侧位置
        that.contentHeight(id) // 计算正文高度
      },
    })
  },

  // 传入参数重新加载
  douMenu: function(e) {
    this.loadData(e.currentTarget.dataset.id)
  },

  swiperbindchange: function(e) {
    this.loadData(e.detail.currentItemId)
  },

  // 防止swiper卡顿
  swiperfinishchange: function(e) {
    this.setData({
      cur: e.detail.current
    })
    if (e.detail.source == "touch") {
      //防止swiper控件卡死
      if (this.data.current == 0 && this.data.rightcurrent > 1) { //卡死时，重置current为正确索引
        this.setData({
          cur: this.data.rightcurrent
        });
      } else { //正常轮转时，记录正确页码索引
        this.setData({
          rightcurrent: this.data.cur
        });
      }
    }
  },

  // 载入当前nav是第几个，计算左侧位置
  scrollLeft: function(currentNav = 1) {
    var that = this

    // 获取导航的初始位置
    const query = wx.createSelectorQuery()
    query.selectAll('.navScroll .item').boundingClientRect();
    query.exec(function(res) {
      var num = 0
      // 计算当前nav左侧view宽度总和
      for (var i = 0; i < currentNav; i++) {
        num += res[0][i].width
      }

      that.setData({
        scrollLeft: Math.ceil(num)
      })
    })
  },
 
  // 计算正文高度
  contentHeight: function(id) {
    var that = this

    var query= wx.createSelectorQuery();
    query.select('#con' + id).boundingClientRect() //里面需要绑定 view组件的id
    query.exec(function (res) { //res为绑定元素信息的数组
      that.setData({
        swiperHeight: res[0].height
      })
    })
  },

  tel: function(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.tel, //此号码并非真实电话号码，仅用于测试
      success: function() {
        console.log("拨打电话成功！")
      },
      fail: function() {
        console.log("拨打电话失败！")
      }
    })
  },


})