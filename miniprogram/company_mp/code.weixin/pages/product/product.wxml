<!--product.wxml-->
<view class="wrapper">
  <view class='body miniH'>
    <view id="product">
      <view class="img">
        <image src="{{product.image}}" width="300" />
      </view>
      <view class="info">
        <view class="title">{{product.name}}</view>
        <view class="defined">
          <view wx:for="{{defined}}" wx:key="defined" wx:for-item="defined">{{defined.arr}}：{{defined.value}}</view>
        </view>
        <view class="price">{{product.price}}</view>
        <view class="tel">
          <view class="dt">{{lang.contact_tel}}：</view>
          <view class="dd" data-tel="{{site.tel}}" bindtap="tel">{{site.tel}}</view>
        </view>
      </view>
      <view class="contentBox">
        <view class="head">{{lang.product_content}}</view>
        <view class="content miniHeight">
          <import src="../../wxParse/wxParse.wxml" />
          <template is="wxParse" data="{{wxParseData:content.nodes}}" />
        </view>
      </view>
    </view>
  </view>
  <view class="footer" wx:if="{{lang.powered_by}}">{{lang.powered_by}}</view>
</view>