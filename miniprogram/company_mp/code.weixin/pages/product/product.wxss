/*
Theme Name: DouPHP Default
Theme URI: http://demo.douco.com/
Description: DouPHP 默认模板
Version: 1.0
Author: DouCo Co.,Ltd.
Author URI: http://www.douco.com/
*/
@import "/wxParse/wxParse.wxss";

/* product
----------------------------------------------- */

#product {
  padding-bottom: 30px;
  background-color: #fff;
}

#product .img {
  border-bottom: 1px solid #eee;
  padding: 10px;
  text-align: center;
}

#product .info {
  padding: 10px;
  line-height: 180%;
}

#product .info .title {
  font-size: 16px;
  color: #333;
}

#product .info .defined {
  color: #999;
  padding: 5px 0;
}

#product .info .price {
  color: #f00;
  font-size: 16px;
}

#product .info .tel {
  margin-top: 20px;
  zoom: 1;
  overflow: hidden;
  line-height: 35px;
  background-color: #19b4ea;
  font-weight: bold;
  font-size: 14px;
}

#product .info .tel .dt {
  float: left;
  width: 82px;
  height: 35px;
  text-align: right;
  padding-right: 3px;
  background-color: #e0e0e0;
  color: #333;
}

#product .info .tel .dd {
  float: left;
  width: 150px;
  height: 35px;
  color: #fff;
  padding-left: 10px;
}

#product .info .tel .dd a {
  color: #fff;
}

#product .contentBox {
  padding: 15px 0;
}

#product .contentBox .head {
  height: 35px;
  line-height: 35px;
  color: #777;
  font-weight: bold;
  font-size: 14px;
  border-bottom: 1px solid #eee;
  padding-left: 10px;
}

#product .contentBox .content {
  padding: 10px;
  color: #888;
  line-height: 200%;
}

#product .contentBox .content image {
  max-width: 100%;
}
