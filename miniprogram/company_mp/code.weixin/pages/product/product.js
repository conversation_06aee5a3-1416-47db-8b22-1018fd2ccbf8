// index.js
// 获取应用实例
const app = getApp()

// 引入wxParse富文本插件
var WxParse = require('../../wxParse/wxParse.js');

// 初始化
var domain = app.globalData.domain

// 引入utils全局函数
var utils = require('../../utils/util.js');

Page({
  onLoad: function(options) {
    var that = this

    // 显示转发按钮
    utils.showShareMenu()

    wx.request({
      url: domain + 'databox.php?rec=common',
      success: function(res) {
        that.setData({
          lang: res.data.lang,
          site: res.data.site
        })
      },
    })

    wx.request({
      url: domain + 'product.php',
      data: {
        id: options.id
      },
      success: function(res) {
        wx.setNavigationBarTitle({
          title: res.data.title
        })

        // 格式化HTML
        var content = res.data.product.content;
        WxParse.wxParse('content', 'html', content, that, 5);

        that.setData({
          product: res.data.product,
          defined: res.data.defined
        })
      },
    })
  },

  tel: function(e) {
    wx.makePhoneCall({
      phoneNumber: e.currentTarget.dataset.tel, //此号码并非真实电话号码，仅用于测试
      success: function() {
        console.log("拨打电话成功！")
      },
      fail: function() {
        console.log("拨打电话失败！")
      }
    })
  },


})