// index.js
// 获取应用实例
const app = getApp()

// 初始化
var domain = app.globalData.domain

// 引入utils全局函数
var utils = require('../../utils/util.js');

Page({
  // 获取数据
  onLoad: function() {
    var that = this; // this代表着当前的对象，它在程序中随着执行的上下文随时会变化，在success中的this跟这里的已经不是同一个对象

    // 显示转发按钮
    utils.showShareMenu()
    
    wx.request({
      url: domain + 'databox.php?rec=common',
      success: function(res) {
        that.setData({
          lang: res.data.lang,
          site: res.data.site
        })
        wx.setNavigationBarTitle({
          title: res.data.site.site_name
        })
      },
    })

    wx.request({
      url: domain,
      success: function(res) {
        that.setData({
          show_list: res.data.show_list,
          recommend_product: res.data.recommend_product,
          recommend_article: res.data.recommend_article
        })
      },
    })

  },

  // 幻灯
  data: {
    indicatorDots: true, // 是否显示面板指示点
    indicatorColor: 'rgba(0, 0, 0, .3)', // 指示点颜色
    indicatorActiveColor: '#FF0000', // 高亮颜色
    autoplay: true,
    interval: 5000, // 自动切换时间间隔
    duration: 500, // 滑动动画时长
    imgheight: '', // 图片最终显示大小是根据imgwidth/imgheight比例然后进行同比例缩放
  },

  imageLoad: function(e) {
    var windowWidth = wx.getSystemInfoSync().windowWidth
    //获取可使用窗口宽度
    var height = e.detail.height
    //获取图片实际高度
    var width = e.detail.width
    //获取图片实际宽度
    var imgheight = windowWidth * height / width +"px"
    //计算等比swiper高度
    this.setData({
      imgheight: imgheight
    })
  },

  swiperbindchange: function(e) {
    this.setData({
      cur: e.detail.current
    })
  },
 
  // 防止swiper卡顿
  swiperfinishchange: function(e) {
    this.setData({
      cur: e.detail.current
    })
    if (e.detail.source == "touch") {
      //防止swiper控件卡死
      if (this.data.current == 0 && this.data.rightcurrent > 1) { //卡死时，重置current为正确索引
        this.setData({
          cur: this.data.rightcurrent
        });
      } else { //正常轮转时，记录正确页码索引
        this.setData({
          rightcurrent: this.data.cur
        });
      }
    }
  },

  // 非tabBar的页面，可以带参数
  douNavigateTo: function(e) {
    var url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url: url
    })
  },

  // 只能跳转到tabBar配置页面
  douSwitchTab: function(e) {
    var url = e.currentTarget.dataset.url;
    wx.switchTab({
      url: url
    })
  },

})