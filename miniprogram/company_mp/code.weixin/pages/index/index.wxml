<!--index.wxml-->
<view class="wrapper">
  <swiper indicator-dots="{{indicatorDots}}" indicator-color="{{indicatorColor}}" indicator-active-color="{{indicatorActiveColor}}" autoplay="{{autoplay}}" current="{{cur}}" interval="{{interval}}" duration="{{duration}}" bindchange="swiperbindchange" bindanimationfinish="swiperfinishchange" style="height: {{imgheight}};">
    <block wx:for="{{show_list}}" wx:key="s" wx:for-item="show">
      <swiper-item>
        <image src="{{show.show_img}}" class="slide" bindload="imageLoad" mode="widthFix" />
      </swiper-item>
    </block>
  </swiper>

  <view class='index'>
    <view class="incBox">
      <view class="head">
        <text class="more" data-url="/pages/product_category/product_category" bindtap="douSwitchTab">{{lang.product_more}}</text>{{lang.product_news}}</view>
      <view class="productList">
        <navigator class="item" hover-class="mouseDown" wx:for="{{recommend_product}}" wx:key="p" wx:for-index="i" wx:for-item="product" url="/pages/product/product?id={{product.id}}">
          <view class="border{{product.bor}}">
            <view class="img">
              <image src="{{product.thumb}}" style="width:135px; height: 135px;" />
            </view>
            <view class="name">{{product.name}}</view>
            <view class="price">{{product.price}}</view>
          </view>
        </navigator>
      </view>
    </view>

    <view class="incBox">
      <view class="head">
        <text class="more" data-url="/pages/article_category/article_category" bindtap="douSwitchTab">{{lang.article_more}}</text>{{lang.article_news}}</view>
      <view class="articleList">
        <navigator class="item" wx:for="{{recommend_article}}" wx:key="a" wx:for-item="article" url="/pages/article/article?id={{article.id}}">
          <view class="title">{{article.title}}</view>
          <view class="info">
            <text>{{lang.add_time}}：{{article.add_time}}</text>
            <text>{{lang.click}}：{{article.click}}</text>
          </view>
        </navigator>
      </view>
    </view>

  </view>

  <view class="footer" wx:if="{{lang.powered_by}}">{{lang.powered_by}}</view>

</view>