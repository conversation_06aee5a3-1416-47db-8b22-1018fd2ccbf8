// pages/product_category/product_category.js
// 获取应用实例
const app = getApp()

// 初始化
var domain = app.globalData.domain

// 引入utils全局函数
var utils = require('../../utils/util.js');

Page({
  data: {
    duration: 200, // 滑动动画时长
  },

  // 传入参数重新加载
  douMenu: function(e) {
    this.loadData(e.currentTarget.dataset.catid)
  },

  swiperbindchange: function(e) {
    this.loadData(e.detail.currentItemId)
  },

  // 防止swiper卡顿
  swiperfinishchange: function(e) {
    this.setData({
      cur: e.detail.current
    })
    if (e.detail.source == "touch") {
      //防止swiper控件卡死
      if (this.data.current == 0 && this.data.rightcurrent > 1) { //卡死时，重置current为正确索引
        this.setData({
          cur: this.data.rightcurrent
        });
      } else { //正常轮转时，记录正确页码索引
        this.setData({
          rightcurrent: this.data.cur
        });
      }
    }
  },

  onLoad: function(options) {
    // 显示转发按钮
    utils.showShareMenu()
   
    wx.request({
      url: domain + 'product_category.php?rec=title',
      success: function(res) {
        wx.setNavigationBarTitle({
          title: res.data.title
        })
      },
    })
    this.loadData(options.cat_id)
  },

  // 获取数据
  loadData: function(cat_id = 0) {
    var that = this

    that.setData({
      currentItemId: cat_id
    })
   
    wx.request({
      url: domain + 'product_category.php?rec=height',
      data: {
        cat_id: cat_id
      },
      success: function(res) {
        that.setData({
          height: res.data.height
        })
      },
    })

    wx.request({
      url: domain + 'product_category.php',
      data: {
        cat_id: cat_id
      },
      success: function(res) {
        that.setData({
          tree: res.data.tree,
          main_data: res.data.main,
          currentItemId: res.data.cat_id
        })

        that.scrollLeft(res.data.current_nav) // 载入当前nav是第几个，计算左侧位置
      },
    })
  },

  // 载入当前nav是第几个，计算左侧位置
  scrollLeft: function(currentNav = 1) {
    var that = this

    // 获取导航的初始位置
    const query = wx.createSelectorQuery()
    query.selectAll('.navScroll .item').boundingClientRect();
    query.exec(function(res) {
      var num = 0
      // 计算当前nav左侧view宽度总和
      for (var i = 0; i < currentNav; i++) {
        num += res[0][i].width
      }

      that.setData({
        scrollLeft: Math.ceil(num)
      })
    })

  }

})