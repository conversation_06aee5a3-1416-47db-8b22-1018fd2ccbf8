{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"urlCheck": true, "es6": false, "postcss": true, "minified": true, "newFeature": true, "coverView": true, "autoAudits": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "uglifyFileName": false, "enhance": true, "useMultiFrameRuntime": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "useStaticServer": true, "showES6CompileOption": false, "disableUseStrict": false, "useCompilerPlugins": false, "minifyWXML": true, "useIsolateContext": true, "ignoreUploadUnusedFiles": true}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "libVersion": "2.26.0", "packOptions": {"ignore": [], "include": []}}