<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="<?php echo $this->_tpl_vars['keywords']; ?>
" />
<meta name="description" content="<?php echo $this->_tpl_vars['description']; ?>
" />
<meta name="generator" content="<?php echo $this->_tpl_vars['generator']; ?>
" />
<title><?php echo $this->_tpl_vars['page_title']; ?>
</title>
<link href="<?php echo $this->_tpl_vars['site']['root_url']; ?>
favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="https://www.woto.cn/theme/default/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="https://www.woto.cn/theme/default/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="https://www.woto.cn/theme/default/style.css" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="<?php echo $this->_tpl_vars['page_title']; ?>
" />
<meta property="og:description" content="<?php echo $this->_tpl_vars['description']; ?>
" />
<meta property="og:image" content="<?php echo $this->_tpl_vars['product']['thumb']; ?>
" />
<meta property="og:url" content="<?php echo $this->_tpl_vars['product']['url']; ?>
" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="<?php echo $this->_tpl_vars['page_title']; ?>
" />
<meta name="twitter:description" content="<?php echo $this->_tpl_vars['description']; ?>
" />
<meta name="twitter:image" content="<?php echo $this->_tpl_vars['product']['thumb']; ?>
" />

<!-- Structured Data -->
<?php echo '
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "CollectionPage",
'; ?>

  "name": "<?php echo $this->smarty_modifier_escape($this->_tpl_vars['page_title'], 'javascript'); ?>
",
  "description": "<?php echo $this->smarty_modifier_escape($this->_tpl_vars['description'], 'javascript'); ?>
",
  "url": "<?php echo $this->smarty_modifier_escape($this->_tpl_vars['cate_info']['url'], 'javascript'); ?>
",
  "hasPart": [
<?php unset($this->_sections['i']);
$this->_sections['i']['name'] = 'i';
$this->_sections['i']['loop'] = is_array($_loop=$this->_tpl_vars['product_list']) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['i']['show'] = true;
$this->_sections['i']['max'] = $this->_sections['i']['loop'];
$this->_sections['i']['step'] = 1;
$this->_sections['i']['start'] = $this->_sections['i']['step'] > 0 ? 0 : $this->_sections['i']['loop']-1;
if ($this->_sections['i']['show']) {
    $this->_sections['i']['total'] = $this->_sections['i']['loop'];
    if ($this->_sections['i']['total'] == 0)
        $this->_sections['i']['show'] = false;
} else
    $this->_sections['i']['total'] = 0;
if ($this->_sections['i']['show']):

            for ($this->_sections['i']['index'] = $this->_sections['i']['start'], $this->_sections['i']['iteration'] = 1;
                 $this->_sections['i']['iteration'] <= $this->_sections['i']['total'];
                 $this->_sections['i']['index'] += $this->_sections['i']['step'], $this->_sections['i']['iteration']++):
$this->_sections['i']['rownum'] = $this->_sections['i']['iteration'];
$this->_sections['i']['index_prev'] = $this->_sections['i']['index'] - $this->_sections['i']['step'];
$this->_sections['i']['index_next'] = $this->_sections['i']['index'] + $this->_sections['i']['step'];
$this->_sections['i']['first']      = ($this->_sections['i']['iteration'] == 1);
$this->_sections['i']['last']       = ($this->_sections['i']['iteration'] == $this->_sections['i']['total']);
?>
    <?php echo '{'; ?>

      "@type": "ListItem",
      "position": <?php echo $this->_sections['i']['iteration']; ?>
,
      "url": "<?php echo $this->smarty_modifier_escape($this->_tpl_vars['product_list'][$this->_sections['i']['index']]['url'], 'javascript'); ?>
"
    <?php echo '}'; ?>
<?php if (! $this->_sections['i']['last']): ?>,<?php endif; ?>
<?php endfor; endif; ?>
  ]
<?php echo '
}
</script>
'; ?>


<!-- Canonical Link -->
<link rel="canonical" href="<?php echo $this->_tpl_vars['product']['url']; ?>
" />
</head>
<body>
<div id="wrapper"> <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/header.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
 <div id="product-category" class="container mb">
  <div class="row">
   <div class="col-md-2"> <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/product_tree.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?> </div>
   <div class="col-md-10"> <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/ur_here.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <div class="row product-list"> 
     <?php $_from = $this->_tpl_vars['product_list']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }$this->_foreach['product_list'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['product_list']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['product']):
        $this->_foreach['product_list']['iteration']++;
?>
     <div class="col-md-3 col-6">
      <div class="item">
       <div class="img scale"><a href="<?php echo $this->_tpl_vars['product']['url']; ?>
"><img src="<?php echo $this->_tpl_vars['product']['thumb']; ?>
" alt="<?php echo $this->_tpl_vars['product']['name']; ?>
" title="<?php echo $this->_tpl_vars['product']['name']; ?>
" /></a></div>
       <div class="name"><a href="<?php echo $this->_tpl_vars['product']['url']; ?>
"><?php echo $this->_tpl_vars['product']['name']; ?>
</a></div>
      </div>
     </div>
     <?php endforeach; endif; unset($_from); ?> 
    </div>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/pager.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?> </div>
  </div>
 </div>
 <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/online_service.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
 <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/footer.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?> </div>
<script type="text/javascript" src="https://www.woto.cn/theme/default/js/jquery.min.js"></script>
<script type="text/javascript" src="https://www.woto.cn/theme/default/js/bootstrap.min.js"></script>
<script type="text/javascript" src="https://www.woto.cn/theme/default/js/dou.js"></script>
</body>
</html>