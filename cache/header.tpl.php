<header id="header">
 <div class="top d-none d-md-block">
  <div class="container">
   <ul class="top-nav">
    <?php $_from = $this->_tpl_vars['nav_top_list']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['nav']):
?> 
    <?php if ($this->_tpl_vars['nav']['child']): ?>
    <li class="parent"><a href="<?php echo $this->_tpl_vars['nav']['url']; ?>
"><?php echo $this->_tpl_vars['nav']['nav_name']; ?>
</a>
     <ul>
      <?php $_from = $this->_tpl_vars['nav']['child']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['child']):
?>
      <li><a href="<?php echo $this->_tpl_vars['child']['url']; ?>
"><?php echo $this->_tpl_vars['child']['nav_name']; ?>
</a></li>
      <?php endforeach; endif; unset($_from); ?>
     </ul>
    </li>
    <li class="spacer"></li>
    <?php else: ?>
    <li><a href="<?php echo $this->_tpl_vars['nav']['url']; ?>
"<?php if ($this->_tpl_vars['nav']['target']): ?> target="_blank"<?php endif; ?>><?php echo $this->_tpl_vars['nav']['nav_name']; ?>
</a></li>
    <li class="spacer"></li>
    <?php endif; ?> 
    <?php endforeach; endif; unset($_from); ?>
    <li><a href="javascript:AddFavorite('<?php echo $this->_tpl_vars['site']['root_url']; ?>
', '<?php echo $this->_tpl_vars['site']['site_name']; ?>
')"><?php echo $this->_tpl_vars['lang']['add_favorite']; ?>
</a></li>
    <?php if ($this->_tpl_vars['open']['user']): ?> 
    <?php if ($this->_tpl_vars['global_user']): ?>
    <li class="spacer"></li>
    <li><a href="<?php echo $this->_tpl_vars['url']['user']; ?>
"><?php echo $this->_tpl_vars['global_user']['user_name']; ?>
，<?php echo $this->_tpl_vars['lang']['user_welcom_top']; ?>
</a></li>
    <li class="spacer"></li>
    <li><a href="<?php echo $this->_tpl_vars['url']['logout']; ?>
"><?php echo $this->_tpl_vars['lang']['user_logout']; ?>
</a></li>
    <?php else: ?>
    <li class="spacer"></li>
    <li><a href="<?php echo $this->_tpl_vars['url']['login']; ?>
"><?php echo $this->_tpl_vars['lang']['user_login_nav']; ?>
</a></li>
    <li class="spacer"></li>
    <li><a href="<?php echo $this->_tpl_vars['url']['register']; ?>
"><?php echo $this->_tpl_vars['lang']['user_register_nav']; ?>
</a></li>
    <?php endif; ?> 
    <?php endif; ?>
   </ul>
   <ul class="search">
    <div class="search-box">
     <form name="search" id="search" method="get" action="<?php echo $this->_tpl_vars['site']['root_url']; ?>
">
      <input name="s" type="text" class="keyword" value="<?php if ($this->_tpl_vars['keyword']): ?><?php echo $this->smarty_modifier_escape($this->_tpl_vars['keyword']); ?>
<?php else: ?><?php echo $this->_tpl_vars['lang']['search_product']; ?>
<?php endif; ?>" onclick="inputClick(this, '<?php echo $this->_tpl_vars['lang']['search_product']; ?>
')" size="25" autocomplete="off">
      <input type="submit" class="btnSearch" value="<?php echo $this->_tpl_vars['lang']['btn_submit']; ?>
">
     </form>
    </div>
   </ul>
  </div>
 </div>
 <nav class="navbar navbar-expand-sm custom_nav">
  <div class="container">
   <div class="navbar-brand">
    <a href="<?php echo $this->_tpl_vars['site']['root_url']; ?>
" class="logo"><img src="https://www.woto.cn/theme/default/images/<?php echo $this->_tpl_vars['site']['site_logo']; ?>
" alt="<?php echo $this->_tpl_vars['site']['site_name']; ?>
" title="<?php echo $this->_tpl_vars['site']['site_name']; ?>
" /></a>
   </div>
   <div class="navbar-action d-md-none">
    <?php if ($this->_tpl_vars['open']['user']): ?>
    <a href="<?php echo $this->_tpl_vars['url']['user']; ?>
" class="fa fa-user-circle-o"></a>
    <?php endif; ?>
    <button type="button" class="menu" data-toggle="collapse" data-target="#main-nav" aria-controls="main-nav" aria-expanded="false" aria-label="Toggle navigation"> <span class="fa fa-bars"></span> </button>
   </div>
   <div class="main-nav collapse navbar-collapse justify-content-md-end" id="main-nav">
    <ul class="navbar-nav">
     <li class="nav-item<?php if ($this->_tpl_vars['index']['cur']): ?> active<?php endif; ?>"> <a class="nav-link" href="<?php echo $this->_tpl_vars['site']['root_url']; ?>
"><?php echo $this->_tpl_vars['lang']['home']; ?>
</a></li>
     <?php $_from = $this->_tpl_vars['nav_middle_list']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }$this->_foreach['nav_middle_list'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['nav_middle_list']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['nav']):
        $this->_foreach['nav_middle_list']['iteration']++;
?>
     <li class="nav-item<?php if ($this->_tpl_vars['nav']['child']): ?> dropdown<?php endif; ?><?php if ($this->_tpl_vars['nav']['cur']): ?> active<?php endif; ?>"> <a href="<?php echo $this->_tpl_vars['nav']['url']; ?>
" class="nav-link<?php if ($this->_tpl_vars['nav']['child']): ?> dropdown-toggle<?php endif; ?><?php if ($this->_tpl_vars['nav']['cur']): ?> active<?php endif; ?>"<?php if ($this->_tpl_vars['nav']['child']): ?> id="dropdown<?php echo $this->_foreach['nav']['iteration']; ?>
" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"<?php endif; ?>><?php echo $this->_tpl_vars['nav']['nav_name']; ?>
</a>
      <?php if ($this->_tpl_vars['nav']['child']): ?>
      <ul class="dropdown-menu" aria-labelledby="dropdown<?php echo $this->_foreach['nav']['iteration']; ?>
">
       <?php $_from = $this->_tpl_vars['nav']['child']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['child']):
?>
       <li<?php if ($this->_tpl_vars['child']['child']): ?> class="dropdown-submenu"<?php endif; ?>> <a class="dropdown-item<?php if ($this->_tpl_vars['child']['child']): ?> dropdown-toggle<?php endif; ?>" href="<?php echo $this->_tpl_vars['child']['url']; ?>
"<?php if ($this->_tpl_vars['child']['child']): ?> data-toggle="dropdown" aria-haspopup="true"<?php endif; ?>><?php echo $this->_tpl_vars['child']['nav_name']; ?>
</a>
        <?php if ($this->_tpl_vars['child']['child']): ?>
        <ul class="dropdown-menu">
         <?php $_from = $this->_tpl_vars['child']['child']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['children']):
?>
         <li><a class="dropdown-item" href="<?php echo $this->_tpl_vars['children']['url']; ?>
"><?php echo $this->_tpl_vars['children']['nav_name']; ?>
</a></li>
         <?php endforeach; endif; unset($_from); ?>
        </ul>
        <?php endif; ?>
       </li>
       <?php endforeach; endif; unset($_from); ?>
      </ul>
      <?php endif; ?>
     </li>
     <?php endforeach; endif; unset($_from); ?>
    </ul>
    <form class="form-inline my-2 my-lg-0 d-md-none" action="<?php echo $this->_tpl_vars['site']['root_url']; ?>
">
     <input class="form-control mr-sm-2" name="s" type="search" value="<?php echo $this->smarty_modifier_escape($this->_tpl_vars['keyword']); ?>
" placeholder="<?php echo $this->_tpl_vars['lang']['search_product']; ?>
" aria-label="<?php echo $this->_tpl_vars['lang']['search_product']; ?>
">
     <button class="btn btn-outline-success my-2 my-sm-0" type="submit"><?php echo $this->_tpl_vars['lang']['btn_submit']; ?>
</button>
    </form>
   </div>
  </div>
 </nav>
</header>
