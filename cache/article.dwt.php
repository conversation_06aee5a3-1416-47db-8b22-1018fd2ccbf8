<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="<?php echo $this->_tpl_vars['keywords']; ?>
" />
<meta name="description" content="<?php echo $this->_tpl_vars['description']; ?>
" />
<meta name="generator" content="<?php echo $this->_tpl_vars['generator']; ?>
" />
<title><?php echo $this->_tpl_vars['page_title']; ?>
</title>
<link href="<?php echo $this->_tpl_vars['site']['root_url']; ?>
favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="https://www.woto.cn/theme/default/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="https://www.woto.cn/theme/default/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="https://www.woto.cn/theme/default/style.css?v=<?php echo $this->_tpl_vars['css_timestamp']; ?>
" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="<?php echo $this->_tpl_vars['page_title']; ?>
" />
<meta property="og:description" content="<?php echo $this->_tpl_vars['description']; ?>
" />
<meta property="og:image" content="<?php echo $this->_tpl_vars['article']['image']; ?>
" />
<meta property="og:url" content="<?php echo $this->_tpl_vars['article']['url']; ?>
" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="<?php echo $this->_tpl_vars['page_title']; ?>
" />
<meta name="twitter:description" content="<?php echo $this->_tpl_vars['description']; ?>
" />
<meta name="twitter:image" content="<?php echo $this->_tpl_vars['article']['image']; ?>
" />

<!-- Structured Data -->
<?php echo '
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "name": "{$article.title}",
  "image": "{$article.image}",
  "description": "{$description}",
  "url": "{$article.url}",
  "datePublished": "{$article.add_time}"
}
</script>
'; ?>


<!-- 内联样式确保文章内容样式正确渲染 -->
<?php echo '
<style type="text/css">
/* 文章内容样式强化 */
#article .content {
  color: #333333 !important; /* 使用!important增加优先级 */
  line-height: 1.6;
  font-size: 16px;
  padding: 15px 0;
  overflow: hidden;
}

/* 确保文章内容中的元素可以保留自定义颜色 */
#article .content * {
  color: inherit; /* 继承父元素的颜色，而不是强制设置 */
}

/* 确保行内样式的颜色优先级更高 */
#article .content [style*="color"] {
  color: inherit !important;
}

#article .content h1, 
#article .content h2, 
#article .content h3, 
#article .content h4, 
#article .content h5, 
#article .content h6 {
 margin: 25px 0 15px;
 color: #333;
 font-weight: bold;
 line-height: 1.4;
}

#article .content h1 { font-size: 28px; }
#article .content h2 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
#article .content h3 { font-size: 20px; }
#article .content h4 { font-size: 18px; }
#article .content h5 { font-size: 16px; }
#article .content h6 { font-size: 16px; color: #777; }

#article .content p {
 margin-bottom: 16px;
 line-height: 1.6;  /* 修改行距为更合理的1.6 */
 /* 移除了text-indent: 2em; 不再强制段首缩进 */
}

#article .content p:empty {
 margin: 0;
}

#article .content img {
 max-width: 100%;
 height: auto;
 display: block;
 margin: 20px auto;
 border-radius: 4px;
}

/* 表格样式 */
#article .content table {
 width: 100%;
 border-collapse: collapse;
 border-spacing: 0;
 margin: 20px 0;
 overflow: auto;
}

#article .content table th,
#article .content table td {
 padding: 10px 15px;
 border: 1px solid #ddd;
 text-align: left;
}

#article .content table th {
 background-color: #f7f7f7;
 font-weight: bold;
}

#article .content table tr:nth-child(even) {
 background-color: #fcfcfc;
}

#article .content table tr:hover {
 background-color: #f5f5f5;
}
</style>
'; ?>


<!-- Canonical Link -->
<link rel="canonical" href="<?php echo $this->_tpl_vars['article']['url']; ?>
" />
</head>
<body>
<div id="wrapper"> <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/header.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
 <div class="container mb">
  <div class="row">
   <div class="col-md-2"> <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/article_tree.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?> </div>
   <div class="col-md-10"> <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/ur_here.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <div id="article">
     <h1><?php echo $this->_tpl_vars['article']['title']; ?>
</h1>
     <div class="info"><?php echo $this->_tpl_vars['lang']['add_time']; ?>
：<?php echo $this->_tpl_vars['article']['add_time']; ?>
 <?php echo $this->_tpl_vars['lang']['click']; ?>
：<?php echo $this->_tpl_vars['article']['click']; ?>
 
      <?php if ($this->_tpl_vars['defined']): ?> 
      <?php $_from = $this->_tpl_vars['defined']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }$this->_foreach['defined'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['defined']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['defined']):
        $this->_foreach['defined']['iteration']++;
?> <?php echo $this->_tpl_vars['defined']['arr']; ?>
：<?php echo $this->_tpl_vars['defined']['value']; ?>
<?php endforeach; endif; unset($_from); ?> 
      <?php endif; ?>
     </div>
     <div class="content"> <?php echo $this->_tpl_vars['article']['content']; ?>
 </div>
     <div class="lift">
      <?php if ($this->_tpl_vars['lift']['previous']): ?>
      <span><?php echo $this->_tpl_vars['lang']['article_previous']; ?>
：<a href="<?php echo $this->_tpl_vars['lift']['previous']['url']; ?>
"><?php echo $this->_tpl_vars['lift']['previous']['title']; ?>
</a></span>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['lift']['next']): ?>
      <span><?php echo $this->_tpl_vars['lang']['article_next']; ?>
：<a href="<?php echo $this->_tpl_vars['lift']['next']['url']; ?>
"><?php echo $this->_tpl_vars['lift']['next']['title']; ?>
</a></span>
      <?php endif; ?>
     </div>
    </div>
    <?php if ($this->_tpl_vars['open']['comment']): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/comment.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>
   </div>
  </div>
 </div>
 <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/online_service.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
 <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/footer.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?> </div>
<script type="text/javascript" src="https://www.woto.cn/theme/default/js/jquery.min.js"></script>
<script type="text/javascript" src="https://www.woto.cn/theme/default/js/bootstrap.min.js"></script>
<script type="text/javascript" src="https://www.woto.cn/theme/default/js/dou.js"></script>
</body>
</html>
