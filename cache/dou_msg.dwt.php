<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta name="keywords" content="<?php echo $this->_tpl_vars['keywords']; ?>
" />
<meta name="description" content="<?php echo $this->_tpl_vars['description']; ?>
" />
<meta name="generator" content="<?php echo $this->_tpl_vars['generator']; ?>
" />
<title><?php echo $this->_tpl_vars['page_title']; ?>
</title>
<link href="<?php echo $this->_tpl_vars['site']['root_url']; ?>
favicon.ico" rel="shortcut icon" type="image/x-icon" />
<link href="https://www.woto.cn/theme/default/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
<link href="https://www.woto.cn/theme/default/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
<link href="https://www.woto.cn/theme/default/style.css" rel="stylesheet" type="text/css" />

<!-- Open Graph tags -->
<meta property="og:title" content="<?php echo $this->_tpl_vars['page_title']; ?>
" />
<meta property="og:description" content="<?php echo $this->_tpl_vars['description']; ?>
" />
<meta property="og:url" content="<?php echo $this->_tpl_vars['url']; ?>
" />
<meta property="og:type" content="website" />

<!-- Twitter Card tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="<?php echo $this->_tpl_vars['page_title']; ?>
" />
<meta name="twitter:description" content="<?php echo $this->_tpl_vars['description']; ?>
" />

<!-- Structured Data -->
<?php echo '
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "{$page_title}",
  "description": "{$description}",
  "url": "{$url}"
}
</script>
'; ?>


<!-- Canonical Link -->
<link rel="canonical" href="<?php echo $this->_tpl_vars['url']; ?>
" />

<?php if ($this->_tpl_vars['url']): ?>
<meta http-equiv="refresh" content="<?php echo $this->_tpl_vars['time']; ?>
; URL=<?php echo $this->_tpl_vars['url']; ?>
" />
<?php else: ?>
<script type="text/javascript">
<?php echo '
function go() {
    window.history.go( - 1);
}
setTimeout("go()", 3000);
'; ?>

</script>
<?php endif; ?>
</head>
<body>
<div id="wrapper"> <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/header.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
 <div id="dou-msg" class="wrap">
  <dl>
   <dt><?php echo $this->_tpl_vars['text']; ?>
</dt>
   <dd><?php echo $this->_tpl_vars['cue']; ?>
<a href="<?php if ($this->_tpl_vars['url']): ?><?php echo $this->_tpl_vars['url']; ?>
<?php else: ?>javascript:history.go(-1);<?php endif; ?>"><?php echo $this->_tpl_vars['lang']['dou_msg_back']; ?>
</a></dd>
  </dl>
 </div>
 <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/online_service.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
 <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "inc/footer.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?> </div>
<script type="text/javascript" src="https://www.woto.cn/theme/default/js/jquery.min.js"></script>
<script type="text/javascript" src="https://www.woto.cn/theme/default/js/bootstrap.min.js"></script>
<script type="text/javascript" src="https://www.woto.cn/theme/default/js/dou.js"></script>
</body>
</html>