<link href="include/UEditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet">
<?php if ($this->_tpl_vars['js'] != 'no'): ?>
<script type="text/javascript" charset="utf-8" src="include/UEditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="include/UEditor/ueditor.all.min.js"></script>
<script type="text/javascript" charset="utf-8" src="include/UEditor/lang/zh-cn/zh-cn.js"></script>
<?php endif; ?>
<div id="<?php echo $this->_tpl_vars['name']; ?>
File" class="fileBox">
    <ul class="fileBtn">
        <li class="btnFile" onclick="fileBox('content', '<?php echo $this->_tpl_vars['name']; ?>
', '<?php echo $this->_tpl_vars['cur']; ?>
', '<?php echo $this->_tpl_vars['item_id']; ?>
');"><?php echo $this->_tpl_vars['lang']['file_insert_image']; ?>
</li>
        <li class="fileStatus" style="display:none"><img src="images/loader.gif" alt="uploading" /></li>
    </ul>
</div>
<script type="text/plain" id="<?php echo $this->_tpl_vars['name']; ?>
" name="<?php echo $this->_tpl_vars['name']; ?>
" class="editor"><?php echo $this->_tpl_vars['value']; ?>
</script>
<script type="text/javascript">var ue = UE.getEditor('<?php echo $this->_tpl_vars['name']; ?>
')</script>
