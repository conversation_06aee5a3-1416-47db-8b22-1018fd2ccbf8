<?php
// 核心计算数据
$data = [
    [[1]],
    [[1, 2]],
    [[1, 2, 4], [1, 3, 4]],
    [[1, 2, 5, 7], [1, 3, 6, 7]],
    [[1, 2, 5, 10, 12], [1, 3, 8, 9, 12], [1, 3, 8, 11, 12], [1, 4, 5, 10, 12]],
    [[1, 2, 5, 11, 13, 18], [1, 2, 5, 11, 16, 18], [1, 2, 9, 12, 14, 18], [1, 2, 9, 13, 15, 18], [1, 3, 8, 14, 17, 18], [1, 4, 6, 10, 17, 18], [1, 5, 7, 10, 17, 18], [1, 6, 8, 14, 17, 18]],
    [[1, 2, 5, 11, 19, 24, 26], [1, 2, 8, 12, 21, 24, 26], [1, 2, 12, 17, 20, 24, 26], [1, 3, 4, 11, 17, 22, 26], [1, 3, 6, 15, 19, 25, 26], [1, 3, 7, 10, 15, 25, 26], [1, 3, 8, 14, 22, 23, 26], [1, 3, 8, 16, 22, 25, 26], [1, 4, 5, 13, 19, 24, 26], [1, 5, 10, 16, 23, 24, 26]],
    [[1, 2, 5, 10, 16, 23, 33, 35], [1, 3, 13, 20, 26, 31, 34, 35]],
    [[1, 2, 6, 13, 26, 28, 36, 42, 45], [1, 4, 10, 18, 20, 33, 40, 44, 45]],
    [[1, 2, 7, 11, 24, 27, 35, 42, 54, 56], [1, 3, 15, 22, 30, 33, 46, 50, 55, 56]],
    [[1, 2, 5, 14, 29, 34, 48, 55, 65, 71, 73], [1, 2, 10, 20, 25, 32, 53, 57, 59, 70, 73], [1, 3, 9, 19, 26, 40, 45, 60, 69, 72, 73], [1, 4, 15, 17, 21, 42, 49, 54, 64, 72, 73]],
    [[1, 3, 7, 25, 30, 41, 44, 56, 69, 76, 77, 86], [1, 10, 11, 18, 31, 43, 46, 57, 62, 80, 84, 86]]
];

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    // 获取并验证输入
    $f0Val = filter_input(INPUT_POST, 'f0', FILTER_VALIDATE_FLOAT);
    $deltaVal = filter_input(INPUT_POST, 'delta', FILTER_VALIDATE_FLOAT);
    $fsVal = filter_input(INPUT_POST, 'fs', FILTER_VALIDATE_INT);
    
    $response = ['success' => false, 'message' => '', 'data' => null];
    
    // 输入验证
    if ($f0Val === false || $f0Val === null) {
        $response['message'] = '起始频率不是数字';
        echo json_encode($response);
        exit;
    }
    
    if ($deltaVal === false || $deltaVal === null) {
        $response['message'] = '间隔不是数字';
        echo json_encode($response);
        exit;
    }
    
    if ($fsVal === false || $fsVal === null || $fsVal < 1) {
        $response['message'] = '信道数不是大于0的整数';
        echo json_encode($response);
        exit;
    }
    
    if ($fsVal > count($data)) {
        $response['message'] = '信道数过大，超出计算范围';
        echo json_encode($response);
        exit;
    }
    
    try {
        // 执行计算
        $raw = $data[$fsVal - 1];
        $results = [];
        
        foreach ($raw as $nthRaw) {
            $result = [];
            foreach ($nthRaw as $n) {
                $cal = $f0Val + ($n - 1) * $deltaVal;
                $result[] = number_format($cal, 6, '.', '');
            }
            $results[] = $result;
        }
        
        $response['success'] = true;
        $response['data'] = $results;
    } catch (Exception $e) {
        $response['message'] = '计算过程出现错误';
    }
    
    echo json_encode($response);
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>无线电频率三阶互调计算 - 深圳市华通通讯有限公司  0755-83763900</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #007bff;
            --primary-dark: #0056b3;
            --background-color: #f8f9fa;
            --card-background: #ffffff;
            --text-color: #333333;
            --border-radius: 8px;
            --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        h1 {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            margin: 0;
            font-size: clamp(1.2rem, 4vw, 2rem);
            box-shadow: var(--box-shadow);
        }

        h1 span {
            display: block;
            font-size: 1rem;
            margin-top: 0.5rem;
            font-weight: normal;
            opacity: 0.9;
        }

        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .card {
            background-color: var(--card-background);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--box-shadow);
            transition: transform var(--transition-speed);
            animation: fadeIn 0.5s ease-out;
        }

        .inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .input-line {
            margin-bottom: 1rem;
        }

        .input-line label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }

        .input-line input,
        .input-line select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e2e8f0;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all var(--transition-speed);
        }

        .input-line input:focus,
        .input-line select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        #calculate {
            display: inline-block;
            padding: 0.75rem 2rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            transition: all var(--transition-speed);
        }

        #calculate:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #alert {
            color: #dc2626;
            margin-top: 1rem;
            font-weight: 500;
        }

        .result {
            background-color: var(--card-background);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-top: 1.5rem;
            box-shadow: var(--box-shadow);
            animation: fadeInUp 0.5s ease-out;
        }

        .result h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }

        .result span {
            display: inline-block;
            background-color: #f1f5f9;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            margin: 0.25rem;
            font-family: monospace;
            animation: fadeIn 0.3s ease-out;
        }

        .summary {
            background-color: #f8fafc;
            padding: 1rem;
            border-radius: var(--border-radius);
            margin: 1rem 0;
            border-left: 4px solid var(--primary-color);
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .card {
                padding: 1rem;
            }
            
            .input-line {
                margin-bottom: 1rem;
            }
            
            #calculate {
                width: 100%;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <h1 class="animate__animated animate__fadeIn">无线电频率三阶互调计算<span>深圳市华通通讯有限公司</span></h1>
    <div class="container">
        <div class="card">
            <div class="inputs">
                <div class="input-line">
                    <label>起始频点：</label>
                    <input type="text" id="f0" placeholder="请输入起始频点 (MHz)">
                </div>
                <div class="input-line">
                    <label>间隔：</label>
                    <select id="delta">
                        <option value="0.00625" selected="selected">0.00625</option>
                        <option value="0.0125">0.0125</option>
                        <option value="0.025">0.025</option>
                        <option value="0.05">0.05</option>
                        <option value="0.1">0.1</option>
                        <option value="0.15">0.15</option>
                        <option value="0.2">0.2</option>
                        <option value="0.25">0.25</option>
                    </select>
                </div>
                <div class="input-line">
                    <label>信道数：</label>
                    <input type="text" id="fs" placeholder="请输入信道数">
                </div>
            </div>
            <button id="calculate" class="animate__animated animate__fadeIn">计算</button>
            <div id="alert"></div>
        </div>
        <div id="resultOutput"></div>
    </div>

    <script>
        document.getElementById('calculate').onclick = function() {
            const alertElement = document.getElementById('alert');
            const calculateButton = document.getElementById('calculate');
            
            alertElement.innerHTML = '';
            calculateButton.disabled = true;
            calculateButton.style.opacity = '0.7';
            
            const formData = new FormData();
            formData.append('f0', document.getElementById('f0').value);
            formData.append('delta', document.getElementById('delta').value);
            formData.append('fs', document.getElementById('fs').value);
            
            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alertElement.innerHTML = data.message;
                    alertElement.classList.add('shake');
                    setTimeout(() => alertElement.classList.remove('shake'), 500);
                    return;
                }
                
                const results = data.data;
                let resultHtml = '';
                
                if (results.length > 0 && results[0].length > 1) {
                    const result = results[0];
                    const first = Number(result[0]);
                    const last = Number(result[result.length - 1]);
                    resultHtml += '<div class="summary">';
                    resultHtml += '<p>带宽：' + (last - first).toFixed(6) + ' MHz';
                    if ((last - first) > 0.6) {
                        resultHtml += '（大于0.6MHz，请用宽带腔体双工器）';
                    }
                    resultHtml += '</p><p>中心频率：' + ((first + last) / 2).toFixed(6) + ' MHz</p>';
                    resultHtml += '</div>';
                }
                
                for (let i = 0; i < results.length; i++) {
                    resultHtml += `<div class="result">`;
                    resultHtml += `<h3>第${i + 1}组：</h3>`;
                    resultHtml += '<p>';
                    const result = results[i];
                    for (let j = 0; j < result.length; j++) {
                        resultHtml += `<span>${result[j]} MHz</span>`;
                    }
                    resultHtml += '</p>';
                    resultHtml += '</div>';
                }
                
                document.getElementById('resultOutput').innerHTML = resultHtml;
            })
            .catch(error => {
                console.error('Error:', error);
                alertElement.innerHTML = '计算过程出现错误';
                alertElement.classList.add('shake');
                setTimeout(() => alertElement.classList.remove('shake'), 500);
            })
            .finally(() => {
                calculateButton.disabled = false;
                calculateButton.style.opacity = '1';
            });
        };

        // 添加回车键支持
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('calculate').click();
            }
        });
    </script>
</body>
</html>